<template>
  <el-form
    ref="ruleFormRef"
    :model="userInfoForm"
    label-width="auto"
    label-position="top"
    :rules="rules"
    size="large"
  >
    <el-form-item
      :label="t('trade_common_setPassword')"
      required
      prop="password"
    >
      <el-input
        v-model="userInfoForm.password"
        type="password"
        clearable
        :placeholder="t('trade_common_setPasswordTip')"
        :show-password="true"
      />
    </el-form-item>
    <el-form-item
      :label="t('trade_common_confirmPassword')"
      required
      prop="nextPassword"
    >
      <el-input
        v-model="userInfoForm.nextPassword"
        type="password"
        clearable
        :placeholder="t('trade_login_passwordConfirmPlace')"
        :show-password="true"
      />
    </el-form-item>
    <el-form-item class="user-login-btn-container">
      <UserError :respMessage="respMessage" :loginForm="userInfoForm" />
      <el-button
        class="user-login-button"
        type="primary"
        @click="onSubmit(ruleFormRef)"
        >{{ t("trade_login_resetAndLogin") }}</el-button
      >
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import type { FormInstance, FormRules } from "element-plus";
import { resetPswByEmail } from "@/api/login";
import { LoginSuccessResponseDataProp } from "../utils/type.d";
import UserError from "./UserError.vue";

const { t } = useI18n();

interface PasswordProp {
  password: string;
  nextPassword: string;
}
const userInfoForm = reactive<PasswordProp>({
  password: "",
  nextPassword: ""
});
const ruleFormRef = ref<FormInstance>();
const respMessage = ref<string>("");
const props = defineProps({
  email: {
    type: String as PropType<string>
  }
});

const validatePass = (rule: any, value: any, callback: any) => {
  const regex =
    /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9,./<>?;\'\:\"\[\]\\\{\}\|\`\-\=\~\!\@#\$%\^&*()_+]{6,16}$/;
  if (regex.test(value)) {
    callback();
  } else {
    callback(new Error(t("trade_common_setPasswordTip")));
  }
};

const validatePwd = (rule: any, value: any, callback: any) => {
  if (value === userInfoForm.password) {
    callback();
  } else {
    callback(new Error(t("trade_login_passwordInconsistency")));
  }
};

const emit = defineEmits<{
  (e: "handleLoginSuccess", data: LoginSuccessResponseDataProp): void;
}>();

const rules = reactive<FormRules<typeof userInfoForm>>({
  password: [
    {
      required: true,
      message: `${t("trade_login_passwordPlaceholder")}`,
      trigger: "blur"
    },
    { validator: validatePass, trigger: "blur" }
  ],
  nextPassword: [
    {
      required: true,
      message: t("trade_common_confirmPasswordTip"),
      trigger: "blur"
    },
    { validator: validatePass, trigger: "blur" },
    { validator: validatePwd, trigger: "blur" }
  ]
});

const onSubmit = (formEl: FormInstance | undefined): void => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      const response = await resetPswByEmail({
        email: props.email,
        password: userInfoForm.password
      });
      if (response.code !== 0) {
        respMessage.value = t(`${response.msg}`);
      } else {
        emit("handleLoginSuccess", response.data);
      }
    }
  });
};

watch(
  () => userInfoForm,
  () => {
    respMessage.value = "";
  },
  { deep: true }
);
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
