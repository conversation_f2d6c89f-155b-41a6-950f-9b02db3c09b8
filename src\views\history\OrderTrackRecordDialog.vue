<template>
  <LkDialog
    ref="LkDialogRef"
    class="choose-processor-dialog"
    :title="t('trade_order_track')"
    destroy-on-close
    :noFooter="true"
    @confirm="handleChooseConfirm"
    @close="handleClose"
  >
    <template #default>
      <el-scrollbar>
        <div class="time-line-body">
          <el-timeline>
            <el-timeline-item
              v-for="item in trackRecords"
              :key="item"
              :timestamp="$formatDate(item.createTime, 'YYYY-MM-DD HH:mm')"
              placement="top"
            >
              <LkAvater
                :size="18"
                :teamInfo="{
                  avatar: item.user.avatar,
                  username: item.user.username
                }"
              />
              <span class="time-line-stamp-text">{{ item.dynamicInfo }}</span>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-scrollbar>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";
import LkAvater from "@/components/lkAvatar/index";
import { useRoute } from "vue-router";

const props = defineProps({
  trackRecords: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const { t } = useI18n();
const route = useRoute();
const LkDialogRef = ref<any>(null);

const open = async (): Promise<void> => {
  LkDialogRef.value.open();
};

const handleClose = (): void => {};

const emit = defineEmits<{
  (e: "handleChooseConfirm", data, type): void;
}>();

const handleChooseConfirm = (): void => {
  LkDialogRef.value.close();
  handleClose();
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.time-line-body {
  max-height: 80vh;
  padding: 16px 140px 0 105px;

  ::v-deep(.el-timeline-item__content) {
    display: flex;
  }

  ::v-deep(.el-timeline-item__tail) {
    top: 18px;
    height: calc(100% - 24px);
  }
}

.el-timeline-item {
  padding-bottom: 30px;
}

.time-line-stamp-text {
  margin-left: 3px;
  font-size: 14px;
  line-height: 20px;
  color: #262626;
  text-align: left;
}
</style>
