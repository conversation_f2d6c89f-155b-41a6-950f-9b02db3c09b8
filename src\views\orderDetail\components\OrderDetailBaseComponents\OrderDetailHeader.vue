<template>
  <div class="order-detail-region-header">
    <div
      class="order-detail-region-title"
      :style="{ maxWidth: `calc(100% - ${operateBtnWidth}px)` }"
    >
      <el-tooltip
        v-if="relationIconType && ['BY_CLONE_MS'].includes(relationIconType)"
        effect="dark"
        :content="t('trade_order_milestoneinteroperability')"
        placement="top"
        :show-after="500"
      >
        <span class="order-detail-region-tag mutually">S</span>
      </el-tooltip>
      <ReText
        type="info"
        class="order-detail-region-header-titlte"
        :tippyProps="{ delay: 50000 }"
      >
        {{
          widgetTilte
            ? t(widgetTilte)
            : milestoneData?.name || milestoneData?.msName
        }}
      </ReText>
      <el-tooltip
        v-if="relationIconType && relationIconType === 'BY_CLONE_ORDER'"
        effect="dark"
        :content="t('trade_order_replayOrderTip')"
        placement="top"
        :show-after="500"
      >
        <span class="order-detail-region-tag replay">R</span>
      </el-tooltip>
    </div>
    <div
      class="order-detail-region-operating-area"
      :data-id="milestoneData.msId"
    >
      <span
        v-for="item in filteredWidgetButtons"
        :key="item.type"
        class="order-detail-region-operating-btn"
        :class="[item.class]"
        @click="handleBtnTrigger(item)"
      >
        <span v-if="!item.dropdownList">
          <i :class="['iconfont', item.icon]" />
          <span>{{ t(item.label) }}</span>
        </span>
        <el-dropdown
          v-if="item.dropdownList && item.dropdownList.length > 0"
          placement="bottom"
          trigger="click"
          @command="handleBtnTrigger"
        >
          <span class="order-detail-region-operating-btn nopadding">
            <i :class="['iconfont', item.icon]" />
            <span>{{ t(item.label) }}</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu class="order-region-popper">
              <el-dropdown-item
                v-for="child in item.dropdownList"
                :key="child.type"
                :class="[
                  child.class,

                  child.type === 'confirmMilestone' ? 'success' : ''
                ]"
                :command="child"
              >
                <i :class="['iconfont', child.icon]" />
                <el-tooltip
                  v-if="child.type === 'cloneMilesStoneFromOtherOrder'"
                  :disabled="child.class !== 'disabled'"
                  effect="dark"
                  :content="`${t('trade_ms_clone_in_no_click_tip')}`"
                  placement="right-start"
                  :show-after="500"
                >
                  <span>{{ t(child.label) }}</span>
                </el-tooltip>
                <span v-else>{{ t(child.label) }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </span>
    </div>
  </div>
  <slot name="relation" />
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ReText } from "@/components/ReText";
import { obtainBtnPermission } from "@/views/orderDetail/components/utils/auth";
import { computed, onMounted, ref } from "vue";

const { t } = useI18n();
const route = useRoute();

const props = defineProps({
  widgetButtons: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetTilte: {
    type: String as PropType<string>,
    default: ""
  },
  relationIconType: {
    type: String as PropType<string>,
    default: ""
  }
});
// 当前操作区域的宽度
const operateBtnWidth = ref(0);

const emit = defineEmits<{
  (e: "handleTrigger", data): void;
}>();

const filteredWidgetButtons = computed(() => {
  const orderId = route.query.orderId as string;
  const msId = props.milestoneData.msId;

  const hasPermission = (btnPermission: any) => {
    return !btnPermission || obtainBtnPermission(btnPermission, msId, orderId);
  };

  return props.widgetButtons
    .filter((item: any) => {
      if (item.dropdownList) {
        return item.dropdownList.some((dropdownItem: any) =>
          hasPermission(dropdownItem.btnPermission)
        );
      } else {
        // 非下拉按钮直接检查权限
        return hasPermission(item.btnPermission);
      }
    })
    .map((item: any) => {
      if (item.dropdownList) {
        // 返回包含过滤后 dropdownList 的新对象
        return {
          ...item,
          dropdownList: item.dropdownList.filter((dropdownItem: any) =>
            hasPermission(dropdownItem.btnPermission)
          )
        };
      }
      return item;
    });
});

const handleBtnTrigger = (item: any) => {
  if (item.class === "disabled") {
    return;
  }
  emit("handleTrigger", item.type || item);
};

// 获取操作区域宽度的函数
const getElementWidth = (selector: string): number => {
  const element = document.querySelector(selector);
  if (element instanceof HTMLElement) {
    const rect = element.getBoundingClientRect();
    return rect.width;
  }
  return 0; // 如果元素不存在，返回默认值 0
};

onMounted(() => {
  // 获取操作区域的宽度
  const width = getElementWidth(
    `.order-detail-region-operating-area[data-id="${props.milestoneData.msId}"]`
  );
  operateBtnWidth.value = width;
});
</script>

<style lang="scss" scoped>
@use "../style/index.scss";
</style>
