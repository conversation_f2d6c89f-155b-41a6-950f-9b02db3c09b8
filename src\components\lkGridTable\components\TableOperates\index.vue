<template>
  <el-popover
    ref="TableOperationPopoverRef"
    placement="bottom"
    trigger="click"
    style="pointer-events: auto; cursor: pointer"
    :popper-style="{ padding: 0, width: 'auto' }"
    @before-enter="handleVisibleChange"
  >
    <template #reference>
      <i
        class="iconfont link-more font24"
        style="pointer-events: auto; cursor: pointer"
      />
    </template>
    <ul class="operation-container">
      <li
        v-for="operationItem in operationList"
        :key="operationItem.slotName"
        :class="[operationItem?.iconClassName, 'operation-col']"
        @click="handleTableOperate(operationItem.slotName)"
      >
        <span class="table-operate-icon">
          <i class="iconfont" :class="operationItem.icon" />
        </span>
        <span class="table-operate-title">
          {{ operationItem?.title }}
        </span>
      </li>
    </ul>
  </el-popover>
</template>
<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { dict } from "@/utils/dict";
import { getWorkOrderOperationList } from "@/api/order";
import { storageLocal } from "@pureadmin/utils";
import { ref } from "vue";

const props = defineProps({
  tableOpeateLists: {
    type: Object as PropType<any>,
    default: () => {}
  },
  presentView: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderPermissionList: {
    type: Array as PropType<any>,
    default: () => []
  },
  row: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const route = useRoute();
const TableOperationPopoverRef = ref<HTMLElement | any>(null);
const operationList = ref<any>([]);

const emit = defineEmits(["handleOperate", "handleVisibleChange"]);

const handleTableOperate = (slotName: string): void => {
  TableOperationPopoverRef.value.hide();
  emit("handleOperate", slotName, { row: props.row });
};

const handleVisibleChange = async () => {
  if (props.presentView.viewType === "MILESTONE") {
    const sccsId = route.query.sccsId;
    const { code, data } = await getWorkOrderOperationList({
      sccsId: sccsId,
      workOrderId: props.row.workOrderId,
      milestoneId: props.row.msId
    });
    if (code === 0) {
      const workOrderOperations = dict.getDictByCode("work_order_operation");
      const translationLang = storageLocal().getItem("translationLang");
      let workOrderOperationObject: any = {};
      let workOrderOperationIcon: any = {
        VIEW: "link-display",
        DELETE: "link-ashbin",
        APPROVAL: "link-reply",
        APPROVAL_ASSIGN: "link-distribute",
        COLLECT: "link-collect",
        COLLECT_ASSIGN: "link-distribute"
      };
      for (let workOrderOperation of workOrderOperations) {
        workOrderOperationObject[workOrderOperation.value] = {
          title:
            translationLang === "zh"
              ? workOrderOperation.label
              : workOrderOperation.labelEn,
          slotName: `work_order_${workOrderOperation.value.toLocaleLowerCase()}`,
          icon: workOrderOperationIcon[workOrderOperation.value],
          iconClassName:
            workOrderOperation.value === "DELETE" ? "link-delete-icon" : ""
        };
      }

      const tableOperationList = data.map(btnValue => {
        return {
          icon: workOrderOperationObject[btnValue].icon,
          title: workOrderOperationObject[btnValue].title,
          slotName: workOrderOperationObject[btnValue].slotName,
          iconClassName: workOrderOperationObject[btnValue].iconClassName
        };
      });
      operationList.value = tableOperationList;
    }
  } else {
    // 查找当前行的权限
    const currentRowPermission =
      props.orderPermissionList?.[props.row.orderId]?.order;

    if (currentRowPermission) {
      // 过滤掉没有权限的按钮
      operationList.value = props.tableOpeateLists.filter(item => {
        return item.permission?.some(perm =>
          currentRowPermission.includes(perm)
        );
      });
    } else {
      operationList.value = props.tableOpeateLists;
    }
  }
};
</script>
<style lang="scss">
.operation-container {
  .operation-col {
    display: flex;
    align-items: center;
    width: 100%;
    height: 36px;
    padding: 0 15px 0 10px;
    cursor: pointer;

    &:hover {
      background: #f2f2f2;
    }
  }
}
</style>
