import { h } from "vue";
import { VTable } from "@visactor/vue-vtable";
import { storageSession } from "@pureadmin/utils";
import { ElRate } from "element-plus";
import DrInput from "@/components/FormDesigner/DrInput";
import DrTextarea from "@/components/FormDesigner/DrTextarea";
import DrInputNumber from "@/components/FormDesigner/DrInputNumber";
import DrPercentage from "@/components/FormDesigner/DrPercentage";
import DrRadio from "@/components/FormDesigner/DrRadio";
import DrCheckbox from "@/components/FormDesigner/DrCheckbox";
import DrDatePicker from "@/components/FormDesigner/DrDatePicker";
import DrRate from "@/components/FormDesigner/DrRate";
import DrSignature from "@/components/FormDesigner/DrSignature";
import DrDialog from "@/components/FormDesigner/DrDialog";
import DrImagesUpload from "@/components/FormDesigner/DrImagesUpload/index";
import DrSCCSMemberSelect from "@/components/FormDesigner/DrSCCSMemberSelect";
import DrSCCSGroupMemberSelect from "@/components/FormDesigner/DrSCCSGroupMemberSelect";
import LKSubTable from "@/components/lkWidgetForm/src/lkSubTable.vue";
import TagColumn from "@/components/lkGridTable/components/TagColumn";
import AvatarGroup from "@/components/lkGridTable/components/AvatarGroup";
import FileColumn from "@/components/lkGridTable/components/FileColumns";
import LkHeaderColumn from "@/components/lkHeaderColumn/index";

const getColumnCustomLayoutTemplate = (column: any, cb: any) => {
  if (["DrRadio", "DrCheckbox"].includes(column.type)) {
    return {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);
        let tagList: any[] = [];

        if (value) {
          const widgetValue = value instanceof Array ? value : value.split(",");
          for (let widgetItem of widgetValue) {
            const optionItem = column.props.options.find(
              childOption => childOption.value === widgetItem
            );
            if (optionItem) {
              tagList.push({
                content: optionItem.label,
                bgValue: optionItem.color
              });
            }
          }
        }

        let container;
        if (tagList instanceof Array && tagList.length > 0) {
          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "center",
            alignItems: "center",
            //@ts-ignore
            vue: {
              element: h(TagColumn, {
                tagList: tagList,
                width: width,
                onClick: () => {
                  table.completeEditCell();
                  table.startEditCell(col, row);
                }
              }),
              container: table.bodyDomContainer
            }
          });
        }
        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (
    [
      "DrSCCSMemberSelect",
      "DrSCCSGroupMemberSelect",
      "DrSCCSMemberSingleSelect",
      "DrSCCSGroupMemberSingleSelect",
      "DrSCCSMemberMultipleSelect",
      "DrSCCSGroupMemberMultipleSelect"
    ].includes(column.type)
  ) {
    return {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);

        let container;
        const widgetData: any = storageSession().getItem("widgetData");

        const dataList = [
          "DrSCCSMemberSelect",
          "DrSCCSMemberSingleSelect",
          "DrSCCSMemberMultipleSelect"
        ].includes(column.type)
          ? widgetData.sccsMemberList
          : widgetData.sccsCoopTeamList;

        if (value) {
          let userInfoList;
          let dataValueList;
          if (
            [
              "DrSCCSMemberSelect",
              "DrSCCSMemberSingleSelect",
              "DrSCCSMemberMultipleSelect"
            ].includes(column.type)
          ) {
            dataValueList = dataList.filter(dataItem =>
              value.split(",").includes(dataItem.userId)
            );

            userInfoList = dataValueList.map(teamMember => {
              return {
                activate: teamMember.activate,
                user: true,
                id: teamMember.userId,
                name: teamMember.username,
                username: teamMember.username,
                avatar: teamMember.avatar,
                edited: false,
                coopUser: false,
                teamName: teamMember.teamName,
                shortName: teamMember.teamShortName,
                email: teamMember.account
              };
            });
          } else {
            dataValueList = dataList.filter(dataItem =>
              value.split(",").includes(dataItem.teamId)
            );

            userInfoList = dataValueList.map(team => {
              return {
                activate: true,
                user: false,
                teamId: team.teamId,
                name: team.teamName,
                avatar: team.teamAvatar,
                edited: false,
                coopTeamUser: true,
                shortName: team.teamShortName,
                email: team.teamShortName
              };
            });
          }

          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            //@ts-ignore
            vue: {
              element: h(AvatarGroup, {
                avatarListGroup: userInfoList,
                size: 26
              }),
              container: table.bodyDomContainer
            }
          });
        }

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (
    [
      "DrImagesUpload",
      "DrFilesUpload",
      "DrAddress",
      "DrEditor",
      "DrExchangeRates"
    ].includes(column.type)
  ) {
    return {
      customLayout: args => {
        const { table, row, col, rect } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);

        let container;
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(DrDialog, {
              widgetConfigure: column,
              widgetRowIndex: row,
              width: width,
              widgetColIndex: col,
              trendsForm: table.records[row - 1],
              onHandleOpenSubTableDialog: (
                widgetData: any,
                rowIndex: number
              ) => {
                cb(widgetData, rowIndex);
              }
            }),
            container: table.bodyDomContainer
          }
        });

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (["DrSignature"].includes(column.type)) {
    return {
      customLayout: args => {
        const { table, row, col, rect } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);

        let container;

        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(DrSignature, {
              widgetConfigure: column,
              widgetRowIndex: row,
              width: width,
              height: height,
              widgetColIndex: col,
              trendsForm: table.records[row - 1],
              onHandleUpdateWidgetData: (widgetData: any, rowIndex: number) => {
                cb(widgetData, rowIndex);
              }
            }),
            container: table.bodyDomContainer
          }
        });

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (column.type === "DrRate") {
    return {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);
        let container;

        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(ElRate, {
              modelValue: value
            }),
            container: table.bodyDomContainer
          }
        });

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  }
};

const subTableOperateList: any = () => {
  return {
    field: "operate",
    title: "",
    //@ts-ignore
    width: 80,
    icon: [
      {
        name: "edit",
        type: "svg",
        positionType: VTable.TYPES.IconPosition.left,
        width: 15,
        height: 15,
        svg: '<svg t="1744688125225" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10007" width="200" height="200"><path d="M853.333333 213.333333c93.866667 0 170.666667 76.8 170.666667 170.666667V853.333333c0 93.866667-76.8 170.666667-170.666667 170.666667H375.466667c-93.866667 0-170.666667-76.8-170.666667-170.666667V384c0-93.866667 76.8-170.666667 170.666667-170.666667H853.333333z m0 85.333334H375.466667c-42.666667 0-85.333333 34.133333-85.333334 76.8V853.333333c0 42.666667 34.133333 85.333333 76.8 85.333334H853.333333c42.666667 0 85.333333-34.133333 85.333334-76.8V384c0-42.666667-34.133333-85.333333-85.333334-85.333333zM699.733333 0c25.6 0 42.666667 17.066667 42.666667 42.666667s-17.066667 42.666667-42.666667 42.666666h-512c-51.2 0-93.866667 42.666667-102.4 93.866667V682.666667c0 25.6-17.066667 42.666667-42.666666 42.666666-17.066667 0-42.666667-17.066667-42.666667-34.133333V187.733333C0 85.333333 76.8 8.533333 179.2 0h520.533333z" fill="#808080" p-id="10008"></path></svg>',
        hover: {
          width: 24,
          height: 24,
          bgColor: "#F2F2F2"
        }
      },
      {
        name: "delete",
        type: "svg",
        positionType: VTable.TYPES.IconPosition.right,
        width: 15,
        height: 15,
        svg: '<svg t="1744687978943" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9830" width="200" height="200"><path d="M930.133333 256H93.866667C68.266667 256 42.666667 230.4 42.666667 204.8s25.6-51.2 51.2-51.2h836.266666c25.6 0 51.2 25.6 51.2 51.2s-25.6 51.2-51.2 51.2zM614.4 102.4H409.6c-25.6 0-51.2-25.6-51.2-51.2S384 0 409.6 0h204.8c25.6 0 51.2 25.6 51.2 51.2s-17.066667 51.2-51.2 51.2zM358.4 768V409.6c0-25.6 25.6-51.2 51.2-51.2s51.2 25.6 51.2 51.2V768c0 25.6-25.6 51.2-51.2 51.2-34.133333 0-51.2-25.6-51.2-51.2z m204.8 0V409.6c0-25.6 25.6-51.2 51.2-51.2s51.2 25.6 51.2 51.2V768c0 25.6-25.6 51.2-51.2 51.2s-51.2-25.6-51.2-51.2zM196.266667 307.2c25.6 0 51.2 25.6 51.2 51.2v512c0 25.6 25.6 51.2 51.2 51.2h418.133333c25.6 0 51.2-25.6 51.2-51.2v-512c0-25.6 25.6-51.2 51.2-51.2s51.2 25.6 51.2 51.2v512c0 85.333333-68.266667 153.6-153.6 153.6H298.666667c-85.333333 0-153.6-68.266667-153.6-153.6v-512c0-25.6 25.6-51.2 51.2-51.2z" fill="#FF0202" p-id="9831"></path></svg>',
        hover: {
          width: 24,
          height: 24,
          bgColor: "#F2F2F2"
        }
      }
    ]
  };
};

const tableColumnEditorConfigure = (
  column: any,
  cb: any,
  parentTableWidget: any
) => {
  return {
    editor: args => {
      if (column.type === "DrFormulas") {
        return "";
      }
      return cb(args, column) ? "" : `${parentTableWidget._fc_id}-dr-editor`;
    },
    fieldFormat: (record: any) => {
      if (
        ["DrPercentage", "DrInputNumber"].includes(column.type) &&
        (record[column._fc_id] === null || record[column._fc_id] === undefined)
      ) {
        return "";
      }
      if (column.type === "DrPercentage" && record[column._fc_id]) {
        return `${record[column._fc_id]}%`;
      } else if (column.type === "DrFormulas") {
        return record[column._fc_id] ? `${record[column._fc_id]}` : "";
      } else {
        return `${record[column._fc_id]}`;
      }
    },
    style: {
      bgColor(args) {
        return cb(args, column) ? "#F2F2F2" : "";
      }
    },
    headerCustomLayout: args => {
      const { table, row, col, rect } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;

      container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //@ts-ignore
        vue: {
          element: h(LkHeaderColumn, {
            column: column,
            width: width,
            onClick: () => {
              table.completeEditCell();
            }
          }),
          container: table.headerDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  };
};

const tableColumnWidgetNameList = [
  "DrRate",
  "DrRadio",
  "DrCheckbox",
  "DrSCCSMemberSelect",
  "DrSCCSGroupMemberSelect",
  "DrSCCSMemberSingleSelect",
  "DrSCCSGroupMemberSingleSelect",
  "DrSCCSMemberMultipleSelect",
  "DrSCCSGroupMemberMultipleSelect",
  "DrImagesUpload",
  "DrFilesUpload",
  "DrAddress",
  "DrEditor",
  "DrExchangeRates",
  "DrSignature"
];

export {
  DrInput,
  DrTextarea,
  DrInputNumber,
  DrPercentage,
  DrRadio,
  DrCheckbox,
  DrDatePicker,
  DrRate,
  DrSignature,
  DrImagesUpload,
  DrDialog,
  DrSCCSMemberSelect,
  DrSCCSGroupMemberSelect,
  LKSubTable,
  TagColumn,
  AvatarGroup,
  FileColumn,
  tableColumnWidgetNameList,
  subTableOperateList,
  tableColumnEditorConfigure,
  getColumnCustomLayoutTemplate
};
