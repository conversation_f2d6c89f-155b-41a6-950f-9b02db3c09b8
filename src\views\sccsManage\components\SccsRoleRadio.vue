<template>
  <el-radio-group v-model="milestoneRadio">
    <el-radio
      v-for="item in sccsDataRoleList"
      :key="item.value"
      :value="item.value"
    >
      {{ t(item.label) }}
    </el-radio>
  </el-radio-group>
</template>
<script lang="ts" setup>
import { PropType, ref, watchEffect } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const milestoneRadio = ref<string>("");
const sccsDataRoleList = ref<any>([
  { value: "ALL_ORDER", label: "trade_common_allOrder" },
  { value: "MY_CREATE", label: "trade_common_createmeorder" },
  { value: "CUSTOM", label: "trade_common_custom" }
]);

const props = defineProps({
  milestoneRadioProp: {
    type: String as PropType<string>,
    default: ""
  }
});

watchEffect(() => {
  if (props.milestoneRadioProp) {
    milestoneRadio.value = props.milestoneRadioProp;
  } else {
    milestoneRadio.value = "";
  }
});
</script>
