<template>
  <!-- 计划开始时间 -->
  <el-form-item :label="t('trade_common_planStart')">
    <el-date-picker
      v-model="milestone.plannedStartDate"
      type="date"
      :placeholder="t('trade_common_beginDate')"
      clearable
      :input-attrs="{ inputmode: 'none' }"
      @change="handleChangePlannedStartDate"
    />
  </el-form-item>

  <!-- 计划工期 -->
  <el-form-item :label="t('trade_common_PlannedTimeConsumption')">
    <el-input-number
      v-model="milestone.plannedDay"
      clearable
      :value-on-clear="null"
      :min="1"
      :precision="0"
      :step="1"
      controls-position="right"
      @change="handleChangePlannedDay"
      @input="handleInput"
    />
  </el-form-item>

  <!-- 计划结束时间 -->
  <el-form-item :label="t('trade_common_planEnd')">
    <el-date-picker
      v-model="milestone.plannedEndDate"
      type="date"
      :input-attrs="{ inputmode: 'none' }"
      :placeholder="t('trade_common_endDate')"
      :disabled-date="handleDisabledDate"
      clearable
      @change="handleChangePlannedEndDate"
    />
  </el-form-item>

  <!-- 负责人 -->
  <!-- <el-form-item>
    <template #label>
      <div class="flex items-center">
        {{ t("trade_common_directorPerson") }}
        <el-tooltip
          effect="dark"
          :content="t('trade_common_directorPersonTooltip')"
          placement="top"
        >
          <span style="margin-left: 4px; cursor: pointer">
            <IconifyIconOnline icon="ep:warning" style="font-size: 16px" />
          </span>
        </el-tooltip>
      </div>
    </template>
    <LkPersonSelect
      v-model="milestone.managerMemberId"
      filterable
      :options="managerList"
      clearable
      :placeholder="t('trade_common_selectText')"
      :mode="false"
      popper-class="manager-popper-class"
      :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
      :props="{
        label: 'username',
        disabled: 'isDisable',
        value: 'teamMemberId'
      }"
    />
  </el-form-item> -->
  <el-form-item>
    <template #label>
      <div class="flex items-center">
        {{ t("trade_common_directorPerson") }}
        <el-tooltip
          effect="dark"
          :content="t('trade_common_directorPersonTooltip')"
          placement="top"
        >
          <span style="margin-left: 4px; cursor: pointer">
            <IconifyIconOnline icon="ep:warning" style="font-size: 16px" />
          </span>
        </el-tooltip>
      </div>
    </template>
    <LkPersonCascader
      v-model="milestone.managerMemberId"
      filterable
      clearable
      popper-class="manager-popper-class"
      :mode="false"
      :options="managerList"
      :placeholder="t('trade_common_selectText')"
      :show-all-levels="false"
    />
  </el-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import LkPersonCascader from "@/components/lkPersonCascader/index";
import { add, sub, differenceInDays } from "date-fns";
import type { PropType } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";
import { cloneDeep, isEqual } from "@pureadmin/utils";

const { t } = useI18n();

const props = defineProps({
  managerList: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  sccsPlanData: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  formRefData: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  milestoneListData: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  milestonePlanData: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  msId: {
    type: String as PropType<string>,
    default: ""
  }
});

const emit = defineEmits([
  "handleMilestoneTimeLine",
  "handlePlanChangeBySystem"
]);

// 里程碑表单数据
const milestone = ref<{
  plannedStartDate: number | string;
  plannedDay: number | null;
  plannedEndDate: number | string;
  managerMemberId: string;
}>({
  plannedStartDate: "",
  plannedDay: null,
  plannedEndDate: "",
  managerMemberId: ""
});

// 监听外部传入的里程碑数据，赋值到本地表单
watch(
  () => props.milestonePlanData,
  () => {
    if (props.milestonePlanData) {
      milestone.value.plannedStartDate =
        props.milestonePlanData.plannedStartDate;
      milestone.value.plannedEndDate = props.milestonePlanData.plannedEndDate;
      milestone.value.plannedDay = props.milestonePlanData.plannedDay ?? null;
      milestone.value.managerMemberId = props.milestonePlanData.managerMemberId;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

// 根据基础日期和参数计算里程碑计划时间
const calculateMilestoneByBaseDate = (
  baseDateRaw: string | number | Date | undefined,
  timeType: string,
  relativeDateType: string,
  plannedDurationDays: number,
  relativeDays: number,
  msId: string,
  emitChange = true
) => {
  if (props.milestonePlanData.actualEndDate) return;

  const oldMilestone = cloneDeep(milestone.value);

  if (!baseDateRaw) {
    milestone.value.plannedStartDate = undefined;
    milestone.value.plannedEndDate = undefined;
    milestone.value.plannedDay = plannedDurationDays;
  } else {
    const relDays = relativeDateType === "TODAY" ? 0 : relativeDays;
    const baseDate = new Date(baseDateRaw);
    const getDate = () =>
      relativeDateType === "BEFORE"
        ? sub(baseDate, { days: +relDays })
        : add(baseDate, { days: +relDays });

    if (timeType === "PLAN_START") {
      const date = getDate();
      milestone.value.plannedStartDate = date.getTime();
      milestone.value.plannedDay = plannedDurationDays;
      milestone.value.plannedEndDate = add(date, {
        days: +plannedDurationDays - 1
      }).getTime();
    } else if (timeType === "PLAN_END") {
      const date = getDate();
      milestone.value.plannedEndDate = date.getTime();
      milestone.value.plannedDay = plannedDurationDays;
      milestone.value.plannedStartDate = sub(date, {
        days: +plannedDurationDays - 1
      }).getTime();
    }

    if (emitChange && !isEqual(milestone.value, oldMilestone)) {
      emit("handlePlanChangeBySystem", msId);
    }
  }
};

// 监听表单引用数据，自动计算计划时间
const dependentWidgetValue = computed(() => {
  const calculationObj = props.sccsPlanData?.calculationDateObject;
  if (calculationObj?.value && props.formRefData.length) {
    const widgetItem = props.formRefData.find(
      form => form.widgetId === calculationObj.value
    );
    return widgetItem?.obj;
  }
  return undefined;
});

watch(
  dependentWidgetValue,
  (newVal, oldVal) => {
    if ((!newVal && !oldVal) || newVal === oldVal) return;
    const {
      timeType,
      relativeDateType,
      plannedDurationDays,
      relativeDays = 0
    } = props.sccsPlanData;
    calculateMilestoneByBaseDate(
      dependentWidgetValue.value,
      timeType,
      relativeDateType,
      plannedDurationDays,
      relativeDays,
      props.msId
    );
  },
  {
    deep: true,
    immediate: true
  }
);

// 依赖的里程碑日期
const dependentMilestoneDate = computed(() => {
  const calculationObj = props.sccsPlanData?.calculationDateObject;
  if (calculationObj?.id && props.milestoneListData.length) {
    const widgetItem = props.milestoneListData.find(
      form => form.msId === calculationObj.id
    );
    return widgetItem?.[calculationObj.field];
  }
  return undefined;
});

// 依赖里程碑日期变化时自动计算本里程碑的计划时间
watch(
  dependentMilestoneDate,
  (newVal, oldVal) => {
    if (newVal === oldVal) return;

    const depDate = dependentMilestoneDate.value;
    if (!depDate) return;

    const { timeType, relativeDateType, relativeDays = 0 } = props.sccsPlanData;
    const plannedDurationDays =
      props.sccsPlanData.plannedDurationDays ?? milestone.value.plannedDay;
    calculateMilestoneByBaseDate(
      depDate,
      timeType,
      relativeDateType,
      plannedDurationDays,
      relativeDays,
      props.msId
    );
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => [dependentMilestoneDate.value, dependentWidgetValue.value],
  () => {
    const { plannedDurationDays } = props.sccsPlanData;
    const isEmpty = (val: any) =>
      val === undefined || val === null || val === "";

    if (
      isEmpty(dependentMilestoneDate.value) &&
      isEmpty(dependentWidgetValue.value)
    ) {
      calculateMilestoneByBaseDate(
        undefined,
        undefined,
        undefined,
        plannedDurationDays,
        undefined,
        props.msId
      );
    }
  },
  {
    deep: true
  }
);

// 监听本地表单数据变化，向父组件同步
watch(
  () => milestone.value,
  () => {
    const { plannedStartDate, plannedEndDate, plannedDay, managerMemberId } =
      milestone.value;

    emit("handleMilestoneTimeLine", {
      msId: props.msId,
      plannedStartDate: plannedStartDate
        ? new Date(plannedStartDate).getTime()
        : null,
      plannedEndDate: plannedEndDate
        ? new Date(plannedEndDate).getTime()
        : null,
      plannedDay: plannedDay ?? null,
      managerMemberId
    });
  },
  { deep: true, immediate: true }
);

/**
 * 变更开始时间
 */
function handleChangePlannedStartDate() {
  const { plannedStartDate, plannedEndDate, plannedDay } = milestone.value;
  if (!plannedStartDate) return;
  if (plannedEndDate && !plannedDay) {
    milestone.value.plannedDay =
      differenceInDays(new Date(plannedEndDate), new Date(plannedStartDate)) +
      1;
  } else if (plannedDay) {
    milestone.value.plannedEndDate = add(new Date(plannedStartDate), {
      days: +plannedDay - 1
    }).getTime();
  }
}

/**
 * 变更结束时间
 */
function handleChangePlannedEndDate() {
  const { plannedStartDate, plannedEndDate, plannedDay } = milestone.value;
  if (!plannedEndDate) return;
  if (plannedStartDate && !plannedDay) {
    milestone.value.plannedDay =
      differenceInDays(new Date(plannedEndDate), new Date(plannedStartDate)) +
      1;
  } else if (plannedDay) {
    milestone.value.plannedStartDate = sub(new Date(plannedEndDate), {
      days: +plannedDay - 1
    }).getTime();
  }
}

/**
 * 变更工期
 */
function handleChangePlannedDay() {
  const { plannedStartDate, plannedEndDate, plannedDay } = milestone.value;
  if (!plannedDay) return;
  if (plannedStartDate && !plannedEndDate) {
    milestone.value.plannedEndDate = add(new Date(plannedStartDate), {
      days: +plannedDay - 1
    }).getTime();
  } else if (!plannedStartDate && plannedEndDate) {
    milestone.value.plannedStartDate = sub(new Date(plannedEndDate), {
      days: +plannedDay - 1
    }).getTime();
  } else if (plannedStartDate && plannedEndDate) {
    milestone.value.plannedEndDate = add(new Date(plannedStartDate), {
      days: +plannedDay - 1
    }).getTime();
  }
}

/**
 * 禁用结束日期（小于开始日期不可选）
 */
function handleDisabledDate(time: Date) {
  return time.getTime() < new Date(milestone.value.plannedStartDate).getTime();
}

/**
 * 工期输入仅允许数字
 */
function handleInput(val: string | number) {
  // 仅允许数字输入
  // milestone.value.plannedDay = val.toString().replace(/[^0-9]/g, '');
}
</script>
