<template>
  <div class="login-form-container">
    <el-form
      ref="userInfoFormRef"
      :model="userInfoForm"
      :rules="userInfoRules"
      label-position="top"
      size="large"
    >
      <!-- 用户名输入 -->
      <div class="login-form-item">
        <el-form-item :label="t('trade_common_name')" prop="username">
          <el-input
            v-model="userInfoForm.username"
            maxlength="20"
            :placeholder="t('trade_common_name')"
            show-word-limit
            clearable
          />
        </el-form-item>
      </div>

      <!-- 性别选择 -->
      <div class="login-form-item">
        <el-form-item :label="t('trade_common_sex')" prop="sex">
          <el-select
            v-model="userInfoForm.sex"
            :placeholder="t('trade_common_sexTip')"
            clearable
            style="width: 100%"
          >
            <el-option :label="t('trade_common_male')" :value="1" />
            <el-option :label="t('trade_common_female')" :value="2" />
          </el-select>
        </el-form-item>
      </div>

      <!-- 密码输入 -->
      <div class="login-form-item">
        <el-form-item :label="t('trade_common_setPassword')" prop="password">
          <el-input
            v-model="userInfoForm.password"
            type="password"
            :placeholder="t('trade_common_setPasswordTip')"
            :show-password="true"
            clearable
            @input="handleClearValidate"
          />
        </el-form-item>
      </div>

      <!-- 确认密码输入 -->
      <div class="login-form-item">
        <el-form-item
          :label="t('trade_common_confirmPassword')"
          prop="nextPassword"
        >
          <el-input
            v-model="userInfoForm.nextPassword"
            type="password"
            :placeholder="t('trade_common_confirmPasswordTip')"
            :show-password="true"
            clearable
          />
        </el-form-item>
      </div>

      <!-- 错误信息显示 -->
      <UserError :respMessage="respMessage" :formData="userInfoForm" />

      <!-- 注册并登录按钮 -->
      <el-button
        type="primary"
        class="login-button"
        :loading="registerLoading"
        @click="handleSubmit"
      >
        {{ t("trade_login_registerandLogin") }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import type { FormInstance, FormRules } from "element-plus";
import { register } from "@/api/login";
import UserError from "./UserError.vue";

const { t } = useI18n();

// 表单引用
const userInfoFormRef = ref<FormInstance>();

// 注册状态
const registerLoading = ref<boolean>(false);
const respMessage = ref<string>("");

// Props
const props = defineProps({
  email: {
    type: String,
    required: true
  }
});

// 用户信息表单数据
interface UserInfoForm {
  username: string;
  sex: number | null;
  password: string;
  nextPassword: string;
}

const userInfoForm = reactive<UserInfoForm>({
  username: "",
  sex: null,
  password: "",
  nextPassword: ""
});

// 密码验证
const validatePass = (rule: any, value: any, callback: any) => {
  const regex =
    /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9,./<>?;\'\:\"\[\]\\\{\}\|\`\-\=\~\!\@#\$%\^&*()_+]{6,16}$/;
  if (regex.test(value)) {
    callback();
  } else {
    callback(new Error(t("trade_common_setPasswordTip")));
  }
};

// 确认密码验证
const validatePwd = (rule: any, value: any, callback: any) => {
  if (value === userInfoForm.password) {
    callback();
  } else {
    callback(new Error(t("trade_login_passwordInconsistency")));
  }
};

// 表单验证规则
const userInfoRules = computed<FormRules<UserInfoForm>>(() => ({
  username: [
    {
      required: true,
      message: t("trade_login_registerUserNameTip"),
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: t("trade_login_passwordPlaceholder"),
      trigger: "blur"
    },
    { validator: validatePass, trigger: ["blur", "change"] }
  ],
  nextPassword: [
    {
      required: true,
      message: t("trade_common_confirmPasswordTip"),
      trigger: "blur"
    },
    { validator: validatePass, trigger: ["blur", "change"] },
    { validator: validatePwd, trigger: ["blur", "change"] }
  ]
}));

// 事件定义
const emit = defineEmits<{
  handleLoginSuccess: [data: any];
}>();

// 清除确认密码验证
const handleClearValidate = () => {
  if (userInfoForm.password && userInfoForm.nextPassword) {
    userInfoFormRef.value?.validateField("nextPassword");
  }
};

// 提交注册
const handleSubmit = async () => {
  try {
    await userInfoFormRef.value?.validate();
  } catch (error) {
    return;
  }

  registerLoading.value = true;
  respMessage.value = "";

  try {
    const response = await register({
      username: userInfoForm.username,
      sex: userInfoForm.sex,
      email: props.email,
      password: userInfoForm.password
    });

    if (response.code !== 0) {
      respMessage.value = response.node || response.msg;
    } else {
      // 注册成功，触发登录
      emit("handleLoginSuccess", response.data);
    }
  } catch (error) {
    console.error("注册失败:", error);
    respMessage.value = "注册失败，请重试";
  } finally {
    registerLoading.value = false;
  }
};

// 监听表单变化，清除错误信息
watch(
  () => userInfoForm,
  () => {
    respMessage.value = "";
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>
