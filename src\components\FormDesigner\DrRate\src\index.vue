<template>
  <div
    style="display: flex; width: 100%"
    :style="{
      'justify-content': widgetRowIndex === -1 ? 'left' : 'center'
    }"
  >
    <el-rate
      v-model="widgetFormData[widgetConfigure._fc_id]"
      clearable
      :max="rateMax"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, ref, watch } from "vue";
import { ElRate } from "element-plus";
import { cloneDeep, isEqual } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const emit = defineEmits(["handleSubTableWidgetValueChange"]);

const rateMax = computed(() => {
  return props.widgetConfigure.props.starCount
    ? props.widgetConfigure.props.starCount
    : 5;
});

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    widgetFormData.value[props.widgetConfigure._fc_id] = cloneDeep(newVal);
  },
  { immediate: true }
);

watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (props.trendsForm) {
      if (
        newVal !== props.trendsForm[props.widgetConfigure._fc_id] &&
        !(oldVal === "" && newVal === 0)
      ) {
        const widgetId = props.widgetConfigure._fc_id;
        const widgetValue = widgetFormData.value[widgetId]
          ? widgetFormData.value[widgetId]
          : "";
        if (props.widgetRowIndex !== -1) {
          emit(
            "handleSubTableWidgetValueChange",
            widgetId,
            {
              obj: widgetValue,
              label: widgetValue,
              widgetId: widgetId,
              $rowIndex: props.widgetRowIndex
            },
            widgetValue
          );
        } else {
          handleWidgetFormsValue(
            widgetId,
            {
              obj: widgetValue,
              label: widgetValue,
              widgetId: widgetId,
              $rowIndex: props.widgetRowIndex
            },
            widgetValue
          );
        }
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
