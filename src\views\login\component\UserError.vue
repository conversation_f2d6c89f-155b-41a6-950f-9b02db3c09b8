<template>
  <div
    v-if="respMessage !== ''"
    class="message-tip"
    :class="
      respMessage === 'trade_auth_login_bad_credentials' ? 'position' : ''
    "
  >
    <span v-html="t(respMessage)" />
    <div
      v-if="respMessage === 'trade_users_not_exists'"
      class="no-padding"
      @click="handleRouter('/register')"
    >
      {{ t("trade_login_register") }}
    </div>
    <div
      v-if="respMessage === 'trade_auth_login_bad_credentials'"
      class="no-padding"
      @click="handleRouter('/resetPassword')"
    >
      {{ t("trade_common_resetPassword") }}
    </div>
    <div
      v-if="respMessage === 'trade_users_register_email_exists'"
      class="no-padding"
      @click="handleRouter('/login')"
    >
      {{ t("trade_common_login") }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { PropType } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

const { t } = useI18n();
const router = useRouter();

const props = defineProps({
  respMessage: {
    type: String as PropType<string>,
    default: ""
  },
  code: {
    type: Number as PropType<number>,
    default: 0
  },
  loginForm: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const handleRouter = url => {
  router.push(url);
};
</script>
<style lang="scss" scoped>
.message-tip {
  line-height: 20px !important;

  .no-padding {
    display: inline-block;
    height: 20px !important;
    padding: 0 !important;
    font-size: 12px;
    color: #0070d2;
    vertical-align: baseline;
    cursor: pointer;
  }
}

.position {
  position: absolute;
  top: -19px;
  left: -110px;
  margin-bottom: 20px;
}
</style>
