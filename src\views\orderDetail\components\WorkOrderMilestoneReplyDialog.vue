<template>
  <el-drawer
    v-model="drawerVisible"
    class="lk-maximum-drawer"
    size="83%"
    header-class="work-order-milestone-header"
    body-class="work-order-milestone-container"
    footer-class="create-work-order-drawer-footer"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <template #header>
      <div class="drawer-header">
        <div class="drawer-header-left">
          {{ dialogTitle }}
        </div>
        <div class="drawer-header-right">
          <div class="drawer-header-btn-next-group">
            <div class="drawer-header-col">
              <i class="iconfont link-quote-third-party" />
              <span class="drawer-header-text">
                {{ t("trade_order_referenceOtherWorkOrders") }}
              </span>
            </div>
            <div class="drawer-header-col">
              <i class="iconfont link-quote-third-party" />
              <span class="drawer-header-text">
                {{ t("trade_order_DataReference") }}
              </span>
              <el-tooltip
                effect="dark"
                :content="t('trade_order_quoteTip')"
                placement="top"
                :showAfter="500"
              >
                <i class="iconfont link-explain" />
              </el-tooltip>
            </div>
          </div>
          <div class="drawer-header-btn-group">
            <div
              class="drawer-header-col"
              :class="{ 'create-work-order-active': mainFormVisible }"
              @click.stop="handleOpenMainFormDialog"
            >
              <i class="iconfont link-basic-info" />
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <div class="drawer-container-left">
        <el-tabs
          v-model="activeName"
          class="work-order-tabs"
          @tab-click="handleTabClick"
        >
          <el-tab-pane
            v-for="item in msTemplateData.workOrderGroupList"
            :key="item.workOrderId"
            :label="item.workOrderName"
            :name="item.workOrderId"
          >
            <el-scrollbar class="horizontal-scrollbar-only">
              <el-collapse v-model="activeNames" class="work-order-collapse">
                <el-collapse-item
                  v-if="msTemplate.workOrderReplyForm"
                  :class="`collaspse-${msTemplate.workOrderReplyForm.id}`"
                  class="collapse-reply-form"
                  :title="msTemplate.workOrderReplyForm.name"
                  name="1"
                >
                  <template #title>
                    <div class="order-form-edit-title-header">
                      <span class="order-form-edit-title">
                        <ReText
                          type="info"
                          :line-clamp="2"
                          :tippyProps="{ delay: 50000 }"
                        >
                          {{ t("trade_work_order_approver") }}
                        </ReText>
                      </span>
                      <span class="order-form-edit-avatar" @click.stop>
                        <LkAvatarGroupNext
                          :size="22"
                          mode="reply"
                          :avatarListGroup="item.replyUserList"
                          :maxAvatar="4"
                        />
                      </span>
                      <span
                        v-if="item.replied"
                        class="milestone-reply-form-span-text"
                      >
                        {{ getTimeLineStampFormat(item.replyTime) }}
                      </span>
                      <span
                        v-else
                        class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
                      >
                        {{ t("trade_order_notCollection") }}
                      </span>
                    </div>
                  </template>
                  <div class="order-detail-desc-col">
                    <OrderDetailDescWidget
                      :widgetForm="msTemplate.workOrderReplyForm.widgetJsonList"
                      :widgetData="getWorkOrderWidgetData(item)"
                    />
                  </div>
                </el-collapse-item>
                <el-collapse-item
                  v-for="milestoneItem in msTemplate.formList"
                  :key="milestoneItem.id"
                  :class="`collaspse-${milestoneItem.id}`"
                  :title="milestoneItem.name"
                  :name="milestoneItem.id"
                >
                  <template #title>
                    <div class="order-form-edit-title-header">
                      <span class="order-form-edit-title">
                        <ReText
                          type="info"
                          :line-clamp="2"
                          :tippyProps="{ delay: 50000 }"
                        >
                          {{ milestoneItem.name }}
                        </ReText>
                      </span>
                      <span class="order-form-edit-avatar" @click.stop>
                        <LkAvatarGroupNext
                          :size="22"
                          :avatarListGroup="
                            currentWorkOrderInfo.editUserList.filter(e =>
                              e.assignedFormIdList.includes(milestoneItem.id)
                            )
                          "
                          :maxAvatar="4"
                        />
                      </span>
                      <span
                        v-if="currentWorkOrderInfo.edited"
                        class="milestone-reply-form-span-text"
                      >
                        {{
                          getTimeLineStampFormat(
                            currentWorkOrderInfo.editFormInfoList.find(
                              e => e.formId === milestoneItem.id
                            )?.editTime
                          )
                        }}
                      </span>
                      <span
                        v-else
                        class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
                      >
                        {{ t("trade_order_notCollection") }}
                      </span>
                    </div>
                  </template>
                  <div class="order-detail-desc-col">
                    <OrderDetailDescWidget
                      :widgetForm="milestoneItem.widgetJsonList"
                      :widgetData="getWorkOrderWidgetData(currentWorkOrderInfo)"
                    />
                  </div>
                </el-collapse-item>
              </el-collapse>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="drawer-container-right">
        <div class="drawer-container-main-body">
          <el-scrollbar class="horizontal-scrollbar-only">
            <LkTrendsAggregationForm
              ref="TrendsAggregationFormRef"
              :formList="[msTemplate.replyForm]"
              :formWidgetData="orderWorkWidgetFormData"
              :operationalFactorData="operationalFactorData"
              :defaultValueActuatorFlag="!msTemplateData.replyTime"
              :workOrderAllowOperation="[msTemplate.replyForm.id]"
              :sccsId="route.query.sccsId"
              :orderId="route.query.orderId"
              :workOrderId="msTemplateData.msReplyId"
              :createUser="{
                userId: orderMilestoneData.createUserId,
                userName: orderMilestoneData.createUserName,
                userAvatar: orderMilestoneData.createUserAvatar,
                edited: orderMilestoneData.edited,
                email: orderMilestoneData.createUserEmail,
                user: true
              }"
              :replayUsers="orderMilestoneData.msReplyUser"
            />
          </el-scrollbar>
        </div>
        <div class="drawer-container-footer">
          <el-button plain @click="handleClose">
            {{ t("trade_common_cancel") }}
          </el-button>
          <el-button
            type="primary"
            color="#0070d2"
            :disabled="btnOperationState"
            @click="handleWorkOrderCollection"
          >
            {{ t("trade_common_confirm") }}
          </el-button>
        </div>
      </div>

      <OrderMainFormHeader
        ref="OrderMainFormRef"
        :mainFormData="orderMainFormData"
      />
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ElMessage, TabsPaneContext } from "element-plus";
import dayjs from "dayjs";
import { ReText } from "@/components/ReText";
import { getDefaultConfigWidgetData } from "@/utils/formulasActuator/formulasActuator";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext/index";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import OrderMainFormHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderMainFormHeader.vue";
import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";
import { settingMilestoneReply } from "@/api/order";
import { emitter } from "@/utils/mitt";

interface RouteParamProp {
  sccsId: string;
  templateId: string;
  orderId?: string;
}

const { t } = useI18n();
const route = useRoute();
const TrendsAggregationFormRef = ref<HTMLElement | any>(null);
const OrderMainFormRef = ref<HTMLElement | any>(null);
const orderMainFormData = ref<any>({});
const orderMilestoneData = ref<any>({});
const activeName = ref<string>("");
const btnOperationState = ref<boolean>(false);
const activeNames = ref<string[]>(["1"]);
const drawerVisible = ref<boolean>(false);
const collectDisabled = ref<boolean>(false);
const mainFormVisible = ref<boolean>(false);
const msTemplate = ref<any>({});
const msTemplateData = ref<any>({});
const orderWorkWidgetFormData = ref<any>({});
const currentWorkOrderInfo = ref<any>({});
const workOrderType = ref<string>("");
const operationalFactorData = ref<any>({});
const routeParams = ref<RouteParamProp>({
  sccsId: "",
  templateId: "",
  orderId: ""
});

const emit = defineEmits(["handleUpdateCollection"]);

const dialogTitle = computed(() => {
  return workOrderType.value === "milestoneReply"
    ? `${t("trade_order_milestoneApprover")}_${msTemplateData.value.msName}`
    : `${t("trade_common_view")}${t("trade_order_milestoneApprover")}_${msTemplateData.value.msName}`;
});

const handleTabClick = (tab: TabsPaneContext, event: Event) => {
  currentWorkOrderInfo.value = msTemplateData.value.workOrderGroupList.find(
    workOrderGroup => workOrderGroup.workOrderId === tab.props.name
  );
};

const getWorkOrderWidgetData = (widgetData): any => {
  const { widgetList, subWidgetMap, linkedReferenceMap } = widgetData;
  const widgetObject = TransformSubmitDataStructure(
    widgetList,
    subWidgetMap,
    linkedReferenceMap
  );
  return widgetObject;
};

const getTimeLineStampFormat = (dateTime: any) => {
  if (dateTime) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "";
  }
};

const open = async (
  milestoneData: any,
  mainFormData: any,
  type?: string,
  customParams?: RouteParamProp
): Promise<void> => {
  if (type) {
    workOrderType.value = type;
  }
  const { msId } = milestoneData;
  routeParams.value = customParams;
  orderMainFormData.value = mainFormData;
  orderMilestoneData.value = milestoneData;
  const orderId = (route.query.orderId as string) || customParams?.orderId;
  getDefaultConfigWidgetData(orderId, customParams).then(
    ({ orderTemplateData, orderData }: any) => {
      const { milestoneList } = orderTemplateData;
      const { milestoneGroupList } = orderData;

      // 当前里程碑模版信息
      msTemplate.value = milestoneList.find(
        milestoneItem => milestoneItem.msId === msId
      );
      // 当前里程碑详细信息
      msTemplateData.value = milestoneGroupList.find(
        milestoneItem => milestoneItem.msId === msId
      );

      let obj = {};
      let anchorLists = [];
      if (msTemplate.value.workOrderReplyForm) {
        anchorLists.push({
          title: msTemplate.value.workOrderReplyForm.name,
          ref: `collaspse-${msTemplate.value.workOrderReplyForm.id}`,
          name: msTemplate.value.workOrderReplyForm.id,
          state: msTemplate.value.workOrderReplyForm.editable,
          stateText: msTemplate.value.workOrderReplyForm.editable
            ? "trade_email_editable"
            : "trade_email_readonly"
        });
      }
      msTemplate.value.formList.forEach(milestoneItem => {
        activeNames.value.push(milestoneItem.id);
        obj[milestoneItem.id] = {
          editable: milestoneItem.editable,
          formPubMongoId: milestoneItem.id,
          formProperty: "DISPLAY"
        };
        anchorLists.push({
          title: milestoneItem.name,
          ref: `collaspse-${milestoneItem.id}`,
          name: milestoneItem.id,
          state: milestoneItem.editable,
          stateText: milestoneItem.editable
            ? "trade_email_editable"
            : "trade_email_readonly"
        });
      });

      const msData = milestoneGroupList.find(
        milestoneItem => milestoneItem.msId === msId
      );
      if (msData.workOrderGroupList.length > 0) {
        activeName.value = msData.workOrderGroupList[0].workOrderId;
      }

      currentWorkOrderInfo.value = msTemplateData.value.workOrderGroupList.find(
        workOrderGroup => workOrderGroup.workOrderId === activeName.value
      );

      const { replyWidgetList } = msTemplateData.value;
      let widgetFormData = [];
      if (replyWidgetList) {
        replyWidgetList.forEach(widgetData => {
          widgetFormData.push({
            label: widgetData.label,
            val: widgetData.obj,
            showObj: widgetData.showObj,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId
          });
        });
      }
      orderWorkWidgetFormData.value = TransformSubmitDataStructure(
        msTemplateData.value.replyWidgetList,
        msTemplateData.value.subWidgetMap,
        msTemplateData.value.linkedReferenceMap
      );

      operationalFactorData.value = getEntireFormData(
        msTemplate.value?.msId,
        ""
      );
      drawerVisible.value = true;
    }
  );
};

const handleOpenMainFormDialog = () => {
  OrderMainFormRef.value.handleOpenMainFormVisible();
  mainFormVisible.value = !mainFormVisible.value;
};

const handleWorkOrderCollection = async () => {
  const workOrderWidgetData =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(true);
  const linkedReferenceSourceList =
    TrendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  if (!workOrderWidgetData) {
    return false;
  }

  const sccsId = (route.query.sccsId as string) || routeParams.value.sccsId;
  const orderId = (route.query.orderId as string) || routeParams.value.orderId;
  const { msId, msReplyId, version } = msTemplateData.value;
  const { code } = await settingMilestoneReply({
    sccsId: sccsId,
    orderId: orderId,
    msId: msId,
    workOrderId: msReplyId,
    version: version,
    widgetItemDataList: workOrderWidgetData,
    linkedReferenceSaveList: linkedReferenceSourceList
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    drawerVisible.value = false;
    emit("handleUpdateCollection");
  }
};

const handleClose = (): void => {
  // basicInfoDrawer.value = false;
  // emailInfoVisible.value = false;
  drawerVisible.value = false;
};

onMounted(() => {
  emitter.on("widgetUploadLoading", (isUploading: boolean) => {
    btnOperationState.value = isUploading;
  });
});

defineExpose({
  open
});
</script>
<style lang="scss">
@use "./style/index.scss";

.work-order-milestone-header {
  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .drawer-header-left {
      font-size: 18px;
      font-weight: bolder;
      line-height: 20px;
      color: #202020;
    }

    .drawer-header-right {
      display: flex;
      align-items: center;

      .drawer-header-btn-next-group {
        position: relative;
        display: flex;
        flex: 1;
        align-items: center;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        .drawer-header-col {
          padding: 3px 10px;
          cursor: pointer;
          border-radius: 4px;

          &:nth-child(2) {
            margin-right: 12px;
          }

          .iconfont {
            font-size: 13px;
            color: #595959;
          }

          .drawer-header-text {
            margin: 0 6px;
            font-size: 14px;
            color: #595959;
          }

          &:hover {
            background: #e5e5e5;
          }
        }
      }

      .drawer-header-btn-group {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 5px;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        .drawer-header-col {
          width: 28px;
          height: 28px;
          margin: 0 4px;
          line-height: 28px;
          text-align: center;
          cursor: pointer;
          border-radius: 4px;

          .iconfont {
            font-size: 14px;
          }

          &.create-work-order-active {
            background: #e1edff;

            .iconfont {
              color: #0070d2;
            }
          }

          &:first-child {
            .iconfont {
              font-size: 16px;
            }
          }

          &:hover {
            background: #e5e5e5;
          }
        }

        &:nth-child(2) {
          margin-right: 10px;
        }
      }
    }
  }
}

.work-order-milestone-container {
  display: flex;
  padding: 0;

  .drawer-container-left {
    flex: 1;
    height: 100%;
    padding: 0;
    border-right: 1px solid #e6eaf0;

    .work-order-tabs {
      height: 100%;

      .el-tab-pane {
        height: 100%;
      }

      .el-tabs__header {
        padding: 0 12px;
        background: #fff;
      }

      .el-tabs__content {
        padding: 10px 12px;
      }
    }
  }

  .drawer-container-right {
    display: flex;
    flex-direction: column;
    width: 538px;
    min-width: 538px;
    background: #fff;
    border-right: 1px solid #e6eaf0;

    .drawer-container-top {
      display: flex;
      align-items: center;
      height: 45px;
      padding: 0 37px;
    }

    .drawer-container-main-body {
      flex: 1;
      max-height: calc(100% - 60px);
      padding: 0 10px;

      .el-collapse-item {
        border: 0 none;

        .el-collapse-item__header {
          display: none;
        }

        .el-collapse-item__wrap {
          border: 0 none;

          .el-collapse-item__content {
            border: 0 none;
          }
        }
      }
    }

    .drawer-container-footer {
      display: flex;
      align-items: center;
      justify-content: end;
      height: 60px;
      padding: 0 18px;
      background: #fff;
      border-top: 1px solid #e6eaf0;
    }
  }
}

.collapse-reply-form {
  .el-collapse-item__header {
    background: #fdf5ed !important;
  }
}
</style>
