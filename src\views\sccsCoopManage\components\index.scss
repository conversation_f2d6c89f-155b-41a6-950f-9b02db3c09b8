.team-module-area-container {
  padding: 14px 25px;

  .team-module-area-header {
    display: flex;
    align-items: center;
    padding: 3px 0;
    border-bottom: 1px solid #e3e5e9;

    .team-module-area-title {
      flex: 1;
      align-items: center;
      font-size: 16px;
      font-weight: bolder;
      line-height: 22px;
      color: #303133;
    }

    .el-button {
      .iconfont {
        align-items: center;
        margin-right: 6px;
        font-size: 12px;
      }
    }
  }

  .team-module-area-body {
    margin: 30px 0;
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  cursor: pointer;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;

  .tag-span-icon {
    position: relative;
    display: inline-block;
    width: 12px;
    height: 12px;

    // background: #fff;
    padding: 2px;
    margin-right: 2px;
    vertical-align: text-top;
    border-radius: 2px;

    .iconfont {
      position: absolute;
      top: 50%;
      left: 50%;
      font-size: 8px;
      color: #fa8d0a;
      transform: translate(-50%, -50%);
    }
  }
}

::v-deep(.el-table) {
  .el-table-sccs-setting-cell {
    .cell {
      text-overflow: initial !important;
    }

    .tag-col {
      padding: 3px 6px;
      margin-right: 4px;
      margin-bottom: 4px;
      font-size: 12px;
      line-height: 20px;
      color: #595959;
      cursor: pointer;
      background: #f0f0f0;
      border: 0 none;
      border-radius: 4px;

      .tag-span-icon {
        position: relative;
        display: inline-block;
        width: 12px;
        height: 12px;
        padding: 2px;
        margin-right: 2px;
        vertical-align: text-top;
        border-radius: 2px;

        .iconfont {
          position: absolute;
          top: 50%;
          left: 50%;
          font-size: 8px;
          color: #fa8d0a;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
}

.el-table-register-tip {
  display: inline-block;
  padding: 2px 6px;
  margin-right: 5px;
  margin-left: 10px;
  font-size: 12px;
  line-height: 17px;
  color: #fa8d0a;
  background: #fff7e6;
  border-radius: 4px;
}

::v-deep(.activate-row) {
  .el-table__cell {
    &:first-child {
      padding: 0 !important;

      .cell {
        width: 100%;
        padding: 0 !important;

        .el-table-register-text {
          display: inline-block;
          padding: 8px !important;
          overflow: hidden;
          text-overflow: ellipsis;
          word-wrap: break-word;
          vertical-align: middle;
        }
      }
    }
  }
}

.tag-owner {
  color: #0070d2 !important;
  background: #eef8ff !important;
}

.tag-manager {
  color: #5f66e5 !important;
  background: #f4f5ff !important;
}

.colorRed {
  margin-right: 8px !important;
  color: #cf1421 !important;
}
