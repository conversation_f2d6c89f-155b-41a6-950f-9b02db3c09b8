import express from "express";
import cors from "cors";
import bodyParser from "body-parser";
import {
  calculateFormulasWidget,
  getEntireFormData,
  transformFormulasValue,
  handleAccordingToFormIdObtainData
} from "./formulas.js";

const app = express();
const port = 38090;

app.use(cors());

// 解析 application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
// 解析 application/json
app.use(bodyParser.json({ limit: "50mb" }));

app.post("/api/calculate-formulas", async (req, res) => {
  const data = req.body; // 获取POST数据
  console.log("收到数据:", data);

  let encapsulationData = await handleAccordingToFormIdObtainData(
    data.detailData,
    data.templateData
  );
  const msId = data.msId ? data.msId : "";
  const workOrderId = data.workOrderId ? data.workOrderId : "";

  const factorData = getEntireFormData(
    {
      templateData: encapsulationData,
      entrieTemplateForm: data.templateData
    },
    msId,
    workOrderId
  );

  const formulasResult = calculateFormulasWidget(
    data.formulas
      .replace(/(?<![><])=(?!=)/g, "==")
      .replace(/<>/g, "!==")
      .replace(/&/g, "+"),
    factorData.entireDetail
  );

  res.json({ status: "success", data: transformFormulasValue(formulasResult) });
});

app.listen(port, () => {
  console.log(`服务器运行在 http://localhost:${port}`);
});
