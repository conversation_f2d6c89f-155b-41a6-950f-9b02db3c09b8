<template>
  <div class="team-member-info-container team-module-area-container">
    <el-tabs v-model="activeName" editable>
      <template #add-icon>
        <el-button type="primary" color="#0070D2" @click="handleAdd">
          <FontIcon icon="link-add" />{{ t("trade_common_create") }}
        </el-button>
      </template>
      <el-tab-pane
        :label="t('trade_sccs_businessRole')"
        name="businessRole"
        lazy
      >
        <SccsBusinessRole
          ref="SccsBusinessRoleRef"
          :basicInfo="basicInfo"
          :groupId="groupId"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="t('trade_sccs_collaborativeRole')"
        name="collaborativeRole"
        lazy
      >
        <SccsCoopTeamRole
          ref="SccsCollaboratorsRoleRef"
          :basicInfo="basicInfo"
          :groupId="groupId"
        />
      </el-tab-pane>
      <el-tab-pane :label="t('trade_sccs_dataFence')" name="dataFence" lazy>
        <SccsDataFence
          ref="SccsDataFenceRef"
          :basicInfo="basicInfo"
          :groupId="groupId"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import type { TabsPaneContext } from "element-plus";
import { useI18n } from "vue-i18n";
import SccsBusinessRole from "./SccsBusinessRole.vue";
import SccsCoopTeamRole from "./SccsCoopTeamRole.vue";
import SccsDataFence from "./SccsDataFence.vue";

const { t } = useI18n();
const activeName = ref("businessRole");
const SccsBusinessRoleRef = ref<any>(null);
const SccsCollaboratorsRoleRef = ref<any>(null);
const SccsDataFenceRef = ref<any>(null);
const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleAdd = (): void => {
  if (activeName.value === "businessRole") {
    SccsBusinessRoleRef.value.addSccsBusinessRole();
  } else if (activeName.value === "collaborativeRole") {
    SccsCollaboratorsRoleRef.value.addSccsBusinessRole();
  } else if (activeName.value === "dataFence") {
    SccsDataFenceRef.value.dataFenceId = "";
    SccsDataFenceRef.value.open();
  }
};
</script>
<style lang="scss" scoped>
.team-module-area-container {
  ::v-deep(.el-tabs) {
    padding: 0 25px !important;

    .team-module-area-btn {
      position: absolute;
      top: 0;
      right: 0;
    }

    .el-tabs__header {
      width: auto !important;

      .el-tabs__item {
        width: auto !important;
        padding: 0 !important;
        margin-right: 22px !important;
        margin-left: 10px !important;

        &.is-active {
          font-weight: bolder;
          color: #0070d2 !important;
          background: transparent !important;
        }

        .el-icon {
          display: none;
        }
      }

      .el-tabs__active-bar {
        height: 3px !important;
        color: #0070d2 !important;
      }

      .el-tabs__new-tab {
        position: absolute;
        top: 50%;
        right: 30px;
        z-index: 1000;
        border: 0 none;
        transform: translateY(-81%);
      }
    }
  }
}
</style>
