<template>
  <el-input-number
    ref="DrInputNumberRef"
    v-model="widgetFormData[widgetConfigure._fc_id]"
    v-thousands="widgetConfigure.props.useThousandSeparator"
    :precision="widgetConfigure.props.precision"
    :min="widgetMinValue"
    :max="widgetMaxValue"
    :value-on-clear="null"
    clearable
    controls-position="right"
    :placeholder="placeholder"
    @change="handleChange"
  >
    <template v-if="unitPosition === 'before' && widgetRowIndex === -1" #prefix>
      <span>{{ unitTitle }}</span>
    </template>
    <template v-if="unitPosition === 'after' && widgetRowIndex === -1" #suffix>
      <span>{{ unitTitle }}</span>
    </template>
  </el-input-number>
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, ref, watch } from "vue";
import { ElInputNumber } from "element-plus";
import { cloneDeep, storageLocal } from "@pureadmin/utils";
import { formatThousandNumber } from "@/utils/common";
import { thousands } from "@/directives/thousands";

const vThousands = thousands;
const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

const unitTitle = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.unitEn || props.widgetConfigure.props?.unit
    : props.widgetConfigure.props?.unit;
});

const DrInputNumberRef = ref<HTMLElement | any>(null);
const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const actuatorDefaultValue: any = inject("ActuatorDefaultValue", null);
const invocationDefaultValueExecutor: any = inject(
  "invocationDefaultValueExecutor",
  null
);

const emit = defineEmits([
  "handleSubTableWidgetValueChange",
  "handleRenderFinish"
]);

const widgetMaxValue = computed(() => {
  const widgetConfigureMax = props.widgetConfigure.props.max;
  return widgetConfigureMax !== null && widgetConfigureMax !== undefined
    ? widgetConfigureMax
    : Infinity;
});

const widgetMinValue = computed(() => {
  const widgetConfigureMin = props.widgetConfigure.props.min;
  return widgetConfigureMin !== null && widgetConfigureMin !== undefined
    ? widgetConfigureMin
    : -Infinity;
});

const unitPosition = computed(() => {
  return props.widgetConfigure.props.unitPosition;
});

watch(
  () => ({ ...props.trendsForm }),
  (newVal, oldVal) => {
    // if (props.widgetRowIndex === -1) {
    widgetFormData.value[props.widgetConfigure._fc_id] =
      typeof cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id] ===
        "string" &&
      cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id] === ""
        ? null
        : typeof cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id] ===
            "string"
          ? parseFloat(
              cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id].replace(
                /,/g,
                ""
              )
            )
          : cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id];

    if (actuatorDefaultValue) {
      const defaultValueConfig =
        props.widgetConfigure.props.defaultValueConfig || {};

      if (
        defaultValueConfig.type === "relate" &&
        newVal &&
        oldVal &&
        newVal.hasOwnProperty(defaultValueConfig.content) &&
        oldVal.hasOwnProperty(defaultValueConfig.content) &&
        newVal[defaultValueConfig.content] !==
          oldVal[defaultValueConfig.content]
      ) {
        widgetFormData.value[props.widgetConfigure._fc_id] =
          invocationDefaultValueExecutor(
            props.widgetConfigure.props.defaultValueConfig,
            props.widgetConfigure
          );
        handleChange();
      } else if (defaultValueConfig.type === "formula") {
        const formulasParams = defaultValueConfig.content.formulasParams.map(
          param => param.split("@@")[1]
        );

        for (let formulasParam of formulasParams) {
          if (
            newVal &&
            oldVal &&
            newVal.hasOwnProperty(formulasParam) &&
            oldVal.hasOwnProperty(formulasParam) &&
            newVal[formulasParam] !== oldVal[formulasParam]
          ) {
            widgetFormData.value[props.widgetConfigure._fc_id] =
              invocationDefaultValueExecutor(
                props.widgetConfigure.props.defaultValueConfig,
                props.widgetConfigure
              );
            handleChange();
            return;
          }
        }
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const handleChange = () => {
  const widgetId = props.widgetConfigure._fc_id;
  const widgetValue = widgetFormData.value[widgetId];
  const widgetLabelText = props.widgetConfigure.props.useThousandSeparator
    ? formatThousandNumber(widgetValue)
    : widgetValue;

  if (props.widgetRowIndex !== -1) {
    emit(
      "handleSubTableWidgetValueChange",
      widgetId,
      {
        label: widgetLabelText,
        obj: widgetFormData.value[widgetId],
        widgetType: "DrInputNumber",
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  } else {
    handleWidgetFormsValue(
      widgetId,
      {
        label: widgetLabelText,
        obj: widgetFormData.value[widgetId],
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  }
};

onMounted(() => {
  emit("handleRenderFinish", DrInputNumberRef.value);
});
</script>
