<template>
  <el-checkbox
    v-for="chooseItem in chooseProcessorList"
    :key="chooseItem.teamMemberId"
    :label="chooseItem.username"
    :value="chooseItem.teamMemberId"
    class="choose-checkbox"
    :disabled="!chooseItem.activate"
    @change="val => handleSelectChoose(chooseItem, val)"
  >
    <div class="choose-processor-item">
      <LkNoPopoverAvatar
        :size="32"
        :teamInfo="{
          avatar: chooseItem.avatar,
          username: chooseItem.username,
          coop: chooseItem.coop
        }"
      />
      <div class="choose-processor-msg-box">
        <div class="choose-processor-msg-text">
          <span v-if="chooseItem.activate">
            {{ chooseItem.username }}
          </span>
          <span v-else class="choose-processor-msg-orange-text">
            <el-tooltip
              effect="dark"
              :content="t('trade_common_notSelectRegister')"
              placement="top"
              :showAfter="500"
            >
              ({{ t("trade_common_unregistered") }})
            </el-tooltip>
          </span>
        </div>
        <div class="choose-processor-msg-text">
          {{ chooseItem.account }}
        </div>
      </div>
    </div>
  </el-checkbox>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import LkNoPopoverAvatar from "@/components/lkNoPopoverAvatar/index";

interface ChooseProcessorProp {
  teamMemberId: string;
  avatar: string;
  activate: boolean;
  username: string;
  account: string;
}

const { t } = useI18n();

const props = defineProps({
  chooseProcessorList: {
    type: Object as PropType<ChooseProcessorProp[]>,
    default: () => {}
  }
});

const emit = defineEmits(["handleSelectChoose"]);

const handleSelectChoose = (chooseData: any, checked: any) => {
  emit("handleSelectChoose", chooseData, checked);
};
</script>
