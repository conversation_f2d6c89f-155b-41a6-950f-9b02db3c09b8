.order-manage-body {
  display: flex;
  justify-content: center;
  padding: 10px;

  .order-manage-left {
    position: relative;
    width: 240px;
    padding: 10px 11px 0;
    margin-right: 5px;
    background: #fff;
    border-radius: 4px;
    transition: all 0.3s ease;

    &.Screen_1366 {
      width: 180px;
    }

    .anchor-icon-left {
      position: absolute;
      top: 4px;
      right: -12px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      padding: 4px;
      text-align: center;
      cursor: pointer;
      background: #fff;
      border: 1px solid rgb(1 6 62 / 9%);
      border-radius: 50%;

      .link-pack-up {
        font-size: 10px;
        color: #8c8c8c;
      }
    }

    &.hideLeft {
      display: none;
    }

    ::v-deep(.order-manage-collapse) {
      background: #f9f9f9 !important;
      border: 0 none;
      border-radius: 4px;

      .el-collapse-item {
        padding-top: 13px !important;
        padding-bottom: 10px !important;

        .el-collapse-item__header {
          height: 20px;
          padding-left: 13px !important;
          margin-bottom: 10px;
          font-size: 14px;
          font-weight: bolder;
          line-height: 20px;
          color: #262626;
          text-align: left;
          background: #f9f9f9 !important;
          border: 0 none;

          i {
            display: none !important;
          }
        }

        .el-collapse-item__wrap {
          background: #f9f9f9 !important;
          border: 0 none;

          .el-collapse-item__content {
            padding-bottom: 0 !important;

            .order-manage-col {
              display: flex;
              align-items: center;
              height: 40px;
              padding: 0 18px;
              font-size: 14px;
              line-height: 40px;
              color: #595959;
              text-align: left;
              cursor: pointer;

              .order-manage-col-text {
                flex: 1;
                align-items: center;

                .order-manage-col-span-text {
                  margin-left: 5px;
                  font-size: 14px;
                  color: #595959;
                }
              }

              .order-manage-col-icon {
                width: 30px;
                height: 40px;

                .el-dropdown {
                  align-items: center;
                  height: 100%;
                  vertical-align: baseline;
                }
              }

              .iconfont {
                margin-right: 2px;
                font-size: 13px;
                color: #595959;

                &.no-margin {
                  margin-right: 0;
                }
              }

              &:hover {
                background: #f0f0f0;

                // .iconfont {
                //   color: #f0f0f0;
                // }
              }

              &.active {
                color: #0070d2;
                background: #eef8ff;

                .iconfont {
                  color: #0070d2;
                }
              }
            }
          }
        }
      }
    }

    ::v-deep(.el-dropdown) {
      display: flex;
      justify-content: center;
      width: 100%;
    }

    .order-manage-sccs-btn {
      width: 100%;
      height: 40px;
      font-size: 14px;
      line-height: 40px;
      color: #595959;
      text-align: center;
      cursor: pointer;
      border-top: 1px solid #ededed;

      .iconfont {
        font-size: 12px;
      }

      &:hover {
        background: #f0f0f0;
      }
    }

    .order-manage-col-main {
      height: calc(100% - 53px);

      .order-manage-col {
        margin-top: 11px;
        background: #f9f9f9;
        border-radius: 4px;

        .order-manage-title {
          height: 36px;
          padding: 0 12px;
          overflow: hidden;
          font-size: 14px;
          font-weight: bolder;
          line-height: 36px;
          color: #262626;
          text-align: left;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .order-manage-list-body {
          .order-manage-draggable-col {
            .item {
              display: flex;
              align-items: center;
              width: 100%;
              height: 40px;
              padding: 0 10px;
              padding-left: 5px;
              cursor: pointer;

              .order-manage-draggable-col-left {
                display: inline-flex;
                flex: 1;
                align-items: center;
                padding-left: 6px;

                .vis-hidden-icon {
                  font-size: 10px;
                  color: #8c8c8c;
                  vertical-align: middle;
                  visibility: hidden;
                }

                .table-manage-icon {
                  margin-right: 3px;
                  margin-left: 6px;
                  color: #595959;
                }

                .order-manage-draggable-col-title {
                  max-width: 146px;
                  margin-left: 5px;
                  font-size: 14px;
                  line-height: 20px;
                  color: #595959;
                  text-align: left;
                }
              }

              .order-manage-draggable-col-right {
                display: flex;
                align-items: center;
                border-radius: 4px;

                .collect-btn {
                  margin-right: 8px;

                  .link-star-filled {
                    &.colorOrange {
                      color: rgb(254 199 60);
                    }
                  }
                }

                ::v-deep(.el-dropdown) {
                  .iconfont {
                    font-size: 18px;
                    visibility: hidden;
                  }
                }
              }

              &:hover {
                background: #f0f0f0;

                .order-manage-draggable-col-left {
                  .iconfont {
                    visibility: initial;
                  }
                }

                .order-manage-draggable-col-right {
                  background: #e5e5e5;

                  .link-more {
                    visibility: initial;
                  }
                }
              }
            }

            &.active {
              background: #eef8ff;

              .order-manage-draggable-col-title {
                color: #0070d2 !important;
              }

              .table-manage-icon {
                color: #0070d2 !important;
              }

              .item {
                &:hover {
                  background: #eef8ff;

                  .order-manage-draggable-col-left {
                    .iconfont {
                      visibility: initial;

                      &.no-margin {
                        margin-right: 0;
                      }
                    }
                  }

                  .order-manage-draggable-col-right {
                    background: #e5e5e5;

                    .link-more {
                      visibility: initial;
                    }

                    .colorRed {
                      color: red !important;
                    }
                  }
                }
              }

              .el-text {
                color: #0070d2 !important;
              }
            }

            &.default-row {
              .item {
                &:hover {
                  background: #f0f0f0;

                  .order-manage-draggable-col-left {
                    .link-more {
                      visibility: initial;
                    }
                  }

                  .order-manage-draggable-col-right {
                    background: #e5e5e5;

                    .link-more {
                      visibility: initial;
                    }
                  }
                }
              }

              .order-manage-draggable-col-right {
                .link-more {
                  visibility: initial;
                }
              }
            }
          }
        }
      }
    }
  }

  .order-manage-right {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    width: calc(100vh - 245px);
    max-width: 95%;
    padding: 0 10px;
    transition: all 0.3s ease;

    .anchor-icon-right {
      position: absolute;
      top: 4px;
      left: -23px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      padding: 4px;
      text-align: center;
      cursor: pointer;
      background: #fff;
      border: 1px solid rgb(1 6 62 / 9%);
      border-radius: 50%;

      &:hover {
        color: #fff;
        background: #0070d2;
        border: 1px solid #ddd;
        border-radius: 13px;
        box-shadow: 0 0 8px 0 rgb(0 0 0 / 16%);

        .link-expand {
          font-size: 10px;
          color: #fff;
        }
      }

      .link-expand {
        font-size: 10px;
        color: #0070d2;
      }

      .svg-icon-order-schedule,
      .svg-icon-order-schedule-white {
        width: 100%;
        height: 100%;
      }

      &.orange {
        background: #ff721b;
      }
    }

    .order-manage-right-table-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      height: 47px;

      .order-manage-right-table-left {
        display: flex;
        align-items: center;

        .order-manage-default {
          display: flex;
          align-items: center;
          width: 100%;
        }

        .order-manage-right-radio {
          background: #fff;
          border-radius: 4px;

          ::v-deep(.el-radio-button) {
            border: 0 none;

            &.is-active {
              .el-radio-button__inner {
                color: #0070d2;
                background: #ddefff;
              }
            }

            .el-radio-button__inner {
              height: 24px;
              padding: 0 5px !important;
              margin: 4px;
              line-height: 24px;
              color: #262626;
              border: 0 none !important;
              box-shadow: none;
            }
          }
        }

        .order-manage-subtable-select-body {
          margin: 0 10px;

          .el-select {
            width: 156px !important;
          }
        }

        .order-manage-switch-body {
          align-items: center;
          margin-left: 20px;

          &.Screen_1366 {
            margin-left: 5px;
          }

          .el-switch {
            --el-switch-on-color: #0070d2;

            height: 100%;
          }
        }

        .order-manage-text {
          font-size: 14px;
          line-height: 20px;
          color: #595959;
          text-align: left;
          white-space: nowrap;

          &.Screen_1366 {
            margin-left: 5px;
          }

          .order-manage-text-name {
            font-weight: bold;
            color: #262626;
          }
        }

        .order-manage-btn {
          display: inline-block;
          padding: 6px 11px;
          margin-left: 10px;
          font-size: 14px;
          line-height: 19px;
          color: #595959;
          text-align: left;
          white-space: nowrap;
          cursor: pointer;
          border-radius: 4px;

          &.Screen_1366 {
            margin-left: 0;
          }

          &.active {
            background: #eef8ff;
          }

          &.order-manage-disabled-btn {
            color: #a8abb2;
            cursor: not-allowed;
          }

          .iconfont {
            margin-right: 6px;
            vertical-align: top;

            &.no-margin {
              margin-right: 0;
            }

            &:only-child {
              margin-right: 0;
            }
          }

          &.is-disabled {
            color: #bfbfbf !important;
          }
        }
      }

      .order-manage-right-table-right {
        display: flex;
        align-items: center;
        text-align: right;

        .order-manage-btn {
          display: inline-block;
          padding: 6px 11px;
          margin-left: 10px;
          font-size: 14px;
          line-height: 19px;
          color: #595959;
          text-align: left;
          white-space: nowrap;
          cursor: pointer;
          border-radius: 4px;

          &.Screen_1366 {
            margin-left: 0;
          }

          ::v-deep(.el-dropdown-link) {
            &:hover {
              background: #e5e5e5;
            }
          }

          &.active {
            color: #0070d2;
            background: #eef8ff;

            &:hover {
              background: #eef8ff;
            }
          }

          &:hover {
            background: #e5e5e5;
          }

          .iconfont {
            margin-right: 6px;
            font-size: 12px;

            &.no-margin {
              margin-right: 0;
            }

            &:only-child {
              margin-right: 0;
            }
          }

          &.is-disabled {
            color: #bfbfbf !important;
          }
        }

        ::v-deep(.order-manage-right-table-input) {
          width: 184px !important;
          margin-right: 6px !important;

          .el-input__wrapper {
            background: transparent !important;
          }

          .el-input-group__append {
            padding: 0;

            .order-manage-input-jump-body {
              display: flex;
              align-items: center;
              padding: 0 10px;

              .el-icon {
                cursor: pointer;
              }
            }
          }
        }

        ::v-deep(.order-manage-right-table-btn) {
          .iconfont {
            margin-right: 3px;
            font-size: 14px;

            &.no-margin {
              margin-right: 0;
            }
          }
        }
      }
    }

    .order-manage-table-container {
      flex: 1;
    }
  }
}

// @media screen and (width <= 1366px) {
//   .order-manage-body {
//     height: calc(100vh - 148px) !important;
//   }
// }

.order-field-popover-main {
  .order-field-popover-top {
    margin: 5px 0;

    .order-field-draggable-col {
      display: flex;
      width: 100%;
      height: 36px;
      padding: 0 14px;
      line-height: 36px;
      cursor: pointer;

      &:hover {
        background: #f2f2f2;
      }

      .item {
        display: flex;
        width: 100%;
      }

      .iconfont {
        margin-right: 11px;
        font-size: 12px;

        &.no-margin {
          margin-right: 0;
        }
      }

      .order-field-draggable-col-title {
        max-width: calc(180px - 20px);
        overflow: hidden;
        font-size: 12px;
        color: #262626;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.hide-col-title {
          color: #bfbfbf;
        }
      }

      .order-field-draggable-col-left {
        display: flex;
        flex: 3;
        width: 100%;
      }

      .order-field-draggable-col-right {
        flex: 1;
        margin-right: 4px;
        text-align: right;

        .iconfont {
          font-size: 16px;
          color: #808080;
        }
      }
    }
  }

  .order-field-settings {
    padding: 0 14px;
    margin: 11px 0;
    font-size: 12px;
    line-height: 17px;
    color: #bfbfbf;
    text-align: left;
  }

  .order-field-popover-bottom {
    margin: 5px 0;
    border-top: 1px solid #ebebeb;

    .order-field-draggable-col {
      display: flex;
      width: 100%;
      height: 36px;
      padding: 0 14px;
      line-height: 36px;
      cursor: pointer;

      &:hover {
        background: #f2f2f2;
      }

      .item {
        display: flex;
        width: 100%;
      }

      .iconfont {
        margin-right: 11px;
        font-size: 12px;

        &.no-margin {
          margin-right: 0;
        }
      }

      .order-field-draggable-col-title {
        max-width: calc(180px - 20px);
        overflow: hidden;
        font-size: 12px;
        color: #262626;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.hide-col-title {
          color: #bfbfbf;
        }
      }

      .order-field-draggable-col-left {
        display: flex;
        flex: 3;
        width: 100%;
      }

      .order-field-draggable-col-right {
        flex: 1;
        margin-right: 4px;
        text-align: right;

        .iconfont {
          font-size: 16px;
          color: #808080;
        }
      }
    }
  }

  .order-field-operate-body {
    height: 54px;
    padding: 0 20px;
    line-height: 54px;
    text-align: right;
    border-top: 1px solid #ebebeb;
  }
}

.order-sort-widget-container {
  .order-sort-widget-title {
    margin-bottom: 20px;
    color: #595959;
  }

  .order-sort-widget-bottom {
    color: #0070d2;
    cursor: pointer;

    .iconfont {
      font-size: 12px;
    }
  }

  .vxe-table-draggable-container {
    margin-bottom: 12px;

    .iconfont {
      font-size: 10px;
    }

    .vxe-table-fields-select-v2 {
      width: 183px;
      margin-left: 6px;
    }

    .vxe-table-sort-select-v2 {
      width: 100px;
      margin-left: 10px;
    }

    .vxe-table-sort-icon {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
}

.vxe-tag-container {
  display: flex;
  align-items: center;
  width: 100%;

  .vxe-tag-col {
    display: inline-flex;
    align-items: center;
    margin-right: 34px;

    .vxe-tag-col-title {
      display: inline-block;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      background: #dfdfdf;
      border-radius: 3px;

      &.primary {
        font-size: 11px;
        color: #0070d2;
        background: #ddefff;
      }

      &.warn {
        font-size: 11px;
        color: #fa8d0a;
        background: #fdebcf;
      }

      .iconfont {
        font-size: 8px;
        vertical-align: middle;
      }
    }

    .vxe-tag-col-desc {
      display: inline-block;
      width: auto;
      font-size: 14px;
      color: #797979;

      &::before {
        font-size: 12px;
        color: #797979;
        content: "：";
      }
    }
  }
}

.download-title {
  margin: 29px 0;
  font-size: 14px;
  line-height: 16px;
  color: #797979;
}

// 批量修改里程碑状态弹窗
.modifyMilestoneStatus-title {
  margin-top: 15px;
  margin-bottom: 24px;
}

.modifyMilestoneStatus-executable-situation {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 66px;
  max-height: 214px;
  background: #f6f6f6;
  border: 1px solid #ebebeb;

  .modifyMilestoneStatus-placeholder-text {
    font-size: 12px;
    line-height: 17px;
    color: #bfbfbf;
  }

  .have-update-ms-status-order-info {
    width: 100%;
    padding: 15px;

    .order-info-title {
      margin-bottom: 24px;
      font-size: 14px;
      line-height: 16px;
      color: #262626;
    }

    .order-info-item {
      margin-bottom: 24px;
    }

    .order-info-item-title {
      margin-bottom: 6px;
      font-size: 14px;
      line-height: 16px;
      color: #797979;
    }

    .order-info-item-orderMarks {
      font-size: 14px;
      line-height: 18px;
      color: #262626;
      word-wrap: break-word;
    }
  }
}

// 批量修改里程碑状态弹窗 end

// 批量修改订单状态弹窗

.modifyOrderStatus-title {
  margin-top: 15px;
  margin-bottom: 24px;
}

.modifyOrderStatus-executable-situation {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 66px;
  max-height: 214px;
  background: #f6f6f6;
  border: 1px solid #ebebeb;

  .modifyOrderStatus-placeholder-text {
    font-size: 12px;
    line-height: 17px;
    color: #bfbfbf;
  }

  .have-update-order-status-order-info {
    width: 100%;
    padding: 15px;

    .order-info-title {
      margin-bottom: 24px;
      font-size: 14px;
      line-height: 16px;
      color: #262626;
    }

    .order-info-item {
      margin-bottom: 24px;
    }

    .order-info-item-title {
      margin-bottom: 6px;
      font-size: 14px;
      line-height: 16px;
      color: #797979;
    }

    .order-info-item-orderMarks {
      font-size: 14px;
      line-height: 18px;
      color: #262626;
      word-wrap: break-word;
    }
  }
}

// 角标组件样式
.custom-checkbox-body {
  width: 100%;

  .triangle-topright {
    // transform: translate(24px, -23px);
    position: absolute;
    top: 0;
    right: 12px;
    width: 0;
    height: 0;
    border-left: 24px solid transparent;
  }

  .triangle-topright-one {
    border-top: 24px solid #ddefff;

    &::after {
      position: absolute;
      top: -24px;
      right: 3px;
      font-size: 12px;
      font-weight: bold;
      color: #0070d2;
      content: "R";
    }
  }

  .triangle-topright-two {
    border-top: 24px solid #ffedd1;

    &::after {
      position: absolute;
      top: -24px;
      right: 3px;
      font-size: 12px;
      font-weight: bold;
      color: #fa8d0a;
      content: "S";
    }
  }

  .triangle-topright-three {
    border-top: 24px solid #dfdfdf;

    &::after {
      position: absolute;
      top: -24px;
      right: 3px;
      font-size: 12px;
      font-weight: bold;
      color: #262626;
      content: "i";
    }
  }
}

.custom-checkbox-popover-body {
  .custom-checkbox-popover-desc {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 20px;
    color: #616266;
  }

  .custom-checkbox-popover-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;

    .custom-checkbox-popover-title {
      padding: 4px 6px;
      font-size: 12px;
      line-height: 20px;
      border-radius: 2px;

      // 原单
      &.custom-order {
        color: #0070d2;
        background: #ddefff;
      }

      // 翻单
      &.custom-order-clone {
        color: #2b69f7;
        background: #dde1ff;
      }

      // 关联（同一个sccs关联）
      &.custom-order-related {
        color: #fa8d0a;
        background: #ffedd1;
      }

      // 跨sccs关联
      &.custom-order-related-cross {
        color: #00b699;
        background: #d9f4f0;
      }

      // 里程碑互用于/工单互用于
      &.custom-ms-clone {
        color: #9870fa;
        background: #e6dcff;
      }

      // 里程碑互用自/工单互用自
      &.custom-ms-clone-from {
        color: #fa8d0a;
        background: #fff1cd;
      }
    }

    .order-mark,
    .milestone-mark,
    .work-order-mark {
      display: inline-flex;
      align-items: center;
    }

    .milestone-name {
      margin-left: 5px;
    }

    .milestone-mark-item {
      display: inline-flex;
      align-items: center;
    }

    .custom-checkbox-popover-tip {
      margin-left: 6px;
      color: #616266;
      cursor: pointer;

      &:hover {
        color: #0070d2;
      }
    }
  }
}

.milestone-reply-form-span-text {
  font-size: 12px;
  color: #262626;

  &.milestone-reply-form-span-red-text {
    font-weight: bolder;
    color: #e62412;
  }
}

.order-manage-footer {
  display: flex;
  align-items: center;
  height: 54px;
  background: #fff;
  border-top: 1px solid #e8e8e8;

  .order-manage-footer-left {
    flex: 1;
  }

  .order-manage-footer-right {
    flex: 1;
    justify-items: self-end;
  }
}
