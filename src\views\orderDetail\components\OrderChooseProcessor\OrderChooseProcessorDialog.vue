<template>
  <LkDialog
    ref="ChooseProcessorDialogRef"
    class="choose-processor-dialog"
    :title="t(dialogTitle)"
    :width="880"
    destroy-on-close
    append-to-body
    @confirm="handleChooseConfirm"
    @close="handleClose"
  >
    <template #default>
      <div class="choose-processor-body">
        <div class="choose-processor-left">
          <el-input
            v-model="choosePersonInput"
            class="choose-processor-input"
            :placeholder="t('trade_common_teamOrMember')"
            clearable
            :prefix-icon="Search"
          />
          <el-tabs
            v-if="personnelControlMode === 'tabMode'"
            v-model="tabActiveName"
            class="choose-processor-tabs"
          >
            <el-tab-pane :label="mainTeamName" name="teamMember">
              <el-scrollbar>
                <el-checkbox-group v-model="chooseProcessorChecked">
                  <ChooseProcessor
                    v-if="filteredChooseProcessorList.length > 0"
                    :chooseProcessorList="filteredChooseProcessorList"
                    @handleSelectChoose="handleSelectChoose"
                  />
                  <el-empty
                    v-else
                    :image-size="100"
                    :description="t('trade_common_no_search_data')"
                  />
                </el-checkbox-group>
              </el-scrollbar>
            </el-tab-pane>
            <el-tab-pane :label="t('trade_sccs_coopTeam')" name="team">
              <el-scrollbar :always="true">
                <ChooseTeam
                  ref="ChooseTeamRef"
                  :coopTeamList="chooseCoopTeamList"
                  :coopTeamData="coopTeamObject"
                  :keyword="choosePersonInput"
                  @handleGetAssignedTeamData="handleGetAssignedTeamData"
                />
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
          <el-scrollbar v-else>
            <el-checkbox-group v-model="chooseProcessorChecked">
              <ChooseProcessor
                :chooseProcessorList="filteredChooseProcessorList"
                @handleSelectChoose="handleSelectChoose"
              />
            </el-checkbox-group>
          </el-scrollbar>
        </div>
        <div class="choose-processor-right">
          <div class="choose-processor-right-header">
            <div class="choose-processor-right-header-left">
              {{ t("trade_email_selected") }}({{ selectedPerson }})
            </div>
            <div
              class="choose-processor-right-header-right"
              @click="handleClearPersonnel"
            >
              {{ t("trade_order_common_clear") }}
            </div>
          </div>
          <div class="choose-processor-right-container">
            <div
              v-if="selectChoosePersonList.length > 0"
              class="choose-processor-right-col"
            >
              <div class="choose-processor-right-col-title">
                {{ selectChoosePersonList[0].teamName }}
                {{
                  selectChoosePersonList[0].teamShortName
                    ? `（${selectChoosePersonList[0].teamShortName}）`
                    : ""
                }}
              </div>
              <div class="choose-processor-right-tag-body">
                <div
                  v-for="teamMember in selectChoosePersonList"
                  :key="teamMember.teamMemberId"
                  class="choose-processor-right-tag"
                >
                  <LkNoPopoverAvatar
                    :size="18"
                    :teamInfo="{
                      avatar: teamMember?.avatar,
                      username: teamMember?.username || teamMember?.teamName,
                      coop: teamMember?.showHand
                    }"
                  />
                  <div class="choose-processor-right-tag-text">
                    {{ teamMember.username || teamMember.teamName }}
                  </div>
                  <i
                    class="iconfont link-clean order-personnel-icon"
                    @click="handleReduceMainTeamPeronnelItem(teamMember)"
                  />
                </div>
              </div>
            </div>

            <div
              v-for="coopTeam in coopTeamList"
              :key="coopTeam.teamName"
              class="choose-processor-right-col"
            >
              <div class="choose-processor-right-col-title">
                {{ coopTeam.teamName }}
                {{
                  coopTeam.teamShortName ? `（${coopTeam.teamShortName}）` : ""
                }}
              </div>
              <div class="choose-processor-right-tag-body">
                <div
                  v-if="coopTeam.coopType === 'assignedTeamId'"
                  class="choose-processor-right-tag"
                >
                  <LkNoPopoverAvatar
                    :size="18"
                    class="colorOrange"
                    :teamInfo="{
                      avatar: coopTeam?.teamMemberData?.teamAvatar,
                      username: coopTeam?.teamMemberData?.teamName,
                      coop: false
                    }"
                  />
                  <div class="choose-processor-right-tag-text">
                    {{ coopTeam?.teamMemberData?.teamShortName }}
                  </div>
                  <i
                    class="iconfont link-clean order-personnel-icon"
                    @click="handleReduceCoopTeamPeronnelItem(coopTeam)"
                  />
                </div>
                <div v-else>
                  <div
                    v-for="teamMember in coopTeam?.teamMemberData"
                    :key="teamMember.teamMemberId"
                    class="choose-processor-right-tag"
                  >
                    <LkNoPopoverAvatar
                      :size="18"
                      :teamInfo="{
                        avatar: teamMember?.avatar,
                        username: teamMember?.username,
                        coop: teamMember?.showHand
                      }"
                    />
                    <div class="choose-processor-right-tag-text">
                      {{ teamMember.username }}
                    </div>
                    <i
                      class="iconfont link-clean order-personnel-icon"
                      @click="
                        handleReduceCoopTeamPeronnelItem(coopTeam, teamMember)
                      "
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { Search } from "@element-plus/icons-vue";
import { storageLocal, storageSession } from "@pureadmin/utils";
import { getWorkOrderProcessorList } from "@/api/order";
import LkDialog from "@/components/lkDialog/index";
import LkNoPopoverAvatar from "@/components/lkNoPopoverAvatar/index";
import ChooseProcessor from "@/views/orderDetail/components/OrderChooseProcessor/ChooseProcessor.vue";
import ChooseTeam from "@/views/orderDetail/components/OrderChooseProcessor/ChooseTeam.vue";

interface ChooseProcessorProp {
  teamMemberId: string;
  avatar: string;
  activate: boolean;
  username: string;
  account: string;
}

const { t } = useI18n();

const props = defineProps({
  personnelControlMode: {
    type: String as PropType<string>,
    default: "tabMode"
  },
  dialogTitle: {
    type: String as PropType<string>,
    default: "trade_order_ChooseProcessor"
  },
  orderChooseType: {
    type: String as PropType<string>,
    default: ""
  }
});

const route = useRoute();
const ChooseProcessorDialogRef = ref<HTMLElement | any>(null);
const ChooseTeamRef = ref<HTMLElement | any>(null);
const mainTeamName = ref<string>("");
const tabActiveName = ref<string>("teamMember");
const selectChoosePersonList = ref<any[]>([]);
const chooseProcessorChecked = ref<string[]>([]);
const chooseProcessorList = ref<ChooseProcessorProp[]>([]);
const chooseCoopTeamList = ref<any[]>([]);
const coopTeamObject = ref<any>({});

const choosePersonInput = ref<string>("");

const coopTeamList = computed(() => {
  return Object.values(coopTeamObject.value) as any;
});

// 过滤搜索结果
const filteredChooseProcessorList = computed(() => {
  return chooseProcessorList.value.filter(
    processor =>
      processor.username?.includes(choosePersonInput.value) ||
      processor.account?.includes(choosePersonInput.value)
  );
});

const selectedPerson = computed(() => {
  let number = 0;
  for (let coopTeamItem of coopTeamList.value) {
    if (coopTeamItem.coopType === "assignedTeamId") {
      number += 1;
    } else if (coopTeamItem.coopType === "assignedTeamMemberId") {
      number += coopTeamItem.teamMemberData.length;
    }
  }
  number += selectChoosePersonList.value.length;
  return number;
});

const handleClearPersonnel = () => {
  coopTeamObject.value = {};
  selectChoosePersonList.value = [];
  chooseProcessorChecked.value = [];
};

/** 主团队人员减少 */
const handleReduceMainTeamPeronnelItem = (mainTeamUserInfo: any) => {
  // 从已选中的处理器列表中移除指定成员
  const index = chooseProcessorChecked.value.findIndex(
    (chooseProcessor: any) =>
      chooseProcessor.teamMemberId === mainTeamUserInfo.teamMemberId
  );
  chooseProcessorChecked.value.splice(index, 1);

  // 从已选择的人员列表中移除指定成员
  const chooseIndex = selectChoosePersonList.value.findIndex(
    (chooseProcessor: any) =>
      chooseProcessor.teamMemberId === mainTeamUserInfo.teamMemberId
  );
  selectChoosePersonList.value.splice(chooseIndex, 1);
};

/** 合作团队人员减少 */
const handleReduceCoopTeamPeronnelItem = (
  coopTeamInfo: any,
  coopTeamMember?: any
) => {
  // 如果合作类型是分配团队ID，直接删除整个团队
  if (coopTeamInfo.coopType === "assignedTeamId") {
    delete coopTeamObject.value[
      coopTeamInfo.teamMemberData.teamId || coopTeamInfo.teamMemberData.id
    ];
  }
  // 如果合作类型是分配团队成员ID，需要处理单个成员的移除
  else if (coopTeamInfo.coopType === "assignedTeamMemberId") {
    // 找到要移除的团队成员在数组中的索引
    const index = coopTeamInfo.teamMemberData.findIndex(
      (teamMember: any) =>
        teamMember.teamMemberId === coopTeamMember.teamMemberId
    );
    // 从团队成员数据中移除指定成员
    coopTeamInfo.teamMemberData.splice(index, 1);

    // 如果移除后团队成员为空，删除整个团队
    if (coopTeamInfo.teamMemberData.length === 0) {
      delete coopTeamObject.value[coopTeamMember.teamId];
    }
    // 否则更新团队对象中的成员数据
    else {
      coopTeamObject.value[coopTeamMember.teamId].teamMemberData =
        coopTeamInfo.teamMemberData;
    }
    // 更新选择的人员列表
    ChooseTeamRef.value.handleSetChoosePersonnList(coopTeamInfo.teamMemberData);
  }
};

const initRenderData = (personnelList: any) => {
  //@ts-ignore
  const mainTeamId = chooseProcessorList.value[0].teamId;
  const mainTeamPersonnelList = personnelList.filter(
    personnel => personnel.teamId === mainTeamId
  );
  chooseProcessorChecked.value = mainTeamPersonnelList.map(
    mainTeamPeronnel => mainTeamPeronnel.teamMemberId
  );
  selectChoosePersonList.value = mainTeamPersonnelList.map(item => ({
    ...item,
    showHand: !!item.coopTeamUser,
    avatar: item.avatar || item.userAvatar
  }));

  const otherTeamPersonnelList = personnelList
    .filter(personnel => personnel.teamId !== mainTeamId)
    .map(personnel => ({
      ...personnel,
      showHand: !!personnel.coop || !!personnel.coopTeamUser
    }));

  let teamPeronnelObject = {};
  for (let otherTeamPersonnel of otherTeamPersonnelList) {
    const teamId = otherTeamPersonnel.teamId || otherTeamPersonnel.id;
    if (!teamId) continue;
    if (!otherTeamPersonnel.user) {
      teamPeronnelObject[teamId] = {
        teamName: otherTeamPersonnel.teamName,
        teamShortName:
          otherTeamPersonnel.shortName || otherTeamPersonnel.teamShortName,
        teamMemberData: Object.assign(otherTeamPersonnel, {
          teamShortName:
            otherTeamPersonnel.shortName || otherTeamPersonnel.teamShortName
        }),
        coopType: "assignedTeamId"
      };
    } else {
      if (teamPeronnelObject.hasOwnProperty(teamId)) {
        teamPeronnelObject[teamId].teamMemberData.push(otherTeamPersonnel);
        continue;
      }
      teamPeronnelObject[teamId] = {
        teamName: otherTeamPersonnel.teamName,
        teamShortName:
          otherTeamPersonnel.shortName || otherTeamPersonnel.teamShortName,
        teamMemberData: [otherTeamPersonnel],
        coopType: "assignedTeamMemberId"
      };
    }
  }

  coopTeamObject.value = teamPeronnelObject;
};

const open = async (
  personnelList: any,
  type?: string,
  customParams?: {
    sccsId: string;
  }
): Promise<void> => {
  coopTeamObject.value = {};
  tabActiveName.value = "teamMember";
  const sccsId = (route.query.sccsId as string) || customParams.sccsId;
  const { data } = await getWorkOrderProcessorList({
    sccsId: sccsId,
    type: type
  });

  chooseProcessorList.value = data.teamMemberList.map(teamMember => {
    return Object.assign(teamMember, {
      user: true,
      coop: teamMember.showHand,
      id: teamMember.teamMemberId
    });
  });
  const teamSignName: string = storageSession().getItem("coopTeamMark");
  const userInfo: any = storageLocal().getItem("user-info");

  chooseCoopTeamList.value =
    teamSignName === "coopTeam"
      ? data.coopTeamList.filter(
          coopTeam => coopTeam.id === userInfo.latestLoginTeamId
        )
      : data.coopTeamList;

  if (personnelList && personnelList.length > 0) {
    initRenderData(personnelList);
  }

  storageLocal().setItem("workOrderProcessorList", {
    sccsMemberList: data.teamMemberList,
    sccsCoopTeamList: data.coopTeamList
  });
  mainTeamName.value = data.sccsTeamInfo.teamShortName;
  ChooseProcessorDialogRef.value.open();
};

const handleSelectChoose = (chooseData: any, checked: boolean): void => {
  if (checked) {
    selectChoosePersonList.value.push(chooseData);
  } else {
    const index = selectChoosePersonList.value.findIndex(
      choose => choose.userId === chooseData.userId
    );
    if (index !== -1) {
      selectChoosePersonList.value.splice(index, 1);
    }
  }
};

const handleGetAssignedTeamData = (
  coopTeamRadioValue: string,
  teamData: any,
  chooseList: any
) => {
  if (!coopTeamRadioValue) {
    delete coopTeamObject.value[teamData.id];
  } else {
    if (coopTeamRadioValue === "assignedTeamId") {
      let chooseObject = {};
      chooseObject[teamData.id] = {
        teamName: teamData.teamName,
        teamShortName: teamData.teamShortName,
        teamMemberData: { ...teamData, teamId: teamData.id },
        coopType: "assignedTeamId"
      };
      Object.assign(coopTeamObject.value, chooseObject);
    } else if (coopTeamRadioValue === "assignedTeamMemberId") {
      if (chooseList.length > 0) {
        const { id: teamId, teamShortName, teamName } = teamData;
        let chooseObject = {};
        chooseObject[teamId] = {
          teamName: teamName,
          teamShortName: teamShortName,
          teamMemberData: chooseList,
          coopType: "assignedTeamMemberId"
        };
        Object.assign(coopTeamObject.value, chooseObject);
      } else {
        delete coopTeamObject.value[teamData.id];
      }
    }
  }
};

const handleClose = (): void => {
  chooseProcessorChecked.value = [];
  selectChoosePersonList.value = [];
};

const emit = defineEmits<{
  (e: "handleChooseConfirm", data: any, triggerType: string): void;
}>();

const handleChooseConfirm = (): void => {
  let assignList = [];
  for (let coopTeamItem of coopTeamList.value) {
    if (coopTeamItem.coopType === "assignedTeamId") {
      assignList.push({
        assignedTeamId:
          coopTeamItem.teamMemberData.teamId || coopTeamItem.teamMemberData.id
      });
    } else if (coopTeamItem.coopType === "assignedTeamMemberId") {
      for (let teamMember of coopTeamItem.teamMemberData) {
        assignList.push({
          assignedTeamMemberId: teamMember.teamMemberId
        });
      }
    }
  }
  for (let selectChoose of selectChoosePersonList.value) {
    assignList.push({
      assignedTeamMemberId: selectChoose.teamMemberId
    });
  }
  emit("handleChooseConfirm", assignList, props.orderChooseType);
  ChooseProcessorDialogRef.value.close();
  handleClose();
};

watch(
  () => selectedPerson.value,
  () => {
    nextTick(() => {
      ChooseProcessorDialogRef.value?.handleSetConfirmBtnDisabled(
        selectedPerson.value === 0
      );
    });
  },
  {
    immediate: true,
    deep: true
  }
);

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "../style/index.scss";

.choose-processor-dialog {
  .el-dialog__body {
    padding: 0;
    background: #fff;

    .choose-processor-body {
      display: flex;
      height: 50vh;

      .choose-processor-left {
        width: 342px;
        padding-right: 13px;
        border-right: 1px solid #eee;

        .choose-processor-input {
          margin-bottom: 15px;
        }

        .choose-processor-tabs {
          height: calc(100% - 47px);

          ::v-deep(.el-tabs__header) {
            margin-bottom: 0 !important;

            .el-tabs__nav-wrap {
              width: 100% !important;
              padding: 0 11px;

              .el-tabs__item {
                padding: 0 !important;
                margin-right: 20px !important;

                &:hover {
                  color: #2082ed !important;
                }
              }

              .el-tabs__item.is-active {
                font-weight: bold;
                color: #2082ed !important;
              }
            }
          }

          ::v-deep(.el-tabs__content) {
            height: 100% !important;
            overflow-y: auto;

            .el-tab-pane {
              height: 100% !important;
              padding: 14px 0;
            }

            .el-scrollbar {
              max-height: 100% !important;
            }

            .choose-checkbox {
              box-sizing: content-box;
              width: 310px;
              padding: 9px;
              margin: 0 !important;

              &:hover {
                background: #f2f2f2;
              }

              .choose-processor-item {
                display: flex;

                .choose-processor-item-text {
                  align-items: center;
                  margin-left: 7px;
                  font-size: 14px;
                  line-height: 14px;
                  color: #262626;
                }

                .choose-processor-msg-box {
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  margin-left: 7px;

                  .choose-processor-msg-text {
                    font-size: 12px;
                    color: #262626;

                    span {
                      display: inline-block;
                      width: 240px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    }

                    .choose-processor-msg-orange-text {
                      color: #fa8d0a;
                    }
                  }
                }
              }
            }
          }

          .choose-coop-team-main-box {
            margin-top: 12px;

            .choose-coop-team-main-header {
              display: flex;
              align-items: center;
              height: 32px;
              margin: 10px 0 8px;
              line-height: 32;
              cursor: pointer;

              .iconfont {
                font-size: 14px;
                vertical-align: middle;
              }

              .choose-coop-team-main-text {
                margin-left: 7px;
                font-size: 14px;
                color: #595959;
              }
            }

            .choose-coop-team-main-header-text {
              margin: 6px;
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }

        ::v-deep(.choose-checkbox) {
          box-sizing: content-box;
          width: 310px;
          padding: 9px;
          margin: 0 !important;

          &:hover {
            background: #f2f2f2;
          }

          .choose-processor-item {
            display: flex;

            .choose-processor-item-text {
              align-items: center;
              margin-left: 7px;
              font-size: 14px;
              line-height: 14px;
              color: #262626;
            }

            .choose-processor-msg-box {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              margin-left: 7px;

              .choose-processor-msg-text {
                font-size: 12px;
                color: #262626;

                span {
                  display: inline-block;
                  width: 240px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .choose-processor-msg-orange-text {
                  color: #fa8d0a;
                }
              }
            }
          }
        }
      }

      .choose-processor-right {
        flex: 1;
        margin-left: 15px;

        .choose-processor-right-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 20px;

          .choose-processor-right-header-left {
            flex: 1;
          }

          .choose-processor-right-header-right {
            font-size: 12px;
            font-weight: normal;
            color: #2082ed;
            text-align: center;
            cursor: pointer;
          }
        }

        .choose-processor-right-container {
          .choose-processor-right-col {
            margin-top: 24px;

            .choose-processor-right-col-title {
              font-size: 12px;
              font-weight: normal;
              line-height: 17px;
              color: #8c8c8c;
              text-align: left;
            }

            .choose-processor-right-tag-body {
              padding: 7px 0;

              .choose-processor-right-tag {
                display: inline-flex;
                align-items: center;
                padding: 3px 6px;
                margin-right: 4px;
                margin-bottom: 4px;
                background: #ebf5fb;
                border-radius: 3px;

                :deep(.el-avatar) {
                  position: relative;
                  top: -1px;
                  cursor: default;
                }

                .choose-processor-right-tag-text {
                  margin: 0 6px 0 2px;
                  font-size: 12px;
                  line-height: 14px;
                  color: #000;
                  text-align: left;
                  cursor: default;
                }

                .iconfont {
                  font-size: 12px;
                  color: #babec6;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 0;
  }
}
</style>
