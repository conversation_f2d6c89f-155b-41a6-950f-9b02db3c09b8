<template>
  <el-cascader
    v-bind="$attrs"
    :options="cascaderData"
    :props="cascaderProps"
    :placeholder="placeholder || t('common_pleaseSelect')"
  >
    <template #default="{ node, data }">
      <div class="cascader-node">
        <template v-if="node.level === 1">
          <!-- 团队节点 -->
          <el-avatar
            class="colorOrange"
            :src="data.teamAvatar"
            :size="18"
            fit="cover"
          >
            {{ data.teamName?.substring(0, 1) }}
          </el-avatar>
          <span class="node-label">{{ data.teamName }}</span>
        </template>
        <template v-else>
          <!-- 成员节点 -->
          <el-tooltip
            v-if="!data.activate"
            effect="dark"
            :content="t('trade_common_notSelectRegister')"
            placement="top"
            :show-after="500"
          >
            <template #default>
              <div class="member-node">
                <span v-if="!data.activate" class="tag-register-tip">
                  ({{ t("trade_common_unregistered") }})
                </span>
                <el-avatar :src="data.avatar" :size="18" fit="cover">
                  {{ data.username?.substring(0, 1) }}
                </el-avatar>
                <span class="node-label">{{ data.username }}</span>
              </div>
            </template>
          </el-tooltip>
          <div v-else class="member-node">
            <el-avatar :src="data.avatar" :size="18" fit="cover">
              {{ data.username?.substring(0, 1) }}
            </el-avatar>
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ data.username }}（{{ data.account }}）
            </ReText>
          </div>
        </template>
      </div>
    </template>
  </el-cascader>
</template>

<script lang="ts" setup>
//@ts-nocheck
import { computed, useAttrs } from "vue";
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";

const { t } = useI18n();
const attrs = useAttrs();

interface MemberInfo {
  teamMemberId: string;
  userId: string;
  username: string;
  avatar: string | null;
  account: string;
  teamId: string;
  teamName: string;
  teamCode: string | null;
  teamShortName: string;
  teamAvatar: string | null;
  showHand: boolean;
  activate: boolean;
  docking: boolean;
}

interface TeamNode {
  value: string;
  label: string;
  teamId: string;
  teamName: string;
  teamAvatar: string | null;
  children: MemberNode[];
}

interface MemberNode {
  value: string;
  label: string;
  teamMemberId: string;
  userId: string;
  username: string;
  avatar: string | null;
  account: string;
  showHand: boolean;
  activate: boolean;
  docking: boolean;
}

const props = defineProps({
  options: {
    type: Array as PropType<MemberInfo[]>,
    default: () => []
  },
  placeholder: {
    type: String,
    default: ""
  },
  teamRenderFields: {
    type: Object,
    default: () => ({
      label: "teamName",
      value: "teamId",
      avatar: "teamAvatar"
    })
  },
  memberRenderFields: {
    type: Object,
    default: () => ({
      label: "username",
      value: "teamMemberId",
      avatar: "avatar"
    })
  }
});

// 级联选择器配置
const cascaderProps = {
  multiple: attrs.multiple || false,
  checkStrictly: attrs.checkStrictly || false,
  value: "value",
  label: "label",
  children: "children",
  leaf: "leaf",
  emitPath: false
};

// 转换数据格式
const cascaderData = computed(() => {
  const teamMap = new Map();

  props.options.forEach(member => {
    const { teamId, teamName, teamAvatar, activate, ...memberInfo } = member;

    if (!teamMap.has(teamId)) {
      teamMap.set(teamId, {
        value: teamId,
        label: teamName,
        teamId,
        teamName,
        teamAvatar,
        children: []
      });
    }

    teamMap.get(teamId).children.push({
      value: memberInfo[props.memberRenderFields.value],
      label: memberInfo[props.memberRenderFields.label],
      leaf: true,
      disabled: !activate,
      activate,
      ...memberInfo
    });
  });

  return Array.from(teamMap.values());
});
</script>

<style lang="scss" scoped>
.cascader-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.member-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.node-label {
  flex: 1;
  margin-left: 8px;
}

.tag-register-tip {
  margin-right: 5px;
  font-size: 12px;
  color: #fa8d0a;
}

::v-deep(.el-avatar) {
  display: inline-block;
  min-width: 18px;
  line-height: 18px !important;
}

.colorOrange {
  background-color: #fa8d0a;
}
</style>

<style lang="scss">
.cascader-popper-class {
  .el-cascader-node.is-disabled {
    background: rgb(242 242 242 / 72%);
  }
}
</style>
