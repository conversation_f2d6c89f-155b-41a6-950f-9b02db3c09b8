import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
import { useI18n } from "@/plugins/i18n";
import { getPlatformConfig } from "./config";
import { createApp, type Directive } from "vue";
import { useElementPlus } from "@/plugins/elementPlus";
import { injectResponsiveStorage } from "@/utils/responsive";
import { websocket } from "@/utils/websocket";
import { dict } from "@/utils/dict";
import { getSystemLang, getSystemDictData } from "@/api/common";
import { getTimeLineStampFormat } from "@/utils/formateDate";
import VueOfficeExcel from "@vue-office/excel";
import VueOfficeDocx from "@vue-office/docx";
import VueOfficePdf from "@vue-office/pdf";
import VueOfficePptx from "@vue-office/pptx";
import MessageBox from "@/components/lkElMessageBox/index";
import FilePreview from "@/components/lkFilePreview/index";
import DialogPreview from "@/components/lkDialogPreview/index";

// 引入重置样式
import "./style/reset.scss";
// 导入公共样式
import "./style/index.scss";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css";
import "element-plus/dist/index.css";

// 导入字体图标
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";
import VxeUITable from "vxe-table";
import { VxeTooltip } from "vxe-pc-ui";
import "vxe-table/lib/style.css";
import { ListTable } from "@visactor/vue-vtable";
import DrCard from "@/components/FormDesigner/DrCard/index";
import DrInput from "@/components/FormDesigner/DrInput/index";
import DrDatePicker from "@/components/FormDesigner/DrDatePicker/index";
import DrTextarea from "@/components/FormDesigner/DrTextarea/index";
import DrCheckbox from "@/components/FormDesigner/DrCheckbox/index";
import DrRadio from "@/components/FormDesigner/DrRadio/index";
import DrLocation from "@/components/FormDesigner/DrLocation/index";
import DrAddress from "@/components/FormDesigner/DrAddress/index";
import DrRate from "@/components/FormDesigner/DrRate/index";
import DrInputNumber from "@/components/FormDesigner/DrInputNumber/index";
import DrPercentage from "@/components/FormDesigner/DrPercentage/index";
import DrEditor from "@/components/FormDesigner/DrEditor/index";
import DrImagesUpload from "@/components/FormDesigner/DrImagesUpload/index";
import DrDivider from "@/components/FormDesigner/DrDivider/index";
import DrExchangeRates from "@/components/FormDesigner/DrExchangeRates/index";
import DrSignature from "@/components/FormDesigner/DrSignature/index";
import DrTableForm from "@/components/FormDesigner/DrTableForm/index";
import DrSCCSMemberSelect from "@/components/FormDesigner/DrSCCSMemberSelect/index";
import DrSCCSGroupMemberSelect from "@/components/FormDesigner/DrSCCSGroupMemberSelect/index";
import DrPlaceholder from "@/components/FormDesigner/DrPlaceholder/index";
import DrFilesUpload from "@/components/FormDesigner/DrFilesUpload/index";
import DrDialog from "@/components/FormDesigner/DrDialog/index";
import DrFormulas from "@/components/FormDesigner/DrFormulas/index";
import DrRelateCard from "@/components/FormDesigner/DrRelateCard/index";
// import { nodeTestParams } from "./utils/test.js";

const app = createApp(App);

// 自定义指令
import * as directives from "@/directives";
Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

// 全局注册@iconify/vue图标库
import {
  IconifyIconOffline,
  IconifyIconOnline,
  FontIcon
} from "./components/ReIcon";
app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("FontIcon", FontIcon);
app.component("DrCard", DrCard);
app.component("DrInput", DrInput);
app.component("DrDatePicker", DrDatePicker);
app.component("DrTextarea", DrTextarea);
app.component("DrCheckbox", DrCheckbox);
app.component("DrRadio", DrRadio);
app.component("DrLocation", DrLocation);
app.component("DrAddress", DrAddress);
app.component("DrRate", DrRate);
app.component("DrInputNumber", DrInputNumber);
app.component("DrPercentage", DrPercentage);
app.component("DrEditor", DrEditor);
app.component("DrImagesUpload", DrImagesUpload);
app.component("DrDivider", DrDivider);
app.component("DrExchangeRates", DrExchangeRates);
app.component("DrSignature", DrSignature);
app.component("DrTableForm", DrTableForm);
app.component("DrSCCSMemberSelect", DrSCCSMemberSelect);
app.component("DrSCCSGroupMemberSelect", DrSCCSGroupMemberSelect);
app.component("DrPlaceholder", DrPlaceholder);
app.component("DrFilesUpload", DrFilesUpload);
app.component("DrDialog", DrDialog);
app.component("DrFormulas", DrFormulas);
app.component("DrRelateCard", DrRelateCard);
app.component("ListTable", ListTable);
app.component("VueOfficeExcel", VueOfficeExcel);
app.component("VueOfficeDocx", VueOfficeDocx);
app.component("VueOfficePdf", VueOfficePdf);
app.component("VueOfficePptx", VueOfficePptx);

// 全局注册按钮级别权限组件
import { Auth } from "@/components/ReAuth";
import { Perms } from "@/components/RePerms";
app.component("Auth", Auth);
app.component("Perms", Perms);

// 全局注册vue-tippy
import "tippy.js/dist/tippy.css";
import "tippy.js/themes/light.css";
import VueTippy from "vue-tippy";

app.use(VueTippy);

// 屏蔽黄色警告信息
app.config.warnHandler = () => null;

// 移除title属性，防止vite热更新时title属性被重置
document.querySelectorAll("[title]").forEach(el => {
  el.removeAttribute("title");
});

// 禁用左滑事件
document.addEventListener("touchstart", handleTouchStart, false);
document.addEventListener("touchmove", handleTouchMove, false);

// node服务调用
// nodeTestParams();

let xDown = null;
let yDown = null;

function handleTouchStart(evt) {
  const firstTouch = evt.touches[0];
  xDown = firstTouch.clientX;
  yDown = firstTouch.clientY;
}

function handleTouchMove(evt) {
  if (!xDown || !yDown) {
    return;
  }

  const xUp = evt.touches[0].clientX;
  const yUp = evt.touches[0].clientY;

  const xDiff = xDown - xUp;
  const yDiff = yDown - yUp;

  if (Math.abs(xDiff) > Math.abs(yDiff)) {
    // 左滑或右滑
    if (xDiff > 0) {
      // 左滑
      evt.preventDefault();
    }
  }

  // 重置坐标
  xDown = null;
  yDown = null;
}

Promise.all([
  getPlatformConfig(app),
  getSystemLang(),
  getSystemDictData()
]).then(async res => {
  const config = res[0];
  setupStore(app);
  app.use(router);
  await router.isReady();
  injectResponsiveStorage(app, config);
  //@ts-ignore
  app.config.silent = true;

  await useI18n(app, res[1].data);

  app
    .use(VxeUITable)
    .use(VxeTooltip)
    .use(MessageBox)
    .use(FilePreview)
    .use(DialogPreview)
    .use(useElementPlus);
  app.config.globalProperties.websocket = websocket.connectWebsocket();
  app.config.globalProperties.$formatDate = getTimeLineStampFormat;
  dict.setDictData(res[2].data);
  app.mount("#app");
});
