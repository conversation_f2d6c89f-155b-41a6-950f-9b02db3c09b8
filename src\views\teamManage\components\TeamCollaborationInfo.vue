<template>
  <div
    class="team-member-info-container team-module-area-container"
    :class="[
      !tablePermissionFlag ? 'team-module-area-permission-empty-container' : ''
    ]"
  >
    <el-tabs v-show="tablePermissionFlag" v-model="activeName" editable>
      <template #add-icon>
        <el-button
          v-if="activeName === 'first'"
          type="primary"
          color="#0070D2"
          @click="handleTeamInvite"
        >
          <FontIcon icon="link-add" />
          {{ t("trade_common_invitations") }}
        </el-button>
        <span v-else />
      </template>
      <el-tab-pane :label="t('trade_team_invitedThem')" name="first" lazy>
        <TeamIsMeinvited ref="TeamIsMeinvitedRef" />
      </el-tab-pane>
      <el-tab-pane :label="t('trade_team_invitedMe')" name="second" lazy>
        <TeamInvitedInMe />
      </el-tab-pane>
    </el-tabs>
    <LkPermissionEmpty v-show="!tablePermissionFlag" />
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import TeamIsMeinvited from "./TeamIsMeinvited.vue";
import TeamInvitedInMe from "./TeamInvitedInMe.vue";
import LkPermissionEmpty from "@/components/lkPermissionEmpty/index";
import { useRoute, useRouter } from "vue-router";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const activeName = ref("first");
const TeamIsMeinvitedRef = ref<any>(null);

const tablePermissionFlag = computed(() => {
  return TeamIsMeinvitedRef.value
    ? TeamIsMeinvitedRef.value.tablePermission
    : true;
});

const handleTeamInvite = (): void => {
  TeamIsMeinvitedRef.value.handleCreateTeamRole();
};

onMounted(() => {
  TeamIsMeinvitedRef.value.handleSearchTable();
});

onMounted(() => {
  const { locateTo, ...restQuery } = route.query;
  if (locateTo && locateTo === "coopTeamManage") {
    activeName.value = "second";
  }
  router.replace({ path: route.path, query: restQuery });
});

onUnmounted(() => {});
</script>

<style lang="scss" scoped>
.team-module-area-container {
  height: 100%;

  &.team-module-area-permission-empty-container {
    align-content: center;
  }

  ::v-deep(.el-tabs) {
    padding: 0 25px !important;

    .team-module-area-btn {
      position: absolute;
      top: 0;
      right: 0;
    }

    .el-tabs__header {
      width: auto !important;

      .el-tabs__item {
        width: auto !important;
        padding: 0 !important;
        margin-right: 22px !important;
        margin-left: 10px !important;

        &.is-active {
          font-weight: bolder;
          color: #0070d2 !important;
          background: transparent !important;
        }

        .el-icon {
          display: none;
        }
      }

      .el-tabs__active-bar {
        height: 3px !important;
        color: #0070d2 !important;
      }

      .el-tabs__new-tab {
        position: absolute;
        top: 50%;
        right: 30px;
        z-index: 1000;
        border: 0 none;
        transform: translateY(-81%);
      }
    }
  }
}
</style>
