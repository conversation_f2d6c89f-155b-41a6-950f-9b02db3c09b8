<template>
  <LkDialog
    ref="LkDialogRef"
    :show-close="false"
    width="80%"
    style="padding: 0"
    class="Write-Email-Coop-Dialog lk-maximum-dialog"
    destroy-on-close
    @close="handleClose"
    @confirm="handleSubmitEmailWidgetData"
  >
    <template #header="{ close }">
      <div class="work-order-flex-header">
        <div class="work-order-tabs-flex-left">
          <div class="work-order-tabs-flex-left-title">
            {{ mainForm.subject }}
          </div>
        </div>
        <div class="work-order-tabs-flex-right">
          <div class="work-order-header-col">
            <div
              class="work-order-header-small-button work-order-header-default-button"
            >
              <i class="iconfont link-close" @click="close" />
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <div class="write-email-coop-body">
        <div class="write-email-coop-container">
          <div class="write-email-coop-detail-body">
            <el-scrollbar>
              <div class="write-email-coop-detail-title">
                {{ t("trade_common_orderMessage") }}
              </div>
              <div class="write-email-coop-detail-form">
                <div class="write-email-coop-detail-system-message">
                  <div class="write-email-coop-detail-system-message-title">
                    {{ t("trade_common_remark") }}
                  </div>
                  <div v-if="mainRemark && mainRemark !== '<p><br></p>'">
                    <Editor
                      v-model="mainRemark"
                      :defaultConfig="{ readOnly: true }"
                    />
                  </div>
                  <div v-else>-</div>
                  <div
                    class="write-email-coop-detail-system-message-title mt30"
                  >
                    {{ t("trade_email_files") }}
                  </div>
                  <div v-if="mainWidgetFiles.length > 0">
                    <OrderFilePreview
                      v-for="item in mainWidgetFiles"
                      :key="item"
                      :filePreview="item"
                    />
                  </div>
                  <div v-else>-</div>
                </div>
                <OrderDetailDescWidget
                  v-if="mainFormData?.widgetJsonList"
                  :widgetForm="mainFormData.widgetJsonList"
                  :widgetData="mainWidgetData"
                />
              </div>
              <div class="write-email-coop-detail-title mt26">
                {{ t("trade_work_order_information") }}
              </div>
              <div class="write-email-coop-detail-form">
                <LkTrendsAggregationForm
                  ref="TrendsAggregationFormRef"
                  :sccsId="sccsIdRef"
                  :formList="milestoneFormData"
                  :formWidgetData="milestoneWidgetData"
                  :trendsFormFlag="true"
                  :hiddenFormIdList="hiddenFormIdList"
                  :operationalFactorData="operationalFactorData"
                  :defaultValueActuatorFlag="false"
                  :workOrderAllowOperation="workOrderAllowOperation"
                />
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import OrderFilePreview from "@/views/orderManage/components/OrderFilePreview.vue";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import LkDialog from "@/components/lkDialog";

import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";
import { Editor } from "@wangeditor/editor-for-vue";
import {
  getEmailCoopReceiverStructure,
  getEmailCoopReceiverDetail,
  submitWorkOrderCoopEmail
} from "@/api/email";
import { ElMessage } from "element-plus";

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const TrendsAggregationFormRef = ref<any | HTMLElement>(null);
const receiverTemplateData = ref<any>([]);
const mainForm = ref<any>({});
const mainFormData = ref<any>({});
const mainWidgetData = ref<any>([]);
const milestoneFormData = ref<any>({});
const mainRemark = ref<string>("");
const mainWidgetFiles = ref<any[]>([]);

const milestoneWidgetData = ref<any>([]);
const currentEmailCoopId = ref<string>("");

const operationalFactorData = ref<any>({});
const sccsIdRef = ref<string>("");
const hiddenFormIdList = ref<string[]>([]);

const workOrderAllowOperation = computed(() => {
  return milestoneFormData.value.filter(ms => ms.editable).map(ms => ms.id);
});

const emit = defineEmits(["refresh"]);

const handleSubmitEmailWidgetData = async () => {
  const widgetItemDataList =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(true);
  const linkedReferenceSourceList =
    TrendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  // widgetItemDataList 为false 说明表单校验不通过
  if (widgetItemDataList === false) {
    return;
  }

  const emailCoopId = currentEmailCoopId.value as string;
  const { code } = await submitWorkOrderCoopEmail({
    id: emailCoopId,
    widgetItemDataList: widgetItemDataList,
    linkedReferenceSaveList: linkedReferenceSourceList
  });
  if (code === 0) {
    LkDialogRef.value.close();
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    emit("refresh");
  }
};

const handleClose = () => {
  LkDialogRef.value.close();
};

const open = (id: string) => {
  currentEmailCoopId.value = id;
  Promise.all([
    getEmailCoopReceiverStructure({ id: id }),
    getEmailCoopReceiverDetail({ id: id })
  ]).then(data => {
    receiverTemplateData.value = data[0].data;

    // 处理表单数据
    mainFormData.value = data[0].data.find(
      mainForm => mainForm.formType === "MAIN_FORM"
    );
    milestoneFormData.value = data[0].data.filter(
      mainForm => mainForm.formType !== "MAIN_FORM"
    );

    // 获取详细数据
    const {
      sccsId,
      widgetDataForOrderList,
      widgetDataSubForOrderMap,
      widgetDataList,
      widgetDataSubMap,
      linkedReferenceMap,
      formHiddenList,
      remark,
      fileList
    } = data[1].data;

    // 统一处理数据转换
    const transformData = (widgetData, widgetSubMap) =>
      TransformSubmitDataStructure(
        widgetData,
        widgetSubMap,
        linkedReferenceMap
      );

    mainWidgetData.value = transformData(
      widgetDataForOrderList,
      widgetDataSubForOrderMap
    );
    milestoneWidgetData.value = transformData(widgetDataList, widgetDataSubMap);

    mainRemark.value = remark;
    mainWidgetFiles.value = fileList;
    mainForm.value = data[1].data;
    sccsIdRef.value = sccsId;
    hiddenFormIdList.value = formHiddenList;

    operationalFactorData.value = getEntireFormData(
      mainForm.value.msId,
      mainForm.value.workOrderId
    );
    LkDialogRef.value.open();
  });
};

defineExpose({
  open
});
</script>
<style lang="scss">
.Write-Email-Coop-Dialog {
  .el-dialog__header {
    padding: 0 !important;
    background: #fff !important;
    border-bottom: 1px solid #e8e8e8;

    .dialog-header {
      display: flex;
      align-items: center;
      height: 60px;
      padding-top: 6px;

      .dialog-header-left {
        flex: 4;
        height: 100%;
        padding-left: 4px;

        .dialog-header-tabs {
          display: flex;
          align-items: center;
          height: 100%;
          border-radius: 4px 0 0;

          .dialog-header-tabs-item {
            align-items: center;
            height: 100%;
            padding: 0 16px;
            line-height: 52px;
            border: 1px solid #e5e7ee;
            border-right: 0 none;

            &.active {
              border-bottom: 0 none;

              .dialog-header-tabs-item-text {
                color: #0070d2;
              }
            }

            &:first-child {
              border-radius: 4px 0 0;
            }

            &:last-child {
              border-right: 1px solid #e5e7ee;
              border-radius: 0 4px 0 0;
            }

            .dialog-header-tabs-item-text {
              margin-right: 7px;
              font-size: 14px;
              color: #262626;
            }

            .dialog-header-tabs-item-icon {
              display: inline-block;
              width: 22px;
              height: 22px;
              margin-right: 4px;
              line-height: 22px;
              text-align: center;
              cursor: pointer;
              border-radius: 2px;

              &:hover {
                background: #e5e5e5;
              }

              .iconfont {
                font-size: 12px;
                color: #808080;
              }
            }
          }
        }
      }

      .dialog-header-right {
        flex: 1;
        text-align: right;

        .dialog-header-box {
          height: 30px;
          padding: 7px 10px;
          font-size: 14px !important;
          color: #595959 !important;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: #e5e5e5;
          }

          .iconfont {
            margin-right: 3px;
            font-size: 12px;
          }
        }

        .dialog-header-info-box {
          margin: 0 20px;

          .dialog-header-icon-box {
            display: inline-block;
            width: 32px;
            height: 30px;
            padding: 4px 0;
            text-align: center;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
              background: #e5e5e5;
            }

            .iconTip {
              font-size: 16px;
              color: #808080 !important;
            }
          }

          &.active {
            background: #e1edff;
          }
        }

        .dialog-header-close-icon {
          position: relative;
          padding: 0 20px;

          .dialog-header-icon-tip {
            // padding: 0 20px;
            box-sizing: border-box;
            display: inline-block;
            height: 30px !important;
            border-radius: 4px;

            .iconfont {
              font-size: 13px;
              color: #808080;
              cursor: pointer;
            }
            // &:hover {
            //   background: #e5e5e5;
            // }
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              display: inline-block;
              width: 1px;
              height: 16px;
              content: "";
              background: #e5e5e5;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
    }
  }

  .el-dialog__body {
    height: calc(100% - 100px);
    background: #e9eaec;

    .dialog-content-flex {
      display: flex;
      height: 100%;
      padding: 10px;
      background: #f6f6f6;

      .dialog-content-flex-left {
        width: 358px;
        padding: 30px 14px;
        margin-right: 13px;
        background: #fff;
        border-radius: 4px 4px 0 0;

        .dialog-content-form {
          .dialog-content-form-row {
            margin-bottom: 25px;

            .dialog-content-title {
              display: flex;
              align-items: center;

              .dialog-content-text {
                margin-bottom: 9px;
                font-size: 14px;
                font-weight: bolder;
                line-height: 16px;
                color: #262626;
                text-align: left;

                &::before {
                  display: inline-block;
                  margin-top: 6px;
                  margin-right: 6px;
                  font-size: 22px;
                  font-weight: bold;
                  line-height: 16px;
                  color: #e61b1b;
                  vertical-align: middle;
                  content: "*";
                }
              }

              .dialog-content-coordination {
                flex: 1;
                margin-right: 3px;
                margin-bottom: 9px;
                font-size: 12px;
                color: #f6974f;
                text-align: right;

                .iconfont {
                  font-size: 13px;
                  color: #808080;
                  cursor: pointer;
                }
              }
            }

            .work-order-personnel-control-operation {
              display: block;
              align-items: center;
              width: 100%;

              .work-order-personnel-control-btn {
                cursor: pointer;

                .iconfont {
                  color: #0070d2;
                }

                .work-order-personnel-control-text {
                  margin-left: 5px;
                  font-size: 13px;
                  font-weight: bold;
                  color: #0070d2;
                }
              }

              .work-order-personnel-control-group {
                .work-order-personnel-control-group-col {
                  display: inline-flex;
                  align-items: center;
                  max-width: 270px;
                  padding: 3px 4px;
                  margin-right: 3px;
                  margin-bottom: 7px;
                  background: #ebf5fb;
                  border-radius: 3px;

                  .work-order-personnel-control-group-name {
                    max-width: 230px;
                    margin: 0 4px;
                    overflow: hidden;
                    font-size: 12px;
                    font-weight: bold;
                    color: #000;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }

                  .iconfont {
                    font-size: 12px;
                    color: #babec6;
                  }
                }

                .iconfont {
                  margin-left: 7px;
                  font-size: 14px;
                  color: #0070d2;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }

      .dialog-content-flex-right {
        flex: 1;

        .dialog-content-flex-right-header {
          margin: 7px 0 12px;
          font-size: 12px;
          font-weight: bolder;
          line-height: 17px;
          color: #262626;
          text-align: left;
        }
      }
    }
  }

  .el-dialog__footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 45px;
    padding: 0 !important;
    padding-right: 10px !important;
    background: #fff;
  }

  .work-order-flex-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 54px;
    padding-left: 25px;

    .work-order-tabs-flex-left {
      display: flex;
      flex: 1;
      align-items: center;
      max-width: 70%;

      .work-order-tabs-flex-left-title {
        max-width: calc(100% - 35px);
        overflow: hidden;
        font-size: 18px;
        font-weight: bolder;
        color: #202020;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .work-order-tabs-flex-tag {
        padding: 2px 4px;
        margin-left: 5px;
        font-size: 12px;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        background: #fa8d0a;
        border: 1px solid #fff;
        border-radius: 3px;
      }
    }

    .work-order-tabs-flex-right {
      display: flex;
      align-items: center;

      .work-order-header-col {
        position: relative;
        display: flex;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        &:last-child {
          &::after {
            display: none;
            content: "";
          }
        }

        &.work-order-header-col-next {
          margin: 0 9px;
        }

        .work-order-header-button {
          padding: 3px 10px;
          margin-right: 12px;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: #e5e5e5;
          }

          .iconfont {
            font-size: 13px;
            color: #595959 !important;

            &:first-child {
              margin-right: 6px;
            }

            &:last-child {
              margin-left: 6px;
            }
          }

          .work-order-header-text {
            font-size: 14px;
            line-height: 20px;
            color: #595959 !important;
            text-align: left;
          }
        }

        .work-order-header-small-button {
          display: inline-block;
          width: 28px;
          height: 30px;
          margin-right: 8px;
          line-height: 30px;
          text-align: center;
          border-radius: 4px;

          &.work-order-header-default-button {
            width: 42px;

            &:hover {
              background: transparent;
            }

            .iconfont {
              font-size: 14px;
            }
          }

          &:hover {
            background: #e5e5e5;
          }

          &.active {
            background: #e1edff;

            .iconfont {
              color: #0070d2;
            }
          }

          .iconfont {
            font-size: 16px;
            color: #808080;
            cursor: pointer;

            &.font18 {
              font-size: 18px;
            }
          }
        }
      }
    }
  }

  .work-order-container {
    position: relative;
    display: flex;
    height: 100%;
    overflow: hidden;

    .work-order-container-left {
      position: relative;
      width: 59%;
      background: #f5f6fa;
      transition: width linear 0.2s;

      .anchor-icon {
        top: 48px;
      }

      .anchor-list-container {
        top: 48px;
      }

      .work-order-container-top {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: calc(100% - 60px);
        padding: 0 10px;

        .work-order-container-top-header {
          display: flex;
          align-items: center;
          width: 100%;
          padding: 12px 27px;
          margin-top: 9px;

          .work-order-header-col {
            display: inline-flex;
            align-items: center;
            margin-right: 35px;

            .work-order-header-col-span {
              padding-left: 14px;
              font-size: 14px;
              line-height: 20px;
              color: #797979;
              text-align: left;

              &::after {
                content: "：";
              }
            }

            .work-order-header-col-main {
              display: flex;
              align-items: center;

              .work-order-header-create-time {
                margin-left: 8px;
                font-size: 14px;
                color: #262626;
              }

              .work-order-header-create-status-text {
                margin-left: 8px;
                font-size: 12px;
                font-weight: bold;
                color: #e62412;
              }
            }
          }
        }

        .work-order-container-top-main {
          flex: 1;
          height: calc(100% - 145px);
        }
      }

      .work-order-container-bottom {
        height: 60px;
        padding: 14px 18px;
        text-align: right;
        background: #fff;
      }
    }

    .work-order-container-right {
      position: absolute;
      width: 41%;
      height: 100%;
      background: #fff;
      border-left: 1px solid #e8e8e8;
      transition: right linear 0.2s;

      .work-order-container-dynamics-card {
        height: 100%;
      }
    }
  }

  .work-order-tabs-container {
    position: absolute;
    top: 54px;
    left: 0;
    z-index: 3333;
    box-sizing: border-box;
    width: 100%;
    height: 480px;
    padding: 33px 43px;
    background: #fff;
    border-top: 1px solid #e5e5e5;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 8px 13px 0 rgb(0 0 0 / 10%);

    &.nopadding {
      padding: 16px 26px !important;
    }
  }
}

.write-email-coop-body {
  width: 100%;
  height: 100%;

  .write-email-coop-container {
    height: 100%;

    .write-email-coop-header {
      display: flex;
      align-items: center;
      padding: 15px 0 57px;

      .write-email-coop-left {
        flex: 1;

        .write-email-coop-title {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: bolder;
          line-height: 16px;
          color: #262626;
          text-align: left;
        }

        .write-email-coop-tip {
          font-size: 14px;
          line-height: 16px;
          color: #262626;
          text-align: left;

          .write-email-coop-email-tip {
            color: #797979;
          }
        }
      }

      .write-email-coop-right {
        width: 179px;

        .sign-image {
          vertical-align: middle;
        }
      }
    }

    .write-email-coop-detail-body {
      height: 100%;
      padding: 10px;
      padding-right: 0;

      .el-scrollbar {
        padding-right: 16px;
      }

      .write-email-coop-detail-title {
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: bolder;
        line-height: 16px;
        color: #262626;

        &.mt26 {
          margin-top: 26px;
        }
      }

      .write-email-coop-detail-form {
        padding: 18px;
        background: #fff;
        border: 1px solid #e6eaf0;
        border-radius: 6px;

        .order-form-edit-collapse {
          .el-collapse-item.HIDDEN {
            display: none;
          }

          .order-state {
            display: inline-block;
            height: 20px;
            padding: 0 5px;
            margin-left: 5px;
            font-size: 12px;
            font-weight: normal;
            line-height: 20px;
            color: #808080;
            text-align: center;
            background: #e2e2e2;
            border-radius: 12px;
          }

          .order-state-editable {
            color: #2082ed;
            background: #d8eff9;
          }
        }
      }
    }

    .write-email-coop-detail-bottom {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 60px;
      padding: 0 24px;
      background: #fff;
      border-top: 1px solid #e6eaf0;
      border-radius: 0 0 6px 6px;
    }
  }
}

.write-email-coop-detail-system-message {
  padding: 26px 22px;
  margin-bottom: 16px;
  background: linear-gradient(#f0f1f6, #f6f8fa);
  border-radius: 6px;

  .write-email-coop-detail-system-message-title {
    margin-bottom: 6px;
    font-size: 14px;
    line-height: 16px;
    color: #707177;
    text-align: left;

    &.mt30 {
      margin-top: 30px;
    }
  }

  ::v-deep(.w-e-text-container) {
    background: transparent !important;
    border: 1px solid #e3e3e3;
  }

  ::v-deep(.order-file-preview-body) {
    .svg-icon {
      width: 28px;
      height: 35px;
      margin-right: 10px;
    }
  }
}
</style>
