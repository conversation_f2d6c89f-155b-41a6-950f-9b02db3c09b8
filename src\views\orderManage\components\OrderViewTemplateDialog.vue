<template>
  <el-drawer
    v-model="dialogVisible"
    class="lk-maximum-drawer"
    size="83%"
    :title="dialogTitle"
    :before-close="handleBeforeCloseInquiry"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div class="order-form-edit-container">
      <LkTrendsAggregationForm
        v-if="orderFormData.length > 0"
        :key="uniqueId"
        ref="trendsAggregationFormRef"
        :formList="orderFormData"
        :formWidgetData="orderWidgetData"
        :operationalFactorData="operationalFactorData"
        :trendsFormFlag="true"
        :defaultValueActuatorFlag="dialogState === 'add'"
        :sccsId="routeParams.sccsId"
        :orderId="orderId"
        :workOrderAllowOperation="[orderFormData[0].id]"
        @handleWidgetChange="handleWidgetChange"
      >
        <template #otherCollapse>
          <el-collapse-item name="msCollapsePlan">
            <template #title>
              <div class="collapse-form-item-title">
                {{ t("trade_common_milestonePlan") }}
              </div>
            </template>
            <el-timeline
              v-if="msPlannedList.length > 0"
              class="milestone-timeline-body"
            >
              <el-form
                label-position="top"
                label-width="auto"
                :model="milestoneForm"
              >
                <el-timeline-item
                  v-for="item in msPlannedList"
                  :key="item.msId"
                  placement="top"
                >
                  <div class="milestone-timeline-header">
                    <span class="milestone-timeline-title">
                      <ReText type="info" :tippyProps="{ delay: 50000 }">
                        {{ item.name }}
                      </ReText>
                    </span>
                    <span
                      v-if="
                        dialogState === 'edit' &&
                        planTimeChangeIdList.includes(item.msId)
                      "
                      class="milestone-timeline-tag"
                    >
                      {{ t("trade_common_hasUpdate") }}
                    </span>
                  </div>
                  <div class="milestone-timeline-relation" />
                  <div class="milestone-timeline-container">
                    <div class="milestone-timeline-container-header">
                      <span class="milestone-timeline-container-tip">
                        {{ t("trade_common_in") }}
                      </span>
                      <ReText type="info" :tippyProps="{ delay: 50000 }">
                        {{ item.formNames }}
                      </ReText>
                    </div>
                    <div class="milestone-timeline-container-body">
                      <orderMilestoneTimeLineForm
                        :msId="item.msId"
                        :managerList="managerList"
                        :milestonePlanData="item"
                        :sccsPlanData="
                          sccsPlanData.find(
                            sccsPlan => sccsPlan.milestoneId === item.msId
                          )
                        "
                        :formRefData="dynamicFormData"
                        :milestoneListData="msPlannedList"
                        @handleMilestoneTimeLine="handleMilestoneTimeLine"
                        @handlePlanChangeBySystem="handlePlanChangeBySystem"
                      />
                    </div>
                  </div>
                </el-timeline-item>
              </el-form>
            </el-timeline>
            <el-empty
              v-else
              style="background: #fff"
              :description="t('trade_common_emptyTip')"
              :image-size="314"
              :image="msNoDataImage"
            />
          </el-collapse-item>
        </template>
      </LkTrendsAggregationForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="handleReset">
          {{ t("trade_common_reset") }}
        </el-button> -->
        <el-button :disabled="submitLoading" @click="handleClose">
          {{ t("trade_common_cancel") }}
        </el-button>
        <el-button
          type="primary"
          color="#0070D2"
          :disabled="btnOperationState"
          :loading="submitLoading"
          @click="handleConfirm"
        >
          {{ t("trade_common_sure") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
  <OrderViewTemplateDetail
    ref="OrderViewTemplateDetailRef"
    @handleAgainCreateOrder="handleAgainCreateOrder"
    @handleCloseOrderDialog="handleCloseOrderDialog"
  />
</template>
<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  ref,
  unref
} from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import orderMilestoneTimeLineForm from "./orderMilestoneTimeLineForm.vue";
import OrderViewTemplateDetail from "./OrderViewTemplateDetail.vue";
import msNoDataImage from "@/assets/images/sccs/sccs-no-image.png";
import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";
import { getDefaultConfigWidgetData } from "@/utils/formulasActuator/formulasActuator";
import { ReText } from "@/components/ReText";
import { emitter } from "@/utils/mitt";
import { ElMessage, useDialog } from "element-plus";
import { differenceWith, isEqual } from "lodash-es";
import {
  createTradeOrder,
  getOrderTemplateDetails,
  getWorkOrderProcessorList,
  updateTradeOrder
} from "@/api/order";
import { getFindWidgetList, getSccsMilestonePlanList } from "@/api/sccs";
import { cloneDeep, debounce, uuid } from "@pureadmin/utils";

interface milestoneData {
  msId?: string;
  plannedStartDate?: string | number;
  plannedEndDate?: string | number;
  plannedDay?: number;
  managerMemberId?: string;
}

const { t } = useI18n();
const route = useRoute();
// const dialog = useDialog();
const { proxy } = getCurrentInstance();
const OrderViewTemplateDetailRef = ref<HTMLElement | any>(null);
const trendsAggregationFormRef = ref<HTMLElement | any>(null);
const btnOperationState = ref<boolean>(false);
const dialogVisible = ref<boolean>(false);
const msPlannedList = ref<any>([]);
const managerList = ref<any[]>([]);
const sccsPlanData = ref<any[]>([]);
const milestoneForm = ref<milestoneData[]>([]);
const planTimeChangeIdList = ref<string[]>([]);
const orderWidgetData = ref<any>([]);
const orderWidgetOriginalData = ref<any>([]);
const snapshotMilestoneData = ref<any>([]);
const orderId = ref<string>("");
let dialogState = ref<string>("add");
let orderFormData = unref<any[]>([]);
const dynamicFormData = ref<any[]>([]);
let dataFenceHiddenWidgetId = unref<any[]>([]);
const submitLoading = ref<boolean>(false);

const uniqueId = ref<string>(uuid(8));

const routeParams = computed(() => {
  return route.query;
});

const dialogTitle = computed(() => {
  return dialogState.value === "add"
    ? t("trade_order_createOrder")
    : t("trade_order_editOrder");
});

const operationalFactorData = computed(() => {
  return getEntireFormData();
});

const emit = defineEmits(["handleCreateOrder"]);

const open = (id?: string) => {
  Promise.all([
    getOrderTemplateDetails({
      templateId: routeParams.value.templateId
    }),
    getFindWidgetList({
      sccsId: routeParams.value.sccsId
    }),
    getSccsMilestonePlanList({
      sccsId: routeParams.value.sccsId
    }),
    getDefaultConfigWidgetData(id),
    getWorkOrderProcessorList({
      sccsId: routeParams.value.sccsId,
      type: "UPDATE_PROCESSOR"
    })
  ]).then(resp => {
    orderFormData = [];
    dataFenceHiddenWidgetId = resp[1].data;

    const mainForm = resp[0].data.mainForm;

    handleObtainMainFormData(dataFenceHiddenWidgetId, mainForm);

    msPlannedList.value = resp[0].data.milestoneList;
    sccsPlanData.value = resp[2].data;

    managerList.value = resp[4].data.teamMemberList;
    dialogState.value = id ? "edit" : "add";
    orderId.value = id;

    if (id) {
      //@ts-ignore
      const orderData = resp[3].orderData;
      orderWidgetData.value = TransformSubmitDataStructure(
        orderData.widgetList,
        orderData.subWidgetMap,
        orderData.linkedReferenceMap
      );

      // 需要合并不同接口返回的数据,一个是配置,一个是数据
      msPlannedList.value = resp[0].data.milestoneList.map(item => {
        const milestoneData = (
          resp[3] as any
        ).orderDetailData.milestoneGroupList.find(
          milestone => milestone.msId === item.msId
        );
        return {
          ...item,
          actualDay: milestoneData?.actualDay,
          actualEndDate: milestoneData?.actualEndDate,
          actualStartDate: milestoneData?.actualStartDate,
          plannedStartDate: milestoneData?.plannedStartDate,
          plannedEndDate: milestoneData?.plannedEndDate,
          plannedDay: milestoneData?.plannedDay,
          managerMemberId: milestoneData?.managerMemberId
        };
      });

      handleWidgetChange(orderWidgetData.value);
    } else {
      orderWidgetData.value = [];
    }

    orderWidgetOriginalData.value = cloneDeep(orderWidgetData.value);
    dialogVisible.value = true;
  });
};

const handleAgainCreateOrder = () => {
  OrderViewTemplateDetailRef.value.handleClose();
  orderWidgetData.value = [];
  handleReset();
};

const handleMilestoneTimeLine = (milestoneItem: any) => {
  const index = milestoneForm.value.findIndex(
    milestoneFormItem => milestoneItem.msId === milestoneFormItem.msId
  );
  index === -1
    ? milestoneForm.value.push(milestoneItem)
    : milestoneForm.value.splice(index, 1, milestoneItem);

  // 手动更新表单后，要移除里程碑自动计算的“有更新”的标签
  planTimeChangeIdList.value = planTimeChangeIdList.value.filter(
    id => id !== milestoneItem.msId
  );
};

const handlePlanChangeBySystem = (msId: string) => {
  nextTick(() => {
    planTimeChangeIdList.value.push(msId);
  });
};

/**
 * 过滤主表单中在数据围栏隐藏的字段
 * @param dataFenceHiddenWidgetId 数据围栏设置的显示字段
 * @param mainFormTemplate 主表单字段
 */
const handleObtainMainFormData = (
  dataFenceHiddenWidgetId: string[],
  mainFormTemplate: any
) => {
  let mainFormWidgetList: any[] = [];
  for (let widget of mainFormTemplate.widgetJsonList) {
    if (widget.type === "DrCard") {
      let childList = [];
      for (let childWidget of widget.children) {
        if (!childWidget.children) {
          childList.push(childWidget);
        } else {
          const colWidget = childWidget.children.filter(
            child => !dataFenceHiddenWidgetId.includes(child._fc_id)
          );
          if (colWidget.length > 0) {
            childList.push(Object.assign(childWidget, { children: colWidget }));
          }
        }
      }
      if (childList.length > 0) {
        mainFormWidgetList.push(Object.assign(widget, { children: childList }));
      }
    } else {
      if (!dataFenceHiddenWidgetId.includes(widget._fc_id)) {
        mainFormWidgetList.push(widget);
      }
    }
  }

  const mainFormConfigure = Object.assign(mainFormTemplate, {
    widgetJsonList: mainFormWidgetList
  });
  orderFormData.push(mainFormConfigure);
};

const handleConfirm = debounce(async () => {
  const sccsId = route.query.sccsId as string;
  const trendsFormWidgetList =
    await trendsAggregationFormRef.value.handleGainWidgetFormData(true);
  const linkedReferenceSourceList =
    trendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  if (!trendsFormWidgetList) return;

  let diffMilestoneData = [];
  if (dialogState.value === "edit") {
    diffMilestoneData = differenceWith(
      milestoneForm.value,
      snapshotMilestoneData.value,
      isEqual
    );
  }

  submitLoading.value = true;
  const { code, data } =
    dialogState.value === "add"
      ? await createTradeOrder({
          sccsId: sccsId,
          milestoneDataList: milestoneForm.value,
          widgetItemDataList: trendsFormWidgetList,
          linkedReferenceSaveList: linkedReferenceSourceList
        })
      : await updateTradeOrder([
          {
            id: orderId.value,
            sccsId: sccsId,
            milestoneDataList: diffMilestoneData,
            widgetItemDataList: trendsFormWidgetList,
            linkedReferenceSaveList: linkedReferenceSourceList
          }
        ]);
  if (code === 0) {
    if (dialogState.value !== "add") {
      ElMessage({
        message:
          dialogState.value === "add"
            ? t("trade_common_createSuccess")
            : t("trade_common_updateSuccess"),
        type: "success"
      });
      dialogVisible.value = false;
      emit("handleCreateOrder");
    } else {
      OrderViewTemplateDetailRef.value.open(data);
    }
  }
  submitLoading.value = false;
}, 1000);

const handleWidgetChange = data => {
  // 通过表单修改变更的数据不会包含初始化的数据，导致后续更新的dynamicFormData内容会把之前的内容给覆盖了
  // 遍历新数据，通过 widgetId 比较来新增或修改数据
  data.forEach(newItem => {
    const existingIndex = dynamicFormData.value
      ? dynamicFormData.value.findIndex(
          existingItem => existingItem.widgetId === newItem.widgetId
        )
      : -1;

    if (existingIndex !== -1) {
      // 如果存在相同 widgetId，则更新数据
      dynamicFormData.value[existingIndex] = newItem;
    } else {
      // 如果不存在相同 widgetId，则新增数据
      dynamicFormData.value.push(newItem);
    }
  });
};

const handleCloseOrderDialog = () => {
  dialogVisible.value = false;
  emit("handleCreateOrder");
};

const handleClose = async () => {
  handleBeforeCloseInquiry(() => {
    dialogVisible.value = false;
  });
};

const handleReset = () => {
  uniqueId.value = uuid(8);
  open(orderId.value);
};

// 点击关闭前询问
const handleBeforeCloseInquiry = async done => {
  const trendsFormWidgetList =
    await trendsAggregationFormRef.value.handleGainWidgetFormData(false);

  const changedTrendsFormWidgetList = trendsFormWidgetList?.filter(item => {
    const originalItem = orderWidgetOriginalData.value.find(
      original => original.widgetId === item.widgetId
    );
    return !originalItem || !isEqual(item.obj, originalItem.obj);
  });

  if (changedTrendsFormWidgetList.length > 0) {
    //@ts-ignore
    const result = await proxy.$submitMessageBox.confirm(
      t("trade_common_dataUpdateTip"),
      {
        confirmButtonText: t("trade_order_submitBtn"),
        cancelButtonText: t("trade_common_cancel"),
        nextButtonText: t("trade_order_unSubmit")
      }
    );
    if (result === "confirm") {
      handleConfirm();
    } else if (result === "nextConfirm") {
      done();
    }
  } else {
    done();
  }
};

onMounted(() => {
  emitter.on("widgetUploadLoading", (isUploading: boolean) => {
    btnOperationState.value = isUploading;
  });
});

defineExpose({
  open,
  reset: handleReset
});
</script>
<style lang="scss" scoped>
.el-collapse-item {
  margin-bottom: 19px;
  border: 1px solid #e6eaf0;
  border-radius: 4px;

  ::v-deep(.el-collapse-item__header) {
    padding: 0 15px;
    background: #f6f9fb;

    .collapse-form-item-title {
      font-size: 14px;
      font-weight: bolder;
      color: #262626;
    }
  }

  ::v-deep(.el-collapse-item__content) {
    padding: 0 20px 10px;

    .el-form-item {
      padding-top: 4px !important;
      margin-bottom: 8px !important;

      .widget-form-item-label {
        display: inline-flex;
        align-items: center;

        .widget-form-item-title {
          margin-left: 5px;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          color: #262626;

          .widget-required {
            color: red;
          }
        }

        .widget-form-item-label-icon {
          font-size: 12px;
          font-weight: 400;
          color: #262626;
          cursor: pointer;
        }
      }

      .el-form-item__content {
        .el-form-item__error {
          position: relative;
          margin-top: 2px;
          color: red;
        }

        .el-input-number {
          width: 100% !important;
        }
      }
    }

    .el-form-item__label {
      margin-bottom: 0 !important;
    }
  }
}

.create-order-dialog {
  .create-order-container {
    display: flex;
    flex-flow: column;
    justify-content: center;
    text-align: center;

    .icon {
      width: 72px;
      height: 72px;
      margin: 25px auto 0;
    }

    .create-order-btn-text {
      margin: 20px 0 56px;
      font-size: 14px;
      color: #8c8c8c;
      text-align: center;

      .create-order-tip {
        font-size: 14px;
        line-height: 14px;
        color: #262626;
      }
    }
  }
}

.milestone-timeline-body {
  margin: 50px 0;

  ::v-deep(.el-timeline-item) {
    .el-timeline-item__timestamp {
      display: none;
    }

    .el-timeline-item__content {
      .milestone-timeline-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .milestone-timeline-title {
          display: inline-flex;
          align-items: center;

          .el-text {
            font-size: 14px;
            font-weight: bolder;
            color: #262626;
          }
        }

        .milestone-timeline-tag {
          display: inline-block;
          padding: 3px 5px;
          margin-left: 7px;
          line-height: 14px;
          color: #fdad4d;
          background: rgb(253 173 77 / 15%);
          border-radius: 10px;
        }
      }

      .milestone-timeline-container {
        margin-right: 50px;
        background: #fff;
        border: 1px solid #eaeaea;
        box-shadow: 0 1px 4px 0 rgb(0 0 0 / 9%);

        .milestone-timeline-container-header {
          display: flex;
          align-items: center;
          height: 40px;
          margin: 0 14px;
          line-height: 40px;
          border-bottom: 1px solid #e3e5e9;

          .milestone-timeline-container-tip {
            // width: 42px;
            width: auto;

            &::after {
              content: "：";
            }
          }

          .el-text {
            flex: 1;
            color: #303133;
          }
        }

        .milestone-timeline-container-body {
          display: inline-flex;
          align-items: center;
          width: 100%;
          padding: 10px 14px;

          .el-form-item {
            width: 25%;
            margin-right: 20px;

            &:last-child {
              margin-right: 0 !important;
            }

            .el-form-item__content {
              width: 100%;

              .el-date-editor {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style>
