<template>
  <el-select-v2 v-bind="$attrs">
    <template #default="{ item }">
      <el-tooltip
        v-if="!item.activate"
        effect="dark"
        :content="t('trade_common_notSelectRegister')"
        placement="top"
        :show-after="500"
      >
        <template #default>
          <div>
            <span
              v-if="!item.activate && teamRenderFields.label !== 'teamName'"
              class="tag-register-tip"
            >
              ({{ t("trade_common_unregistered") }})
            </span>
            <el-avatar
              :class="
                teamRenderFields.label === 'teamName' ? 'colorOrange' : ''
              "
              :src="item[teamRenderFields.avatar]"
              :size="18"
              fit="cover"
            >
              {{ item[teamRenderFields.label]?.substring(0, 1) }}
            </el-avatar>
            {{ item[teamRenderFields.label] }}
          </div>
        </template>
      </el-tooltip>
      <div v-else class="avatar-body">
        <span
          v-if="!item.activate && teamRenderFields.label !== 'teamName'"
          class="tag-register-tip"
        >
          ({{ t("trade_common_unregistered") }})
        </span>
        <el-avatar
          :class="teamRenderFields.label === 'teamName' ? 'colorOrange' : ''"
          :src="item[teamRenderFields.avatar]"
          :size="18"
          fit="cover"
        >
          {{ item[teamRenderFields.label]?.substring(0, 1) }}
        </el-avatar>
        <ReText type="info" :tippyProps="{ delay: 50000 }">
          {{ item[teamRenderFields.label] }}（{{ item.account }}）
        </ReText>
      </div>
    </template>
    <template #label="{ label, value }">
      <LKAvatar
        :teamInfo="{
          avatar: getUserAvatar(value),
          username: label
        }"
        :class="teamRenderFields.label === 'teamName' ? 'colorOrange' : ''"
        :size="18"
      />{{ label }}
    </template>
  </el-select-v2>
</template>
<script lang="ts" setup>
//@ts-nocheck
import { useAttrs } from "vue";
import { useI18n } from "vue-i18n";
import LKAvatar from "@/components/lkAvatar/index";
import { ReText } from "@/components/ReText";

const { t } = useI18n();
const attrs = useAttrs();

const props = defineProps({
  teamRenderFields: {
    type: String as PropType<string>,
    default: ""
  },
  mode: {
    type: Boolean as PropType<boolean>,
    default: true
  }
});

const getUserAvatar = (id: string) => {
  const memberItem = (attrs.options as any).find(
    memberLi => memberLi[attrs.props.value] === id
  );
  return memberItem ? memberItem[props.teamRenderFields.avatar] : "";
};
</script>
<style lang="scss" scoped>
.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.avatar-body {
  display: flex;
  align-items: center;

  ::v-deep(.el-avatar) {
    display: inline-block;
    min-width: 18px;
    line-height: 18px !important;
  }
}
</style>
<style lang="scss">
.manager-popper-class {
  .el-select-dropdown__item.is-disabled {
    background: rgb(242 242 242 / 72%);
  }
}
</style>
