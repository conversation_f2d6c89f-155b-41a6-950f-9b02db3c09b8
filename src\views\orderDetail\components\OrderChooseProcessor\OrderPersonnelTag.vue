<template>
  <div class="order-personnel-container">
    <div class="work-order-add-container" @click="handleAddUserInfo">
      <i class="iconfont link-add" />
      <span class="work-order-add-text">
        {{ t("trade_order_AddProcessor") }}
      </span>
    </div>
    <div
      v-for="userItem in userList"
      :key="memberInfo(userItem)?.id"
      class="order-personnel-col"
    >
      <LkAvatar
        :size="18"
        :class="memberInfo(userItem)?.className"
        :shape="memberInfo(userItem)?.shape"
        :teamInfo="{
          avatar: memberInfo(userItem)?.avatar,
          username: memberInfo(userItem)?.username,
          coop: memberInfo(userItem)?.coop,
          email: userItem.account
        }"
      />
      <div class="order-personnel-text">
        {{ memberInfo(userItem)?.showName }}
      </div>
      <i
        class="iconfont link-clean order-personnel-icon"
        @click="handleRemoveUserInfo(userItem)"
      />
    </div>
  </div>
  <OrderChooseProcessorDialog
    ref="WorkOrderChooseProcessorRef"
    @handleChooseConfirm="handleAllChooseConfirm"
  />
</template>
<script lang="ts" setup>
import { computed, ref, toRaw, watch } from "vue";
import { useI18n } from "vue-i18n";
import { storageLocal } from "@pureadmin/utils";
import LkAvatar from "@/components/lkAvatar/index";
import OrderChooseProcessorDialog from "@/views/orderDetail/components/OrderChooseProcessor/OrderChooseProcessorDialog.vue";

const props = defineProps({
  sccsId: {
    type: String as PropType<string>,
    default: ""
  },
  personnelList: {
    type: Array as PropType<any>,
    default: () => []
  },
  workOrderUserInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  type: {
    type: String as PropType<string>,
    default: ""
  }
});

const emit = defineEmits(["handleChangePersonnel"]);

const { t } = useI18n();
const WorkOrderChooseProcessorRef = ref<HTMLElement | any>(null);

const userList = ref<any>([]);

const memberInfo = computed(() => {
  return info => {
    return {
      className: info.user ? "" : "colorOrange",
      shape: info.user ? "circle" : "square",
      avatar: info.user ? info.avatar : info.teamAvatar,
      username: info.user ? info.username : info.teamName,
      coop: info.user ? info.coop : false,
      showName: info.user ? info.username : info.teamShortName,
      id: info.user ? info.teamMemberId : info.teamId
    };
  };
});

const handleRemoveUserInfo = (userInfo: any) => {
  let personnelInfoList = [];
  if (userInfo.user) {
    personnelInfoList = props.personnelList.filter(
      personnel => personnel.assignedTeamMemberId !== userInfo.teamMemberId
    );
  } else {
    personnelInfoList = props.personnelList.filter(
      personnel => personnel.assignedTeamId !== userInfo.id
    );
  }
  emit("handleChangePersonnel", props.workOrderUserInfo, personnelInfoList);
};

const handleAddUserInfo = () => {
  WorkOrderChooseProcessorRef.value.open(toRaw(userList.value), props.type, {
    sccsId: props.sccsId
  });
};

const handleAllChooseConfirm = (data): void => {
  emit("handleChangePersonnel", props.workOrderUserInfo, data);
};

watch(
  () => props.personnelList,
  () => {
    const workOrderProcessorListData: any = storageLocal().getItem(
      "workOrderProcessorList"
    );

    if (workOrderProcessorListData && props.personnelList.length > 0) {
      const { sccsMemberList, sccsCoopTeamList } = workOrderProcessorListData;
      let personnelList = [];

      let sccsMember = [];
      for (let coopTeam of sccsCoopTeamList) {
        sccsMember = sccsMember.concat(coopTeam.teamMemberList);
      }

      sccsMember = sccsMember.concat(sccsMemberList);

      for (let personnel of props.personnelList) {
        if (Object.keys(personnel)[0] === "assignedTeamMemberId") {
          const userInfo = sccsMember.find(
            member => member.teamMemberId === personnel["assignedTeamMemberId"]
          );
          personnelList.push(
            Object.assign(userInfo, {
              user: true,
              coop: userInfo.showHand
            })
          );
        } else if (Object.keys(personnel)[0] === "assignedTeamId") {
          const teamInfo = sccsCoopTeamList.find(
            team => team.id === personnel["assignedTeamId"]
          );
          personnelList.push(Object.assign(teamInfo, { user: false }));
        }
      }
      userList.value = personnelList;
    } else {
      userList.value = [];
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.order-personnel-container {
  .order-personnel-col {
    display: inline-flex;
    align-items: center;
    height: 24px;
    padding: 0 5px;
    margin-top: 10px;
    margin-right: 6px;
    background: #ebf5fb;
    border-radius: 3px;

    .order-personnel-text {
      margin: 0 5px 0 3px;
      font-size: 12px;
      font-weight: normal;
      line-height: 24px;
      color: #000;
      text-align: left;
    }

    .order-personnel-icon {
      font-size: 12px;
      color: #babec6;
      cursor: pointer;
    }
  }

  .work-order-add-container {
    display: inline-block;
    padding: 0 9px;
    margin-top: 10px;
    margin-right: 3px;
    cursor: pointer;
    background: #efefef;
    border-radius: 3px;

    .iconfont {
      font-size: 12px;
      color: #595959;
    }

    .work-order-add-text {
      margin-left: 4px;
      font-size: 12px;
      color: #595959;
    }
  }
}
</style>
