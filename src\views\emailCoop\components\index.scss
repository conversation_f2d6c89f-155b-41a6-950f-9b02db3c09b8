.email-validate-body {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: #f5f6fa;

  .email-validate-container {
    display: inline-block;
    width: 522px;
    padding: 47px 47px 63px 50px;
    background: #fff;
    border-radius: 8px;

    .email-validate-title {
      margin-bottom: 10px;
      font-size: 26px;
      font-weight: bolder;
      line-height: 37px;
      color: #000;
    }

    .email-validate-tip {
      font-size: 14px;
      line-height: 20px;
      color: #000;
    }

    .email-validate-sheet {
      margin-top: 44px;

      .email-validate-form-title {
        .email-validate-form-name {
          font-size: 14px;
          line-height: 20px;
          color: #262626;
          text-align: left;

          &::after {
            display: inline-block;
            margin-right: 4px;
            font-size: 14px;
            line-height: 20px;
            color: #262626;
            content: "：";
          }
        }

        .email-validate-form-code {
          font-size: 14px;
          font-weight: bolder;
          line-height: 20px;
          color: #000;
        }
      }

      .email-validate-mail-form-title {
        margin-top: 25px;
        font-size: 14px;
        line-height: 20px;
        color: #262626;
        text-align: left;
      }

      .email-validate-mail-form {
        display: flex;
        align-items: center;
        width: 100%;
        margin-top: 8px;

        .email-validate-mail-container {
          flex: 1;
          margin-right: 12px;
        }

        .send-verify-code-btn {
          padding: 0 13px;
          color: #0070d2;
          background: #eef8ff;
          border: 1px solid #ccecff;
          border-radius: 4px;

          &:hover {
            color: #fff;
            background: #0070d2;
            border: 1px solid #0071d2;
          }
        }

        .count-down-body {
          color: #87c9ff;
          border: 1px solid #e0dfe4;
          border-radius: 4px;

          ::v-deep(.el-statistic) {
            .el-statistic__number {
              font-size: 14px;
              color: #87c9ff;
            }

            .el-statistic__suffix span {
              font-size: 14px;
              color: #87c9ff;
            }
          }
        }
      }

      .email-validate-mail-form-bottom-title {
        margin-top: 8px;
        margin-bottom: 80px;
        font-size: 12px;
        line-height: 17px;
        color: #8c8c8c;
      }

      .email-validate-btn-body {
        .el-button {
          width: 100%;
          border-radius: 8px;
        }
      }
    }
  }
}

.email-collaborate-table-body {
  height: 100%;

  .email-collaborate-table-header {
    display: flex;
    align-items: center;

    .email-collaborate-table-left {
      display: flex;
      flex: 1;
      align-items: center;

      .email-collaborate-table-left-tip {
        margin-left: 20px;
        font-size: 12px;
        color: #595959;

        .email-collaborate-table-left-block {
          display: inline-block;
          width: 15px;
          height: 10px;
          margin-right: 4px;
          background: #fafae8;
          border: 1px solid #e8e8e8;
          border-radius: 2px;
        }
      }
    }
  }

  .email-collaborate-table-container {
    height: calc(100% - 47px);
    margin-top: 15px;

    .vxe-grid {
      ::v-deep(.vxe-header--column.FAFAE8) {
        background: #fafae8 !important;
        border-right: 1px solid #e8e8e8;
      }

      ::v-deep(.grid-table-operate) {
        display: flex;
        align-items: center;
        justify-content: space-around;

        .grid-table-operate-btn {
          font-size: 14px;
          line-height: 20px;
          color: #0070d2;
          cursor: pointer;

          &:last-child {
            color: #cf1421;
          }
        }
      }
    }
  }
}

.create-email-collaborate-container {
  position: relative;
  display: flex;
  height: 100%;

  .create-email-collaborate-left {
    width: 358px;
    min-width: 358px;

    ::v-deep(.el-scrollbar__view) {
      height: 100%;

      .create-email-collaborate-form {
        padding: 14px;
        background: #fff;
        border-radius: 4px 4px 0 0;

        .create-email-collaborate-form-list {
          margin-bottom: 26px;
          border: 1px solid #e9eaed;
          border-radius: 4px;

          .create-email-collaborate-form-li {
            display: flex;
            align-items: center;
            height: 50px;
            padding: 0 12px;
            border-bottom: 1px solid #e9eaed;

            &:last-child {
              border-bottom: 0 none;
            }

            .create-email-collaborate-form-li-left {
              max-width: 170px;
              overflow: hidden;
              font-size: 13px;
              font-weight: bolder;
              line-height: 14px;
              color: #262626;
              text-align: left;
              text-overflow: ellipsis;
              word-wrap: break-word;
            }

            .create-email-collaborate-form-li-right {
              flex: 1;
              text-align: right;

              .create-email-collaborate-form-tag {
                display: inline-block;
                width: 48px;
                height: 20px;
                margin-right: 11px;
                font-size: 12px;
                line-height: 20px;
                color: #2082ed;
                text-align: center;
                background: #d8eff9;
                border-radius: 12px;
              }
            }
          }
        }

        .create-email-collaborate-file-form {
          .create-email-collaborate-form-tip {
            font-size: 14px;
            line-height: 20px;
            color: #a8abb2;
            text-align: left;
          }

          .create-email-collaborate-form-select {
            margin: 15px 0;
          }

          .upload-tip-text {
            font-size: 12px;
            line-height: 17px;
            color: #a1a1a1;
            text-align: center;
          }

          .avatar-uploader {
            max-width: 330px;
          }
        }

        .custom-form-item {
          .el-form-item__label {
            width: 100%;

            .custom-form-label-body {
              display: inline-flex;
              align-items: center;
              width: calc(100% - 10px);

              .custom-form-label-title {
                flex: 1;
              }
            }
          }
        }
      }
    }
  }

  .create-email-collaborate-right {
    flex: 1;
    max-width: calc(100% - 358px);
    margin-left: 17px;

    .order-form-edit-collapse {
      .el-collapse-item.HIDDEN {
        display: none;
      }

      .order-state {
        display: inline-block;
        height: 20px;
        padding: 0 5px;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        color: #808080;
        text-align: center;
        background: #e2e2e2;
        border-radius: 12px;
      }

      .order-state-editable {
        color: #2082ed;
        background: #d8eff9;
      }
    }
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3000;
    width: 100%;
    height: 100%;
    background: #fff0;

    /* 半透明黑色遮罩 */
  }
}

::v-deep(.column-title-body) {
  display: flex;
  align-items: center;

  .column-title {
    flex: 1;
  }

  .column-icon {
    width: 20px;
    text-align: right;
    cursor: pointer;

    .iconfont {
      font-size: 12px;
      font-weight: normal;
      color: #797979;
    }
  }
}
