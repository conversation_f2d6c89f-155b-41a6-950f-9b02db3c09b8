<template>
  <div class="sccs-group-body">
    <LkDialog
      ref="LkDialogRef"
      :noFooter="true"
      class="no-footer-dialog"
      :title="
        editStatus === 'add'
          ? t('trade_home_addGroup')
          : t('trade_home_editGroup')
      "
      :append-to-body="true"
      @close="handleClose"
    >
      <template #default>
        <el-form
          ref="formRef"
          :model="sizeForm"
          :rules="rules"
          label-position="top"
        >
          <el-form-item
            :label="t('trade_home_groupName')"
            required
            prop="groupName"
          >
            <el-input
              v-model="sizeForm.groupName"
              show-word-limit
              maxlength="20"
              clearable
            />
          </el-form-item>
          <el-form-item class="dialog-footer-btn-group">
            <el-button @click="resetForm(formRef)">
              {{ t("trade_common_cancel") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="onSubmit(formRef)"
            >
              {{ t("trade_common_confirm") }}
            </el-button>
          </el-form-item>
        </el-form>
      </template>
    </LkDialog>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog";
import type { FormInstance, FormRules } from "element-plus";
import { createSccsGroup, updateSccsGroup } from "@/api/sccs";
import { ElMessage } from "element-plus";

const { t } = useI18n();
const LkDialogRef = ref<any | HTMLElement>(null);
const formRef = ref<any | HTMLElement>(null);
const editStatus = ref<string>("add");
const sizeForm = reactive({
  id: "",
  groupName: ""
});
const rules = reactive<FormRules>({
  groupName: [
    {
      required: true,
      message: t("trade_home_groupNameInputTip"),
      trigger: "change"
    }
  ]
});

const emit = defineEmits<{
  (e: "updateSccsGroup"): void;
}>();

const open = (data: any): void => {
  if (data && data.hasOwnProperty("id")) {
    editStatus.value = "edit";
    sizeForm.id = data.id;
    sizeForm.groupName = data.groupName;
  } else {
    editStatus.value = "add";
    sizeForm.id = "";
    sizeForm.groupName = "";
  }

  LkDialogRef.value.open();
};

const handleClose = (): void => {
  sizeForm.groupName = "";
  formRef.value.clearValidate();
};

const onSubmit = (formEl: FormInstance | undefined): void => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      const { code } =
        editStatus.value === "add"
          ? await createSccsGroup({
              id: sizeForm.id,
              groupName: sizeForm.groupName
            })
          : await updateSccsGroup({
              id: sizeForm.id,
              groupName: sizeForm.groupName
            });
      if (code === 0) {
        ElMessage({
          message: t("trade_common_updateSuccess"),
          type: "success"
        });
        LkDialogRef.value.close();
        emit("updateSccsGroup");
      }
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  LkDialogRef.value.close();
};

defineExpose({
  open
});
</script>
