<template>
  <el-collapse v-model="activeNames" class="order-form-edit-collapse">
    <el-collapse-item
      v-for="item in widgetData"
      :key="item.id"
      :name="item.id"
      :class="[
        item.workOrderType ? 'orange-collapse-item' : '',
        getFormState(item.id) ? 'order-collapse-item-hidden' : '',
        `collaspse-${item.id}`
      ]"
    >
      <template #title>
        <div class="order-form-edit-title-header">
          <span class="order-form-edit-title">
            <ReText type="info" :line-clamp="2" :tippyProps="{ delay: 50000 }">
              {{ widgetDropDownTitle(item) }}
            </ReText>
          </span>
          <div
            v-if="!item.workOrderType && showTitleTip"
            class="order-form-edit-title-header-right"
          >
            <span
              v-if="getAvatarGroupList(dataListInfo, item.id).length === 0"
              class="order-form-edit-tip"
            >
              <i class="iconfont link-tips-warm" />
              {{ t("trade_common_createWorkOrderTip") }}
            </span>
            <span v-else @click.stop>
              <LkAvatarGroupNext
                :size="22"
                :avatarListGroup="getAvatarGroupList(dataListInfo, item.id)"
                :maxAvatar="4"
              />
            </span>
          </div>
        </div>
      </template>
      <div v-if="processingMode === 'mode'">
        <div v-if="!item.workOrderType">
          <LkDynamicForm
            v-if="item.widgetJsonList.length > 0"
            ref="WidgetDynamicFormRef"
            :formWidgets="item.widgetJsonList"
            :widgetForm="item"
            :widgetData="widgetFormData"
            :visibleWidgetForm="visibleWidgetForm"
            :otherFormData="otherFormData"
            :orderId="route.query.orderId"
            @handleVisibleFormWidget="handleVisibleFormWidget"
            @handleFormulasBindValueChange="handleFormulasBindValueChange"
          />
          <el-empty
            v-else
            style="background: #fff"
            :description="t('trade_common_emptyTip')"
            :image-size="314"
            :image="sccsGroupNoImage"
          />
        </div>

        <OrderDetailDescWidget
          v-if="item.workOrderType"
          :widgetForm="item.widgetJsonList"
          :widgetData="widgetFormData"
        />
      </div>
      <div v-else-if="processingMode === 'person'">
        <LkDynamicForm
          v-if="editWidgetFormId.includes(item.id)"
          ref="WidgetDynamicFormRef"
          :formWidgets="item.widgetJsonList"
          :widgetForm="item"
          :widgetData="widgetFormData"
          :visibleWidgetForm="visibleWidgetForm"
          :otherFormData="otherFormData"
          :orderId="route.query.orderId"
          @handleVisibleFormWidget="handleVisibleFormWidget"
          @handleFormulasBindValueChange="handleFormulasBindValueChange"
        />
        <OrderDetailDescWidget
          v-if="!editWidgetFormId.includes(item.id)"
          :widgetForm="item.widgetJsonList"
          :widgetData="widgetFormData"
        />
      </div>
    </el-collapse-item>
  </el-collapse>
</template>
<script lang="ts" setup>
import { computed, nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { getEntireFormData } from "@/utils/formDesignerUtils";
import { storageLocal } from "@pureadmin/utils";
import LkDynamicForm from "@/components/lkDynamicForm/index";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import sccsGroupNoImage from "@/assets/images/sccs/sccs-no-image.png";
import { ReText } from "@/components/ReText";

const { t } = useI18n();
const route = useRoute();
const WidgetDynamicFormRef = ref<HTMLElement | any>(null);
const loading = ref<boolean>(false);
const activeNames = ref([]);
const visibleWidgetForm = ref<any>({});
const otherFormData = ref<any>({});
const widgetSubmitData = ref<any>({});

const props = defineProps({
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetFormData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  showTitleTip: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  processingMode: {
    type: String as PropType<string>,
    default: "mode"
  },
  editWidgetFormId: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  workOrderData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  renderDefaultValue: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  dataListInfo: {
    type: Array as PropType<any>,
    default: () => []
  },
  msId: {
    type: String as PropType<string>,
    default: ""
  },
  workOrderId: {
    type: String as PropType<string>,
    default: ""
  }
});

const getFormState = computed(() => {
  return (id: string) => {
    if (props.dataListInfo.length === 0) {
      return false;
    }
    const formItem = props.dataListInfo.workOrderProcessorVOList.find(
      form => form.formId === id
    );
    return formItem?.formProperty === "HIDDEN" || false;
  };
});

const widgetDropDownTitle = (item): string => {
  return !item.workOrderType
    ? item.name
    : item.workOrderType === "workOrderReply"
      ? t("trade_order_taskReply")
      : t("trade_template_replyBtnName");
};

const getAvatarGroupList = (dataList: any, id: string) => {
  const workOrderProcessorListData: any = storageLocal().getItem(
    "workOrderProcessorList"
  );
  if (workOrderProcessorListData) {
    const { sccsMemberList, sccsCoopTeamList } = workOrderProcessorListData;
    const formItem = dataList.workOrderProcessorVOList.find(
      form => form.formId === id
    );
    let sccsMember = [];
    for (let coopTeam of sccsCoopTeamList) {
      sccsMember = sccsMember.concat(coopTeam.teamMemberList);
    }
    sccsMember = sccsMember.concat(sccsMemberList);
    let userInfoList = [];
    if (formItem) {
      for (let userInfo of formItem.collectUserVOList) {
        if (userInfo.hasOwnProperty("assignedTeamMemberId")) {
          const userInfoData = sccsMember.find(
            member => member.teamMemberId === userInfo["assignedTeamMemberId"]
          );
          //@ts-ignore
          const { latestLoginTeamId } = storageLocal().getItem("user-info");
          userInfoList.push(
            Object.assign(userInfoData, {
              user: true,
              userAvatar: userInfoData.avatar,
              userName: userInfoData.username,
              coopTeamUser: latestLoginTeamId !== userInfoData.teamId,
              email: userInfoData.account,
              shortName: userInfoData.teamShortName
            })
          );
        } else if (userInfo.hasOwnProperty("assignedTeamId")) {
          const userInfoData = sccsCoopTeamList.find(
            team => team.id === userInfo["assignedTeamId"]
          );
          userInfoList.push(
            Object.assign(userInfoData, {
              user: false,
              shortName: userInfoData.teamShortName
            })
          );
        }
      }
    }

    return userInfoList;
  } else {
    return [];
  }
};

const handleVisibleFormWidget = (visibleWidgetFormData: any) => {
  visibleWidgetForm.value = Object.assign(
    visibleWidgetForm.value,
    visibleWidgetFormData
  );
};

const handleFormulasBindValueChange = (calculateFactorsData: any) => {
  otherFormData.value = Object.assign(
    otherFormData.value,
    calculateFactorsData
  );
};

/**
 * 创建工单获取表单数据
 */
const handleWidgetChange = async () => {
  // 过滤formProperty为HIDDEN的表单
  const workOrderFormList = props.dataListInfo.workOrderProcessorVOList.filter(
    form => form.formProperty !== "HIDDEN"
  );
  let widgetList = [];
  for (let i = 0, len = workOrderFormList.length; i < len; i++) {
    const workOrderData = workOrderFormList[i];
    if (workOrderData.collectUserVOList.length === 0) {
      const widgetForm =
        await WidgetDynamicFormRef.value[i].handleGainWidgetFormData();

      if (!widgetForm) {
        return false;
      }
      widgetList = widgetList.concat(widgetForm);
    } else {
      if (WidgetDynamicFormRef.value[i]) {
        const widgetForm =
          await WidgetDynamicFormRef.value[i].handleGainWidgetFormData(false);
        widgetList = widgetList.concat(widgetForm);
      }
    }
  }
  return widgetList;
};

const handleDropDownWidgetData = async () => {
  let widgetList = [];
  for (let widgetFormRef of WidgetDynamicFormRef.value) {
    const widgetForm = await widgetFormRef.handleGainWidgetFormData();
    if (!widgetForm) {
      return false;
    }
    widgetList = widgetList.concat(widgetForm);
  }
  return widgetList;
};

watch(
  () => props.widgetData,
  () => {
    let formEl =
      props.editWidgetFormId.length === 0
        ? props.widgetData.filter(widget => widget.formType === "FORM")
        : props.widgetData.filter(
            widget =>
              widget.formType === "FORM" &&
              props.editWidgetFormId.includes(widget.id)
          );

    const widgetJsonList = [];
    props.widgetData.map(widget => {
      activeNames.value.push(widget.id);
      widgetJsonList.push(...widget.widgetJsonList);
    });

    let visibleFormObject = {};
    for (let widget of widgetJsonList) {
      visibleFormObject[widget._fc_id] = true;
    }
    visibleWidgetForm.value = visibleFormObject;
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  handleWidgetChange,
  handleDropDownWidgetData
});
</script>
<style lang="scss" scoped>
.el-collapse.order-form-edit-collapse {
  .el-collapse-item.orange-collapse-item {
    .el-collapse-item__header {
      background: #fdf5ed !important;
    }
  }

  .el-collapse-item.order-collapse-item-hidden {
    display: none !important;
  }
}

.order-form-edit-title-header {
  display: flex;
  align-items: center;
  width: calc(100% - 20px);

  .order-form-edit-title {
    display: inline-flex;
    align-items: center;
    margin-right: 25px;
    font-size: 14px;
    line-height: 16px;
    color: #262626;

    .is-line-clamp {
      font-size: inherit;
      line-height: inherit;
      color: inherit;
      text-align: left;
    }
  }

  .order-form-edit-title-header-right {
    flex-shrink: 0;
    white-space: nowrap;

    .order-form-edit-tip {
      font-size: 12px;
      font-weight: normal;
      color: #fa8d0b;

      .iconfont {
        margin-right: 2px;
        font-size: 12px;
        vertical-align: text-top;
      }
    }
  }
}
</style>
