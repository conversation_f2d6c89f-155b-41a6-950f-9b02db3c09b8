<template>
  <LkDialog
    ref="LkDialogRef"
    width="80%"
    :title="t('trade_order_cancel')"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <template #default>
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-position="top"
        style="padding: 0 30px"
      >
        <div class="order-cancel-tip">
          <i class="iconfont link-tips-warm" />
          {{ t("trade_order_cancelOrderTip") }}
        </div>
        <el-form-item
          :label="t('trade_order_Reasoncancellation')"
          prop="cancelReasons"
        >
          <el-checkbox-group v-model="ruleForm.cancelReasons">
            <el-row :gutter="20">
              <el-col v-for="item in cancelReasonList" :key="item.id" :span="8">
                <el-checkbox
                  :label="item.orderCancelReason"
                  :value="item.orderCancelReason"
                />
              </el-col>
              <el-col :span="8">
                <el-checkbox label="其他原因" value="其他原因" />
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="t('trade_common_remark')">
          <el-input
            v-model="ruleForm.cancelRemark"
            type="textarea"
            :rows="8"
            clearable
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import LkDialog from "@/components/lkDialog/index";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { getSccsOrderCancelReason } from "@/api/sccs";
import { cancelTradeOrder, batchUpdateOrderStatus } from "@/api/order";

const { t } = useI18n();
const route = useRoute();
const LkDialogRef = ref<any | HTMLElement>(null);
const ruleFormRef = ref<FormInstance>();
const cancelReasonList = ref<any>([]);
const isBatch = ref<Boolean>(false);
const operationOrderIds = ref<any>([]);
const ruleForm = reactive<any>({
  cancelReasons: [],
  cancelRemark: ""
});

const rules = reactive<FormRules<any>>({
  cancelReasons: [
    {
      required: true,
      message: t("trade_order_ReasoncancellationTip"),
      trigger: "change"
    }
  ]
});

const open = async (
  multiple: boolean,
  operationOrderIdList: []
): Promise<void> => {
  isBatch.value = multiple || false;
  operationOrderIds.value = operationOrderIdList || [];
  const { sccsId } = route.query;
  const { data } = await getSccsOrderCancelReason({
    sccsId: sccsId as string,
    orderCancelReason: ""
  });
  cancelReasonList.value = data;
  LkDialogRef.value.open();
};

const emit = defineEmits(["handleCancelOrder"]);
const handleConfirm = async (): Promise<void> => {
  ruleFormRef.value.validate(async valid => {
    if (valid) {
      if (!isBatch.value) {
        // 单个订单取消
        const { sccsId, orderId } = route.query;
        const { code } = await cancelTradeOrder({
          sccsId: sccsId,
          orderId: orderId,
          cancelRemark: ruleForm.cancelRemark,
          cancelReason: ruleForm.cancelReasons.join(",")
        });
        if (code === 0) {
          handleClose();
          // deleteThatMenu({ path: route.path, meta: route.meta }, route, router);
          emit("handleCancelOrder");
        }
      } else if (isBatch.value) {
        // 批量订单取消
        const { sccsId } = route.query;
        const { code } = await batchUpdateOrderStatus({
          sccsId: sccsId,
          orderIdList: operationOrderIds.value,
          operationType: "CANCEL",
          cancelRemark: ruleForm.cancelRemark,
          cancelReason: ruleForm.cancelReasons.join(",")
        });
        if (code === 0) {
          ElMessage({
            message: t("trade_common_updateSuccess"),
            type: "success"
          });
          emit("handleCancelOrder");
          handleClose();
          // deleteThatMenu({ path: route.path, meta: route.meta }, route, router);
        }
      }
    }
  });
};

const handleClose = (): void => {
  ruleForm.cancelRemark = "";
  ruleForm.cancelReasons = [];
  ruleFormRef.value.resetFields();
  ruleFormRef.value.clearValidate();
  LkDialogRef.value.close();
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.order-cancel-tip {
  display: flex;
  align-items: center;
  height: 33px;
  padding: 0 12px;
  margin: 6px 0 20px;
  font-size: 13px;
  color: #fa8d0a;
  background: #fdf6ec;
  border-radius: 4px;

  .iconfont {
    margin-right: 6px;
    font-size: 13px;
    vertical-align: middle;
  }
}
</style>
