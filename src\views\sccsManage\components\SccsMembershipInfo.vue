<template>
  <div class="team-member-info-container team-module-area-container">
    <div class="team-module-area-header">
      <div class="team-module-area-title">
        {{ t("trade_team_membersManage") }}
      </div>
      <el-button type="primary" color="#0070D2" @click="handleAdd">
        <FontIcon icon="link-add" />
        {{ t("trade_common_increase") }}
      </el-button>
    </div>
    <div class="team-module-area-body">
      <el-form
        ref="FormRef"
        label-position="top"
        label-width="auto"
        :model="form"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="`${t('trade_team_inputSearch')}：`">
              <el-input
                v-model="form.keyword"
                clearable
                :placeholder="t('trade_common_searchAccount')"
                @blur="handleSearch"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="`${t('trade_sccs_businessRole')}：`">
              <el-select-v2
                v-model="form.sccsRoleId"
                filterable
                :options="sccsRoleList"
                clearable
                :placeholder="t('trade_common_selectText')"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="`${t('trade_sccs_dataFence')}：`">
              <el-select-v2
                v-model="form.dataFenceId"
                filterable
                :options="sccsFenceList"
                clearable
                :placeholder="t('trade_common_selectText')"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item style="margin-top: 30px">
              <el-button @click="resetForm">
                {{ t("trade_common_reSet") }}
              </el-button>
              <el-button type="primary" color="#0070D2" @click="handleSearch">
                {{ t("trade_common_query") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        :border="true"
        stripe
        max-height="calc(100vh - 280px)"
        :row-class-name="handleTableRowStyle"
        :tooltip-options="{ showAfter: 500 }"
        :empty-text="
          loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
        "
      >
        <el-table-column
          :label="t('trade_common_name')"
          width="120"
          prop="username"
        >
          <template #default="scope">
            <span v-if="!scope.row.activate" class="el-table-register-tip">
              {{ t("trade_common_unregistered") }}
            </span>
            <ReText
              class="el-table-register-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ scope.row.username }}
            </ReText>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_login_email')"
          prop="email"
          width="230"
          show-overflow-tooltip
        />
        <el-table-column
          :label="t('trade_sccs_businessRole')"
          prop="sccsRoleList"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="scope">
            <ReText
              v-for="(item, index) in scope.row.sccsMemberRoleList"
              :key="index"
              type="info"
              :class="['tag-col', item.manager ? 'tag-manager' : '']"
              :tippyProps="{ delay: 50000 }"
            >
              <span v-if="!item.manager">{{ item.name }}</span>
              <span v-else>{{ t("trade_common_businessAdmin") }}</span>
            </ReText>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_sccs_dataFence')"
          prop="sccsDataFenceList"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="scope">
            <ReText
              v-for="(item, index) in scope.row.sccsDataFenceList"
              :key="index"
              class="tag-col"
              type="info"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.dataFenceName }}
            </ReText>
          </template>
        </el-table-column>
        <el-table-column width="70" align="center">
          <template #default="{ row }">
            <LkTableOperate
              :tableOpeateLists="tableOpeateLists"
              :messageBoxConfirmObject="messageBoxConfirmObject"
              :row="row"
              @handleOperate="handleOperate"
            />
          </template>
        </el-table-column>
      </el-table>
      <LkPagination
        ref="LkPaginationRef"
        :total="total"
        @updatePagination="handleSearch"
      />
      <SccsAddMembershipDialog
        ref="SccsAddMembershipDialogRef"
        :basicInfo="basicInfo"
        :groupId="groupId"
        @handleSuccess="handleSearch"
      />
      <SccsEditMembershipDialog
        ref="SccsEditMembershipDialogRef"
        :basicInfo="basicInfo"
        :groupId="groupId"
        @handleSuccess="handleSearch"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import LkPagination from "@/components/lkPagination/index";
import SccsAddMembershipDialog from "./SccsMemberShip/SccsAddMembershipDialog.vue";
import SccsEditMembershipDialog from "./SccsMemberShip/SccsEditMembershipDialog.vue";
import { message } from "@/utils/message";
import { ReText } from "@/components/ReText";
import {
  getSccsFenceList,
  getSccsRoleList,
  getSccsMemberListPage,
  deleteSccsMember
} from "@/api/sccs";
import LkTableOperate from "@/components/lkTableOperate/index";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";

const { t } = useI18n();
const form = reactive({
  keyword: "",
  sccsRoleId: "",
  dataFenceId: ""
});
let tableData = ref<any[]>([]);
let sccsFenceList = ref<any[]>([]);
let sccsRoleList = ref<any[]>([]);
const SccsAddMembershipDialogRef = ref<any>(null);
const SccsEditMembershipDialogRef = ref<any>(null);
const LkPaginationRef = ref<any>(null);
let total = ref<number>(0);
let loading = ref<boolean>(false);
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_common_deleteMemberTip",
  messageBoxTitle: "trade_team_deleteMembers",
  messageBoxTipArray: ["username"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-setting",
    title: "trade_common_settingRole",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleTableRowStyle = ({ row, rowIndex }): any => {
  return !row.activate ? "activate-row" : "";
};

const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "edit") {
    handleSettingRole(row);
  } else {
    handleDelete(row.id);
  }
};

const init = (): void => {
  loading.value = true;
  const sccsId = props.basicInfo.id;
  Promise.all([
    getSccsFenceList({ sccsId: sccsId }),
    getSccsRoleList({ sccsId: sccsId })
  ])
    .then(res => {
      sccsFenceList.value = res[0].data;
      sccsRoleList.value = res[1].data;
      handleSearch();
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 300);
    });
};

const resetForm = () => {
  form.sccsRoleId = "";
  form.keyword = "";
  form.dataFenceId = "";
  handleSearch();
};

const handleSearch = async (): Promise<void> => {
  loading.value = true;
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getSccsMemberListPage({
    ...form,
    ...pageParams,
    ...{ sccsId: props.basicInfo.id }
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleAdd = (): void => {
  SccsAddMembershipDialogRef.value.open();
};

const handleDelete = async (id: string): Promise<void> => {
  const { code } = await deleteSccsMember({
    id: id,
    sccsId: props.basicInfo.id
  });
  if (code === 0) {
    message(t("trade_common_deleteSuccess"), {
      customClass: "el",
      type: "success"
    });
    handleSearch();
  }
};

const handleSettingRole = (row: any) => {
  SccsEditMembershipDialogRef.value.open(row);
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
