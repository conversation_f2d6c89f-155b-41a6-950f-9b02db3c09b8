<template>
  <div class="order-milestone-card-body">
    <div
      v-for="item in orderMileStoneList"
      :key="item.msId"
      class="order-milestone-card-col"
    >
      <div class="order-milestone-card-link-body">
        <i class="iconfont link-connect" />
      </div>
      <div class="order-milestone-card" :class="item.status">
        <div class="order-milestone-card-title">
          {{ item.name }}
        </div>
        <div class="order-milestone-card-tag-body">
          <span
            v-for="milestone in getMilestoneLabelList(item.msId)"
            :key="milestone.labelId"
            class="order-milestone-card-tag"
            :style="{ background: milestone.style }"
          >
            {{ milestone.labelValue }}
          </span>
        </div>
        <div
          v-if="item.msReplyUser && item.msReplyUser.length > 0"
          class="order-milestone-card-milestone-approval-body"
        >
          <div class="order-milestone-card-milestone-approval-icon">
            <i class="iconfont link-reply" />
          </div>
          <span class="order-milestone-card-milestone-approval-title">
            {{ t("trade_template_replyBtnName") }}：
            <LkAvatarGroupNext
              class="orange-avatar-group"
              :size="22"
              :avatarListGroup="item.msReplyUser"
              :maxAvatar="4"
            />
          </span>
        </div>
        <div
          v-if="workOrderUserInfoListNumber(item) !== 0"
          class="order-milestone-card-milestone-approval-body"
        >
          <div class="order-milestone-card-milestone-approval-icon">
            <i class="iconfont link-workorder font16 verticalBottom" />
          </div>
          <span class="order-milestone-card-milestone-approval-title">
            {{ t("trade_work_order") }}
            ({{ workOrderUserInfoListNumber(item) }})：
          </span>
        </div>
        <div
          v-if="workOrderUserInfoListNumber(item) !== 0"
          class="order-milestone-card-avatar-group-main clearfix"
        >
          <div
            v-for="(workOrderItem, index) in item.workOrderUserInfoList"
            :key="workOrderItem.workOrderId"
            class="order-milestone-card-avatar-group-col"
          >
            <span class="order-milestone-card-avatar-group-title">
              {{ index + 1 }}.
            </span>
            <span class="mr10">
              <LkAvatarGroupNext
                v-if="
                  workOrderItem.replyUserList &&
                  workOrderItem.replyUserList.length > 0
                "
                class="orange-avatar-group"
                :avatarList="workOrderItem.replyUserList"
                :size="22"
                :maxAvatar="4"
              />
            </span>

            <LkAvatarGroupNext
              :avatarList="workOrderItem.editUserList"
              :size="22"
              :maxAvatar="4"
            />
          </div>
        </div>
        <div
          v-if="item.workOrderUserInfoList.length > 4"
          class="order-milestone-card-avatar-group-more-body"
        >
          ...
        </div>
        <div class="order-milestone-card-avatar-group-more-hover-detail">
          <el-popover
            placement="right"
            :width="300"
            trigger="click"
            :showArrow="false"
          >
            <template #reference>
              <div
                class="order-milestone-card-avatar-group-more-hover-detail-btn"
              >
                <i class="iconfont link-list" />
                <span
                  class="order-milestone-card-avatar-group-more-hover-detail-text"
                >
                  {{ t("trade_common_details") }}
                </span>
              </div>
            </template>
            <OrderMilestoneCardPopover
              :milestoneData="item"
              :labelList="getMilestoneLabelList(item.msId)"
            />
          </el-popover>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useI18n } from "vue-i18n";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext/index";
import OrderMilestoneCardPopover from "./OrderMilestoneCardPopover.vue";
import { MoreFilled } from "@element-plus/icons-vue";

interface workOrderUserInfo {
  replyUserList: any;
  editUserList: any;
  workOrderId: string;
}

const { t } = useI18n();

const props = defineProps({
  orderMileStoneList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  labelList: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const getMilestoneLabelList = (msId: string) => {
  return props.labelList.filter(labelItem => labelItem.relationId === msId);
};

const getWorkOrderUserInfoList = (
  workOrderUserInfoList
): workOrderUserInfo[] => {
  return workOrderUserInfoList.slice(0, 4) as workOrderUserInfo[];
};

const workOrderUserInfoListNumber = (item: any) => {
  return getWorkOrderUserInfoList(item.workOrderUserInfoList).length;
};
</script>
<style lang="scss">
.order-milestone-card-body {
  display: flex;

  .order-milestone-card-col {
    display: flex;

    .order-milestone-card-link-body {
      width: 30px;
      text-align: center;

      .iconfont {
        color: #e3dfee;
      }
    }

    &:first-child {
      .order-milestone-card-link-body {
        display: none;
      }
    }

    .order-milestone-card {
      position: relative;
      display: inline-block;
      width: 267px;
      height: fit-content;
      max-height: 220px;
      padding: 13px 11px;
      text-align: left;
      cursor: pointer;
      background: #fff;
      border-top: 4px solid #dcdcdc;
      border-bottom: 1px solid rgb(10 22 83 / 13%);
      border-radius: 4px;
      box-shadow: 0 1px 6px 0 rgb(10 22 83 / 13%);

      &.NOT_START {
        border-top: 4px solid #ccecff;
      }

      &.ON_GOING {
        border-top: 4px solid #358ff0;
      }

      &.COMPLETE {
        border-top: 4px solid #64d16d;
      }

      &.CANCEL {
        border-top: 4px solid #dcdcdc;
      }

      &:hover {
        .order-milestone-card-avatar-group-more-hover-detail {
          .order-milestone-card-avatar-group-more-hover-detail-btn {
            display: inline-block;
            transition: all 0.5s;
          }
        }
      }

      .order-milestone-card-title {
        width: 100%;
        overflow: hidden;
        line-height: 20px;
        color: #262626;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .order-milestone-card-tag-body {
        margin: 10px 0;

        .order-milestone-card-tag {
          padding: 2px 6px;
          margin-right: 4px;
          font-size: 12px;
          line-height: 14px;
          color: #fff;
          border-radius: 12px;

          &:first-child {
            background: #2bd673;
          }

          &:nth-child(2) {
            background: #ff5b9d;
          }

          &:nth-child(3) {
            background: #ff8e56;
          }
        }
      }

      .order-milestone-card-milestone-approval-body {
        display: flex;
        align-items: center;

        .order-milestone-card-milestone-approval-icon {
          width: 20px;
          text-align: center;

          .iconfont {
            margin-right: 5px;
            font-size: 12px;
            vertical-align: middle;
          }
        }

        .order-milestone-card-milestone-approval-title {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      .order-milestone-card-avatar-group-main {
        max-height: 62px;
        margin-top: 7px;
        overflow: hidden;

        .order-milestone-card-avatar-group-col {
          float: left;
          padding: 3px;
          padding-right: 10px;
          margin: 0 6px 6px 0;
          background: #f2f2f2;
          border-radius: 3px;
        }
      }

      .order-milestone-card-avatar-group-more-body {
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        color: #262626;
        text-align: left;
      }

      .order-milestone-card-avatar-group-more-hover-detail {
        position: absolute;
        top: 11px;
        right: 6px;

        .order-milestone-card-avatar-group-more-hover-detail-btn {
          // display: inline-block;
          display: none;
          width: auto;
          padding: 6px;
          background: #fff;
          border: 1px solid #dfdfdf;
          border-radius: 4px;
          box-shadow: 0 0 6px 0 rgb(0 0 0 / 13%);

          .iconfont {
            margin-right: 5px;
            font-size: 14px;
          }

          .order-milestone-card-avatar-group-more-hover-detail-text {
            font-size: 14px;
            line-height: 20px;
            color: #595959;
          }
        }
      }
    }
  }
}
</style>
