<template>
  <div class="order-detail-container">
    <div class="order-detail-header">
      <div class="order-detail-header-left">
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item
            :to="{
              path: '/orderManage',
              query: {
                sccsId: route.query.sccsId,
                templateId: route.query.templateId,
                coopTeamMark: route.query.coopTeamMark,
                sccsName: route.query.sccsName
              }
            }"
          >
            <span class="breadcrumb-max-width">
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                {{ route.query.sccsName }}
              </ReText>
            </span>
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            <span class="breadcrumb-max-width">
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                {{ route.query.orderMark }}
              </ReText>
            </span>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="order-detail-header-right">
        <div class="order-detail-header-switch">
          <el-switch
            v-model="orderDetailsWidthMode"
            :width="28"
            size="small"
            :active-text="t('trade_order_allWidth')"
            @change="handleChangeSwitch"
          />
        </div>
        <div class="order-detail-header-group">
          <el-dropdown @command="handleCommand">
            <span class="order-detail-dropdown-btn">
              <i :class="['iconfont', cardViewMode.icon]" />
              <span class="order-detail-dropdown-btn-text">
                {{ t(cardViewMode.text) }}
              </span>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="item in cardViewList"
                  :key="item"
                  command="a"
                >
                  <i class="iconfont" :class="item.icon" />{{ t(item.text) }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div
      v-loading="detailLoading"
      class="order-detail-body"
      :class="[
        !orderDetailsWidthMode ? '' : 'full-width-container',
        hide ? 'hideLeft' : '',
        useAppStoreHook().viewportSize.width <= 1366
          ? 'order-detail-body-small'
          : ''
      ]"
    >
      <Transition
        leave-active-class="animate__animated animate__fadeOutLeft animate__faster"
        enter-active-class="animate__animated animate__fadeInLeft animate__faster"
      >
        <div
          v-show="orderTimeLineVisible"
          class="order-detail-body-left"
          :class="[hide ? 'hideLeft' : '']"
        >
          <OrderTimeLine
            id="order-time-line"
            ref="OrderTimeLineRef"
            data-id="order-time-line"
            :milestoneCardList="milestoneCardList"
            :milestoneCard="milestoneCard"
            :widgetLists="widgetLists"
            :widgetBasicInfo="widgetBasicInfo"
            :labelList="orderLabelList"
            @handleAddWorkOrder="handleAddWorkOrder"
            @up-data-count="upDataCount"
          />
          <div class="anchor-icon-left" @click="hideLeft">
            <i class="iconfont link-pack-up" />
          </div>
        </div>
      </Transition>
      <div
        class="order-detail-body-right"
        :text="t('trade_common_loadingText')"
        :class="
          useAppStoreHook().viewportSize.width <= 1366 &&
          !orderDetailsWidthMode &&
          hide
            ? 'order-detail-body-right-small'
            : ''
        "
      >
        <div v-if="hide" class="anchor-icon-right" @click="hideLeft">
          <i class="iconfont link-expand" />
        </div>
        <el-scrollbar
          :noresize="true"
          always
          class="order-detail-main-container"
        >
          <OrderDetailCard
            id="order-detail-basic-info"
            v-loading="mainFormLoading"
            taskMode="basicInfo"
            data-id="order-detail-basic-info"
            :relationIconType="orderRelationObj.relationIconType"
            :orderCancelData="orderCancelData"
            :widgetForm="mainWidgetForm"
            :widgetData="orderWidgetList"
            :milestoneRow="mainWidgetRow"
            :orderStateFlag="orderStateFlag"
            :visibleFormData="mainFormVisibleObject"
            @handleTrigger="type => handleTrigger(type, widgetData)"
          >
            <template #orderRelations>
              <OrderDetailRelations
                type="ORDER"
                :orderRelationMap="orderRelationObj.orderRelationMap"
                @updateOrderRelation="initOrderDetail"
              />
            </template>
          </OrderDetailCard>
          <OrderDetailMileStoneCard
            v-for="item in milestoneCardList"
            :id="item.msId"
            :key="item.msId"
            :data-id="item.msId"
            :orderCountMap="orderCountMap"
            :milestoneRow="item"
            :relationIconType="item.relationIconType"
            :orderStateFlag="orderStateFlag"
            :milestoneCard="getMilestoneData(item.msId)"
            :activeWorkOrderId="activeWorkOrderId"
            :visibleFormData="temporaryStorageFormVisibleObject"
            :msVisibleIdsList="msVisibleIdsList"
            :msVisibleFormLoading="msVisibleFormLoading"
            @handleTrigger="(type, data) => handleTrigger(type, item, data)"
            @upDataCount="upDataCount"
            @handleTriggerWorkOrder="handleTriggerWorkOrder"
          >
            <template #milestoneRelations>
              <OrderDetailRelations
                type="MILESTONE"
                :orderRelationMap="orderRelationObj.milestoneRelationMap"
                :msId="item.msId"
                @updateOrderRelation="initOrderDetail"
              />
            </template>
            <template #workOrderRelations="{ workOrderInfo }">
              <OrderDetailRelations
                type="WORKORDER"
                :hideBackgroudColor="true"
                :orderRelationMap="
                  orderRelationObj.workOrderRelations[
                    workOrderInfo.workOrderId
                  ] || {}
                "
                :msId="item.msId"
                @updateOrderRelation="initOrderDetail"
              />
            </template>
          </OrderDetailMileStoneCard>
        </el-scrollbar>
      </div>
    </div>
  </div>
  <CreateWorkOrderDialog
    ref="CreateWorkOrderDialogRef"
    @handleCreateWorkOrderSuccess="handleCreateWorkOrderSuccess"
  />
  <EditWorkOrderDialog
    ref="EditWorkOrderDialogRef"
    @handleCreateWorkOrderSuccess="handleCreateWorkOrderSuccess"
  />
  <OrderChooseProcessorDialog
    ref="chooseProcessorRef"
    :personnelControlMode="orderChooseMode"
    :dialogTitle="orderChooseTitle"
    :orderChooseType="orderChooseType"
    @handleChooseConfirm="handleChooseConfirm"
  />
  <OrderTrackRecordDialog
    ref="OrderTrackRecordDialogRef"
    :trackRecords="trackRecords"
  />
  <OrderHistoryRecordDialog
    ref="OrderHistoryRecordDialogRef"
    :type="historyDialogType"
    :historyRecords="historyRecords"
    :currentMainWorkOrderId="currentMainWorkOrderId"
    :widgetForm="mainWidgetForm"
    :widgetData="orderWidgetList"
    :milestoneCard="milestoneCard"
  />
  <WorkOrderCollectionDialog
    ref="WorkOrderCollectionDialogRef"
    @handleUpdateCollection="handleCreateWorkOrderSuccess"
  />
  <WorkOrderMilestoneReplyDialog
    ref="WorkOrderMilestoneReplyDialogRef"
    @handleUpdateCollection="handleCreateWorkOrderSuccess"
  />
  <WorkOrderReplyDialog
    ref="WorkOrderReplyDialogRef"
    @handleUpdateCollection="handleCreateWorkOrderSuccess"
  />
  <OrderViewTemplateDialog
    ref="OrderViewTemplateDialogRef"
    @handleCreateOrder="handleUpdateMainForm"
  />
  <OrderCancelDialog
    ref="OrderCancelDialogRef"
    @handleCancelOrder="handleCreateWorkOrderSuccess"
  />
  <EmailCoop ref="EmailCoopDialogRef" />
</template>
<script lang="ts" setup>
import {
  onMounted,
  ref,
  markRaw,
  ComponentPublicInstance,
  computed,
  nextTick
} from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { ArrowRight, WarningFilled } from "@element-plus/icons-vue";
import { debounce } from "lodash-es";
import { cloneDeep, storageLocal } from "@pureadmin/utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils.tsx";
import { getDefaultConfigWidgetData } from "@/utils/formulasActuator/formulasActuator";
import { deleteThatMenu } from "@/utils/dynamicTag";
import CreateWorkOrderDialog from "@/views/orderDetail/components/CreateWorkOrderDialog.vue";
import EditWorkOrderDialog from "@/views/orderDetail/components/EditWorkOrderDialog.vue";
import WorkOrderCollectionDialog from "@/views/orderDetail/components/WorkOrderCollectionDialog.vue";
import WorkOrderReplyDialog from "@/views/orderDetail/components/WorkOrderReplyDialog.vue";
import WorkOrderMilestoneReplyDialog from "@/views/orderDetail/components/WorkOrderMilestoneReplyDialog.vue";
import OrderDetailRelations from "@/views/orderDetail/components/OrderDetailRelations.vue";
import OrderDetailCard from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDetailCard.vue";
import OrderTimeLine from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderTimeLine.vue";
import OrderDetailMileStoneCard from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDetailMileStoneCard.vue";
import OrderChooseProcessorDialog from "@/views/orderDetail/components/OrderChooseProcessor/OrderChooseProcessorDialog.vue";
import OrderCancelDialog from "@/views/orderDetail/components/OrderCancelDialog.vue";
import OrderTrackRecordDialog from "@/views/history/OrderTrackRecordDialog.vue";
import OrderHistoryRecordDialog from "@/views/history/OrderHistoryRecordDialog.vue";
import OrderViewTemplateDialog from "@/views/orderManage/components/OrderViewTemplateDialog.vue";
import { ReText } from "@/components/ReText";
import EmailCoop from "@/views/emailCoop/index.vue";
import { useAppStoreHook } from "@/store/modules/app";
import { emitter } from "@/utils/mitt";
import { getVisibleDivs } from "@/utils/common";
import { ExecuteFormVisibleExpression } from "@/utils/formVisibleExpression";
import { getFindWidgetList } from "@/api/sccs";
import { getOrderDynamicList, getWorkOrderHistoryList } from "@/api/history";
import { useOnceWhen } from "@/hooks";
import {
  getOrderMsBasicInfo,
  completeTradeOrder,
  restartTradeOrder,
  deleteTradeOrder,
  complateMilestone,
  cancelMilestone,
  restartMilestone,
  stopWorkOrderCollection,
  deleteWorkOrder,
  assignMilestoneReply,
  updateMilestoneReply,
  assignMilestoneWorkOrderCoopTeamReplier,
  updateCoolTeamCollector,
  updateCoolTeamReplier,
  assignMilestoneCollect,
  assignMilestoneWorkOrderReply,
  getTradeSccsRolePermission,
  getTradeCoopSccsRolePermission,
  updateWorkOrderReply,
  getOrderRelationInfo,
  findOrderWorkOrderRelationMap,
  getWorkOrderCount
} from "@/api/order";

type refItem = HTMLElement | ComponentPublicInstance | null;
interface queryDo {
  msId: string;
}

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const activeWorkOrderId = ref<any>("");
const chooseProcessorRef = ref<HTMLElement | any>(null);
const OrderTrackRecordDialogRef = ref<HTMLElement | any>(null);
const OrderHistoryRecordDialogRef = ref<HTMLElement | any>(null);
// 历史记录弹窗类型（"MAIN_WORK_ORDER" , "MILE_STONE","WORK_ORDER"）
const historyDialogType = ref<string>("");
const mainFormLoading = ref<boolean>(false); // 主表单显隐计算loading状态
const msVisibleFormLoading = ref<any>({}); // 里程碑表单显隐计算loading状态
const detailLoading = ref<boolean>(false);

const hide = ref(false);
const hideLeft = () => {
  hide.value = !hide.value;
};

const WorkOrderCollectionDialogRef = ref<HTMLElement | any>(null);
const WorkOrderMilestoneReplyDialogRef = ref<HTMLElement | any>(null);
const WorkOrderReplyDialogRef = ref<HTMLElement | any>(null);
const OrderViewTemplateDialogRef = ref<HTMLElement | any>(null);
const CreateWorkOrderDialogRef = ref<HTMLElement | any>(null);
const EditWorkOrderDialogRef = ref<HTMLElement | any>(null);
const OrderCancelDialogRef = ref<HTMLElement | any>(null);
const EmailCoopDialogRef = ref<HTMLElement | any>(null);
const OrderTimeLineRef = ref<HTMLElement | any>(null);
const orderDetailsWidthMode = ref<boolean>(false);
const orderTimeLineVisible = ref<boolean>(true);
const mainWidgetForm = ref<any>({});
const orderCancelData = ref<any>({});
const mainWidgetRow = ref<any>({});
const orderStateFlag = ref<string>("");
const milestoneCard = ref<any>([]);
const milestoneCardList = ref<any>([]);
const widgetData = ref<any>({});
const widgetLists = ref<any[]>([]);
const widgetBasicInfo = ref<any>({});
const orderChooseTitle = ref<string>("");
const orderChooseType = ref<string>("");
const orderChooseRowData = ref<any>({});
const detailTemplateData = ref<any>({});
const orderChooseMode = ref<string>("listMode");
const loading = ref<boolean>(false);
const orderWidgetList = ref<any>([]);
const orderLabelList = ref<any>([]);
const temporaryStorageFormVisibleObject = ref<any>({});
const mainFormVisibleObject = ref<any>({});
const msVisibleIdsList = ref<string[]>([]);
// 主工单id
const currentMainWorkOrderId = ref<string>("");
const cardViewMode = ref<any>({
  icon: "link-list",
  text: "trade_common_details"
});
const cardViewList = ref<any>([
  {
    icon: "link-list",
    text: "trade_common_details"
  },
  {
    icon: "link-card",
    text: "trade_common_card"
  },
  {
    icon: "link-table",
    text: "trade_common_table"
  }
]);
// 跟踪记录
const trackRecords = ref<any>([]);
const historyRecords = ref<any>([]);
// 定位信息
const positionInfo = ref<queryDo>();

// 订单关联信息
const orderRelationObj = ref<any>({
  relationIconType: "",
  orderRelationMap: {},
  milestoneRelationMap: {},
  workOrderRelations: []
});

const orderCountMap = ref(new Map());

const handleOrderDetailScroll = (workOrderId?: string): any => {
  const showRegionDomList = getVisibleDivs(
    document.querySelector(".order-detail-main-container")
  );
  const milestoneVisibleIds = showRegionDomList.map((dom: HTMLElement) => {
    return dom.getAttribute("data-id");
  });
  msVisibleIdsList.value = milestoneVisibleIds.filter(
    msVisible => msVisible !== "order-detail-basic-info"
  );

  const hiddenMilestoneVisibleIds = Array.from(
    document.querySelectorAll(".order-detail-region-body")
  )
    .filter(
      (dom: HTMLElement) =>
        !milestoneVisibleIds.includes(dom.getAttribute("data-id"))
    )
    .map((dom: HTMLElement) => {
      return dom.getAttribute("data-id");
    });

  handleCalculationFormVisible(milestoneVisibleIds, workOrderId);
  if (hiddenMilestoneVisibleIds.length > 0) {
    hiddenCalculationFormVisible(hiddenMilestoneVisibleIds);
  }
};

const handleTriggerWorkOrder = (visibleDataObject: any) => {
  temporaryStorageFormVisibleObject.value = Object.assign(
    cloneDeep(temporaryStorageFormVisibleObject.value),
    visibleDataObject
  );
};

const hiddenCalculationFormVisible = (ids: string[]) => {
  const worker = new Worker(
    new URL("../../webWorkers/formVisibleWorker.ts", import.meta.url),
    {
      type: "module" // 如果需要使用 ES 模块
    }
  );

  for (let msId of ids) {
    const {
      formVisibleTemplateFormConfigure,
      orderDetailData,
      orderTemplateData
    } = detailTemplateData.value;
    let workerData = JSON.stringify({
      ids: ids,
      msId: msId,
      templateData: orderTemplateData,
      detailData: orderDetailData,
      formVisibleTemplateFormConfigure: formVisibleTemplateFormConfigure,
      msPresentationItemData: getMilestoneData(msId),
      milestoneCardList: milestoneCardList.value
    });
    worker.postMessage(workerData);

    worker.onmessage = function (e) {
      const { formId, formResultData } = e.data;

      if (!!formId) {
        msVisibleFormLoading.value[formId] = false;
        temporaryStorageFormVisibleObject.value = Object.assign(
          cloneDeep(temporaryStorageFormVisibleObject.value),
          formResultData
        );
      }
    };
  }
};

const handleCalculationFormVisible = (ids: string[], workOrderId?: string) => {
  // 计算主表单显示隐藏逻辑
  if (ids.includes("order-detail-basic-info")) {
    const operationalFactorData = getEntireFormData("", "");
    const mainFormId = mainWidgetRow.value.id;
    const mainFormVisibleFormList =
      operationalFactorData.formVisibleTemplateFormConfigure.filter(
        relate => relate.formId === mainFormId
      );
    if (mainFormVisibleFormList.length > 0) {
      mainFormLoading.value = true;
      if (Object.keys(mainFormVisibleObject.value).length === 0) {
        let formVisibleTrendsObject = {};
        for (let visibleTemplateItem of mainFormVisibleFormList) {
          const formVisibleBoolean = ExecuteFormVisibleExpression(
            visibleTemplateItem.conditions,
            operationalFactorData
          );
          for (let widgetId of visibleTemplateItem.widgetIdList) {
            formVisibleTrendsObject[widgetId] = formVisibleBoolean;
          }
        }

        mainFormVisibleObject.value = formVisibleTrendsObject;
        mainFormLoading.value = false;
      } else {
        return;
      }
    }
  }
  const msPresentationList = milestoneCardList.value.filter(ms =>
    ids.includes(ms.msId)
  );
  const formVisibleData = cloneDeep(temporaryStorageFormVisibleObject.value);
  let formVisibleObject: any = {};
  for (let msPresentationItem of msPresentationList) {
    const msTemplateData: any = getMilestoneData(msPresentationItem.msId);
    if (msPresentationItem.msReplyId) {
      if (!!formVisibleData.hasOwnProperty(msPresentationItem.msReplyId)) {
        continue;
      }

      // 里程碑批复
      const operationalFactorData = getEntireFormData(
        msPresentationItem.msId,
        ""
      );
      const msReplyVisibleFormList =
        operationalFactorData.formVisibleTemplateFormConfigure.filter(
          relate => relate.formId === msTemplateData.replyForm.id
        );

      if (msReplyVisibleFormList.length > 0) {
        let formVisibleTrendsObject = {};
        for (let visibleTemplateItem of msReplyVisibleFormList) {
          const formVisibleBoolean = ExecuteFormVisibleExpression(
            visibleTemplateItem.conditions,
            operationalFactorData
          );
          for (let widgetId of visibleTemplateItem.widgetIdList) {
            formVisibleTrendsObject[widgetId] = formVisibleBoolean;
          }
        }

        let trendFormObject: any = {
          [msPresentationItem.msReplyId]: formVisibleTrendsObject
        };
        formVisibleObject = Object.assign(formVisibleData, trendFormObject);
        msVisibleFormLoading.value[msPresentationItem.msReplyId] = false;
      }
    } else {
      if (msPresentationItem.workOrderGroupList.length === 0) {
        continue;
      }
      // 工单批复
      const formId = workOrderId
        ? workOrderId
        : msPresentationItem.workOrderGroupList?.[0]?.workOrderId;

      if (!!formVisibleData.hasOwnProperty(formId)) {
        continue;
      }

      const operationalFactorData = getEntireFormData(
        msPresentationItem.msId,
        formId
      );

      const bindFormIdList = msTemplateData.workOrderReplyForm
        ? [...msTemplateData.formList, msTemplateData.workOrderReplyForm].map(
            form => form.id
          )
        : msTemplateData.formList.map(form => form.id);

      const operationFormVisibleList =
        operationalFactorData.formVisibleTemplateFormConfigure.filter(relate =>
          bindFormIdList.includes(relate.formId)
        );

      if (operationFormVisibleList.length > 0) {
        let formVisibleTrendsObject = {};
        for (let visibleTemplateItem of operationFormVisibleList) {
          const formVisibleBoolean = ExecuteFormVisibleExpression(
            visibleTemplateItem.conditions,
            operationalFactorData
          );
          for (let widgetId of visibleTemplateItem.widgetIdList) {
            formVisibleTrendsObject[widgetId] = formVisibleBoolean;
          }
        }

        let trendFormObject: any = {
          [formId]: formVisibleTrendsObject
        };

        formVisibleObject = {
          ...formVisibleData,
          ...trendFormObject
        };

        msVisibleFormLoading.value[formId] = false;
      }
    }
  }
  temporaryStorageFormVisibleObject.value = formVisibleObject;
};

const handleCommand = (): void => {};

const getMilestoneData = (msId: string): void => {
  return milestoneCard.value.find(milestoneItem => milestoneItem.msId === msId);
};

const initRouter = (): void => {
  const { orderMark } = route.query;
  const params = route.query;

  useMultiTagsStoreHook().handleTags("push", {
    path: `/orderDetail`,
    name: "orderDetail",
    query: params,
    meta: {
      title: {
        zh: `${orderMark}`
      },
      dynamicLevel: 3
    }
  });
  // 路由跳转
  router.push({ name: "orderDetail", query: params });
};

const handleChangeSwitch = () => {
  storageLocal().setItem("user-view-mode", orderDetailsWidthMode.value);
};

const handleUpdateMainForm = () => {
  mainFormVisibleObject.value = {};
  handleCreateWorkOrderSuccess("");
};

const handleCreateWorkOrderSuccess = (workOrderId?: string): void => {
  widgetLists.value = [];
  initOrderDetail(workOrderId);
};

const handleAddWorkOrder = (milestoneData: any): void => {
  const { sccsId } = route.query;

  CreateWorkOrderDialogRef.value.open(
    { msId: milestoneData.msId, msName: milestoneData.name },
    { widgetForm: mainWidgetForm.value, widgetData: orderWidgetList.value },
    sccsId
  );
};

const handleTrigger = debounce(
  (type?: any, milestoneData?: any, workOrderData?: any) => {
    const { sccsId, orderId } = route.query;
    if (type === "addTaskOrder") {
      CreateWorkOrderDialogRef.value.open(
        milestoneData,
        { widgetForm: mainWidgetForm.value, widgetData: orderWidgetList.value },
        sccsId
      );
    } else if (type === "confirmMilestone") {
      // 完成里程碑
      ElMessageBox.confirm(
        t("trade_order_confrimMilestoneTip"),
        t("trade_order_confrimMilestone"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(async () => {
        const { code } = await complateMilestone({
          sccsId: sccsId,
          orderId: orderId,
          msId: milestoneData.msId
        });
        operateAfter(code);
      });
    } else if (type === "restartMilestone") {
      // 重启里程碑
      ElMessageBox.confirm(
        t("trade_order_restartMilestoneTip"),
        t("trade_order_restartMilestone"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(async () => {
        const { code } = await restartMilestone({
          sccsId: sccsId,
          orderId: orderId,
          msId: milestoneData.msId
        });
        operateAfter(code);
      });
    } else if (type === "cancelMilestone") {
      // 取消里程碑
      ElMessageBox.confirm(
        t("trade_order_cancelMilestoneTip"),
        t("trade_order_cancelMilestone"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(async () => {
        const { code } = await cancelMilestone({
          sccsId: sccsId,
          orderId: orderId,
          msId: milestoneData.msId
        });
        operateAfter(code);
      });
    } else if (type === "orderCompletion") {
      // 完成订单
      ElMessageBox.confirm(
        t("trade_order_completionTip"),
        t("trade_order_completion"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(async () => {
        const { code } = await completeTradeOrder({
          orderId: orderId as string,
          sccsId: sccsId as string
        });
        operateAfter(code);
      });
    } else if (type === "orderCancel") {
      // 取消订单
      OrderCancelDialogRef.value.open();
    } else if (type === "orderRestart") {
      // 重启订单
      ElMessageBox.confirm(
        t("trade_order_restartTip"),
        t("trade_order_restart"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(async () => {
        const { code } = await restartTradeOrder({
          sccsId: sccsId as string,
          orderId: orderId as string
        });
        operateAfter(code);
      });
    } else if (type === "deleteOrder") {
      // 删除订单
      ElMessageBox.confirm(
        t("trade_order_deleteOrderTip"),
        t("trade_order_delete"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-class",
          cancelButtonClass: "cancel-message-btn-class",
          customClass: "order_confirm_message_box",
          type: "error",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(async () => {
        const { code } = await deleteTradeOrder({
          id: orderId as string,
          sccsId: sccsId as string
        });
        if (code == 0) {
          ElMessage({
            message: t("trade_common_updateSuccess"),
            type: "success"
          });
          deleteThatMenu({ path: route.path, meta: route.meta }, route, router);
        }
        // operateAfter(code);
      });
    } else if (type === "stopCollect") {
      // 终止采集
      ElMessageBox.confirm(
        t("trade_order_stopCollectTip"),
        t("trade_order_stopCollect"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-class",
          cancelButtonClass: "cancel-message-btn-class",
          customClass: "order_confirm_message_box",
          type: "error",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(async () => {
        const { code } = await stopWorkOrderCollection({
          id: workOrderData.workOrderId,
          sccsId: sccsId,
          orderId: orderId,
          msId: milestoneData.msId
        });
        operateAfter(code);
      });
    } else if (type === "deleteWorkOrder") {
      // 删除工单
      const removeTip = `${t("trade_order_removeWorkOrderTip")}「${workOrderData.workOrderName}」？`;
      ElMessageBox.confirm(removeTip, t("trade_order_removeWorkOrder"), {
        confirmButtonText: t("trade_common_confirm"),
        cancelButtonText: t("trade_common_cancel"),
        confirmButtonClass: "confrim-message-btn-class",
        cancelButtonClass: "cancel-message-btn-class",
        customClass: "order_confirm_message_box",
        type: "error",
        icon: markRaw(WarningFilled),
        center: true
      }).then(async () => {
        const { code } = await deleteWorkOrder({
          id: workOrderData.workOrderId,
          sccsId: sccsId,
          orderId: orderId,
          msId: milestoneData.msId
        });
        operateAfter(code);
      });
    } else if (type === "approvalAssignPerson") {
      // 修改sccs协作团队工单批复人
      orderChooseTitle.value = "trade_order_updateWorkOrderApprover";
      orderChooseType.value = type;
      orderChooseMode.value = "listMode";
      orderChooseRowData.value = workOrderData;

      const userInfo = storageLocal().getItem("user-info") as any;
      const replyUserLists = workOrderData.replyUserList.filter(
        user => user.teamId === userInfo.latestLoginTeamId
      );

      const replyUserList = replyUserLists.map(replyUser => {
        return Object.assign(replyUser, {
          teamMemberId: replyUser.teamMemberId,
          avatar: replyUser.userAvatar,
          username: replyUser.userName,
          coop: replyUser.coopTeamUser
        });
      });
      chooseProcessorRef.value.open(replyUserList, "ASSIGN_PROCESSOR");
    } else if (type === "collectorsPerson") {
      // 修改sccs协作团队工单采集人
      orderChooseTitle.value = "trade_order_updateWorkOrderCollectors";
      orderChooseType.value = type;
      orderChooseMode.value = "listMode";
      orderChooseRowData.value = workOrderData;

      const userInfo = storageLocal().getItem("user-info") as any;
      const editUserLists = workOrderData.editUserList.filter(
        user => user.teamId === userInfo.latestLoginTeamId
      );

      const editUserList = editUserLists.map(replyUser => {
        return Object.assign(replyUser, {
          teamMemberId: replyUser.teamMemberId,
          avatar: replyUser.userAvatar,
          username: replyUser.userName,
          coop: replyUser.coopTeamUser
        });
      });
      chooseProcessorRef.value.open(editUserList, "ASSIGN_PROCESSOR");
    } else if (type === "milestoneAssign") {
      // 修改里程碑批复人
      orderChooseTitle.value = "trade_order_updateMilestoneApprover";
      orderChooseType.value = type;
      orderChooseMode.value = "listMode";
      orderChooseRowData.value = milestoneData;
      chooseProcessorRef.value.open([], "ASSIGN_PROCESSOR");
    } else if (type === "updateMilestonePersonnel") {
      // 修改里程碑处理人
      orderChooseTitle.value = "trade_order_updateMileStoneCollectors";
      orderChooseType.value = type;
      orderChooseMode.value = "tabMode";
      orderChooseRowData.value = milestoneData;
      const msReplyUserList = milestoneData.msReplyUser.map(replyUser => {
        return Object.assign(replyUser, {
          teamMemberId: replyUser.teamMemberId,
          avatar: replyUser.userAvatar,
          username: replyUser.userName,
          coop: replyUser.coopTeamUser
        });
      });
      chooseProcessorRef.value.open(msReplyUserList, "ASSIGN_PROCESSOR");
    } else if (type === "updateMilestoneCoopPersonnel") {
      // 修改里程碑处理人
      orderChooseTitle.value = "trade_order_updateMileStoneCollectors";
      orderChooseType.value = type;
      orderChooseMode.value = "listMode";
      orderChooseRowData.value = milestoneData;

      const userInfo = storageLocal().getItem("user-info") as any;
      const editUserLists = milestoneData.msReplyUser.filter(
        user => user.teamId === userInfo.latestLoginTeamId
      );

      const msReplyUserList = editUserLists.map(replyUser => {
        return Object.assign(replyUser, {
          teamMemberId: replyUser.teamMemberId,
          avatar: replyUser.userAvatar,
          username: replyUser.userName,
          coop: replyUser.coopTeamUser
        });
      });

      chooseProcessorRef.value.open(msReplyUserList, "ASSIGN_PROCESSOR");
    } else if (type === "approvalDistribute") {
      // 工单批复指派
      orderChooseTitle.value = "trade_order_AssignWorkOrderApproval";
      orderChooseType.value = type;
      orderChooseMode.value = "listMode";
      orderChooseRowData.value = workOrderData;
      chooseProcessorRef.value.open([], "ASSIGN_PROCESSOR");
    } else if (type === "collectDistribute") {
      // 工单采集指派
      orderChooseTitle.value = "trade_order_WorkOrderCollectionAssignment";
      orderChooseType.value = type;
      orderChooseMode.value = "listMode";
      orderChooseRowData.value = workOrderData;
      chooseProcessorRef.value.open([], "ASSIGN_PROCESSOR");
    } else if (type === "modifyWorkOrderProcessor") {
      // 修改工单批复人
      orderChooseTitle.value = "trade_order_updateWorkOrderApprover";
      orderChooseType.value = type;
      orderChooseMode.value = "tabMode";
      orderChooseRowData.value = workOrderData;
      const msReplyUserList = workOrderData.replyUserList.map(replyUser => {
        return Object.assign(replyUser, {
          teamMemberId: replyUser.teamMemberId,
          avatar: replyUser.userAvatar,
          username: replyUser.userName,
          coop: replyUser.coopTeamUser
        });
      });
      // chooseProcessorRef.value.open(msReplyUserList, "ASSIGN_PROCESSOR");
      chooseProcessorRef.value.open(msReplyUserList, "UPDATE_PROCESSOR"); // 修改工单批复人需要可以选择主团队成员
    } else if (type === "orderHistory") {
      historyDialogType.value = "MAIN_WORK_ORDER";
      // 获取历史记录
      getWorkOrderHistoryList({
        workOrderId: currentMainWorkOrderId.value
      }).then(res => {
        if (res.code === 0) {
          historyRecords.value = res.data;
          OrderHistoryRecordDialogRef.value.open(historyRecords.value, 0);
        }
      });
    } else if (type === "orderTrackRecord") {
      // 获取跟踪记录
      getOrderDynamicList({ orderId: orderId }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          trackRecords.value = res.data;
          OrderTrackRecordDialogRef.value.open();
        }
      });
      // 订单跟踪记录
    } else if (type === "workOrderCollect") {
      // 工单采集
      WorkOrderCollectionDialogRef.value.open(workOrderData, {
        widgetForm: mainWidgetForm.value,
        widgetData: orderWidgetList.value
      });
    } else if (type === "milestoneReply" || type === "milestoneDynamic") {
      // 里程碑批复
      WorkOrderMilestoneReplyDialogRef.value.open(
        milestoneData,
        widgetData.value,
        type
      );
    } else if (type === "workOrderReply" || type === "workOrderDynamic") {
      // 工单批复
      WorkOrderReplyDialogRef.value.open(
        workOrderData,
        { widgetForm: mainWidgetForm.value, widgetData: orderWidgetList.value },
        type
      );
    } else if (type === "editOrder") {
      // 编辑订单
      const { orderId } = route.query;
      OrderViewTemplateDialogRef.value.open(
        // orderWidgetList.value,
        // milestoneCardList.value,
        orderId
      );
    } else if (type === "modifyProcessor") {
      // 内部sccs修改处理人
      EditWorkOrderDialogRef.value.open(sccsId, workOrderData, {
        widgetForm: mainWidgetForm.value,
        widgetData: orderWidgetList.value
      });
    } else if (type === "emailCollaborate") {
      // 邮件协作
      const { workOrderId, msId, workOrderName } = workOrderData;
      const btnVisible = getEmailCoopRole(milestoneData, workOrderData);
      EmailCoopDialogRef.value.open(
        workOrderId,
        msId,
        workOrderName,
        btnVisible
      );
    } else if (
      type === "addMutuallyUseWorkOrder" ||
      type === "workOrderSharedIn" ||
      type === "milesStoneSharedIn" ||
      type === "cloneMilesStoneFromOtherOrder"
    ) {
      //互用工单
      const { sccsId, templateId, coopTeamMark, sccsName, orderId, orderMark } =
        route.query;
      const { workOrderId, workOrderName } = workOrderData ? workOrderData : {};
      const typeMap = new Map<string, string>();
      typeMap.set("addMutuallyUseWorkOrder", "WORK_ORDER_CLONE");
      typeMap.set("workOrderSharedIn", "WORK_ORDER_CLONE");
      typeMap.set("milesStoneSharedIn", "MS_CLONE");
      typeMap.set("cloneMilesStoneFromOtherOrder", "MS_CLONE");

      // // 路由跳转
      const parameter = {
        sccsId: sccsId,
        templateId: templateId,
        coopTeamMark: coopTeamMark,
        sccsName: sccsName,
        type,
        typeValue: typeMap.get(type),
        orderId,
        orderMark,
        msId: milestoneData.msId,
        msName: milestoneData.msName,
        workOrderId: workOrderId,
        workOrderName: workOrderName,
        todo: type === "cloneMilesStoneFromOtherOrder" ? "0" : "1"
      };
      // // 路由跳转
      router.push({ name: "orderManage", query: parameter });
    }
  },
  300,
  {
    leading: false,
    trailing: true
  }
);

const getEmailCoopRole = (milestoneData, rowData) => {
  if (
    milestoneData.status === "CANCEL" ||
    milestoneData.status === "COMPLETE" ||
    rowData.stopped
  ) {
    return false;
  }
  if (rowData.replied) {
    return false;
  }
  const { latestLoginTeamMemberId } = storageLocal().getItem(
    "user-info"
  ) as any;
  const index = rowData["editUserList"].findIndex(
    msReplyUser => msReplyUser.teamMemberId === latestLoginTeamMemberId
  );

  const teamRoleLists = storageLocal().getItem(
    `${rowData.orderId}_userTeamRole`
  )[rowData.msId];
  const { coopTeamMark } = route.query;
  return index !== -1
    ? true
    : coopTeamMark === "1" &&
        teamRoleLists.includes("assign_work_order_collection");
};

const handleChooseConfirm = async (
  chooseTeamMemberIds: string[],
  triggerType: string
): Promise<void> => {
  const { sccsId, orderId } = route.query;
  //todo 代码简化
  if (triggerType === "milestoneAssign") {
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: orderChooseRowData.value.msId,
      workOrderId: orderChooseRowData.value.msReplyId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "updateMilestonePersonnel") {
    const { sccsId, orderId } = route.query;
    const { msId, msReplyId } = orderChooseRowData.value;
    const { code } = await updateMilestoneReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: msReplyId,
      milestoneReplyAssignedUserVOList: chooseTeamMemberIds
    });
    operateAfter(code);
  } else if (triggerType === "updateMilestoneCoopPersonnel") {
    const { sccsId, orderId } = route.query;
    const { msId, msReplyId } = orderChooseRowData.value;
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneWorkOrderCoopTeamReplier({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: msReplyId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "approvalAssignPerson") {
    // 修改sccs协作团队工单批复人
    const { msId, workOrderReplyId } = orderChooseRowData.value;
    let chooseTeamMemberId = [];

    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await updateCoolTeamReplier({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: workOrderReplyId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "collectorsPerson") {
    // 修改sccs协作团队工单采集人
    const { msId, workOrderId } = orderChooseRowData.value;
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await updateCoolTeamCollector({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "collectDistribute") {
    // 工单采集指派
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneCollect({
      sccsId: sccsId,
      orderId: orderId,
      msId: orderChooseRowData.value.msId,
      workOrderId: orderChooseRowData.value.workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "approvalDistribute") {
    // 工单批复指派
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneWorkOrderReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: orderChooseRowData.value.msId,
      workOrderId: orderChooseRowData.value.workOrderReplyId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "modifyWorkOrderProcessor") {
    //修改工单批复人
    const { sccsId, orderId } = route.query;
    const { msId, workOrderReplyId } = orderChooseRowData.value;
    const { code } = await updateWorkOrderReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: workOrderReplyId,
      workOrderReplyAssignedUserVOList: chooseTeamMemberIds
    });
    operateAfter(code);
  }
};

initRouter();

const operateAfter = code => {
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    initOrderDetail();
  }
};
const upDataCount = (msId, id, type) => {
  // 获取当前模块的动态数量
  // 根据当前返回的id查找对应的里程碑或者工单数据
  // 找到对应的数据后，判断是否存在cloneSourceId
  // 如果存在cloneSourceId，就根据cloneSourceId去获取动态数量
  // 如果不存在cloneSourceId，就根据workOrderId去获取动态数量
  // 将请求结果解析后，赋值给orderCountMap.value

  const msData = milestoneCardList.value.find(ms => ms.msId === msId);
  if (!msData) {
    return;
  }
  let params = [];
  if (type === "MS_REPLY_DYNAMIC") {
    if (msData.cloneSourceId) {
      params.push({
        workOrderId: msData.cloneSourceId,
        dataTypeEnum: type
      });
    } else if (!msData.cloneSourceId && msData.msReplyId) {
      params.push({
        workOrderId: msData.msReplyId,
        dataTypeEnum: type
      });
    } else {
      if (msData.workOrderGroupList.length > 0) {
        params.push({
          workOrderId: msData.workOrderGroupList[0].cloneSourceId
            ? msData.workOrderGroupList[0].cloneSourceId
            : msData.workOrderGroupList[0].workOrderId,
          dataTypeEnum: "WORK_ORDER_DYNAMIC"
        });
      }
    }
  } else if (type === "WORK_ORDER_DYNAMIC") {
    const worderOrderData = msData.workOrderGroupList?.find(
      e => e.workOrderId === id
    );
    if (worderOrderData.cloneSourceId) {
      params.push({
        workOrderId: worderOrderData.cloneSourceId,
        dataTypeEnum: type
      });
    } else if (!worderOrderData.cloneSourceId && worderOrderData.workOrderId) {
      params.push({
        workOrderId: worderOrderData.workOrderId,
        dataTypeEnum: type
      });
    }
  }

  params.push({
    workOrderId: id,
    dataTypeEnum: "COOP_EMAIL"
  });

  params.length > 0 &&
    getWorkOrderCount(params).then(res => {
      if (res.code === 0) {
        // 返回的数据解析成Map对象
        // 解析成Map对象后，赋值给orderCountMap.value
        if (Object.keys(res.data).length > 0) {
          Object.keys(res.data).forEach(key => {
            orderCountMap.value.set(key, res.data[key]);
          });
        }
      }
    });
};

// 获取当前订单的动态数据
const getCount = async () => {
  const allMsData = milestoneCardList.value;
  let params = [];
  allMsData.forEach(msData => {
    if (msData.cloneSourceId) {
      params.push({
        workOrderId: msData.cloneSourceId,
        dataTypeEnum: "MS_REPLY_DYNAMIC"
      });
    } else if (!msData.cloneSourceId && msData.msReplyId) {
      params.push({
        workOrderId: msData.msReplyId,
        dataTypeEnum: "MS_REPLY_DYNAMIC"
      });
    } else {
      if (msData.workOrderGroupList.length > 0) {
        params.push({
          workOrderId: msData.workOrderGroupList[0].cloneSourceId
            ? msData.workOrderGroupList[0].cloneSourceId
            : msData.workOrderGroupList[0].workOrderId,
          dataTypeEnum: "WORK_ORDER_DYNAMIC"
        });

        params.push({
          workOrderId: msData.workOrderGroupList[0].cloneSourceId
            ? msData.workOrderGroupList[0].cloneSourceId
            : msData.workOrderGroupList[0].workOrderId,
          dataTypeEnum: "COOP_EMAIL"
        });
      }
    }
  });
  const res = await getWorkOrderCount(params);
  if (res.code === 0) {
    // 返回的数据解析成Map对象
    // 解析成Map对象后，赋值给orderCountMap.value
    if (Object.keys(res.data).length > 0) {
      Object.keys(res.data).forEach(key => {
        orderCountMap.value.set(key, res.data[key]);
      });

      emitter.emit("handleWorkOrderEmailCoopCount", res.data);
    }
  }
};

const initOrderDetail = (workOrderId?: string): void => {
  detailLoading.value = true;
  const { sccsId, orderId } = route.query as any;
  getDefaultConfigWidgetData(orderId).then(
    ({
      orderTemplateData,
      orderDetailData,
      formVisibleTemplateFormConfigure
    }: any) => {
      Promise.all([
        getOrderMsBasicInfo({
          orderId: orderId
        }),
        getFindWidgetList({
          sccsId: sccsId
        }),
        getOrderRelationInfo({
          idList: [orderId]
        }),
        findOrderWorkOrderRelationMap({
          orderId: orderId
        })
      ]).then(async (data: any) => {
        const { milestoneList, mainForm } = orderTemplateData;
        detailTemplateData.value = {
          orderTemplateData: orderTemplateData,
          orderDetailData: orderDetailData,
          formVisibleTemplateFormConfigure: formVisibleTemplateFormConfigure
        };
        let mainFormWidgetList = [];

        mainForm.widgetJsonList.forEach(widget => {
          if (widget.type === "DrCard") {
            widget.children.forEach(child => {
              if (child.children) {
                const childList = child.children.filter(
                  childWidget => !data[1].data.includes(childWidget._fc_id)
                );
                child.children = childList;
              }
            });
            mainFormWidgetList.push(widget);
          } else {
            if (!data[1].data.includes(widget._fc_id)) {
              mainFormWidgetList.push(widget);
            }
          }
        });
        mainWidgetForm.value = mainFormWidgetList;
        mainWidgetRow.value = mainForm;

        orderLabelList.value = orderDetailData.labelList.filter(
          label => !data[1].data.includes(label.labelId)
        );
        const {
          widgetList,
          milestoneGroupList,
          subWidgetMap,
          linkedReferenceMap,
          cancelReasonList,
          cancelRemark,
          orderState,
          mainWorkOrderId,
          relationIconType
        } = orderDetailData;

        // 当前订单是否为翻单
        // 初始化订单标识
        orderRelationObj.value.orderRelationMap = {};
        orderRelationObj.value.milestoneRelationMap = {};
        const orderMsRelations = data[2].data as object;
        const orderRelation = orderMsRelations[route.query.orderId as string];
        for (let key in orderRelation) {
          if (
            [
              "ORDER_BY_CLONE",
              "ORDER_CLONE",
              "ORDER_SAME_SCCS_RELATION",
              "ORDER_CROSS_SCCS_RELATION"
            ].includes(key)
          ) {
            orderRelationObj.value.orderRelationMap[key] = orderRelation[key];
          } else if (["MILESTONE_CLONE", "MILESTONE_BY_CLONE"].includes(key)) {
            orderRelationObj.value.milestoneRelationMap[key] =
              orderRelation[key];
          }
        }
        orderRelationObj.value.relationIconType = relationIconType;
        orderRelationObj.value.workOrderRelations = data[3].data;
        // 处理子表数据
        let widgetFormData = TransformSubmitDataStructure(
          widgetList,
          subWidgetMap,
          linkedReferenceMap
        );
        // 详情接口增加主工单ID
        currentMainWorkOrderId.value = mainWorkOrderId;
        orderWidgetList.value = widgetFormData;
        orderStateFlag.value = orderState;
        orderCancelData.value = {
          reasonList: cancelReasonList,
          cancelRemark: cancelRemark
        };
        milestoneCardList.value = milestoneGroupList;

        // 初始化动态数据
        milestoneCard.value = milestoneList;
        let milestoneVisibleForm = {};
        for (let msData of milestoneGroupList) {
          const msTemplateData: any = getMilestoneData(msData.msId);
          if (!!msData.msReplyId) {
            const msReplyVisibleFormList =
              formVisibleTemplateFormConfigure.filter(
                relate => relate.formId === msTemplateData.replyForm.id
              );
            milestoneVisibleForm[msData.msReplyId] =
              msReplyVisibleFormList.length !== 0;
          } else {
            const bindFormIdList = msTemplateData.workOrderReplyForm
              ? [
                  ...msTemplateData.formList,
                  msTemplateData.workOrderReplyForm
                ].map(form => form.id)
              : msTemplateData.formList.map(form => form.id);

            const operationFormVisibleList =
              formVisibleTemplateFormConfigure.filter(relate =>
                bindFormIdList.includes(relate.formId)
              );

            if (msData.workOrderGroupList.length > 0) {
              milestoneVisibleForm[msData.workOrderGroupList[0].workOrderId] =
                operationFormVisibleList.length !== 0;
            }
          }
        }
        msVisibleFormLoading.value = milestoneVisibleForm;

        if (widgetList) {
          widgetList.forEach(widget => {
            widgetData.value[widget.widgetId] = widget["label"];
          });
        }
        const {
          createUser,
          createTime,
          updateTime,
          updateUser,
          msBaseInfoList
        } = data[0].data;
        widgetLists.value = [];

        widgetBasicInfo.value = {
          state: orderDetailData.orderState,
          name: t("trade_order_basicInfo"),
          createTime: createTime,
          createUser: createUser,
          updateTime: updateTime,
          updateUser: updateUser
        };
        widgetLists.value.push(...msBaseInfoList);
        let resp: any = {};
        const { sccsId, orderId, coopTeamMark } = route.query;
        if (coopTeamMark === "2") {
          resp = await getTradeSccsRolePermission({
            sccsId: sccsId,
            orderIdList: [orderId]
          });
        } else {
          resp = await getTradeCoopSccsRolePermission({
            sccsId: sccsId,
            orderIdList: [orderId]
          });
        }
        if (resp.code === 0) {
          //@ts-ignore
          storageLocal().setItem(`${orderId}_userTeamRole`, resp.data[orderId]);
        }
        if (positionInfo.value) {
          // setPosition(positionInfo.value);
        }
        detailLoading.value = false;

        temporaryStorageFormVisibleObject.value = {};
        handleOrderDetailScroll();
      });
    }
  );
};

onMounted(() => {
  initOrderDetail();
  // 判断是否存在浏览器会话信息
  // 如果有就定位，并且清除会话信息
  if (sessionStorage.getItem("orderDetailPagePositionInfo")) {
    let queryDo = JSON.parse(
      sessionStorage.getItem("orderDetailPagePositionInfo")
    );
    positionInfo.value = queryDo;
  }
  const userViewBool = storageLocal().getItem("user-view-mode");
  orderDetailsWidthMode.value = !!userViewBool;

  if (useAppStoreHook().viewportSize.width <= 1366) {
    orderDetailsWidthMode.value = true;
  }

  emitter.on("handleSwitchDetailLoading", (bool: boolean) => {
    loading.value = bool;
  });

  // 监听数据加载完成，并根据 URL 参数模拟调用 handleTrigger
  useOnceWhen(
    () => [
      milestoneCardList.value.length > 0,
      route.query.msId || route.query.milestoneId
    ],
    ([milestoneLoaded, msId]) => milestoneLoaded && !!msId,
    () => {
      const { workOrderId, action } = route.query as any;
      const msId = route.query.msId || route.query.milestoneId;
      if (!msId) return;

      const milestoneData = milestoneCardList.value.find(
        (item: any) => item.msId === msId
      );
      if (!milestoneData) return;

      activeWorkOrderId.value = workOrderId;

      if (!action) return;
      let selector = `[data-ms-id="${msId}"][data-action="${action}"]`;

      if (workOrderId) {
        const workOrderData = milestoneData.workOrderGroupList.find(
          (item: any) => item.workOrderId === workOrderId
        );
        if (!workOrderData) return;
        selector = `[data-ms-id="${msId}"][data-work-order-id="${workOrderId}"][data-action="${action}"]`;
      }
      nextTick(() => {
        const targetBtn = document.querySelector(
          selector
        ) as HTMLElement | null;
        if (targetBtn?.click) {
          targetBtn.click();
        }
      });

      sessionStorage.removeItem("orderDetailPagePositionInfo");
    }
  );
});
</script>
<style lang="scss" scoped>
@use "./components/style/index.scss";
</style>
