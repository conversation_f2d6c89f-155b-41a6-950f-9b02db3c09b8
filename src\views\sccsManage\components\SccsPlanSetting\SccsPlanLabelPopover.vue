<template>
  <div class="sccs-plan-input-body">
    <el-input
      v-model="planForm.labelName"
      class="plan-input"
      filterable
      clearable
      :maxlength="maxlength"
      style="width: 230px"
      @change="handlePlanFormLabelName"
      @clear="handleClearLabelName"
    />
    <el-popover
      placement="right"
      :width="400"
      :show-arrow="false"
      trigger="click"
      :visible="popoverVisible"
    >
      <template #reference>
        <i
          class="iconfont link-system-language plan-icon"
          :class="{
            'active-plan-input': planForm.labelNameZh || planForm.labelNameEn
          }"
          @click="handleOpenTagDialog"
        />
      </template>
      <div class="sccs-setting-plan-tip">
        {{ t("trade_sccs_plantagTip") }}
      </div>
      <el-form-item :label="t('trade_common_zhCN')" label-position="top">
        <el-input v-model="planForm.labelNameZh" clearable />
      </el-form-item>
      <el-form-item :label="t('trade_common_enUS')" label-position="top">
        <el-input v-model="planForm.labelNameEn" clearable />
      </el-form-item>
      <el-form-item
        label-position="top"
        style="display: flex; justify-self: end; margin-bottom: 0"
      >
        <el-button plain @click="handleReset">
          {{ t("trade_common_cancel") }}
        </el-button>
        <el-button type="primary" color="#0070D2" @click="handleTagChange">
          {{ t("trade_common_confirm") }}
        </el-button>
      </el-form-item>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { useI18n } from "vue-i18n";

interface PlanProp {
  labelName: string;
  labelNameZh: string;
  labelNameEn: string;
}

const { t } = useI18n();
const emit = defineEmits(["handleSelectTag", "handleClearLabelName"]);

const props = defineProps({
  defaultPlanForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  maxlength: {
    type: (String || Number) as PropType<any>,
    default: ""
  }
});

const popoverVisible = ref<boolean>(false);
let planForm = ref<PlanProp>({
  labelName: "",
  labelNameZh: "",
  labelNameEn: ""
});

const handleOpenTagDialog = (): void => {
  popoverVisible.value = true;
};

const handleTagChange = (): void => {
  popoverVisible.value = false;
  emit("handleSelectTag", planForm.value);
};

const handleReset = (): void => {
  planForm.value.labelNameZh = "";
  planForm.value.labelNameEn = "";
  popoverVisible.value = false;
};

const handlePlanFormLabelName = () => {
  emit("handleSelectTag", planForm.value);
};

// 增加清楚输入框值与弹窗的联动
const handleClearLabelName = () => {
  planForm.value.labelName = "";
  planForm.value.labelNameZh = "";
  planForm.value.labelNameEn = "";
  emit("handleClearLabelName", planForm.value);
};

watchEffect(() => {
  if (props.defaultPlanForm) {
    planForm.value = props.defaultPlanForm;
  }
});
</script>
<style lang="scss" scoped>
.sccs-plan-input-body {
  display: flex;
  align-items: center;
  height: 32px;
  border: 1px solid #e0dfe4;
  border-radius: 4px;

  .sccs-setting-plan-tip {
    margin-bottom: 14px;
    font-size: 14px;
    line-height: 20px;
    color: #8c8c8c;
  }

  .plan-input {
    height: 30px;

    ::v-deep(.el-input__wrapper) {
      padding-right: 0;
      box-shadow: none;
    }
  }

  .sccs-plan-icon-body {
    width: 30px;
  }

  .plan-icon {
    display: inline-block;
    width: 30px;
    height: 30px;
    font-size: 14px;
    line-height: 30px;
    color: #979797;
    text-align: center;
    cursor: pointer;

    &.active-plan-input {
      color: #0070d2;
      background: linear-gradient(to left, #ccecff, #eef8ff);
    }
  }
}
</style>
