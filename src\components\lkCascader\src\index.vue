<template>
  <div ref="CascaderRef" class="cascader-panel-container">
    <div class="cascader-panel-body">
      <el-input
        v-model="input"
        :placeholder="t(placeholder)"
        :suffix-icon="!cascaderVisible ? ArrowDown : ArrowUp"
        :class="{
          'delete-input': widgetDeleted
        }"
        @click="handleFocus()"
      />
    </div>

    <Transition
      leave-active-class="animate__animated animate__fadeDownUp animate__faster"
      enter-active-class="animate__animated animate__fadeIn animate__faster"
    >
      <el-cascader-panel
        v-show="cascaderVisible"
        ref="CascaderPanelRef"
        v-model="cascaderOptions"
        class="cascader-panel-popover"
        style="width: fit-content"
        :options="cascaderOptionList"
        @change="handleCascaderChange"
      />
    </Transition>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";
import { onClickOutside } from "@vueuse/core";
import { cloneDeep } from "@pureadmin/utils";

const props = defineProps({
  options: {
    type: Array as PropType<any>,
    default: () => []
  },
  placeholder: {
    type: String as PropType<string>,
    default: "trade_common_selectText"
  }
});

const emit = defineEmits(["handleChange"]);

const { t } = useI18n();
const input = ref<string>("");
const cascaderOptions = ref<any>([]);
const CascaderRef = ref<HTMLElement | any>(null);
const CascaderPanelRef = ref<HTMLElement | any>(null);
const cascaderVisible = ref<boolean>(false);
const widgetDeleted = ref<boolean>(false);
const CascaderPanelData = ref<any>([]);
const cascaderOptionList = ref<any>([]);

onClickOutside(CascaderRef, (event: any) => {
  // nextTick(() => {
  //   input.value = CascaderPanelRef.value.getCheckedNodes()[0].data.label;
  //   widgetDeleted.value =
  //     CascaderPanelRef.value.getCheckedNodes()[0].data.deleted;
  //   CascaderPanelData.value = CascaderPanelRef.value.getCheckedNodes();
  //   cascaderVisible.value = false;
  //   emit("handleChange", CascaderPanelData.value, cascaderOptions.value);
  // });
});

const handleFocus = () => {
  cascaderVisible.value = !cascaderVisible.value;
};

const handleCascaderChange = () => {
  input.value = CascaderPanelRef.value.getCheckedNodes()[0].data.label;
  widgetDeleted.value =
    CascaderPanelRef.value.getCheckedNodes()[0].data.deleted;
  CascaderPanelData.value = CascaderPanelRef.value.getCheckedNodes();
  cascaderVisible.value = false;
  emit("handleChange", CascaderPanelData.value, cascaderOptions.value);
};

watch(
  () => props.options,
  () => {
    const optionProps = cloneDeep(props.options);
    optionProps.forEach(option => {
      option.children.forEach(child => {
        // console.log(child);
        child.children = child.children.filter(
          childOptions => !childOptions.deleted
        );
      });
    });
    cascaderOptionList.value = optionProps;
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  data: CascaderPanelData.value
});
</script>
<style lang="scss" scoped>
.cascader-panel-container {
  position: relative;
  width: 150px;

  .cascader-panel-body {
    margin-right: 10px;
    margin-bottom: 10px;

    .delete-input {
      ::v-deep(.el-input__inner) {
        color: #f56c6c;
        text-decoration: line-through;
      }
    }
  }

  .cascader-panel-popover {
    position: absolute;
    background: #fff;
  }
}
</style>
