<template>
  <div
    class="order-file-preview-body"
    style="pointer-events: auto; cursor: pointer"
    :style="{
      'max-width': `${width}px`,
      overflow: 'hidden',
      'text-overflow': 'ellipsis',
      'word-wrap': 'break-word'
    }"
  >
    <div
      v-for="file in filePreviewList"
      :key="file"
      class="file-col"
      style="pointer-events: auto; cursor: pointer"
    >
      <el-tooltip
        effect="dark"
        :content="file.name"
        placement="top"
        :show-after="500"
      >
        <svg
          v-if="['docx', 'doc'].includes(file.name.split('.')[1])"
          class="icon svg-icon"
          aria-hidden="true"
          :show-after="500"
          @click="handleOpenFilePreview('VueOfficeDocx', file)"
        >
          <use xlink:href="#link-word" />
        </svg>
        <svg
          v-else-if="['ppt', 'pptx'].includes(file.name.split('.')[1])"
          class="icon svg-icon"
          aria-hidden="true"
          @click="handleOpenFilePreview('VueOfficePptx', file)"
        >
          <use xlink:href="#link-ppt" />
        </svg>
        <svg
          v-else-if="
            [
              'bmp',
              'gif',
              'jpeg',
              'jpg',
              'pjpeg',
              'png',
              'tiff',
              'webp'
            ].includes(file.name.split('.')[1])
          "
          class="icon svg-icon"
          aria-hidden="true"
          @click="handleOpenFilePreview('ElImage', file)"
        >
          <use xlink:href="#link-photo" />
        </svg>
        <svg
          v-else-if="['pdf'].includes(file.name.split('.')[1])"
          class="icon svg-icon"
          aria-hidden="true"
          @click="handleOpenFilePreview('VueOfficePdf', file)"
        >
          <use xlink:href="#link-pdf" />
        </svg>
        <svg
          v-else-if="['xlsx', 'xls'].includes(file.name.split('.')[1])"
          class="icon svg-icon"
          aria-hidden="true"
          @click="handleOpenFilePreview('VueOfficeExcel', file)"
        >
          <use xlink:href="#link-excel1" />
        </svg>
        <svg v-else class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#link-unknown-format" />
        </svg>
      </el-tooltip>
      <LKFilePreview ref="LKFilePreviewRef" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { getCurrentInstance } from "vue";
import LKFilePreview from "@/components/lkFilePreview/index";
import { ElTooltip } from "element-plus";

const props = defineProps({
  filePreviewList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  fileList: {
    type: Array as PropType<any>,
    default: () => []
  },
  width: {
    type: String as PropType<string>,
    default: () => ""
  }
});

const { proxy } = getCurrentInstance();

const handleOpenFilePreview = async (componentName: any, file: any) => {
  // @ts-ignore
  await proxy.$filePreview.preview({
    componentName: componentName,
    fileData: file,
    fileList: props.fileList
  });
};
</script>
<style lang="scss">
.order-file-preview-body {
  display: inline-flex;
  cursor: pointer;

  .svg-icon {
    align-items: center;
    width: 24px;
    height: 24px;
  }
}
</style>
