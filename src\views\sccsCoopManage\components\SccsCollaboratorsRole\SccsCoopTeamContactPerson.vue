<template>
  <div class="role-per-col">
    <el-checkbox-group v-model="checkedCities" :disabled="true">
      <el-row :gutter="20">
        <el-col
          v-for="item in sccsSettingData.childrenList"
          :key="item"
          :span="8"
        >
          <el-checkbox
            class="checkbox-group-col"
            :label="item.label"
            :value="item.key"
          >
            <div class="checkbox-span">
              {{ item.label }}
              <el-tooltip
                v-if="item.remark"
                effect="dark"
                :content="item.remark"
                placement="top"
                :show-after="500"
              >
                <i class="iconfont link-explain font14 color80" />
              </el-tooltip>
            </div>
          </el-checkbox>
        </el-col>
      </el-row>
    </el-checkbox-group>
  </div>
</template>
<script lang="ts" setup>
import { ref, PropType, watchEffect } from "vue";

const checkedCities = ref([]);

const props = defineProps({
  sccsSettingData: {
    type: Object as PropType<any>
  },
  sccsToolRolePermItemList: {
    type: Object as PropType<any>
  }
});

watchEffect(() => {
  let bindDataIds: string[] = [];
  if (props.sccsToolRolePermItemList instanceof Array) {
    props.sccsToolRolePermItemList[0].permissionKeyList.forEach(
      permissionKey => {
        bindDataIds.push(permissionKey);
      }
    );
  } else {
    bindDataIds = [];
  }
  checkedCities.value = bindDataIds;
});

defineExpose({
  checkedCities
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  margin-bottom: 10px;
  font-weight: bolder;
}

.role-per-col {
  box-sizing: border-box;
  width: auto !important;
  padding: 0 !important;
  margin: 12px 13px !important;

  .el-collapse {
    border: 0 none !important;
  }
}

.checkbox-group-col {
  width: 100% !important;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  ::v-deep(.el-checkbox__label) {
    .checkbox-span-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .checkbox-flex {
        flex: 1;

        &:nth-child(2) {
          padding-right: 20px;
          text-align: right;
        }
      }
    }
  }
}

::v-deep(.el-collapse-item) {
  .el-collapse-item__header {
    display: flex;
    flex-flow: row-reverse;
    width: auto !important;
    border: 0 none !important;

    .checkbox-group-col {
      margin-bottom: 0 !important;
    }

    .icon-ele {
      margin-right: 5px;
      font-size: 18px;
      color: #8c8c8c;
      vertical-align: middle;
    }
  }

  .el-collapse-item__content {
    padding-left: 23px !important;
    border: 0 none !important;
  }

  .el-collapse-item__wrap {
    border: 0 none !important;
  }
}
</style>
