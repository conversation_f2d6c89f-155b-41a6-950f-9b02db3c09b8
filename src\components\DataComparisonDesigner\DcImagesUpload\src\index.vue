<template>
  <div class="dc-body">
    <el-row class="dc-title"
      ><el-tooltip
        v-if="widgetForm.info"
        :content="widgetForm.info"
        placement="top"
        :show-after="500"
        popper-class="widget-popper-label-class"
      >
        <i class="iconfont link-explain font12" style="margin-right: 5px" />
      </el-tooltip>
      {{ widgetForm.title }}</el-row
    >
    <el-row v-if="lastVal">
      <div
        class="dc-lastVal"
        :class="judgeValueReturnStatus(currentVal, lastVal)"
      >
        <span
          v-show="
            ['delete', 'edit'].includes(
              judgeValueReturnStatus(currentVal, lastVal)
            )
          "
          class="delete-line"
        />
        <el-image
          v-for="(item, index) in lastVal"
          :key="item"
          :src="item.url"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="getWidgetImageList(lastVal)"
          :initial-index="index"
          style="width: 116px; margin-right: 5px"
          fit="contain"
        />
      </div>
    </el-row>

    <el-row>
      <div
        v-if="judgeValueReturnStatus(currentVal, lastVal) !== 'nochange'"
        class="dc-currentVal"
        :class="judgeValueReturnStatus(currentVal, lastVal)"
      >
        <el-image
          v-for="(item, index) in currentVal"
          :key="item"
          :src="item.url"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="getWidgetImageList(currentVal)"
          :initial-index="index"
          style="width: 116px; margin-right: 5px"
          fit="contain"
        />
      </div>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref, computed } from "vue";

const props = defineProps({
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  lastHistoryObj: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const lastVal = computed(() => {
  if (props.lastHistoryObj instanceof Array) {
    const widgetData = props.lastHistoryObj.find(
      widgetRow => widgetRow.widgetId === props.widgetForm._fc_id
    );
    return widgetData ? widgetData.obj : [];
  } else {
    return [];
  }
});

const currentVal = computed(() => {
  if (props.widgetData instanceof Array) {
    const widgetData = props.widgetData.find(
      widgetRow => widgetRow.widgetId === props.widgetForm._fc_id
    );
    return widgetData ? widgetData.obj : [];
  } else {
    return [];
  }
});

const judgeValueReturnStatus = (
  currentValue?: any,
  lastValue?: any
): string => {
  let status = "";
  if (
    JSON.stringify(currentValue.map(e => e.uid)) ===
      JSON.stringify(lastValue.map(e => e.uid)) ||
    (!currentValue && !lastValue)
  ) {
    status = "nochange";
  } else if (lastValue.length === 0 && currentValue.length > 0) {
    status = "add";
  } else if (lastValue.length > 0 && currentValue.length === 0) {
    status = "delete";
  } else if (
    lastValue.length > 0 &&
    currentValue.length > 0 &&
    lastValue !== currentValue
  ) {
    status = "edit";
  }
  return status;
};

const getWidgetImageList = widgetData => {
  let widgetDataList = [];
  widgetData.forEach(widgetItem => widgetDataList.push(widgetItem.url));
  return widgetDataList;
};
</script>
<style lang="scss" scoped>
@use "../../style.scss";
</style>
