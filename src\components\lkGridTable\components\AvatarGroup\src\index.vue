<template>
  <div
    v-if="avatarBool"
    class="avater-next-group-container"
    style="pointer-events: auto; cursor: pointer"
  >
    <el-popover placement="bottom" trigger="click" width="auto">
      <div class="avater-next-group-popover">
        <div
          v-for="avatarItem in avatarList"
          :key="avatarItem.id"
          :class="{
            'avater-next-col': true,
            'reply-avatar': avatarItem.edited
          }"
        >
          <span
            v-if="!avatarItem.activate"
            style="margin-right: 5px; color: #fa8d0a !important"
          >
            (未注册)
          </span>
          <el-avatar
            :size="32"
            :class="[avatarItem.user ? '' : 'colorOrange']"
            :shape="avatarItem.user ? 'circle' : 'square'"
            :src="avatarItem.avatar"
            fit="cover"
          >
            {{ avatarItem.name }}
          </el-avatar>
          <div v-if="!avatarItem.coopUser" class="avater-group-popover-text">
            <ReText
              v-if="avatarItem.user"
              type="info"
              class="avater-group-popover-user-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ avatarItem.username }}
              <span v-if="avatarItem.activate">({{ avatarItem.email }})</span>
            </ReText>
            <ReText
              v-else
              type="info"
              class="avater-group-popover-team-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ avatarItem.username }}
            </ReText>
          </div>
          <div v-else class="avater-group-popover-text">
            <ReText
              type="info"
              class="avater-group-popover-user-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ avatarItem.username }}({{ avatarItem.email }})
            </ReText>
          </div>
        </div>
      </div>
      <template #reference>
        <div>
          <div
            v-for="avatarItem in avatarList"
            :key="avatarItem.id"
            :class="{
              'avatar-item-col': true,
              'coop-avatar': avatarItem.coopUser
            }"
          >
            <el-avatar
              :size="size"
              :class="{
                'reply-avatar': avatarItem.edited,
                colorOrange: !avatarItem.user
              }"
              :shape="avatarItem.user ? 'circle' : 'square'"
              :src="avatarItem.avatar"
              fit="cover"
            >
              {{ avatarItem.name }}
            </el-avatar>
          </div>
          <span
            v-if="avatarList && avatarList.length > 3"
            class="avatar-next-text"
          >
            +{{ avatarList.length - 3 }}
          </span>
        </div>
      </template>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { ReText } from "@/components/ReText";
import { ElPopover, ElAvatar } from "element-plus";

const props = defineProps({
  avatarListGroup: {
    type: Array as PropType<any>,
    default: () => []
  },
  size: {
    type: Number as PropType<number>,
    default: 32
  }
});

const avatarList = ref<any[]>([]);
const avatarBool = ref<boolean>(false);

watch(
  () => props.avatarListGroup,
  () => {
    avatarList.value = props.avatarListGroup.map(avatarItem => {
      return {
        id: avatarItem.id,
        name: avatarItem.name?.slice(0, 1),
        user: avatarItem.user,
        username: avatarItem.name,
        avatar: avatarItem.avatar,
        edited: avatarItem.edited,
        coopUser: avatarItem.coopTeamUser,
        email: avatarItem.email,
        activate: avatarItem.activate
      };
    });
    avatarBool.value = true;
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.avater-next-group-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;

  ::v-deep(.avatar-item-col) {
    position: relative;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;

    &.coop-avatar {
      position: relative;

      .svg-icon {
        position: absolute;
        right: -5px;
        bottom: -3px;
        z-index: 336;
        width: 13px;
        height: 13px;
      }
    }

    .el-avatar {
      position: relative;
      z-index: 333;
      margin-right: -6px;

      &.reply-avatar {
        border: 2px solid #ffb780;
      }
    }

    &:nth-child(2) {
      z-index: 332;
    }

    &:nth-child(3) {
      z-index: 331;
    }

    &:nth-child(4) {
      .el-avatar {
        position: relative;
        z-index: 330;
        background: rgb(0 0 0 / 30%) !important;
        border-color: #fff;

        &::after {
          position: absolute;
          top: 0;
          left: 0;
          display: inline-block;
          width: 100%;
          height: 100%;
          content: "";
          background: rgb(38 38 38 / 64%);
        }
      }

      .svg-icon {
        display: none;
      }
    }

    &:nth-child(n + 5) {
      display: none;
    }
  }

  .avatar-next-text {
    position: absolute;
    top: 50%;
    right: 2%;
    z-index: 335;
    font-size: 12px;
    color: #fff;
    transform: translateY(-50%);
  }
}

.avater-next-group-popover {
  .avater-next-col {
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px;

    &.reply-avatar {
      position: relative;

      &::after {
        position: absolute;
        top: 8px;
        left: 8px;
        z-index: 337;
        display: inline-block;
        width: 32px !important;
        height: 32px !important;
        content: "";
        background: rgb(0 0 0 / 30%) !important;
        border-radius: 50%;
      }

      &::before {
        position: absolute;
        z-index: 338;
        width: 32px;
        font-family: iconfont !important;
        font-size: 10px;
        color: #00cc7b;
        text-align: center;
        content: "\e7ad";
      }
    }

    .svg-icon {
      position: absolute;
      bottom: 8px;
      left: 30px;
      z-index: 339;
      width: 13px;
      height: 13px;
    }

    .avater-group-popover-text {
      display: inline-flex;
      flex: 1;
      flex-direction: column;
      margin-left: 8px;

      .avater-group-popover-user-text {
        flex: 1;
        width: 100%;
        overflow: hidden;
        font-size: 12px;
        color: #262626;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .avater-group-popover-team-text {
        width: 100%;
        font-size: 12px;
        color: #262626;
        text-align: left;
      }
    }
  }
}
</style>
