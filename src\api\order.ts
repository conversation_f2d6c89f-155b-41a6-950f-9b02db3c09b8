import { http } from "@/utils/http";
import type { Result } from "./type";

interface milestoneData {
  msId?: string;
  planStartDate?: string;
  planEndDate?: string;
  planCostDay?: number;
  managerId?: string;
}

interface widgetItemDataListProp {
  formId: string;
  widgetId: string;
  label: string;
  obj: string;
  childrenList?: widgetItemDataListProp[];
}

/**
 * 取模板详情
 * @param data
 * @returns
 */
export const getOrderTemplateDetails = (params: {
  templateId: string | any;
}) => {
  return http.request<Result>("get", "/trade/order/get-main-form-and-ms-data", {
    params
  });
};

/**
 * 创建贸易端订单
 * @returns
 */
export const createTradeOrder = (data: {
  sccsId: string;
  milestoneDataList: milestoneData[];
  widgetItemDataList: widgetItemDataListProp;
  linkedReferenceSaveList: any[];
}) => {
  return http.request<Result>("post", "/trade/order/create", { data });
};

/**
 * 取订单默认视图字段
 * @returns
 */
export const getDefaultView = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/orderView/get-default-view", {
    params
  });
};

/**
 * 取订单视图列表
 * @returns
 */
export const getUserOrderView = (params: { sccsId: string }) => {
  return http.request<Result>(
    "get",
    "/trade/orderView/get-user-all-view-list",
    {
      params
    }
  );
};

/**
 * 查询贸易端订单
 * @returns
 */
export const getOrderList = (data: {
  sccsId: string;
  pageNo: number;
  pageSize: number;
  sccsConditionList?: any;
  keyword?: any;
}) => {
  return http.request<Result>("post", "/trade/order/search", {
    data
  });
};

/**
 * 查询贸易端工单视图
 * @returns
 */
export const getWorkOrderList = (data: {
  sccsId: string;
  pageNo: number;
  pageSize: number;
  sccsConditionList?: any;
  keyword?: any;
}) => {
  return http.request<Result>("post", "/trade/order/search-workOrder-view", {
    data
  });
};

/**
 * 取模板详情
 * @returns
 */
export const getOrderTemplateDetail = (params: {
  templateId: string;
  orderId: string;
}) => {
  return http.request<Result>("get", "/trade/order/get-template-detail", {
    params
  });
};

/**
 * 取模板详情
 * @returns
 */
export const getOrderViewById = (params: {
  sccsId: string;
  orderListViewId: string;
}) => {
  return http.request<Result>("get", "/trade/orderView/get-order-view-by-id", {
    params
  });
};

/**
 * 查询贸易端订单详情
 * @returns
 */
export const getOrderDetail = (params: {
  sccsId: string;
  orderId: string | any;
}) => {
  return http.request<Result>("get", "/trade/order/search-detail", {
    params
  });
};

/**
 * 取订单和里程碑基础信息
 * @returns
 */
export const getOrderMsBasicInfo = (params: { orderId: string }) => {
  return http.request<Result>(
    "get",
    "/trade/order/get-order-and-ms-base-info",
    {
      params
    }
  );
};

/**
 * 获取处理人列表
 * @returns
 */
export const getWorkOrderProcessorList = (params: {
  sccsId: any;
  type: string;
}) => {
  return http.request<Result>("get", "/trade/work-order/list-processor", {
    params
  });
};

/**
 * 获取某个里程碑下的工单结构
 * @returns
 */
export const getWorkOrderStructure = (params: {
  sccsId: string;
  milestoneId: string;
  orderId: string;
  workOrderId?: string;
}) => {
  return http.request<Result>(
    "get",
    "/trade/work-order/get-work-order-structure",
    {
      params
    }
  );
};

/**
 * 创建贸易端工单
 * @returns
 */
export const createWorkOrder = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/create", { data });
};

/**
 * 取里程碑所有工单数量(工单名字使用)
 * @returns
 */
export const getMileStoneWorkOrderCount = (params: any) => {
  return http.request<Result>("get", "/trade/work-order/count-for-milestone", {
    params
  });
};

/**
 * 删除贸易端订单
 * @returns
 */
export const deleteTradeOrder = (params: { id: string; sccsId: string }) => {
  return http.request<Result>("delete", "/trade/order/delete", {
    params
  });
};

/**
 * 完成订单
 * @returns
 */
export const completeTradeOrder = (data: {
  orderId: string;
  sccsId: string;
}) => {
  return http.request<Result>("post", "/trade/order/complete-order", {
    data
  });
};

/**
 * 取消订单
 * @returns
 */
export const cancelTradeOrder = (data: any) => {
  return http.request<Result>("post", "/trade/order/cancel-order", {
    data
  });
};

/**
 * 重启订单
 * @returns
 */
export const restartTradeOrder = (data: {
  sccsId: string;
  orderId: string;
}) => {
  return http.request<Result>("post", "/trade/order/restart-order", {
    data
  });
};

/**
 * 完成里程碑
 * @returns
 */
export const complateMilestone = (data: any) => {
  return http.request<Result>("post", "/trade/milestone/complete-ms", {
    data
  });
};

/**
 * 取消里程碑
 * @returns
 */
export const cancelMilestone = (data: any) => {
  return http.request<Result>("post", "/trade/milestone/cancel-ms", {
    data
  });
};

/**
 * 重开里程碑
 * @returns
 */
export const restartMilestone = (data: any) => {
  return http.request<Result>("post", "/trade/milestone/restart-ms", {
    data
  });
};

/**
 * 终止采集
 * @returns
 */
export const stopWorkOrderCollection = (params: any) => {
  return http.request<Result>("get", "/trade/work-order/stop-collection", {
    params
  });
};

/**
 * 删除贸易端工单
 * @returns
 */
export const deleteWorkOrder = (params: {
  id: string;
  sccsId: string | string[];
  orderId: string | string[];
  msId: string | string[];
}) => {
  return http.request<Result>("delete", "/trade/work-order/delete", {
    params
  });
};

/**
 * 协作团队-分配里程碑批复
 * @returns
 */
export const assignMilestoneReply = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order/assign-for-milestone-reply",
    {
      data
    }
  );
};

/**
 * 内部SCCS-修改里程碑批复人
 * @returns
 */
export const updateMilestoneReply = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order/update-milestone-reply",
    {
      data
    }
  );
};

/**
 * 协作SCCS-修改工单采集人
 * @returns
 */
export const updateCoolTeamCollector = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order/coop-team-update-collector",
    {
      data
    }
  );
};

/**
 * 协作SCCS-修改工单批复人
 * @returns
 */
export const updateCoolTeamReplier = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order/coop-team-update-replier",
    {
      data
    }
  );
};

/**
 * 协作团队-分配采集工单
 * @returns
 */
export const assignMilestoneCollect = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/assign-for-collect", {
    data
  });
};

/**
 * 协作团队-分配工单批复
 * @returns
 */
export const assignMilestoneWorkOrderReply = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/assign-for-reply", {
    data
  });
};

/**
 * 协作SCCS-修改里程碑批复人
 * @returns
 */
export const assignMilestoneWorkOrderCoopTeamReplier = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order/coop-team-update-milestone-replier",
    {
      data
    }
  );
};

/**
 * 工单采集
 * @returns
 */
export const workOrderCollection = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/collect", {
    data
  });
};

/**
 * 获得贸易端工单详情
 * @returns
 */
export const getWorkOrderDetail = (params: any) => {
  return http.request<Result>("get", "/trade/work-order/detail", {
    params
  });
};

/**
 * 里程碑批复
 * @returns
 */
export const settingMilestoneReply = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/reply-milestone", {
    data
  });
};

/**
 * 工单批复
 * @returns
 */
export const settingWorkOrderReply = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/reply-workOrder", {
    data
  });
};

/**
 * 更新贸易端订单
 * @returns
 */
export const updateTradeOrder = (data: any) => {
  return http.request<Result>("post", "/trade/order/update", {
    data
  });
};

/**
 * 取消翻单
 * @returns
 */
export const deleteCloneOrder = (params: { id: string }) => {
  return http.request<Result>(
    "delete",
    "/trade/order-clone/cancel-clone-order",
    {
      params
    }
  );
};

/**
 * 取消工单互用
 * @returns
 */
export const cancelWorkOrderClone = (params: { id: string }) => {
  return http.request<Result>(
    "delete",
    "/trade/work-order-clone/cancel-clone-work-order",
    {
      params
    }
  );
};

/**
 * 取消里程碑互用
 * @returns
 */
export const cancelMilestoneClone = (params: {
  id: string;
  cancelType?: string;
}) => {
  return http.request<Result>("delete", "/trade/ms-clone/cancel-clone-ms", {
    params
  });
};

/**
 * 删除sccs关联设置
 * @returns
 */
export const deleteSccsRelation = (params: { id: string }) => {
  return http.request<Result>("delete", "/trade/sccs-relation/delete", {
    params
  });
};

/**
 * 取消关联订单
 * @returns
 */
export const cancelOrderRelation = (params: { id: string }) => {
  return http.request<Result>(
    "delete",
    "/trade/order-clone/cancel-relation-order",
    {
      params
    }
  );
};

/**
 * 取当前用户在当前sccs的角色权限(和订单挂钩的权限)
 * @returns
 */
export const getTradeSccsRolePermission = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/sccs-role/get-cur-user-sccs-role-order-perm-map",
    {
      data
    }
  );
};

/**
 * 获取当前用户在当前协作sccs的角色权限(和订单挂钩的权限)
 * @returns
 */
export const getTradeCoopSccsRolePermission = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/sccs-coop-role/user-coop-sccs-role-order-perm-map",
    {
      data
    }
  );
};

/**
 * 内部SCCS-修改工单采集人和工单批复人(类似编辑工单)
 * @returns
 */
export const updateWorkOrderOperator = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/update-operator", {
    data
  });
};

/**
 * 订单列统计
 * @returns
 */
export const getOrderColumnStatistics = (data: any) => {
  return http.request<Result>("post", "/trade/order/column-statistics", {
    data
  });
};

/**
 * 工单列统计
 * @returns
 */
export const getWorkOrderColumnStatistics = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/order/work-order-column-statistics",
    {
      data
    }
  );
};

/**
 * 获取sccs查询条件设置下拉组件
 * @returns
 */
export const getOrderSearchFileds = (params: any) => {
  return http.request<Result>("get", "/trade/order/order-search-filed", {
    params
  });
};

/**
 * 新增订单视图
 * @returns
 */
export const addOrderView = (data: any) => {
  return http.request<Result>("post", "/trade/orderView/add-order-view", {
    data
  });
};

/**
 * 删除订单视图
 * @returns
 */
export const deleteOrderView = (params: { id: string }) => {
  return http.request<Result>("delete", "/trade/orderView/delete", {
    params
  });
};

/**
 * 修改订单视图
 * @returns
 */
export const updateOrderView = (data: any) => {
  return http.request<Result>("post", "/trade/orderView/update-order-view", {
    data
  });
};

/**
 * 文件控件列表
 * @returns
 */
export const getSccsFileWidgetList = (params: any) => {
  return http.request<Result>("get", "/trade/sccs/file-widget-item-list", {
    params
  });
};

/**
 * 批量下载
 * @returns
 */
export const batchDownloadFiles = (data: any) => {
  return http.request<Result>("post", "/trade/order/batch-downLoad", {
    data
  });
};

/**
 * 内部SCCS-修改工单批复人
 * @returns
 */
export const updateWorkOrderReply = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order/update-work-order-reply",
    {
      data
    }
  );
};

/**
 * 修改订单视图排序
 * @returns
 */
export const updateOrderViewSort = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/orderView/update-order-view-index",
    {
      data
    }
  );
};

/**
 * 修改收藏状态
 * @returns
 */
export const updateTaskFavorite = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/userFavorite/update-task-favorite",
    {
      data
    }
  );
};

/**
 * 订单列表导出
 * @returns
 */
export const exportExcel = (data: any) => {
  return http.request<Result>("post", "/trade/import-export/export-excel", {
    data
  });
};

/**
 * 文件控件列表
 * @returns
 */
export const getOrderMark = (params: any) => {
  return http.request<Result>("get", "/trade/order/order-mark", {
    params
  });
};

/**
 * 获取当前用户扩展数据个数|邮件协作|动态
 * @returns
 */
export const getWorkOrderCount = (data: any) => {
  return http.request<Result>("post", "/trade/work-order/count", {
    data
  });
};

// 批量操作

/**
 * 获取有修改里程碑状态权限的订单信息
 * @returns
 */
export const getHaveUpdateMsStatusOrderInfo = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/milestone/get-have-update-ms-status-perm-order-info",
    {
      data
    }
  );
};

/**
 * 批量修改里程碑状态
 * @returns
 */
export const batchUpdateMsStatus = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/milestone/batch-update-ms-status",
    {
      data
    }
  );
};

/**
 * 获取可以批量操作的里程碑列表
 * @returns
 */
export const getBatchOperationMilestoneList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/milestone/get-batch-operation-milestone-list",
    {
      params
    }
  );
};

/**
 * 批量修改订单状态
 * @returns
 */
export const batchUpdateOrderStatus = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/order/batch-update-order-status",
    {
      data
    }
  );
};

/**
 * 获取有修改状态权限的订单信息
 * @returns
 */
export const getHaveUpdateOrderStatusOrderInfo = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/order/get-have-update-status-perm-order-info",
    {
      data
    }
  );
};

/**
 * 获取子表信息
 * @returns
 */
export const getSubTableInfoData = (params: any) => {
  return http.request<Result>("get", "/trade/order/sub-table-info", {
    params
  });
};

/**
 * 里程碑互用，工单互用
 * @returns
 */

// 查询工单互用信息map
export const getWorkOrderCloneInfo = (data: any) => {
  return http.request<Result>("post", "/trade/work-order-clone/relation/info", {
    data
  });
};

// 查询所有关联信息(翻单、关联)
export const getOrderRelationInfo = (data: any) => {
  return http.request<Result>("post", "/trade/order/relation/info", {
    data
  });
};

// 里程碑新建互用工单
export const createNewCloneWorkOrder = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order-clone/create-new-clone-work-order",
    {
      data
    }
  );
};
// 里程碑互用
export const createNewCloneMilestone = (data: any) => {
  return http.request<Result>("post", "/trade/ms-clone/create-new-clone-ms", {
    data
  });
};
// 查询订单内所有工单的互用信息
export const findOrderWorkOrderRelationMap = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-clone/find-order-work-order-relation-map",
    {
      params
    }
  );
};

// 取消关联订单
export const cancelRelationOrder = (params: any) => {
  return http.request<Result>(
    "delete",
    "/trade/order-clone/cancel-relation-order",
    {
      params
    }
  );
};

// 取消翻单
export const cancelCloneOrder = (params: any) => {
  return http.request<Result>(
    "delete",
    "/trade/order-clone/cancel-clone-order",
    {
      params
    }
  );
};

// 取消里程碑互用
export const cancelCloneMs = (data: any) => {
  return http.request<Result>("post", "/trade/ms-clone/cancel-clone-ms", {
    data
  });
};

// 取消互用工单
export const cancelCloneWorkOrder = (params: any) => {
  return http.request<Result>(
    "delete",
    "/trade/work-order-clone/cancel-clone-work-order",
    {
      params
    }
  );
};

// 判断是否有跳转订单权限
export const canGetOrderInfo = (params: any) => {
  return http.request<Result>("get", "/trade/order/can-get-order-info", {
    params
  });
};

/**
 * 工单操作列表
 * @returns
 */
export const getWorkOrderOperationList = (params: any) => {
  return http.request<Result>("get", "/trade/work-order/operation-list", {
    params
  });
};
