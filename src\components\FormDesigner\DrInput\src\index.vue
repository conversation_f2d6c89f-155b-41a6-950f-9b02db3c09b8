<template>
  <el-input
    ref="DrInputRef"
    v-model="widgetFormData[widgetConfigure._fc_id]"
    :placeholder="placeholder"
    clearable
    @change="handleChange"
  />
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, onUnmounted, ref, watch } from "vue";
import { ElInput } from "element-plus";
import { cloneDeep, isEqual } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const placeholder = computed(() => {
  return (
    props.widgetConfigure.props?.placeholderEn ||
    props.widgetConfigure.props?.placeholder
  );
});

const DrInputRef = ref<HTMLElement | any>(null);
const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const actuatorDefaultValue: any = inject("ActuatorDefaultValue", null); // 表单是否需要执行默认值功能
const invocationDefaultValueExecutor: any = inject(
  "invocationDefaultValueExecutor",
  null
);

const emit = defineEmits([
  "handleSubTableWidgetValueChange",
  "handleRenderFinish"
]);

watch(
  () => ({ ...props.trendsForm }),
  (newVal, oldVal) => {
    if (props.trendsForm) {
      const cloneDeepValue = cloneDeep(props.trendsForm)[
        props.widgetConfigure._fc_id
      ];
      widgetFormData.value[props.widgetConfigure._fc_id] =
        cloneDeepValue instanceof Array
          ? cloneDeepValue.join(",")
          : cloneDeepValue;

      if (actuatorDefaultValue) {
        const defaultValueConfig =
          props.widgetConfigure.props.defaultValueConfig || {};

        if (
          defaultValueConfig.type === "relate" &&
          newVal &&
          oldVal &&
          newVal.hasOwnProperty(defaultValueConfig.content) &&
          oldVal.hasOwnProperty(defaultValueConfig.content) &&
          newVal[defaultValueConfig.content] !==
            oldVal[defaultValueConfig.content]
        ) {
          widgetFormData.value[props.widgetConfigure._fc_id] =
            invocationDefaultValueExecutor(
              props.widgetConfigure.props.defaultValueConfig
            );
          handleChange();
        } else if (defaultValueConfig.type === "formula") {
          const formulasParams = defaultValueConfig.content.formulasParams.map(
            param => param.split("@@")[1]
          );

          for (let formulasParam of formulasParams) {
            if (
              newVal &&
              oldVal &&
              newVal.hasOwnProperty(formulasParam) &&
              oldVal.hasOwnProperty(formulasParam) &&
              newVal[formulasParam] !== oldVal[formulasParam]
            ) {
              widgetFormData.value[props.widgetConfigure._fc_id] =
                invocationDefaultValueExecutor(
                  props.widgetConfigure.props.defaultValueConfig
                );
              handleChange();
              return;
            }
          }
        }
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const handleChange = () => {
  const widgetId = props.widgetConfigure._fc_id;
  if (props.widgetRowIndex !== -1) {
    emit(
      "handleSubTableWidgetValueChange",
      widgetId,
      {
        label: widgetFormData.value[widgetId],
        obj: widgetFormData.value[widgetId],
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  } else {
    handleWidgetFormsValue(
      widgetId,
      {
        label: widgetFormData.value[widgetId],
        obj: widgetFormData.value[widgetId],
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  }
};

// watch(
//   () => widgetFormData.value[props.widgetConfigure._fc_id],
//   (newVal, oldVal) => {
//     if (props.trendsForm) {
//       if (
//         newVal !== props.trendsForm[props.widgetConfigure._fc_id] &&
//         !(!newVal && !oldVal)
//       ) {
//         const widgetId = props.widgetConfigure._fc_id;
//         if (props.widgetRowIndex !== -1) {
//           emit(
//             "handleSubTableWidgetValueChange",
//             widgetId,
//             {
//               label: widgetFormData.value[widgetId],
//               obj: widgetFormData.value[widgetId],
//               widgetId: widgetId,
//               $rowIndex: props.widgetRowIndex
//             },
//             widgetFormData.value[widgetId]
//           );
//         } else {
//           handleWidgetFormsValue(
//             widgetId,
//             {
//               label: widgetFormData.value[widgetId],
//               obj: widgetFormData.value[widgetId],
//               widgetId: widgetId,
//               $rowIndex: props.widgetRowIndex
//             },
//             widgetFormData.value[widgetId]
//           );
//         }
//       }
//     }
//   },
//   {
//     deep: true,
//     immediate: true
//   }
// );

onMounted(() => {
  emit("handleRenderFinish", DrInputRef.value);
});
</script>
<style lang="scss" scoped>
.error-col {
  .el-input {
    border: 1px solid #e62412;
    border-radius: 4px;
  }
}
</style>
