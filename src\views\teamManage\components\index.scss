.team-module-area-container {
  height: 100%;
  padding: 14px 25px;

  &.team-module-area-permission-empty-container {
    align-content: center;
  }

  .team-module-area-header {
    display: flex;
    align-items: center;
    padding: 3px 0;
    border-bottom: 1px solid #e3e5e9;

    .team-module-area-title {
      flex: 1;
      align-items: center;
      font-size: 16px;
      font-weight: bolder;
      line-height: 22px;
      color: #303133;
    }

    .el-button {
      .iconfont {
        align-items: center;
        margin-right: 6px;
        font-size: 12px;
      }
    }
  }

  .team-module-area-body {
    margin: 30px 0;
  }
}

::v-deep(.el-table) {
  .el-table-sccs-setting-cell {
    .cell {
      display: flex;
      text-overflow: initial !important;
    }
  }

  .el-table-sccs-setting-cell2 {
    .cell {
      text-overflow: initial !important;
    }
  }
}
.tag-row {
  width: 100%;
  white-space: pre-wrap;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  .tag-row-item {
    width: fit-content;
    max-width: 100%;
  }
}
.tag-col {
  max-width: 100%;
  padding: 3px 6px;
  margin-right: 4px;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  cursor: pointer;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;

  ::v-deep(.el-tag__content) {
    max-width: 100%;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .tag-span-icon {
    position: relative;
    display: inline-block;
    width: 12px;
    height: 12px;

    // background: #fff;
    padding: 2px;
    margin-right: 2px;
    vertical-align: baseline;
    border-radius: 2px;

    .svg {
      width: 12px;
      height: 12px;
    }
  }
}

.el-table-register-tip {
  position: absolute;
  top: 0;
  left: 0;
  padding: 2px 6px;
  margin-right: 5px;
  font-size: 12px;
  line-height: 17px;
  color: #fa8d0a;
  background: #fff7e6;
  border-radius: 4px;
}

.tag-owner {
  color: #0070d2 !important;
  background: #eef8ff !important;
}

.tag-manager {
  color: #5f66e5 !important;
  background: #f4f5ff !important;
}

.colorRed {
  margin-right: 8px !important;
  color: #cf1421 !important;
}
