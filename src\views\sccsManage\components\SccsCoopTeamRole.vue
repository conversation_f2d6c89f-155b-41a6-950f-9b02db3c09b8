<template>
  <div class="team-module-area-body">
    <el-form
      ref="FormRef"
      label-position="top"
      label-width="auto"
      :model="form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_inputSearch')}：`">
            <el-input
              v-model="form.roleName"
              clearable
              :placeholder="t('trade_sccs_collaborativeRoleNameTip')"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="`${t('trade_sccs_coopTeam')}：`">
            <LkTeamSelectV2
              v-model="form.teamId"
              filterable
              :options="sccsList"
              clearable
              :placeholder="t('trade_common_selectText')"
              :teamRenderFields="{ avatar: 'teamAvatar', label: 'teamName' }"
              :props="{ label: 'teamName', value: 'teamId' }"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item style="margin-top: 30px">
            <el-button @click="resetForm">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchTable"
              >{{ t("trade_common_query") }}</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="tableData"
      :border="true"
      stripe
      :tooltip-options="{ showAfter: 500 }"
      :empty-text="
        loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
      "
    >
      <el-table-column
        :label="t('trade_sccs_collaborativeRoleName')"
        width="255"
        prop="name"
        show-overflow-tooltip
      />
      <el-table-column
        :label="t('trade_sccs_coopTeam')"
        prop="teamMemberInfoList"
        class-name="el-table-sccs-setting-cell"
      >
        <template #default="scope">
          <ReText
            v-for="item in scope.row.teamMemberInfoList"
            :key="item.teamId"
            class="tag-col"
            :tippyProps="{ delay: 50000 }"
          >
            <LkAvatar
              :size="18"
              fit="cover"
              class="colorOrange"
              shape="square"
              :teamInfo="{
                avatar: item.teamAvatar,
                username: item.teamName
              }"
            />
            {{ item.teamName }}
          </ReText>
        </template>
      </el-table-column>
      <el-table-column width="70" align="center">
        <template #default="{ row }">
          <LkTableOperate
            :tableOpeateLists="tableOpeateLists"
            :messageBoxConfirmObject="messageBoxConfirmObject"
            :row="row"
            @handleOperate="handleOperate"
          />
        </template>
      </el-table-column>
    </el-table>
    <LkPagination
      ref="LkPaginationRef"
      :total="total"
      @updatePagination="handleSearchTable"
    />
    <SccsCoopTeamRoleDialog
      ref="SccsCoopTeamRoleDialogRef"
      :basicInfo="basicInfo"
      @updateSccsSuccess="handleSearchTable"
    />
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { reactive, ref, watchEffect } from "vue";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import LkAvatar from "@/components/lkAvatar/index";
import LkPagination from "@/components/lkPagination/index";
import LkTableOperate from "@/components/lkTableOperate/index";
import { ReText } from "@/components/ReText";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";
import {
  getSccsCoopTeamRole,
  getSccsCoopTeamList,
  deleteSccsCoopTeam
} from "@/api/sccs";
import { message } from "@/utils/message";
import SccsCoopTeamRoleDialog from "./SccsCoopTeamRole/SccsCoopTeamRoleDialog.vue";

const { t } = useI18n();
const LkPaginationRef = ref<any>(null);
const FormRef = ref<any>(null);
const SccsCoopTeamRoleDialogRef = ref<any>(null);
let tableData = ref<any[]>([]);
let sccsList = ref<any[]>([]);
let total = ref<number>(0);
let loading = ref<boolean>(false);
const form = reactive({
  roleName: "",
  teamId: ""
});
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_team_deleteSccsCoopRole",
  messageBoxTitle: "trade_sccs_delSccsCoopRole",
  messageBoxTipArray: ["name"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-edit",
    title: "trade_common_edit",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);

const resetForm = () => {
  form.roleName = "";
  form.teamId = "";
  handleSearchTable();
};

const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "edit") {
    handleEdit(row.id);
  } else {
    handleDeleteTable(row.id);
  }
};

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleSearchTable = async (): Promise<void> => {
  loading.value = true;
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getSccsCoopTeamRole({
    sccsId: props.basicInfo.id,
    ...pageParams,
    ...form
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleEdit = (id: string): void => {
  SccsCoopTeamRoleDialogRef.value.open(id);
};

const handleDeleteTable = async (id): Promise<void> => {
  const res = await deleteSccsCoopTeam({
    id: id,
    sccsId: props.basicInfo.id
  });
  if (res.code === 0) {
    message(t("trade_common_deleteSuccess"), {
      customClass: "el",
      type: "success"
    });
    handleSearchTable();
  }
};

const handleCreateTeamRole = (): void => {};

const init = (): void => {
  loading.value = true;
  Promise.all([getSccsCoopTeamList({ sccsId: props.basicInfo.id })])
    .then(res => {
      sccsList.value = res[0].data;
      handleSearchTable();
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 300);
    });
};

const addSccsBusinessRole = (): void => {
  SccsCoopTeamRoleDialogRef.value.open();
};

watchEffect(() => {
  if (props.basicInfo && props.basicInfo.id) {
    init();
  }
});

defineExpose({
  handleCreateTeamRole,
  addSccsBusinessRole
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.team-module-area-body {
  margin-top: 0 !important;

  .team-module-area-btn {
    position: absolute;
    top: 0;
    right: 0;
  }

  ::v-deep(.tag-col) {
    padding: 3px 6px;
    margin-right: 4px;
    overflow: hidden;
    font-size: 12px;
    line-height: 20px;
    color: #595959;
    text-overflow: ellipsis;
    word-wrap: break-word;
    background: #f0f0f0;
    border: 0 none;
    border-radius: 4px;
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
