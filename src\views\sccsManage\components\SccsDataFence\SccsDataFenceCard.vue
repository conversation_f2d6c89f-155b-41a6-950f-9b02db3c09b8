<template>
  <div class="data-fence-card-container">
    <div class="data-fence-card-title">
      <span class="data-fence-card-tip">{{ widget.props.header }}</span>
      <el-checkbox
        v-model="cardCheckAll"
        :indeterminate="cardIndeterminate"
        @change="handleCardChangeCheck"
      >
        {{ t("trade_common_CannotBeViewed") }}
      </el-checkbox>
    </div>
    <el-checkbox-group
      v-model="widgetChildList"
      class="data-fence-card-body"
      @change="handleCheckedCard"
    >
      <el-row :gutter="20">
        <el-col
          v-for="colItem in widget.children"
          :key="colItem._fc_id"
          :span="colItem.props.span"
          :offset="colItem.props.offset"
          :push="colItem.props.push"
          :pull="colItem.props.pull"
        >
          <el-form-item
            v-for="widgetItem in colItem.children"
            :key="widgetItem._fc_id"
            :label="widgetItem.title"
            class="data-fence-form-item"
          >
            <el-checkbox
              :label="t('trade_common_CannotBeViewed')"
              :value="widgetItem._fc_id"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-checkbox-group>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  widget: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetHiddenIdList: {
    type: Array as PropType<any>,
    default: () => []
  },
  dataFenceWidgetHiddenIds: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const { t } = useI18n();
const cardCheckAll = ref<boolean>(false);
const cardIndeterminate = ref<boolean>(false);
const widgetChildList = ref<any[]>([]);

const emit = defineEmits(["handleChangeCardCheckbox"]);

const widgetCardChildList = computed(() => {
  const widgetList = props.widget.children.flatMap(
    childWidget => childWidget?.children
  );
  return widgetList;
});

const handleCardChangeCheck = (bool: any) => {
  if (bool) {
    const widgetChildId = widgetCardChildList.value.map(
      cardChild => cardChild?._fc_id
    );
    widgetChildList.value = widgetChildId;
    cardCheckAll.value = true;
  } else {
    widgetChildList.value = [];
    cardCheckAll.value = false;
  }
};

const handleCheckedCard = (val: string[]) => {
  if (val.length === widgetCardChildList.value.length) {
    cardIndeterminate.value = false;
    cardCheckAll.value = true;
  } else if (val.length === 0) {
    cardIndeterminate.value = false;
    cardCheckAll.value = false;
  } else {
    cardIndeterminate.value = true;
    cardCheckAll.value = false;
  }
};

const findCommonElements = (arrA, arrB) => {
  const setB = new Set(arrB);
  return arrA.filter(element => setB.has(element));
};

watch(
  () => widgetChildList.value,
  () => {
    emit("handleChangeCardCheckbox", props.widget, widgetChildList.value);
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => [() => props.dataFenceWidgetHiddenIds, () => props.widget],
  () => {
    const labelFieldIdList = widgetCardChildList.value.map(
      cardChild => cardChild?._fc_id
    );
    const labelFields = findCommonElements(
      props.dataFenceWidgetHiddenIds,
      labelFieldIdList
    );

    widgetChildList.value = labelFields;
    emit("handleChangeCardCheckbox", props.widget, widgetChildList.value);
    if (labelFields.length === widgetCardChildList.value.length) {
      cardIndeterminate.value = false;
      cardCheckAll.value = true;
    } else if (labelFields.length === 0) {
      cardIndeterminate.value = false;
      cardCheckAll.value = false;
    } else {
      cardIndeterminate.value = true;
      cardCheckAll.value = false;
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.data-fence-card-container {
  .data-fence-card-title {
    display: flex;
    align-items: center;
    height: 48px;
    border-bottom: 1px solid #e5e5e5;

    .data-fence-card-tip {
      margin-right: 22px;
      font-size: 16px;
      font-weight: bolder;
      line-height: 16px;
      color: #262626;
      text-align: left;
    }
  }

  .data-fence-card-body {
    margin: 18px 0;
  }

  .el-row {
    margin: 0 !important;

    .el-checkbox-group {
      width: 100% !important;
    }
  }
}
</style>
