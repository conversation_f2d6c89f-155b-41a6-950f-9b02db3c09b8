<template>
  <div class="team-member-info-container team-module-area-container">
    <div class="team-module-area-header">
      <div class="team-module-area-title">
        {{ t("trade_team_membersManage") }}
      </div>
      <el-button type="primary" color="#0070D2" @click="handleAdd">
        <FontIcon icon="link-add" />
        {{ t("trade_common_increase") }}
      </el-button>
    </div>
    <div class="team-module-area-body">
      <el-form
        ref="FormRef"
        label-position="top"
        label-width="auto"
        :model="form"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="`${t('trade_team_inputSearch')}：`">
              <el-input
                v-model="form.keyword"
                clearable
                :placeholder="t('trade_common_searchAccount')"
                @blur="handleSearch"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="`${t('trade_sccs_businessRole')}：`">
              <el-select-v2
                v-model="form.sccsRoleId"
                filterable
                :options="sccsRoleList"
                clearable
                :props="{ label: 'name', value: 'id' }"
                :placeholder="t('trade_common_selectText')"
                @change="handleSearch"
              >
                <template #default="{ item }">
                  <div
                    class="text-ellipsis"
                    :style="{ color: item.isDocking ? '#5f66e5' : '' }"
                  >
                    {{ item.name }}
                  </div>
                </template>
              </el-select-v2>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item style="margin-top: 30px">
              <el-button @click="resetForm">
                {{ t("trade_common_reSet") }}
              </el-button>
              <el-button type="primary" color="#0070D2" @click="handleSearch">
                {{ t("trade_common_query") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        :border="true"
        stripe
        max-height="calc(100vh - 280px)"
        :row-class-name="handleTableRowStyle"
        :tooltip-options="{ showAfter: 500 }"
        :empty-text="
          loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
        "
      >
        <el-table-column
          :label="t('trade_common_name')"
          width="120"
          prop="username"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="scope">
            <span v-if="!scope.row.activate" class="el-table-register-tip">
              {{ t("trade_common_unregistered") }}
            </span>
            <ReText
              :key="scope.row.username"
              class="el-table-register-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ scope.row.username }}
            </ReText>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_login_email')"
          prop="email"
          width="230"
          class-name="el-table-sccs-setting-cell"
        />
        <el-table-column
          :label="t('trade_user_role')"
          prop="sccsRoleList"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="scope">
            <ReText
              v-for="(item, index) in scope.row.sccsMemberRoleList"
              :key="index"
              type="info"
              :class="['tag-col', item.isDocking ? 'tag-manager' : '']"
              :tippyProps="{ delay: 50000 }"
            >
              <span v-if="!item.manager">{{ item.name }}</span>
              <span v-else>{{ t("trade_common_businessAdmin") }}</span>
            </ReText>
          </template>
        </el-table-column>
        <el-table-column width="70">
          <template #default="{ row }">
            <el-dropdown trigger="click">
              <i class="iconfont link-more-left" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleSettingRole(row)">
                    <i class="iconfont link-setting font14" />{{
                      t("trade_common_settingRole")
                    }}
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleDelete(row)">
                    <i class="iconfont link-ashbin colorRed font14" />
                    <span class="colorRed">{{ t("trade_common_delete") }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <LkPagination
        ref="LkPaginationRef"
        :total="total"
        @updatePagination="handleSearch"
      />
      <SccsAddMembershipDialog
        ref="SccsAddMembershipDialogRef"
        :basicInfo="basicInfo"
        :groupId="groupId"
        @handleSuccess="handleSearch"
      />
      <SccsEditMembershipDialog
        ref="SccsEditMembershipDialogRef"
        :basicInfo="basicInfo"
        :groupId="groupId"
        @handleSuccess="handleSearch"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, markRaw, h } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import LkPagination from "@/components/lkPagination/index";
import { ReText } from "@/components/ReText";
import SccsAddMembershipDialog from "./SccsMemberShip/SccsAddMembershipDialog.vue";
import SccsEditMembershipDialog from "./SccsMemberShip/SccsEditMembershipDialog.vue";
import {
  getSccsFenceList,
  getSccsCoopRoleList,
  getSccsMemberListPage,
  deleteSccsMember
} from "@/api/sccs";

const { t } = useI18n();
const form = reactive({
  keyword: "",
  sccsRoleId: "",
  dataFenceId: ""
});
let tableData = ref<any[]>([]);
let sccsFenceList = ref<any[]>([]);
let sccsRoleList = ref<any[]>([]);
const SccsAddMembershipDialogRef = ref<any>(null);
const SccsEditMembershipDialogRef = ref<any>(null);
const LkPaginationRef = ref<any>(null);
let total = ref<number>(0);
const loading = ref<boolean>(true);

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleTableRowStyle = ({ row, rowIndex }): any => {
  return !row.activate ? "activate-row" : "";
};

const init = (): void => {
  const sccsId = props.basicInfo.id;
  Promise.all([
    getSccsFenceList({ sccsId: sccsId }),
    getSccsCoopRoleList({ sccsId: sccsId })
  ]).then(res => {
    sccsFenceList.value = res[0].data;
    sccsRoleList.value = res[1].data;
    handleSearch();
  });
};

const resetForm = () => {
  form.sccsRoleId = "";
  form.keyword = "";
  form.dataFenceId = "";
  handleSearch();
};

const handleSearch = async (): Promise<void> => {
  loading.value = true;
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getSccsMemberListPage({
    ...form,
    ...pageParams,
    ...{ sccsId: props.basicInfo.id }
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleAdd = (): void => {
  SccsAddMembershipDialogRef.value.open();
};

const handleDelete = async (row: any): Promise<void> => {
  const { username, id } = row;
  const sccsBasicInfoId = props.basicInfo.id;
  ElMessageBox.confirm(
    `${t("trade_common_deleteMemberListTip")}「${username}」？`,
    t("trade_common_deleteTip"),
    {
      confirmButtonText: t("trade_common_confirm"),
      cancelButtonText: t("trade_common_cancel"),
      confirmButtonClass: "confrim-message-btn-class",
      cancelButtonClass: "cancel-message-btn-class",
      type: "error",
      icon: markRaw(WarningFilled),
      center: true
    }
  ).then(async () => {
    const { code } = await deleteSccsMember({
      id: id,
      sccsId: sccsBasicInfoId
    });
    if (code === 0) {
      ElMessage({
        message: t("trade_common_deleteSuccess"),
        type: "success"
      });
      handleSearch();
    }
  });
};

const handleSettingRole = (row: any) => {
  SccsEditMembershipDialogRef.value.open(row);
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
// ::v-deep(.el-table) {
//   .el-table__body .el-table__row {
//     height: 60px !important;
//   }
// }
</style>
