<template>
  <LkDialog
    ref="SubTableDialogRef"
    class="lk-middle-dialog subform-table-dialog"
    append-to-body
    @confirm="handleConfirm"
  >
    <template #default>
      <div class="subform-table-container">
        <ListTable
          ref="tableInstanceRef"
          :options="option"
          :style="{ height: `${height}px` }"
        />
      </div>
    </template>
  </LkDialog>
</template>
<script setup lang="ts">
import { computed, h, markRaw, nextTick, ref } from "vue";
import LkDialog from "@/components/lkDialog/index";
import { VTable } from "@visactor/vue-vtable";
import { getSubTableInfoData } from "@/api/order";
import { ElImage, ElRate, ElTag } from "element-plus";
import FileColumn from "@/components/lkGridTable/components/FileColumns";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";

const height = ref<number>(0);
const option = markRaw({
  records: [],
  columns: [],
  defaultHeaderRowHeight: 36,
  widthMode: "autoWidth",
  defaultRowHeight: 40,
  columnWidthComputeMode: "only-header",
  frozenColCount: 3,
  rightFrozenColCount: 1,
  hover: {
    highlightMode: "row",
    disableHover: true
  },
  select: {
    highlightMode: "row",
    disableSelect: true
  },
  customConfig: {
    createReactContainer: true
  },
  resize: {
    columnResizeMode: "header"
  },
  rowSeriesNumber: {
    title: "序号",
    width: "80"
  },
  theme: {
    headerStyle: {
      color: "#797979",
      fontSize: "13",
      fontWeight: "bolder",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    bodyStyle: {
      color: "#262626",
      fontSize: "13",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    scrollStyle: {
      visible: "always",
      hoverOn: false,
      width: 10,
      scrollRailColor: "#fbfbfb",
      scrollSliderColor: "#d3d3d3"
    },
    buttonStyle: {
      buttonColor: "transparent",
      buttonBorderColor: "transparent",
      buttonHoverColor: "transparent",
      buttonHoverBorderColor: "transparent"
    }
  }
});
const SubTableDialogRef = ref<any | HTMLElement>(null);
const tableInstanceRef = ref<HTMLElement | any>(null);

const handleConfirm = (): void => {};

const tableInstance = computed(() => {
  return tableInstanceRef.value.vTableInstance;
});

const columnSlots = ref<any>({
  USERIMAGE: {
    customRender(args) {
      if (args.row === 0 || args.col === 0) return null;
      const elements = [];
      let top = 30;
      let maxWidth = 0;

      const tableRow = tableInstance.value.getCellOriginRecord(
        args.col,
        args.row
      );

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const widgetObject = tableRow?.widgetList.find(
        widget => widget.widgetId === fieldName
      );

      if (fieldName.indexOf("manager") === -1) {
        if (widgetObject && widgetObject.showObj.length > 0) {
          widgetObject.showObj.map((widgetItem, widgetIndex) => {
            if (widgetItem.avatar) {
              elements.push({
                type: "image",
                shape: "circle",
                src: widgetItem.avatar,
                width: 24,
                height: 24,
                x: 20 + widgetIndex * 30,
                y: 8
              });
            } else {
              elements.push({
                type: "circle",
                radius: 12,
                fill: "#0070d2",
                x: 32 + widgetIndex * 30,
                y: 18
              });
              elements.push({
                type: "text",
                fill: "#ffffff",
                fontSize: 12,
                textBaseline: "middle",
                text: widgetItem.name.slice(0, 1),
                width: 50,
                height: 24,
                x: 26 + widgetIndex * 30,
                y: 18
              });
            }
          });
        }
      } else {
        const { avatar, username, email } =
          tableInstance.value.getCellOriginRecord(args.col, args.row)[
            fieldName
          ];
        if (username && email) {
          if (avatar) {
            elements.push({
              type: "image",
              shape: "circle",
              src: avatar,
              width: 24,
              height: 24,
              x: 20,
              y: 8
            });
          } else {
            elements.push({
              type: "circle",
              radius: 12,
              fill: "#0070d2",
              x: 32,
              y: 18
            });
            elements.push({
              type: "text",
              fill: "#ffffff",
              fontSize: 12,
              textBaseline: "middle",
              text: username.slice(0, 1),
              width: 50,
              height: 24,
              x: 26,
              y: 18
            });
          }
        }
      }

      return {
        elements,
        expectedHeight: top + 20,
        expectedWidth: maxWidth + 20
      };
    }
  },
  RATE: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        vue: {
          element: h(ElRate, {
            modelValue: value ? parseFloat(value) : 0,
            disabled: true,
            max: value ? parseFloat(value) : 0
          }),
          container: table.bodyDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  IMAGE: {
    customRender(args) {
      if (args.row === 0 || args.col === 0) return null;
      const { width } = args.rect;
      const { value } = args;
      const elements = [];
      let top = 30;
      let maxWidth = 0;

      if (!(value instanceof Array)) {
        elements.push({
          type: "image",
          src: value,
          width: width - 30,
          height: 32,
          x: 20,
          y: 4
        });
      }

      return {
        elements,
        expectedHeight: top + 20,
        expectedWidth: maxWidth + 20
      };
    }
  },
  SELECTION: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const regex =
        /<span\s[^>]*?style\s*=\s*["'][^>]*?background:\s*([^;>"']+)[^>]*>([^<]+)<\/span>/gi;

      const matches = [];
      let match;
      while ((match = regex.exec(value)) !== null) {
        const [, bgValue, content] = match;
        matches.push({ bgValue, content });
      }

      let container;
      if (matches instanceof Array && matches.length > 0) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          flexWrap: "nowrap",
          alignItems: "center"
        });

        matches.map((match, index) => {
          const childEl = new VTable.CustomLayout.Group({
            height,
            width: width / matches.length,
            display: "flex",
            alignItems: "center",
            vue: {
              element: h(
                ElTag,
                {
                  key: match.content,
                  type: "info",
                  color: match.bgValue
                },
                () => match.content
              )
            }
          });
          container.add(childEl);
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  FILEUPLOAD: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      if (value && value instanceof Array) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          vue: {
            element: h(FileColumn, {
              filePreviewList: value,
              fileList: value
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  IMAGELIST: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        flexWrap: "nowrap",
        alignItems: "center"
      });

      if (value && value instanceof Array) {
        value.map((image, index) => {
          const childEl = new VTable.CustomLayout.Group({
            height,
            width: width / value.length,
            display: "flex",
            alignItems: "center",
            vue: {
              element: h(ElImage, {
                src: image.url,
                fit: "contain"
              })
            }
          });
          container.add(childEl);
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  AVATARGROUP: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      if (value && value instanceof Array && value.length > 0) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          vue: {
            element: h(LkAvatarGroupNext, {
              avatarListGroup: value,
              size: 26
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  }
});

const open = (
  orderId: string,
  subTableWidgetId: string,
  childColumns: any[]
): void => {
  SubTableDialogRef.value.open();
  nextTick(async () => {
    height.value = (
      document.querySelector(".subform-table-container") as HTMLElement
    ).offsetHeight;
    const { code, data } = await getSubTableInfoData({
      typeEnum: "MAIN_FORM",
      relatedId: orderId
    });
    if (code === 0) {
      let fileColumnList = [];
      for (let column of childColumns) {
        let fieldObject: any = {};
        if (column.widgetType === "RATE") {
          fieldObject = columnSlots.value["RATE"];
        } else if (column.widgetType === "FILE_UPLOAD") {
          fieldObject = columnSlots.value["FILEUPLOAD"];
        } else if (column.widgetType === "IMAGE_UPLOAD") {
          fieldObject = columnSlots.value["IMAGELIST"];
        } else if (["RADIO", "CHECKBOX"].includes(column.widgetType)) {
          fieldObject = columnSlots.value["SELECTION"];
        } else if (["SIGNATURE"].includes(column.widgetType)) {
          fieldObject = columnSlots.value["IMAGE"];
        } else if (
          [
            "MEMBER",
            "COOP_TEAM",
            "MULTIPLE_COOP_TEAM",
            "MULTIPLE_MEMBER"
          ].includes(column.widgetType)
        ) {
          fieldObject = columnSlots.value["USERIMAGE"];
        }

        fileColumnList.push(
          Object.assign(
            {
              field: column.name,
              title: column.label,
              width: column.width,
              hide: false,
              fieldFormat: record => {
                if (column.widgetType === "PERCENT") {
                  return record[column.name] ? `${record[column.name]}%` : "";
                }
                return record[column.name];
              }
            },
            fieldObject
          )
        );
      }
      tableInstance.value.updateColumns(fileColumnList);
      const subWidgetList = data[subTableWidgetId];
      if (subWidgetList) {
        const subTableAssemblyData = Object.values(
          subWidgetList.reduce((res, item) => {
            res[item.rowIndex]
              ? res[item.rowIndex].push(item)
              : (res[item.rowIndex] = [item]);
            return res;
          }, {})
        );

        let tableRow = [];
        subTableAssemblyData.forEach((subTableRow: any) => {
          let subTableWidgetObject = {};
          childColumns.forEach(orderField => {
            const item = subTableRow.find(
              subRow => subRow.widgetId === orderField.name
            );
            subTableWidgetObject[orderField.name] = item ? item.label : "";
          });
          tableRow.push(subTableWidgetObject);
        });

        tableInstance.value.setRecords(tableRow);
      }
    }
  });
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.order-sub-table-container {
  .order-sub-table-btn {
    font-size: 14px;
    color: #2082ed;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.subform-table-dialog {
  .el-dialog__body {
    .subform-table-container {
      height: 100%;
    }
  }
}
</style>
