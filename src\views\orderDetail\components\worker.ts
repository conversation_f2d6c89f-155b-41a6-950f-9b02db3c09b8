import { calculateFormVisibleExpression } from "@/utils/formVisibleExpression";

self.onmessage = async e => {
  const formIdList = e.data.formIdList;
  const entireDetail = e.data.entireDetail;
  const formVisibleConditions = e.data.formVisibleConditions;
  const checkboxList = e.data.checkboxList;

  let formVisibleData = {};
  let newEntireDetail = {};

  for (let checkboxItem of checkboxList) {
    const targetKey = checkboxItem._fc_id;
    let result;
    let detailObject = {};
    let outKeyId = "";
    for (const outerKey of Object.keys(entireDetail)) {
      if (entireDetail[outerKey][targetKey] !== undefined) {
        result = entireDetail[outerKey][targetKey];
        detailObject = entireDetail[outerKey];
        outKeyId = outerKey;
        break;
      }
    }
    if (result instanceof Array) {
      const resultList = result.map(resultItem => {
        if (resultItem === "") {
          return "";
        }
        if (resultItem.indexOf(",") > -1) {
          const checkboxData = resultItem.split(",");
          return checkboxData.map(checkbox => {
            const item = checkboxItem.props.options.find(
              option => option.label === checkbox
            );
            return item ? item.value : "";
          });
        } else {
          const item = checkboxItem.props.options.find(
            option => option.label === resultItem
          );
          return item ? item.value : "";
        }
      });

      let resultObject = {};
      resultObject[checkboxItem._fc_id] = resultList;
      Object.assign(detailObject, resultObject);
    } else {
      const resultList = result.split(",").map(resultItem => {
        if (resultItem === "") {
          return "";
        }
        if (resultItem.indexOf(",") > -1) {
          const checkboxData = resultItem.split(",");
          return checkboxData.map(checkbox => {
            const item = checkboxItem.props.options.find(
              option => option.label === checkbox
            );
            return item && item.value ? item.value : "";
          });
        } else {
          const item = checkboxItem.props.options.find(
            option => option.label === resultItem
          );
          return item && item.value ? item.value : "";
        }
      });
      let resultObject = {};
      resultObject[checkboxItem._fc_id] = resultList;
      Object.assign(detailObject, resultObject);
    }

    let parentObject = {};
    parentObject[outKeyId] = detailObject;
    newEntireDetail = Object.assign(entireDetail, parentObject);
  }

  for (let formId of formIdList) {
    const visibleFormData = calculateFormVisibleExpression(
      formId,
      { entireDetail: newEntireDetail },
      formVisibleConditions
    );
    Object.assign(formVisibleData, visibleFormData);
  }
  self.postMessage({ formVisibleData: formVisibleData, formId: formIdList });
};
