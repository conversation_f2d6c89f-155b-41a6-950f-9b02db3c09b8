<template>
  <div class="sccs-data-fence-template-top">
    <div class="sccs-data-fence-template-top-header">
      <div class="sccs-data-fence-template-top-title">
        <span class="sccs-data-fence-template-top-tip">
          {{ t("trade_common_label2") }}
        </span>
        <el-checkbox
          v-model="labelFieldsCheckAll"
          class="checkbox-parent"
          :indeterminate="labelFieldsIndeterminate"
          @change="handleCheckedLabelFields"
        >
          {{ t("trade_common_CannotBeViewed") }}
        </el-checkbox>
      </div>
    </div>
    <el-checkbox-group
      v-model="labelCheckIdList"
      @change="handleCheckedLableFields"
    >
      <el-row :gutter="20" class="sccs-data-fence-container">
        <el-col
          v-for="labelField in labelFields"
          :key="labelField.id"
          :span="6"
          class="sccs-data-fence-col"
        >
          <div class="sccs-data-fence-text">
            {{ labelField.labelName }}
          </div>
          <el-checkbox
            :model-value="labelCheckIdList.includes(labelField.id)"
            :label="t('trade_common_CannotBeViewed')"
            :value="labelField.id"
          />
        </el-col>
      </el-row>
    </el-checkbox-group>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const labelFieldsCheckAll = ref<boolean>(false);
const labelFieldsIndeterminate = ref<boolean>(false);
const labelCheckIdList = ref<any[]>([]);

const props = defineProps({
  labelFields: {
    type: Array as PropType<any>,
    default: () => []
  },
  dataFenceWidgetHiddenIds: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const emit = defineEmits(["handleChangeFieldsChecked"]);

const handleCheckedLabelFields = (bool: any) => {
  if (bool) {
    const labelFieldIdList = props.labelFields.map(labelField => labelField.id);
    labelCheckIdList.value = labelFieldIdList;
  } else {
    labelCheckIdList.value = [];
  }
  labelFieldsCheckAll.value = bool;
  if (labelCheckIdList.value.length === props.labelFields.length) {
    labelFieldsIndeterminate.value = false;
  } else if (labelCheckIdList.value.length === 0) {
    labelFieldsIndeterminate.value = false;
  } else {
    labelFieldsIndeterminate.value = true;
  }
};

const handleCheckedLableFields = (value: any[]) => {
  if (value.length === props.labelFields.length) {
    labelFieldsIndeterminate.value = false;
    labelFieldsCheckAll.value = true;
  } else if (value.length === 0) {
    labelFieldsIndeterminate.value = false;
    labelFieldsCheckAll.value = false;
  } else {
    labelFieldsIndeterminate.value = true;
    labelFieldsCheckAll.value = false;
  }
};

const findCommonElements = (arrA, arrB) => {
  const setB = new Set(arrB);
  return arrA.filter(element => setB.has(element));
};

watch(
  () => labelCheckIdList.value,
  () => {
    emit("handleChangeFieldsChecked", labelCheckIdList.value);
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => [...props.dataFenceWidgetHiddenIds, ...props.labelFields],
  () => {
    const labelFieldIdList = props.labelFields.map(labelField => labelField.id);
    const labelFields = findCommonElements(
      props.dataFenceWidgetHiddenIds,
      labelFieldIdList
    );
    labelCheckIdList.value = labelFields;
    emit("handleChangeFieldsChecked", labelCheckIdList.value);
    if (labelFields.length === props.labelFields.length) {
      labelFieldsIndeterminate.value = false;
      labelFieldsCheckAll.value = true;
    } else if (labelFields.length === 0) {
      labelFieldsIndeterminate.value = false;
      labelFieldsCheckAll.value = false;
    } else {
      labelFieldsIndeterminate.value = true;
      labelFieldsCheckAll.value = false;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  labelFieldIds: labelCheckIdList.value
});
</script>
