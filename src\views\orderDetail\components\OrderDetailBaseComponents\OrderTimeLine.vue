<template>
  <el-scrollbar>
    <div class="order-time-line-body">
      <div
        class="order-time-line-col"
        :class="[
          anchorId === 'order-detail-basic-info' ? 'active' : '',
          widgetBasicInfo.state
        ]"
        @click="handleSelectAnchor('order-detail-basic-info')"
      >
        <div class="order-time-line-col-header">
          <ReText
            type="info"
            class="order-time-line-col-title"
            :tippyProps="{ delay: 50000 }"
          >
            {{ widgetBasicInfo.name }}
          </ReText>
        </div>
        <div class="order-tag-body">
          <ReText
            v-for="label in getMainFormLabelList()"
            :key="label.labelId"
            class="order-tag"
            :style="{ 'border-color': label.style, color: label.style }"
          >
            {{ label.labelValue }}
          </ReText>
        </div>
        <div class="order-tag-data-form-body">
          <div class="order-tag-data-col">
            <span class="order-tag-data-title">
              {{ t("trade_common_create") }}
            </span>
            <span class="order-tag-data-value">
              <LKAvater
                :teamInfo="{
                  avatar: widgetBasicInfo.createUser?.avatar,
                  username: widgetBasicInfo.createUser?.username,
                  email: widgetBasicInfo.createUser?.email
                }"
                :size="18"
              />
              <span class="order-tag-data-value-date-picker">
                {{
                  dayjs(new Date(widgetBasicInfo.createTime)).format(
                    "YYYY/MM/DD HH:mm:ss"
                  )
                }}
              </span>
            </span>
          </div>
          <div class="order-tag-data-col">
            <span class="order-tag-data-title">
              {{ t("trade_common_updates") }}
            </span>
            <span
              v-if="widgetBasicInfo.updateUser"
              class="order-tag-data-value"
            >
              <LKAvater
                :teamInfo="{
                  avatar: widgetBasicInfo.updateUser.avatar,
                  username: widgetBasicInfo.updateUser.username,
                  email: widgetBasicInfo.updateUser?.email
                }"
                :size="18"
              />
              <span class="order-tag-data-value-date-picker">
                {{
                  dayjs(new Date(widgetBasicInfo.updateTime)).format(
                    "YYYY/MM/DD HH:mm:ss"
                  )
                }}
              </span>
            </span>
            <span v-else class="order-tag-data-value">-</span>
          </div>
        </div>
      </div>
      <div
        v-for="item in widgetLists"
        :id="item.msId"
        :key="item.msId"
        :data-id="item.msId"
        class="order-time-line-col"
        :class="[anchorId === item.msId ? 'active' : '', item.status]"
        @click="handleSelectAnchor(item.msId, 'click')"
      >
        <div class="order-time-line-col-header">
          <ReText
            type="info"
            class="order-time-line-col-title"
            :tippyProps="{ delay: 50000 }"
          >
            {{ item.name }}
          </ReText>

          <el-tooltip
            v-if="createWorkOrderBtnVisible(item)"
            effect="dark"
            :content="t('trade_order_create_task')"
            placement="top"
            :show-after="500"
          >
            <i
              class="iconfont link-add"
              @click.stop="handleAddWorkOrder(item)"
            />
          </el-tooltip>
        </div>
        <div class="order-tag-body">
          <ReText
            v-for="label in getLabelList(item.msId)"
            :key="label.labelId"
            class="order-tag"
            :style="{ 'border-color': label.style, color: label.style }"
          >
            {{ label.labelValue }}
          </ReText>
        </div>
        <div class="order-tag-data-form-body">
          <div class="order-tag-data-col">
            <span class="order-tag-data-title">
              {{ t("trade_common_directorPerson") }}
            </span>
            <span v-if="item.managerUser" class="order-tag-data-value">
              <LKAvater
                :teamInfo="{
                  avatar: item.managerUser?.avatar,
                  username: item.managerUser?.username,
                  email: item.managerUser?.email
                }"
                :size="18"
              />
            </span>
            <span v-else class="order-tag-data-value">-</span>
          </div>
          <div class="order-tag-data-col">
            <span class="order-tag-data-title">
              {{ t("trade_common_plan") }}
            </span>
            <span class="order-tag-data-value">
              {{ formatDateValue(item.plannedStartDate) }}
              <span class="order-tag-data-value-tip">-</span>
              {{ formatDateValue(item.plannedEndDate) }}
            </span>
          </div>
          <div class="order-tag-data-col">
            <span class="order-tag-data-title">
              {{ t("trade_order_actualStart") }}
            </span>
            <div class="order-tag-data-value-flex">
              <span class="order-tag-data-value">
                {{ formatDateValue(item.actualStartDate) }}
              </span>
              <span
                v-if="
                  dateLabelFlag(item.plannedStartDate, item.actualStartDate)
                "
                class="order-data-val-tag-item"
                :class="[
                  getDateStateText(item.plannedStartDate, item.actualStartDate)
                    ?.timeClassName
                ]"
              >
                {{
                  getDateStateText(item.plannedStartDate, item.actualStartDate)
                    ?.planTimeText
                }}
              </span>
            </div>
          </div>
          <div class="order-tag-data-col">
            <span class="order-tag-data-title">
              {{ t("trade_order_actualEnd") }}
            </span>
            <div class="order-tag-data-value-flex">
              <span class="order-tag-data-value">
                {{ formatDateValue(item.actualEndDate) }}
              </span>
              <span
                v-if="dateLabelFlag(item.plannedEndDate, item.actualEndDate)"
                class="order-data-val-tag-item"
                :class="[
                  getDateStateText(item.plannedEndDate, item.actualEndDate)
                    ?.timeClassName
                ]"
              >
                {{
                  getDateStateText(item.plannedEndDate, item.actualEndDate)
                    ?.planTimeText
                }}
              </span>
            </div>
          </div>
          <div
            v-if="getMilestoneCardData(item.msId).visible"
            class="order-tag-data-col"
          >
            <span class="order-tag-data-title">
              {{ t("trade_template_replyBtnName") }}
            </span>
            <span class="order-tag-data-value">
              <i
                v-if="item.msReplyCommitStatus === 'COMMITTED'"
                class="iconfont link-work-order-success workOrderSuccessIconfont"
              />
              <span v-if="item.msReplyCommitStatus === 'NOT_CREATE'"> - </span>
              <span
                v-if="item.msReplyCommitStatus === 'NOT_COMMIT'"
                class="order-tag-data-not-submit-text"
              >
                {{ t("trade_order_notSubmit") }}
              </span>
            </span>
          </div>
          <div
            v-if="getMilestoneData(item.msId).workOrderReplyForm"
            class="order-tag-data-col"
          >
            <span class="order-tag-data-title">
              {{ t("trade_order_taskReplyComplete") }}
            </span>
            <span class="order-tag-data-value">
              {{ item.completeReplyWorkOrderCount }}/{{ item.workOrderCount }}
            </span>
            <el-tooltip
              effect="dark"
              :content="t('trade_order_WorkOrderTooltipTip')"
              placement="top"
              :show-after="500"
            >
              <i class="iconfont link-explain order-tag-icon" />
            </el-tooltip>
          </div>
          <div class="order-tag-data-col">
            <span class="order-tag-data-title">
              {{ t("trade_order_workOrderTaskCompleted") }}
            </span>
            <span class="order-tag-data-value">
              {{ item.completeEditWorkOrderCount }}/{{ item.workOrderCount }}
            </span>
            <el-tooltip
              effect="dark"
              :content="t('trade_order_WorkOrderTooltipNextTip')"
              placement="top"
              :show-after="500"
            >
              <i class="iconfont link-explain order-tag-icon" />
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="order-state-body">
        <div
          v-for="item in orderStateList"
          :key="item.value"
          class="order-state-col"
          :class="item.value"
        >
          {{ t(item.label) }}
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import dayjs from "dayjs";
import { ReText } from "@/components/ReText";
import { differenceInDays } from "date-fns";
import { storageLocal } from "@pureadmin/utils";
import LKAvater from "@/components/lkAvatar/index";
import { useOnceWhen } from "@/hooks";

const props = defineProps({
  widgetLists: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetBasicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  containerRef: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneCard: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneCardList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  labelList: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const { t } = useI18n();
const route = useRoute();
const orderStateList = ref([
  {
    value: "not-started",
    label: "trade_order_stateNotStart",
    status: "NOT_START"
  },
  {
    value: "progress",
    label: "trade_order_stateProgress",
    status: "ON_GOING"
  },
  {
    value: "completed",
    label: "trade_order_stateCompleted",
    status: "COMPLETE"
  },
  {
    value: "cancel",
    label: "trade_order_stateCancel",
    status: "CANCEL"
  }
]);
const anchorId = ref<string>("order-detail-basic-info");

const getMilestoneData = (msId: string): any => {
  return props.milestoneCard.find(milestoneItem => milestoneItem.msId === msId);
};

const getMilestoneCardData = (msId: string): any => {
  return props.milestoneCardList.find(
    milestoneItem => milestoneItem.msId === msId
  );
};

const getLabelList = id => {
  return props.labelList.filter(labelItem => labelItem.relationId === id);
};

const getMainFormLabelList = () => {
  return props.labelList.filter(labelItem => labelItem.isMainForm === 1);
};

const createWorkOrderBtnVisible = item => {
  if (!item) return false;

  const { orderId } = route.query;
  const teamRoleObject = storageLocal().getItem(`${orderId}_userTeamRole`);
  if (!teamRoleObject) {
    return false;
  }
  const teamRoleLists =
    teamRoleObject[typeof item === "string" ? item : item.msId];
  return (
    !["COMPLETE", "CANCEL"].includes(props.widgetBasicInfo.state) &&
    ["NOT_START", "ON_GOING"].includes(item.status) &&
    teamRoleLists.includes("create_work_order")
  );
};

const handleSelectAnchor = (id, actionType?): void => {
  if (actionType !== "click") {
    const timeLineDom = document.querySelector(
      `.order-time-line-col[data-id="${id}"]`
    );
    timeLineDom && timeLineDom.scrollIntoView();
  }
  const dom = document.querySelector(
    `.order-detail-region-body[data-id="${id}"]`
  );
  anchorId.value = id;
  dom && dom.scrollIntoView();
  // 每次选择里程碑后，都需要重新获取当前里程碑默认tab的动态数量
  emit("upDataCount", id, id, "MS_REPLY_DYNAMIC");
};

const formatDateValue = (dateVal: any) => {
  return dateVal ? dayjs(new Date(dateVal)).format("YYYY/MM/DD") : "-";
};

const getDateStateText = (planDate: any, actualDate: any) => {
  const realDateTime = actualDate || new Date().getTime();
  const realDay = dayjs(realDateTime).startOf("day");
  const planDay = dayjs(planDate).startOf("day");
  const isSameDay = realDay.isSame(planDay, "day");
  let planTimeText;

  if (!isSameDay) {
    const diffDay = Math.abs(realDay.diff(planDay, "day"));
    planTimeText =
      realDay.diff(planDay, "day") > 0
        ? t("trade_order_delay", { params0: diffDay })
        : t("trade_order_inAdvance", { params0: diffDay });

    return {
      planTimeText: planTimeText,
      timeClassName: realDay.diff(planDay, "day") < 0 ? "inAdvance" : "delay"
    };
  }
};

/**
 * 获取是否有提前，延迟标签
 * @param planDate  计划时间
 * @param actualDate  实际时间
 */
const dateLabelFlag = (planDate: any, actualDate: any) => {
  // 实际开始有值 且 与计划开始不等，或实际开始无值 且 当天>计划开始
  if (planDate && actualDate) {
    const planDateDay = dayjs(planDate);
    const actualDateDay = dayjs(actualDate);
    const isSameDay = planDateDay.isSame(actualDateDay, "day");
    return !isSameDay;
  } else if (planDate && !actualDate) {
    const currentDate = dayjs();
    const specifiedDate = dayjs(planDate);
    const isCurrentDateAfter = currentDate.isAfter(specifiedDate);
    return !!isCurrentDateAfter;
  }
  return false;
};

const emit = defineEmits(["handleAddWorkOrder", "upDataCount"]);

const handleAddWorkOrder = (data): void => {
  emit("handleAddWorkOrder", data);
};

watch(
  () => props.milestoneCardList,
  () => {
    setTimeout(() => {
      document.querySelector(".order-time-line-col.active")?.scrollIntoView();
    });
  }
);

// 当url有参数 msId 时候，默认定为到对应里程碑。只会在加载组件后执行一次。
useOnceWhen(
  () => [props.milestoneCardList.length],
  () => {
    return (
      (route.query.msId || route.query.milestoneId) &&
      props.milestoneCardList.length
    );
  },
  () => {
    const milestoneId = route.query.msId || route.query.milestoneId;
    const milestoneItem = props.milestoneCardList.find(
      item => item.msId === milestoneId
    );
    if (milestoneItem) {
      nextTick(() => {
        handleSelectAnchor(milestoneItem.msId);
      });
    }
  }
);

defineExpose({
  handleSelectAnchor
});
</script>
<style lang="scss" scoped>
@use "../style/index.scss";
</style>
