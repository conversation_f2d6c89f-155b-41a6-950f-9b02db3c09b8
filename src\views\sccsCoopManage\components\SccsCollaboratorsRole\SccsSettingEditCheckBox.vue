<template>
  <div class="role-per-col">
    <div v-if="formElReadonly" class="role-mask" />
    <el-checkbox
      v-model="checkAll"
      class="checkbox-parent"
      :indeterminate="isIndeterminate"
      @change="handleCheckAllChange"
    >
      {{ sccsSettingData.label }}
    </el-checkbox>
    <el-checkbox-group
      v-model="checkedCities"
      @change="handleCheckedCitiesChange"
    >
      <el-checkbox
        v-for="city in sccsSettingData.childrenList"
        :key="city"
        class="checkbox-group-col"
        :label="city.label"
        :value="city.key"
      >
        <div class="checkbox-span">
          {{ city.label }}
          <span class="checkbox-tip">{{ city.remark }}</span>
        </div>
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
<script lang="ts" setup>
import { ref, PropType, watchEffect } from "vue";
import { useI18n } from "vue-i18n";

const checkAll = ref(false);
const isIndeterminate = ref(false);
const checkedCities = ref([]);
let cities = [];

const { t } = useI18n();
const props = defineProps({
  sccsSettingData: {
    type: Object as PropType<any>
  },
  sccsToolRolePermItemList: {
    type: Object as PropType<any>
  },
  formElReadonly: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const handleCheckAllChange = (val: boolean) => {
  let checkedList = [];
  //@ts-ignore
  cities.childrenList.map(child => checkedList.push(child.key));
  checkedCities.value = val ? checkedList : [];
  isIndeterminate.value = false;
};

const handleCheckedCitiesChange = (value: string[]) => {
  const checkedCount = value.length;
  //@ts-ignore
  checkAll.value = checkedCount === cities.childrenList.length;

  isIndeterminate.value =
    //@ts-ignore
    checkedCount > 0 && checkedCount < cities.childrenList.length;
};

watchEffect(() => {
  let bindDataIds: string[] = [];
  if (Object.keys(props.sccsToolRolePermItemList).length > 0) {
    props.sccsSettingData.childrenList.map(child => {
      const index =
        props.sccsToolRolePermItemList.sccsToolRolePermItemList.findIndex(
          role => role.permissionKey === child.key
        );
      if (index !== -1) {
        bindDataIds.push(child.key);
      }
    });
  }

  checkedCities.value = bindDataIds;
  const checkedCount = bindDataIds.length;
  cities = props.sccsSettingData as any;
  //@ts-ignore
  checkAll.value = checkedCount === cities.childrenList.length;
  isIndeterminate.value =
    //@ts-ignore
    checkedCount > 0 && checkedCount < cities.childrenList.length;
});

const getData = (): any => {
  let requirePermission = [];
  checkedCities.value.map(permission =>
    requirePermission.push({
      permissionKey: permission
    })
  );
  return requirePermission;
};

defineExpose({
  checkedCities,
  getData
});
</script>
<style lang="scss" scoped>
.role-per-col {
  position: relative;

  .role-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2000;
    width: 100%;
    height: 100%;
    cursor: not-allowed;
    background: transparent;
  }

  .checkbox-parent {
    margin-bottom: 10px;
    font-weight: bolder;
  }

  .checkbox-group-col {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
