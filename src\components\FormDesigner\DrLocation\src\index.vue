<template>
  <el-input
    v-model="widgetFormData[widgetConfigure._fc_id]"
    clearable
    :disabled="!widgetConfigure.props?.allowInput"
    :placeholder="placeholder"
  >
    <template #append>
      <el-button @click="handleLocation">
        <i class="iconfont link-location" />
      </el-button>
    </template>
  </el-input>
  <div id="container" />
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, ref, watch } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";
import { cloneDeep, isEqual, storageLocal } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

const map = ref(null);
const marker = ref(null);
const locationInfo = ref(null);
const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const handleSubTableWidgetFormsValue: any = inject(
  "handleSubTableWidgetFormsValue",
  null
);

const handleLocation = (): void => {};

const initMap = async () => {
  const AMap = await AMapLoader.load({
    key: "adf68d1e3c0e3dc7f3d49fc336a2ec93", // 替换为您申请的key
    version: "2.0",
    plugins: ["AMap.Geolocation"]
  });

  // 创建地图实例
  map.value = new AMap.Map("container", {
    zoom: 15,
    center: [116.397428, 39.90923]
  });

  // 创建定位对象
  const geolocation = new AMap.Geolocation({
    enableHighAccuracy: true,
    timeout: 100000,
    buttonPosition: "RB",
    buttonOffset: new AMap.Pixel(10, 20),
    zoomToAccuracy: true
  });

  map.value.addControl(geolocation);

  // 获取定位
  geolocation.getCurrentPosition((status, result) => {
    if (status === "complete") {
      const { position, accuracy } = result;

      // 更新位置信息
      locationInfo.value = {
        lng: position.lng,
        lat: position.lat,
        accuracy: accuracy
      };

      // 添加标记点
      marker.value = new AMap.Marker({
        position: position,
        map: map.value
      });

      // 添加定位精度圈
      const circle = new AMap.Circle({
        center: position,
        radius: accuracy,
        fillOpacity: 0.2,
        strokeWeight: 1
      });
      map.value.add(circle);

      // 移动地图中心点
      map.value.setCenter(position);
      console.log(locationInfo.value);
    } else {
      console.log(result);
      alert("定位失败，请检查定位权限！");
    }
  });
};

// 监听控件传递进来的值
watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    widgetFormData.value[props.widgetConfigure._fc_id] = cloneDeep(newVal);
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (
      newVal !== props.trendsForm[props.widgetConfigure._fc_id] &&
      !(!newVal && !oldVal)
    ) {
      if (props.widgetRowIndex !== -1) {
        handleSubTableWidgetFormsValue(props.widgetConfigure._fc_id, {
          label: widgetFormData.value[props.widgetConfigure._fc_id],
          obj: widgetFormData.value[props.widgetConfigure._fc_id],
          widgetId: props.widgetConfigure._fc_id,
          $rowIndex: props.widgetRowIndex
        });
      } else {
        handleWidgetFormsValue(props.widgetConfigure._fc_id, {
          label: widgetFormData.value[props.widgetConfigure._fc_id],
          obj: widgetFormData.value[props.widgetConfigure._fc_id],
          widgetId: props.widgetConfigure._fc_id,
          $rowIndex: props.widgetRowIndex
        });
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

// onMounted(() => {
//   initMap();
// });
</script>
