<template>
  <el-dropdown
    placement="bottom"
    trigger="click"
    @visible-change="handleCommand"
  >
    <i class="iconfont link-more font24" />
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="item in props.tableOpeateLists"
          :key="item.icon"
          :class="item.iconClassName"
          @click="handleTableOperate(item.slotName)"
        >
          <span class="table-operate-icon">
            <i class="iconfont" :class="item.icon" />
          </span>
          <span class="table-operate-title">
            {{ t(item.title) }}
          </span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script lang="ts" setup>
import { markRaw } from "vue";
import { useI18n } from "vue-i18n";
import { WarningFilled } from "@element-plus/icons-vue";
import {
  ElMessageBox,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu
} from "element-plus";

interface tableOpeate {
  icon: string;
  title: string;
  slotName: string;
}

interface messageBoxConfirmProp {
  messageBoxTip: string;
  messageBoxTitle: string;
  messageBoxTipArray: string[];
}

const { t } = useI18n();

const props = defineProps({
  tableOpeateLists: {
    type: Object as PropType<tableOpeate[]>,
    default: () => {}
  },
  messageBoxConfirmObject: {
    type: Object as PropType<messageBoxConfirmProp[]>,
    default: () => {}
  },
  row: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const emit = defineEmits(["handleOperate", "handleVisibleChange"]);

const handleCommand = (bool: boolean) => {
  emit("handleVisibleChange", bool, { row: props.row });
};

const handleTableOperate = (slotName: string): void => {
  if (slotName === "delete") {
    let removeI18nList = [];
    props.messageBoxConfirmObject?.messageBoxTipArray.map(boxTip =>
      removeI18nList.push(props.row[boxTip])
    );
    const assistantLangText =
      props.messageBoxConfirmObject?.messageBoxTipArray.length > 0
        ? `${t(props.messageBoxConfirmObject?.messageBoxTip)}「${removeI18nList}」？`
        : `${t(props.messageBoxConfirmObject?.messageBoxTip)}`;
    ElMessageBox.confirm(
      assistantLangText,
      t(props.messageBoxConfirmObject?.messageBoxTitle),
      {
        confirmButtonText: t("trade_common_confirm"),
        cancelButtonText: t("trade_common_cancel"),
        confirmButtonClass: "confrim-message-btn-class",
        cancelButtonClass: "cancel-message-btn-class",
        type: "error",
        icon: markRaw(WarningFilled),
        center: true
      }
    ).then(() => {
      emit("handleOperate", slotName, { row: props.row });
    });
  } else {
    emit("handleOperate", slotName, { row: props.row });
  }
};
</script>
<style lang="scss">
.table-operate-icon {
  width: 24px;
  text-align: center;

  .iconfont {
    font-size: 14px;
  }
}

.table-operate-title {
  font-size: 14px;
  line-height: 20px;
  color: #303133;
}

.link-delete-icon {
  color: #cf1421 !important;

  .table-operate-title {
    color: #cf1421 !important;
  }
}

.link-delete-icon.el-dropdown-menu__item:not(.is-disabled):hover {
  color: #cf1421 !important;

  .table-operate-title {
    color: #cf1421 !important;
  }
}
</style>
