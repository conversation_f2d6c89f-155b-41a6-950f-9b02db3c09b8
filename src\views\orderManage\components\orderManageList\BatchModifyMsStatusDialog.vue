<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_batch_modify_milestone_status')"
    @confirm="handleModifyMilestoneStatusConfirm"
    @close="handleCloseModifyMilestoneStatusDialog"
  >
    <template #default>
      <div
        class="modifyMilestoneStatus-title"
        v-html="batchModifyMsStatusSelectedTip"
      />
      <el-form
        ref="modifyMilestoneFormRef"
        :model="modifyMilestoneForm"
        :rules="modifyMilestoneRules"
        label-position="top"
      >
        <el-form-item :label="t('trade_template_milestoneName')" prop="msId">
          <el-select
            v-model="modifyMilestoneForm.msId"
            filterable
            :placeholder="t('trade_batch_edit_milestone_status_placeholder')"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in milestoneListOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('trade_operation_type')" prop="operationType">
          <el-select
            v-model="modifyMilestoneForm.operationType"
            filterable
            :placeholder="t('trade_select_operation_type_placeholder')"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in operationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div
        v-loading="modifyMilestoneStatusLoading"
        class="modifyMilestoneStatus-executable-situation"
      >
        <div
          v-if="Object.keys(updateMsStatusOrder).length > 0"
          class="have-update-ms-status-order-info"
        >
          <div class="order-info-title">
            {{ t("trade_only_execute_modifiable_orders") }}
          </div>
          <el-scrollbar height="104px">
            <div class="order-info-list">
              <div class="order-info-item">
                <div class="order-info-item-title">
                  {{
                    `${t("trade_modifiable_order")}(${updateMsStatusOrder.operableOrder.length})：`
                  }}
                </div>
                <div class="order-info-item-orderMarks">
                  <span>{{
                    updateMsStatusOrder.operableOrder.length === 0
                      ? t("trade_nothing")
                      : operableOrderStr
                  }}</span>
                </div>
              </div>
              <div class="order-info-item">
                <div class="order-info-item-title">
                  {{
                    `${t("trade_cannot_modify_order")}(${updateMsStatusOrder.notOperableOrder.length})：`
                  }}
                </div>
                <div class="order-info-item-orderMarks">
                  <span>{{
                    updateMsStatusOrder.notOperableOrder.length === 0
                      ? t("trade_nothing")
                      : notOperableOrderStr
                  }}</span>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <span v-else class="modifyMilestoneStatus-placeholder-text">{{
          t("trade_please_select_milestones_first_placeholder")
        }}</span>
      </div>
    </template>
  </LkDialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, markRaw, PropType, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox, type FormRules } from "element-plus";
import { useRoute } from "vue-router";
import LkDialog from "@/components/lkDialog/index";
import { WarningFilled } from "@element-plus/icons-vue";

import {
  getBatchOperationMilestoneList,
  getHaveUpdateMsStatusOrderInfo,
  batchUpdateMsStatus
} from "@/api/order";

const props = defineProps({
  currentSccsId: {
    type: String as PropType<any>,
    default: ""
  },
  selectedNumber: {
    type: Number as PropType<any>,
    default: 0
  },
  tableSelectedList: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const { t } = useI18n();
const route = useRoute();

// 批量修改里程碑状态
const LkDialogRef = ref<HTMLElement | any>(null);
const modifyMilestoneFormRef = ref<HTMLElement | any>(null);
const modifyMilestoneForm = ref<any>({
  msId: "", // 里程碑id
  operationType: "" // 操作类型 1.RESTART 2.CANCEL 3.COMPLETE
});
const modifyMilestoneRules = reactive<FormRules<any>>({
  msId: [
    {
      required: true,
      message: t("trade_batch_edit_milestone_status_placeholder"),
      trigger: "change"
    }
  ],
  operationType: [
    {
      required: true,
      message: t("trade_select_operation_type_placeholder"),
      trigger: "change"
    }
  ]
});
const milestoneListOptions = ref<any>([]);
const operationTypeOptions = ref<any>([
  {
    label: t("trade_order_restartMilestone"),
    value: "RESTART"
  },
  {
    label: t("trade_order_confrimMilestone"),
    value: "COMPLETE"
  },
  {
    label: t("trade_order_cancelMilestone"),
    value: "CANCEL"
  }
]);
const updateMsStatusOrder = ref<any>({});
const notOperableOrderStr = ref<string>("");
const operableOrderStr = ref<string>("");
// 可操作的订单id列表
const operationOrderIdList = ref<any>([]);
const modifyMilestoneStatusLoading = ref<boolean>(false);

const batchModifyMsStatusSelectedTip = computed(() => {
  return useI18n().t("trade_batchModifyMsStatusSelectedTip", {
    params0: `<span>${props.selectedNumber}</span>`
  });
});
// 批量修改里程碑状态 确认提交
const handleModifyMilestoneStatusConfirm = () => {
  // 校验表单
  modifyMilestoneFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return;
    }
    // 校验通过，发送请求
    // 判断当前操作类型给出不同提示
    const operationTypeMap = {
      RESTART: {
        title: t("trade_order_restartMilestone"),
        message: t("trade_order_restartMilestoneTip")
      },
      CANCEL: {
        title: t("trade_order_cancelMilestone"),
        message: t("trade_order_cancelMilestoneTip")
      },
      COMPLETE: {
        title: t("trade_order_confrimMilestone"),
        message: t("trade_order_confrimMilestoneTip")
      }
    };
    if (operationOrderIdList.value && operationOrderIdList.value.length === 0) {
      ElMessage({
        message: t("trade_no_operated_order_tip"),
        type: "warning"
      });
    } else {
      ElMessageBox.confirm(
        operationTypeMap[modifyMilestoneForm.value.operationType].message,
        operationTypeMap[modifyMilestoneForm.value.operationType].title,
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      )
        .then(async () => {
          const { code } = await batchUpdateMsStatus({
            sccsId: props.currentSccsId,
            orderIdList: operationOrderIdList.value,
            msId: modifyMilestoneForm.value.msId,
            operationType: modifyMilestoneForm.value.operationType
          });
          operateAfter(code);
        })
        .catch(e => {
          console.log(e);
        });
    }
  });
};
const operateAfter = code => {
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    modifyMilestoneFormRef.value.resetFields();
    modifyMilestoneFormRef.value.clearValidate();
    // 更新表格数据
    LkDialogRef.value.close();

    handleReloadTableData();
  }
};
// 批量修改里程碑状态 关闭弹窗
const handleCloseModifyMilestoneStatusDialog = () => {
  modifyMilestoneFormRef.value.resetFields();
  modifyMilestoneFormRef.value.clearValidate();
  LkDialogRef.value.close();
};
// 初始化里程碑列表
const open = async () => {
  LkDialogRef.value.open();
  // 2.获取可以批量操作的里程碑列表
  const { code, data } = await getBatchOperationMilestoneList({
    sccsId: props.currentSccsId,
    batchOperationType: "BATCH_UPDATE_MILESTONE_STATUS"
  });
  if (code === 0) {
    // 3.获取可以批量操作的里程碑列表
    milestoneListOptions.value = data?.map(item => {
      return {
        label: item.name,
        value: item.id
      };
    });
  }
};
defineExpose({
  open
});

const emit = defineEmits(["handleReloadTableData"]);
// 重新加载表格数据
const handleReloadTableData = () => {
  emit("handleReloadTableData");
};

watch(
  () => modifyMilestoneForm.value,
  () => {
    if (
      modifyMilestoneForm.value.msId &&
      modifyMilestoneForm.value.operationType
    ) {
      let params = {
        sccsId: props.currentSccsId,
        msId: modifyMilestoneForm.value.msId,
        operationType: modifyMilestoneForm.value.operationType,
        orderIdList: props.tableSelectedList.map(item => item.orderId)
      };
      modifyMilestoneStatusLoading.value = true;
      getHaveUpdateMsStatusOrderInfo(params).then(res => {
        updateMsStatusOrder.value = res.data;
        let notArray = [] as any;
        let operableArray = [] as any;
        operationOrderIdList.value = [];
        notOperableOrderStr.value = "";
        operableOrderStr.value = "";
        if (res.data.notOperableOrder.length > 0) {
          updateMsStatusOrder.value.notOperableOrder.forEach(item => {
            notArray.push(item.orderMark);
          });
          notOperableOrderStr.value = notArray.join("、");
        }
        if (res.data.operableOrder.length > 0) {
          updateMsStatusOrder.value.operableOrder.forEach(item => {
            operableArray.push(item.orderMark);
            operationOrderIdList.value.push(item.id);
          });
          operableOrderStr.value = operableArray.join("、");
        }
        modifyMilestoneStatusLoading.value = false;
      });
    } else {
      updateMsStatusOrder.value = {};
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss">
@use "../index.scss";
</style>
