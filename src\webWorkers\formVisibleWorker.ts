import dayjs from "dayjs";
import { ExecuteFormVisibleExpression } from "@/utils/formVisibleExpression";

self.onmessage = async e => {
  const {
    detailData,
    ids,
    msId,
    milestoneCardList,
    formVisibleTemplateFormConfigure,
    msPresentationItemData,
    templateData
  } = JSON.parse(e.data);

  const formVisibleResult = await handleCalculationFormVisible(
    ids,
    templateData,
    detailData,
    formVisibleTemplateFormConfigure,
    msPresentationItemData,
    milestoneCardList,
    msId
  );

  if (!!formVisibleResult) {
    self.postMessage({
      formId: Object.keys(formVisibleResult)[0],
      formResultData: formVisibleResult
    });
  } else {
    self.postMessage({});
  }
};

/**
 * 1.接口数据转化成表单需要的值
 * @param widgetList 控件值
 * @param subWidgetMap 子表控件的值
 * @param linkedReferenceMap 关联引用控件的值
 * @returns
 */
export const TransformSubmitDataStructure = (
  widgetList,
  subWidgetMap,
  linkedReferenceMap
) => {
  let widgetFormData = [];
  if (!widgetList) return;
  for (let i = 0, len = widgetList.length; i < len; i++) {
    const widgetData = widgetList[i];
    if (!widgetData.parentWidgetId) {
      if (!["TABLE_FORM", "DR_RELATE_CARD"].includes(widgetData.widgetType)) {
        const index = widgetFormData.findIndex(
          widgetItem => widgetItem.widgetId === widgetData.widgetId
        );
        if (index === -1) {
          widgetFormData.push({
            label: widgetData.label,
            obj: getWidgetBindValue(widgetData),
            showObj: widgetData.showObj,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId
          });
        } else {
          widgetFormData.splice(index, 1, {
            label: widgetData.label,
            obj: getWidgetBindValue(widgetData),
            showObj: widgetData.showObj,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId
          });
        }
      } else if (widgetData.widgetType === "DR_RELATE_CARD") {
        if (
          linkedReferenceMap &&
          linkedReferenceMap.hasOwnProperty(widgetData.widgetId)
        ) {
          const linkedReferenceMapData = linkedReferenceMap[
            widgetData.widgetId
          ].map(linkReference => {
            if (linkReference.widgetType === "TABLE_FORM") {
              const subTableAssemblyData = Object.values(
                subWidgetMap[linkReference.widgetId].reduce((res, item) => {
                  res[item.rowIndex]
                    ? res[item.rowIndex].push(item)
                    : (res[item.rowIndex] = [item]);
                  return res;
                }, {})
              );
              subTableAssemblyData.forEach((subTableRowData: any) => {
                subTableRowData.forEach(subTableChildRowData =>
                  getWidgetBindValue(subTableChildRowData)
                );
              });
              return Object.assign(linkReference, {
                childrenList: subTableAssemblyData
              });
            } else {
              return linkReference;
            }
          });

          const index = widgetFormData.findIndex(
            widgetItem => widgetItem.widgetId === widgetData.widgetId
          );
          if (index === -1) {
            widgetFormData.push({
              label: null,
              obj: linkedReferenceMapData,
              formId: widgetData.formId,
              widgetId: widgetData.widgetId
            });
          } else {
            widgetFormData.splice(index, 1, {
              label: null,
              obj: linkedReferenceMapData,
              formId: widgetData.formId,
              widgetId: widgetData.widgetId
            });
          }
        }
      } else {
        if (subWidgetMap[widgetData.widgetId]) {
          const subTableAssemblyData = Object.values(
            subWidgetMap[widgetData.widgetId].reduce((res, item) => {
              res[item.rowIndex]
                ? res[item.rowIndex].push(item)
                : (res[item.rowIndex] = [item]);
              return res;
            }, {})
          );
          subTableAssemblyData.forEach((subTableRowData: any) => {
            subTableRowData.forEach(subTableChildRowData =>
              getWidgetBindValue(subTableChildRowData)
            );
          });
          widgetFormData.push({
            label: null,
            obj: null,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId,
            childrenList: subTableAssemblyData
          });
        }
      }
    }
  }
  return widgetFormData;
};

export const handleObtainDynamicDefaultValue = (
  formWidgets,
  widgetDefaultData
) => {
  let widgetObject = {};
  for (let i = 0, len = formWidgets.length; i < len; i++) {
    const widget = formWidgets[i];
    if (widget.type === "DrDivider") {
      continue;
    } else if (widget.type === "DrCard") {
      widget.children.forEach(widgetChild => {
        if (widgetChild.children) {
          widgetChild.children.forEach(widgetChildItem => {
            if (
              ["DrInputNumber", "DrPercentage"].includes(widgetChildItem.type)
            ) {
              widgetObject[widgetChildItem._fc_id] = getFormulasWidgetValues(
                widgetChildItem,
                widgetDefaultData
              )
                ? getFormulasWidgetValues(widgetChildItem, widgetDefaultData)
                : 0;
            } else {
              widgetObject[widgetChildItem._fc_id] = getFormulasWidgetValues(
                widgetChildItem,
                widgetDefaultData
              );
            }
          });
        }
      });
    } else if (widget.type === "DrTableForm") {
      if (widgetDefaultData) {
        const widgetItem = widgetDefaultData.find(
          widgetChildData => widgetChildData.widgetId === widget._fc_id
        );

        const childrenWidgetIds = widget.children
          .slice(1)
          .map(childWidget => childWidget.children[0]?._fc_id);

        let subTableObject = {};
        if (widgetItem?.childrenList) {
          for (let widgetId of childrenWidgetIds) {
            let widgetDataList = [];
            for (let childRow of widgetItem?.childrenList) {
              const widgetColData = childRow.find(
                childCol => childCol.widgetId === widgetId
              );
              widgetDataList.push(widgetColData?.obj);
            }
            subTableObject[widgetId] = widgetDataList;
          }
        }

        widgetObject[widget._fc_id] = subTableObject;
      }
    } else {
      if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
        widgetObject[widget._fc_id] = getFormulasWidgetValues(
          widget,
          widgetDefaultData
        )
          ? getFormulasWidgetValues(widget, widgetDefaultData)
          : 0;
      } else {
        widgetObject[widget._fc_id] = getFormulasWidgetValues(
          widget,
          widgetDefaultData
        );
      }
    }
  }

  return widgetObject;
};

export const getFormulasWidgetValues = (widget, defaultData) => {
  if (defaultData && defaultData.length !== 0) {
    const widgetItem = defaultData.find(
      widgetItem => widgetItem.widgetId === widget._fc_id
    );
    if (widgetItem) {
      if (widget.type === "DrRate") {
        return widget.props?.starCount > widgetItem.obj
          ? widgetItem.obj
          : widget.props?.starCount;
      } else if (widget.type === "DrInputNumber") {
        if (!widget.props.min && !widget.props.max) {
          return widgetItem.obj ? widgetItem.obj : 0;
        }
        if (widget.props.min && widget.props.min > widgetItem.obj) {
          return widget.props.min;
        }
        if (widget.props.max && widget.props.max < widgetItem.obj) {
          return widget.props.max;
        }
      } else if (widget.type === "DrCheckbox") {
        let checkboxHtml = "";
        if (widgetItem.obj) {
          for (let widgetItemId of widgetItem.obj) {
            const widgetOptionItem = widget.props.options.find(
              option => option.value === widgetItemId
            );
            if (widgetOptionItem) {
              checkboxHtml += `,${widgetOptionItem.label}`;
            }
          }
          return checkboxHtml.substr(1);
        }
        return checkboxHtml;
      } else if (widget.type === "DrRadio") {
        const widgetOptionItem = widget.props.options.find(
          option => option.value === widgetItem.obj
        );
        return widgetOptionItem ? widgetOptionItem.label : "";
      } else if (widget.type === "DrAddress") {
        return widgetItem.label ? widgetItem.label : "";
      } else if (widget.type === "DrExchangeRates") {
        return widgetItem.obj ? widgetItem.obj : {};
      } else if (widget.type === "DrDatePicker") {
        return widgetItem.obj ? widgetItem.obj : "";
      } else if (
        ["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(widget.type)
      ) {
        return widgetItem.obj ? widgetItem.obj : {};
      } else {
        return widgetItem.obj;
      }
    } else {
      if (widget.type === "DrCheckbox") {
        return [];
      } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
        return null;
      } else if (widget.type === "DrAddress") {
        return "";
      } else {
        return "";
      }
    }
  } else {
    if (widget.type === "DrCheckbox") {
      return [];
    } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
      return null;
    } else if (widget.type === "DrExchangeRates") {
      return {};
    } else {
      return "";
    }
  }
};

export const renderWidget = (widgetJsonList, widgetId, milestoneItemData) => {
  let workOrderWidgetReplyObject = {};
  let workOrderReplyArrayObject = {};
  const workOrderWidgetIdList = widgetJsonList.map(
    workOrderReplyWidget => workOrderReplyWidget._fc_id
  );
  let workOrderWidgetList = {};
  for (let widgetName of workOrderWidgetIdList) {
    workOrderWidgetList[widgetName] = [];
  }

  if (milestoneItemData && milestoneItemData.workOrderGroupList) {
    for (let workOrderItem of milestoneItemData.workOrderGroupList) {
      const milestoneData = handleObtainDynamicDefaultValue(
        widgetJsonList,
        TransformSubmitDataStructure(
          workOrderItem.widgetList,
          workOrderItem.subWidgetMap,
          workOrderItem.linkedReferenceMap
        )
      );

      for (let milestoneName in milestoneData) {
        let workOrderList = workOrderWidgetList[milestoneName];
        if (!(workOrderWidgetList[milestoneName] instanceof Array)) {
          workOrderList = [];
        }

        workOrderList.push(milestoneData[milestoneName]);
      }

      workOrderReplyArrayObject[workOrderItem.workOrderId] = milestoneData;
    }
  }

  workOrderWidgetReplyObject[widgetId] = {
    flatMapData: workOrderReplyArrayObject,
    nestMapData: workOrderWidgetList
  };
  return workOrderWidgetReplyObject;
};

/**
 * 根据表单类型转化控件值
 * @param widget
 * @returns
 */
export const getWidgetBindValue = widget => {
  if (widget.widgetType === "DATE_PICKER") {
    return matchDateFormatStr(widget.label, widget.obj);
  } else {
    if (typeof widget.obj === "string") {
      return widget.obj.replace(/\\/g, "&&amp;");
    }
    return widget.obj;
  }
};

/**
 * 日期时间控件：前端传递的是时间戳，但是后端返回的是字符串，前端在传递进入控件之前，需要进行类型转化
 * @param valData
 * @param timeStamp
 * @returns
 */
export const matchDateFormatStr = (valData, timeStamp) => {
  if (!valData) return "";
  const dateFormatAll = valData.split(" ").length > 1;
  if (dateFormatAll) {
    return dayjs(new Date(timeStamp)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    if (valData.split(" ")[0].split("-").length === 1) {
      return dayjs(new Date(timeStamp)).format("YYYY");
    } else {
      return dayjs(new Date(timeStamp)).format("YYYY-MM-DD");
    }
  }
};

const getFormListWidgetJsonList = widgetJsonList => {
  let widgetList = [];
  for (let widgetJson of widgetJsonList) {
    if (widgetJson.type === "DrCard") {
      const cardChilds = widgetJson.children
        .filter(col => col.children)
        .map(col => col.children[0])
        .filter(Boolean);
      widgetList.push(...getFormListWidgetJsonList(cardChilds));
    } else {
      widgetList.push(widgetJson);
    }
  }
  return widgetList;
};

const getWorkOrderGroupData = workOrderData => {
  if (workOrderData.length === 0) {
    return {};
  }
  let workOrderKeys = Object.keys(workOrderData[0]);
  let workOrderObject = {};
  for (let workOrderKey of workOrderKeys) {
    workOrderObject[workOrderKey] = workOrderData.map(
      workOrder => workOrder[workOrderKey]
    );
  }
  return workOrderObject;
};

/**
 * 获取当前订单的整个表单数据源
 * @param milestoneId 里程碑id 允许为空
 * @param workOrderId 工单id 允许为空
 * @returns
 */
export const getEntireFormData = (
  widgetConfigData,
  milestoneId,
  workOrderId
) => {
  const { entrieTemplateForm, templateData } = widgetConfigData;
  const detailData = templateData.orderDetailData;
  let orderDetailObject = {};
  let checkboxList = [];
  let widgetList = [];
  const mainFormData = TransformSubmitDataStructure(
    detailData.widgetList,
    detailData.subWidgetMap,
    detailData.linkedReferenceMap
  );
  const mainFormObject = handleObtainDynamicDefaultValue(
    entrieTemplateForm.mainForm.widgetJsonList,
    mainFormData
  );

  orderDetailObject[entrieTemplateForm.mainForm.id] = mainFormObject;
  checkboxList = checkboxList.concat(
    getFormListWidgetJsonList(
      entrieTemplateForm.mainForm.widgetJsonList
    ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
  );
  widgetList = widgetList.concat(
    getFormListWidgetJsonList(entrieTemplateForm.mainForm.widgetJsonList)
  );

  if (
    detailData.milestoneGroupList &&
    milestoneId &&
    entrieTemplateForm.milestoneList.length !== 0
  ) {
    for (let msFormDetail of detailData.milestoneGroupList) {
      const { msId, subWidgetMap, replyWidgetList, linkedReferenceMap } =
        msFormDetail;
      const msTemplateConfig = entrieTemplateForm.milestoneList.find(
        msConfig => msConfig.msId === msId
      );

      if (msTemplateConfig.replyForm) {
        const msReplyObject = handleObtainDynamicDefaultValue(
          msTemplateConfig.replyForm.widgetJsonList,
          TransformSubmitDataStructure(
            replyWidgetList,
            subWidgetMap,
            linkedReferenceMap
          )
        );

        orderDetailObject[msTemplateConfig.replyForm.id] = msReplyObject;
        checkboxList = checkboxList.concat(
          getFormListWidgetJsonList(
            msTemplateConfig.replyForm.widgetJsonList
          ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
        );
        widgetList = widgetList.concat(
          getFormListWidgetJsonList(msTemplateConfig.replyForm.widgetJsonList)
        );
      }

      let msFormWidgetList = [];
      for (let msForm of msTemplateConfig.formList) {
        msFormWidgetList = msFormWidgetList.concat(msForm.widgetJsonList);
      }

      if (msId === milestoneId) {
        const currentWorkOrderForm = msFormDetail.workOrderGroupList.find(
          workOrderData => workOrderData.workOrderId === workOrderId
        );

        for (let msFormItem of msTemplateConfig.formList) {
          const msFormWidgetList = getFormListWidgetJsonList(
            msFormItem.widgetJsonList
          );

          const value = currentWorkOrderForm
            ? handleObtainDynamicDefaultValue(
                msFormWidgetList,
                TransformSubmitDataStructure(
                  currentWorkOrderForm.widgetList,
                  currentWorkOrderForm.subWidgetMap,
                  currentWorkOrderForm.linkedReferenceMap
                )
              )
            : getWorkOrderGroupData(
                msFormDetail.workOrderGroupList.map(workOrder => {
                  return handleObtainDynamicDefaultValue(
                    msFormWidgetList,
                    TransformSubmitDataStructure(
                      workOrder.widgetList,
                      workOrder.subWidgetMap,
                      workOrder.linkedReferenceMap
                    )
                  );
                })
              );

          orderDetailObject[msFormItem.id] = value;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList).filter(
              widget => ["DrCheckbox", "DrRadio"].includes(widget.type)
            )
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList)
          );
        }

        if (msTemplateConfig.workOrderReplyForm) {
          const msReplyWidgetList = getFormListWidgetJsonList(
            msTemplateConfig.workOrderReplyForm.widgetJsonList
          );

          const value = currentWorkOrderForm
            ? handleObtainDynamicDefaultValue(
                msReplyWidgetList,
                TransformSubmitDataStructure(
                  currentWorkOrderForm.widgetList,
                  currentWorkOrderForm.subWidgetMap,
                  currentWorkOrderForm.linkedReferenceMap
                )
              )
            : handleObtainDynamicDefaultValue(
                msReplyWidgetList,
                TransformSubmitDataStructure([], [], {})
              );

          orderDetailObject[msTemplateConfig.workOrderReplyForm.id] = value;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            )
          );
        }
      } else {
        for (let msFormItem of msTemplateConfig.formList) {
          const msFormWidgetList = getFormListWidgetJsonList(
            msFormItem.widgetJsonList
          );
          let msFormObject = {};
          for (let msFormWidgetItem of msFormWidgetList) {
            msFormObject[msFormWidgetItem._fc_id] = [];
            for (let workOrderDetail of msFormDetail.workOrderGroupList) {
              const value = handleObtainDynamicDefaultValue(
                [msFormWidgetItem],
                TransformSubmitDataStructure(
                  workOrderDetail.widgetList,
                  workOrderDetail.subWidgetMap,
                  workOrderDetail.linkedReferenceMap
                )
              );
              msFormObject[msFormWidgetItem._fc_id].push(
                ...Object.values(value)
              );
            }
          }
          orderDetailObject[msFormItem.id] = msFormObject;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList).filter(
              widget => ["DrCheckbox", "DrRadio"].includes(widget.type)
            )
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList)
          );
        }

        if (msTemplateConfig.workOrderReplyForm) {
          const workOrderReplyWidgetList = getFormListWidgetJsonList(
            msTemplateConfig.workOrderReplyForm.widgetJsonList
          );
          let msFormObject = {};
          for (let msFormWidgetItem of workOrderReplyWidgetList) {
            msFormObject[msFormWidgetItem._fc_id] = [];
            for (let workOrderDetail of msFormDetail.workOrderGroupList) {
              const value = handleObtainDynamicDefaultValue(
                [msFormWidgetItem],
                TransformSubmitDataStructure(
                  workOrderDetail.widgetList,
                  workOrderDetail.subWidgetMap,
                  workOrderDetail.linkedReferenceMap
                )
              );
              msFormObject[msFormWidgetItem._fc_id].push(
                ...Object.values(value)
              );
            }
          }
          orderDetailObject[msTemplateConfig.workOrderReplyForm.id] =
            msFormObject;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            )
          );
        }
      }
    }
  }

  return {
    checkboxList: checkboxList,
    entireFormObject: Object.assign({}, ...Object.values(orderDetailObject)),
    entireDetail: orderDetailObject,
    widgetList: widgetList
  };
};

export const handleAccordingToFormIdObtainData = (data, templateData) => {
  return new Promise(async resolve => {
    // 1.主表单
    const mainFormData = handleObtainDynamicDefaultValue(
      templateData.mainForm.widgetJsonList,
      TransformSubmitDataStructure(
        data.widgetList,
        data.subWidgetMap,
        data.linkedReferenceMap
      )
    );

    let milestoneReplyObject = {};
    let workOrderReplyObject = {};
    let milestoneWidgetReplyObject = {};
    let workOrderWidgetReplyObject = {};
    let workOrderList = {};

    for (let milestone of templateData.milestoneList) {
      const milestoneItemData = data.milestoneGroupList.find(
        milestoneData => milestoneData.msId === milestone.msId
      );

      // 2.里程碑批复的数据
      if (
        milestoneItemData &&
        milestone &&
        milestone.replyForm &&
        milestone.replyForm.widgetJsonList.length > 0
      ) {
        milestoneReplyObject[milestone.msId] = handleObtainDynamicDefaultValue(
          milestone.replyForm.widgetJsonList,
          TransformSubmitDataStructure(
            milestoneItemData.replyWidgetList,
            milestoneItemData.subWidgetMap,
            milestoneItemData.linkedReferenceMap
          )
        );
        milestoneWidgetReplyObject[milestone.replyForm.id] =
          handleObtainDynamicDefaultValue(
            milestone.replyForm.widgetJsonList,
            TransformSubmitDataStructure(
              milestoneItemData.replyWidgetList,
              milestoneItemData.subWidgetMap,
              milestoneItemData.linkedReferenceMap
            )
          );
      }

      // 3.工单类型的数据
      let workOrderObject = {};
      if (milestoneItemData && milestoneItemData.workOrderGroupList) {
        for (let workOrderItem of milestoneItemData.workOrderGroupList) {
          if (milestone.workOrderReplyForm?.widgetJsonList) {
            let milestoneFormJsonList = [];
            let workOrderFormJsonList = [];
            milestone.formList.forEach(milestoneForm => {
              milestoneFormJsonList.push(...milestoneForm.widgetJsonList);
            });
            if (milestone.workOrderReplyForm?.widgetJsonList) {
              workOrderFormJsonList.push(
                ...milestone.workOrderReplyForm?.widgetJsonList
              );
            }

            workOrderObject[workOrderItem.workOrderId] = {
              widgetList: handleObtainDynamicDefaultValue(
                milestoneFormJsonList,
                TransformSubmitDataStructure(
                  workOrderItem.widgetList,
                  workOrderItem.subWidgetMap,
                  workOrderItem.linkedReferenceMap
                )
              ),
              workOrderReplyWidgetList: handleObtainDynamicDefaultValue(
                workOrderFormJsonList,
                TransformSubmitDataStructure(
                  workOrderItem.widgetList,
                  workOrderItem.subWidgetMap,
                  workOrderItem.linkedReferenceMap
                )
              )
            };
          }
        }
      }
      workOrderReplyObject[milestone.msId] = workOrderObject;
    }

    for (let milestone of templateData.milestoneList) {
      const milestoneItemData = data.milestoneGroupList.find(
        milestoneData => milestoneData.msId === milestone.msId
      );

      // 2.里程碑批复的数据
      if (
        milestoneItemData &&
        milestone &&
        milestone.replyForm &&
        milestone.replyForm.widgetJsonList.length > 0
      ) {
        milestoneWidgetReplyObject[milestone.replyForm.id] =
          handleObtainDynamicDefaultValue(
            milestone.replyForm.widgetJsonList,
            TransformSubmitDataStructure(
              milestoneItemData.replyWidgetList,
              milestoneItemData.subWidgetMap,
              milestoneItemData.linkedReferenceMap
            )
          );
      }

      // 3.工单类型的数据
      if (milestone.workOrderReplyForm) {
        workOrderWidgetReplyObject = renderWidget(
          milestone.workOrderReplyForm.widgetJsonList,
          milestone.workOrderReplyForm.id,
          milestoneItemData
        );
      }

      for (let workOrder of milestone.formList) {
        workOrderList[workOrder.id] = renderWidget(
          workOrder.widgetJsonList,
          workOrder.id,
          milestoneItemData
        );
      }
    }

    let mainFormObject = {};
    mainFormObject[templateData.mainForm.id] = mainFormData;
    // 表单显隐数据设置
    let formVisibleSettingObject = {};
    let mainFormVisibleObject = {};
    mainFormVisibleObject[templateData.mainForm.id] = mainFormData;
    formVisibleSettingObject["mainForm"] = mainFormVisibleObject;

    for (let milestone of templateData.milestoneList) {
      let msReplyObject = {};
      if (milestone.replyForm) {
        msReplyObject[milestone.replyForm.id] =
          milestoneReplyObject[milestone.msId];
      }

      formVisibleSettingObject[milestone.msId] = Object.assign(msReplyObject, {
        workOrderData: workOrderReplyObject[milestone.msId]
      });
    }

    resolve({
      formVisibleObject: formVisibleSettingObject,
      orderDetailData: data,
      formulaObject: Object.assign(
        mainFormObject,
        workOrderWidgetReplyObject,
        workOrderList,
        milestoneWidgetReplyObject
      )
    });
  });
};

const handleCalculationFormVisible = async (
  ids: string[],
  templateData: any,
  detailData: any,
  formVisibleTemplateFormConfigure: any,
  msPresentationItemData: any,
  milestoneCardList: any,
  msId: string
) => {
  const msPresentationList = milestoneCardList.filter(ms =>
    ids.includes(ms.msId)
  );
  let encapsulationData = await handleAccordingToFormIdObtainData(
    detailData,
    templateData
  );
  let entireFormData = {
    templateData: encapsulationData,
    entrieTemplateForm: templateData
  };
  const msPresentationItem = msPresentationList.find(
    msPresent => msPresent.msId === msId
  );
  const msTemplateData: any = msPresentationItemData;
  if (msPresentationItem && msPresentationItem.msReplyId) {
    // 里程碑批复
    const operationalFactorData: any = getEntireFormData(
      entireFormData,
      msPresentationItem.msId,
      ""
    );

    if (msTemplateData.replyForm && msTemplateData.replyForm.id) {
      const msReplyVisibleFormList = formVisibleTemplateFormConfigure.filter(
        relate => relate.formId === msTemplateData.replyForm.id
      );

      if (msReplyVisibleFormList.length > 0) {
        let formVisibleTrendsObject = {};
        for (let visibleTemplateItem of msReplyVisibleFormList) {
          const formVisibleBoolean = ExecuteFormVisibleExpression(
            visibleTemplateItem.conditions,
            operationalFactorData
          );
          for (let widgetId of visibleTemplateItem.widgetIdList) {
            formVisibleTrendsObject[widgetId] = formVisibleBoolean;
          }
        }

        let trendFormObject: any = {
          [msPresentationItem.msReplyId]: formVisibleTrendsObject
        };
        return trendFormObject;
      }
    }
  } else {
    if (
      !msPresentationItem ||
      (msPresentationItem && msPresentationItem.workOrderGroupList.length === 0)
    ) {
      return;
    }

    // 工单批复
    const formId = msPresentationItem?.workOrderGroupList?.[0]?.workOrderId;

    const operationalFactorData: any = getEntireFormData(
      entireFormData,
      msPresentationItem.msId,
      formId
    );

    const bindFormIdList = msTemplateData.workOrderReplyForm
      ? [...msTemplateData.formList, msTemplateData.workOrderReplyForm].map(
          form => form.id
        )
      : msTemplateData.formList.map(form => form.id);

    const operationFormVisibleList = formVisibleTemplateFormConfigure.filter(
      relate => bindFormIdList.includes(relate.formId)
    );

    if (operationFormVisibleList.length > 0) {
      let formVisibleTrendsObject = {};
      for (let visibleTemplateItem of operationFormVisibleList) {
        const formVisibleBoolean = ExecuteFormVisibleExpression(
          visibleTemplateItem.conditions,
          operationalFactorData
        );

        for (let widgetId of visibleTemplateItem.widgetIdList) {
          formVisibleTrendsObject[widgetId] = formVisibleBoolean;
        }
      }

      let trendFormObject: any = {
        [formId]: formVisibleTrendsObject
      };
      return trendFormObject;
    }
  }
};
