<template>
  <el-form
    ref="widgetFormRef"
    label-position="top"
    label-width="auto"
    :model="widgetsForm"
    scroll-to-error
  >
    <div
      v-for="item in formWidgets"
      :key="item._fc_id"
      :class="[`widget_${item._fc_id}`]"
    >
      <el-form-item
        v-if="
          !(
            widgetVisibleForm.hasOwnProperty(item._fc_id) &&
            widgetVisibleForm[item._fc_id] === false
          )
        "
        :label-position="getLabelPosition(item.wrap)"
        :label-width="item.wrap?.labelWidth"
        :rules="getWidgetRules(item.$required, item)"
        :prop="item._fc_id"
      >
        <template #label>
          <div
            v-if="item.type !== 'DrDivider' && item.type !== 'DrCard'"
            class="widget-form-item-label"
          >
            <el-tooltip
              v-if="item.info"
              popper-class="widget-popper-label-class"
              effect="light"
              :content="item.props?.infoEn || item.info"
              placement="top"
              :show-after="500"
            >
              <i class="iconfont link-explain widget-form-item-label-icon" />
            </el-tooltip>
            <div class="widget-form-item-title">
              <span v-if="item.$required" class="widget-required">*</span>
              {{ item.props?.titleEn || item.title }}
            </div>
          </div>
        </template>
        <component
          :is="item.type"
          :widgetsData="item"
          :widgetsForm="widgetsForm"
          :currentFormWidgetData="widgetFormData"
          :isRenderDefaultValue="renderDefaultBool"
          :formConfig="formConfig"
          :orderId="orderId"
          :otherFormData="otherFormData"
          :widgetVisibleForm="widgetVisibleForm"
          :calculateFactorsData="calculateFactorsData"
          :widgetFormEncapsulatedData="widgetFormEncapsulatedData"
          @update:modelValue="handleWidgetFormsValue"
        />
        <template #error="{ error }">
          <span class="widget-form-error-body">{{ error }}</span>
        </template>
      </el-form-item>
    </div>
  </el-form>
</template>
<script lang="ts" setup>
import { ref, provide, computed, watch } from "vue";
import {
  calculateFormBetweenVisibleExpression,
  calculateFormVisibleExpression
} from "@/utils/formVisibleExpression";
import {
  initFormModalData,
  getWidgetRules
} from "@/utils/formDesignerUtils.tsx";

const props = defineProps({
  widgetForm: {
    type: String as PropType<string>,
    default: ""
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetUpdateBeforeData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderId: {
    type: String as PropType<any>,
    default: ""
  },
  // 表单显隐对象
  visibleWidgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  // 其他表单数据
  otherFormData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const widgetFormRef = ref<any | HTMLElement>(null);
const formWidgets = ref<any>();
const widgetsForm = ref<any>();
const widgetFormData = ref<any>([]);
const renderDefaultBool = ref<boolean>(false);
const widgetFormEncapsulatedData = ref<any>();
const widgetFormulasData = ref<any>([]); // 公式控件的值
const widgetVisibleForm = ref<any>({});
const calculateFactorsData = ref<any>(null);

// 表单数据
const formConfig = computed(() => {
  return props.widgetForm;
});

const emit = defineEmits([
  "handleWidgetChange",
  "handleVisibleFormWidget",
  "handleFormulasBindValueChange"
]);

const getLabelPosition = (wrap: any): any => {
  const wrapClass = wrap && wrap.class ? wrap.class.split("-")[2] : "top";
  return wrapClass;
};

const initRenderForm = (
  formWidgetLists: any[],
  formWidgetData: any[],
  isRenderDefaultValue: boolean,
  formVisibleListData: any[]
) => {
  const formulasIdList = formWidgetLists
    .filter(widget => widget.type === "DrFormulas")
    .map(widget => widget._fc_id);
  const widgetData =
    formWidgetData?.filter(
      widget => !formulasIdList.includes(widget.widgetId)
    ) || [];
  let widgetObject = initFormModalData(formWidgetLists, widgetData);
  renderDefaultBool.value = isRenderDefaultValue;
  formWidgets.value = formWidgetLists;
  widgetsForm.value = widgetObject;
  widgetFormEncapsulatedData.value = formVisibleListData;
  //@ts-ignore
  widgetVisibleForm.value = calculateFormVisibleExpression(
    props.widgetForm.id,
    formVisibleListData
  );
};

const getFormAllWidget = () => {
  let widgetIdList = [];
  props.widgetForm.widgetJsonList.forEach(widgetItem => {
    if (!["DrCard"].includes(widgetItem.type)) {
      widgetIdList.push(widgetItem._fc_id);
    }
  });
  const drCardWidget = props.widgetForm.widgetJsonList.filter(
    widgetItem => widgetItem.type === "DrCard"
  );
  drCardWidget.forEach(widget => {
    widget.children.forEach(widgetChild => {
      if (widgetChild.children) {
        widgetChild.children.forEach(widgetChilds => {
          widgetIdList.push(widgetChilds._fc_id);
        });
      }
    });
  });
  return widgetIdList;
};

/**
 * 控件提交的全部数据
 * @param field 控件id
 * @param value 控件需要提交给后端的值
 */
const handleWidgetFormsValue = (field: string, value: any) => {
  const widgetInFormList = getFormAllWidget();
  if (!widgetInFormList.includes(field)) {
    return;
  }
  if (widgetFormRef.value) {
    const index = widgetFormData.value.findIndex(
      widgetData => widgetData.widgetId === field
    );
    const widgetData = Object.assign({ formId: props.widgetForm.id }, value);

    index === -1
      ? widgetFormData.value.push(widgetData)
      : widgetFormData.value.splice(index, 1, widgetData);

    if (value.widgetType) {
      if (value.widgetType === "DrTableForm") {
        widgetsForm.value[field] = value.childrenList;
      } else {
        widgetsForm.value[field] =
          value.widgetType &&
          [
            "DrImagesUpload",
            "DrFilesUpload",
            "DrAddress",
            "DrExchangeRates"
          ].includes(value.widgetType)
            ? value.obj
            : value.label;
      }

      if (
        [
          "DrImagesUpload",
          "DrFilesUpload",
          "DrEditor",
          "DrSignature",
          "DrExchangeRates"
        ].includes(value.widgetType)
      ) {
        widgetFormRef.value.validateField(field);
      }
    }
    emit("handleWidgetChange", widgetFormData.value);
    let formulasObject = {};
    formulasObject[props.widgetForm.id] = widgetsForm.value;
    emit("handleFormulasBindValueChange", formulasObject);
  }
};

/**
 * 触发公式计算后的返回值
 * @param widgetId
 * @param value
 */
const handleWidgetFormulas = (widgetId: string, value: any) => {
  const index = widgetFormulasData.value.findIndex(
    widgetData => widgetData.widgetId === widgetId
  );
  const widgetFormulaData = Object.assign(
    { formId: props.widgetForm.id },
    value
  );
  index === -1
    ? widgetFormulasData.value.push(widgetFormulaData)
    : widgetFormulasData.value.splice(index, 1, widgetFormulaData);

  if (value.widgetType) {
    widgetsForm.value[widgetId] = value.obj;
    let formulasObject = {};
    formulasObject[props.widgetForm.id] = widgetsForm.value;
    emit("handleFormulasBindValueChange", formulasObject);
  }
};

const handleGainWidgetFormData = (validateBoolean: boolean = true): any => {
  return new Promise((resolve, reject) => {
    if (!widgetFormRef.value) return;
    if (validateBoolean) {
      if (formWidgets.value) {
        widgetFormRef.value.validate((valid, invalidFields) => {
          if (valid) {
            const widgetData = widgetFormData.value.filter(
              widget => widget.formId === props.widgetForm.id
            );
            const widgetFormulaData = widgetFormulasData.value.filter(
              widget => widget.formId === props.widgetForm.id
            );
            resolve([...widgetData, ...widgetFormulaData]);
          } else {
            const element = document.querySelector(
              `.widget_${Object.keys(invalidFields)[0]}`
            );
            if (element) {
              element.scrollIntoView({ behavior: "smooth" });
            }
            resolve(false);
          }
        });
      } else {
        resolve([]);
      }
    } else {
      const widgetData = widgetFormData.value.filter(
        widget => widget.formId === props.widgetForm.id
      );
      const widgetFormulaData = widgetFormulasData.value.filter(
        widget => widget.formId === props.widgetForm.id
      );
      resolve([...widgetData, ...widgetFormulaData]);
    }
  });
};

const getFormListWidgetJsonList = (widgetJsonList: any) => {
  let widgetList = [];
  for (let widgetJson of widgetJsonList) {
    if (widgetJson.type === "DrCard") {
      const cardChilds = widgetJson.children
        .filter(col => col.children)
        .map(col => col.children[0]);
      widgetList.push(...getFormListWidgetJsonList(cardChilds));
    } else {
      widgetList.push(widgetJson);
    }
  }
  return widgetList;
};

watch(
  () => widgetFormData.value,
  () => {
    if (widgetFormEncapsulatedData.value.hasOwnProperty("entireDetail")) {
      // 1.表单显隐逻辑
      let currentFormData = {};
      currentFormData[props.widgetForm.id] = widgetsForm.value;

      let betweenData = Object.assign(
        widgetFormEncapsulatedData.value.entireDetail,
        props.otherFormData,
        currentFormData
      );
      const visibleFormCalculateFactor = calculateFactorsData.value
        ? calculateFactorsData.value
        : {
            entireDetail: betweenData,
            checkboxList: widgetFormEncapsulatedData.value.checkboxList
          };

      const widgetVisibleFormObject = calculateFormBetweenVisibleExpression(
        visibleFormCalculateFactor
      );
      emit("handleVisibleFormWidget", widgetVisibleFormObject);
    }
  },
  {
    deep: true
  }
);

watch(
  () => props.visibleWidgetForm,
  () => {
    if (props.visibleWidgetForm) {
      widgetVisibleForm.value = props.visibleWidgetForm;
    }
  },
  {
    deep: true
  }
);

provide("widgetFormRef", widgetFormRef);
provide("handleWidgetFormsValue", handleWidgetFormsValue);
provide("handleWidgetFormulas", handleWidgetFormulas);

defineExpose({
  initRenderForm,
  handleGainWidgetFormData
});
</script>
<style lang="scss" scoped>
.widget-popper-class {
  max-width: 300px;
}

.el-card {
  width: 100%;
  border: none;
}

.widget-form-error-body {
  width: 100%;
  overflow: hidden;
  font-size: 12px;
  line-height: 18px;
  color: #e62412;
  text-overflow: ellipsis;
  word-wrap: break-word;
}

::v-deep(.el-form-item__label) {
  width: 100%;
  margin-bottom: 0 !important;
  font-weight: 600 !important;
  color: #262626;
}

::v-deep(.widget-after-data) {
  font-size: 12px;
  color: #fa8d09;
}

::v-deep(.widget-dynamic-form-header) {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .iconfont {
    font-size: 12px;
    color: #fa8d09;
  }

  .widget-dynamic-form-title {
    margin-left: 6px;
    font-size: 12px;
    color: #797979;
    text-align: left;
  }
}

::v-deep(.widget-form-item-label) {
  display: inline-flex;
  align-items: center;
  max-width: 100%;

  .widget-form-item-label-icon {
    margin-right: 5px;
    font-size: 12px;
    vertical-align: middle;
  }

  .widget-form-item-title {
    flex: 1;
    max-width: 100%;
    font-size: 14px;
    font-weight: normal;
    color: #262626;
    word-wrap: break-word;

    .widget-required {
      color: red;
    }
  }

  .widget-form-item-logo {
    .iconfont {
      font-size: 10px;
      color: #fa8d09;
    }
  }
}

::v-deep(.el-input-number) {
  width: 100% !important;
}

::v-deep(.el-date-editor) {
  width: 100% !important;
}

::v-deep(.el-cascader) {
  width: 100% !important;
  margin-bottom: 10px;
}
</style>
