<template>
  <div class="lk-pagination-body">
    <el-pagination
      v-model:current-page="pageParams.pageNo"
      v-model:page-size="pageParams.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalNumber"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import type { ComponentSize } from "element-plus";

interface PageParamsProp {
  pageSize: number;
  pageNo: number;
}

const props = defineProps({
  total: {
    type: Number as PropType<number>,
    default: 0
  }
});

let pageParams = ref<PageParamsProp>({
  pageSize: 10,
  pageNo: 1
});
const size = ref<ComponentSize>("small");
const background = ref<boolean>(false);
const disabled = ref<boolean>(false);
const totalNumber = ref<number>(0);

watchEffect((): void => {
  totalNumber.value = props.total;
});

const emit = defineEmits<{
  (e: "updatePagination", data): void;
}>();

const handleSizeChange = (val: number) => {
  emit("updatePagination", pageParams);
};

const handleCurrentChange = (val: number) => {
  emit("updatePagination", pageParams);
};

defineExpose({
  pageParams
});
</script>
<style lang="scss" scoped>
.lk-pagination-body {
  display: flex;
  justify-content: end;
  margin-top: 10px;
}
</style>
