<template>
  <div class="role-per-col">
    <el-checkbox
      v-model="checkAll"
      class="checkbox-parent"
      :indeterminate="isIndeterminate"
      :disabled="disabled"
      @change="handleCheckAllChange"
    >
      {{ sccsSettingData.label }}
    </el-checkbox>
    <el-checkbox-group
      v-model="SccsOrderChecked"
      :disabled="disabled"
      @change="handleCheckedCitiesChange"
    >
      <el-checkbox
        v-for="city in sccsSettingData.childrenList"
        :key="city"
        class="checkbox-group-col"
        :label="city.label"
        :disabled="disabled"
        :value="city.key"
      >
        <div class="checkbox-span">
          {{ city.label }}
          <span class="checkbox-tip">{{ city.remark }}</span>
        </div>
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
<script lang="ts" setup>
import { ref, PropType, watchEffect } from "vue";

const checkAll = ref(false);
const isIndeterminate = ref(false);
const SccsOrderChecked = ref([]);
let cities = [];

const props = defineProps({
  sccsSettingData: {
    type: Object as PropType<any>
  },
  sccsToolRolePermItemList: {
    type: Object as PropType<any>
  },
  disabled: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const handleCheckAllChange = (val: boolean) => {
  let checkedList = [];
  //@ts-ignore
  cities.childrenList.map(child => checkedList.push(child.key));
  SccsOrderChecked.value = val ? checkedList : [];
  isIndeterminate.value = false;
};

const handleCheckedCitiesChange = (value: string[]) => {
  const checkedCount = value.length;
  //@ts-ignore
  checkAll.value = checkedCount === cities.childrenList.length;

  isIndeterminate.value =
    //@ts-ignore
    checkedCount > 0 && checkedCount < cities.childrenList.length;
};

watchEffect(() => {
  let bindDataIds: string[] = [];
  if (Object.keys(props.sccsToolRolePermItemList).length > 0) {
    props.sccsSettingData.childrenList.map(child => {
      const index =
        props.sccsToolRolePermItemList.sccsToolRolePermItemList.findIndex(
          role => role.permissionKey === child.key
        );
      if (index !== -1) {
        bindDataIds.push(child.key);
      }
    });
  }

  SccsOrderChecked.value = bindDataIds;
  const checkedCount = bindDataIds.length;
  cities = props.sccsSettingData as any;
  //@ts-ignore
  checkAll.value = checkedCount === cities.childrenList.length;
  isIndeterminate.value =
    //@ts-ignore
    checkedCount > 0 && checkedCount < cities.childrenList.length;
});

defineExpose({
  SccsOrderChecked
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  margin-bottom: 10px;
  font-weight: bolder;
}

.checkbox-group-col {
  width: 100% !important;
  padding-left: 8px;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    cursor: pointer;
    background: #f5f7fa;
  }
}
</style>
