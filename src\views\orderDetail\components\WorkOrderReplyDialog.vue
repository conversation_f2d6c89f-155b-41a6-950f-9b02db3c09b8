<template>
  <el-drawer
    v-model="drawerVisible"
    class="lk-maximum-drawer"
    size="83%"
    header-class="work-order-msReply-header"
    body-class="work-order-msReply-container"
    footer-class="create-work-order-drawer-footer"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <template #header>
      <div class="drawer-header">
        <div class="drawer-header-left">
          {{ dialogTitle }}
          <span v-if="workOrderTodoState" class="drawer-header-tag">
            {{ t("trade_order_todo") }}
          </span>
        </div>
        <div class="drawer-header-right">
          <div class="drawer-header-btn-next-group">
            <div class="drawer-header-col">
              <i class="iconfont link-quote-third-party" />
              <span class="drawer-header-text">
                {{ t("trade_order_referenceOtherWorkOrders") }}
              </span>
            </div>
            <div class="drawer-header-col">
              <i class="iconfont link-quote-third-party" />
              <span class="drawer-header-text">
                {{ t("trade_order_DataReference") }}
              </span>
              <el-tooltip
                effect="dark"
                :content="t('trade_order_quoteTip')"
                placement="top"
                :showAfter="500"
              >
                <i class="iconfont link-explain" />
              </el-tooltip>
            </div>
          </div>
          <div class="drawer-header-btn-group">
            <el-tooltip :content="t('trade_common_orderMessage')">
              <div
                class="drawer-header-col"
                :class="{ 'create-work-order-active': mainFormVisible }"
                @click.stop="handleOpenMainFormDialog"
              >
                <i class="iconfont link-basic-info" />
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <div class="drawer-container-left">
        <el-scrollbar>
          <el-collapse v-model="activeNames" class="work-order-collapse">
            <template
              v-for="milestoneItem in widgetWorkOrderFormData"
              :key="milestoneItem.id"
            >
              <el-collapse-item
                :class="`collaspse-${milestoneItem.id}`"
                :title="milestoneItem.name"
                :name="milestoneItem.id"
              >
                <template #title>
                  <div class="order-form-edit-title-header">
                    <span class="order-form-edit-title">
                      <ReText
                        type="info"
                        :line-clamp="2"
                        :tippyProps="{ delay: 50000 }"
                      >
                        {{ milestoneItem.name }}
                      </ReText>
                    </span>
                    <span class="order-form-edit-avatar" @click.stop>
                      <LkAvatarGroupNext
                        :size="22"
                        :avatarListGroup="
                          msTemplateData.editUserList.filter(e =>
                            e.assignedFormIdList.includes(milestoneItem.id)
                          )
                        "
                        :maxAvatar="4"
                      />
                    </span>
                    <span
                      v-if="msTemplateData.edited"
                      class="milestone-reply-form-span-text"
                    >
                      {{
                        getTimeLineStampFormat(
                          msTemplateData.editFormInfoList.find(
                            e => e.formId === milestoneItem.id
                          )?.editTime
                        )
                      }}
                    </span>
                    <span
                      v-else
                      class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
                    >
                      {{ t("trade_order_notCollection") }}
                    </span>
                  </div>
                </template>
                <div class="order-detail-desc-col">
                  <OrderDetailDescWidget
                    :widgetForm="milestoneItem.widgetJsonList"
                    :widgetData="getWorkOrderWidgetData(msTemplateData)"
                  />
                </div>
              </el-collapse-item>
            </template>
          </el-collapse>
        </el-scrollbar>
      </div>
      <div class="drawer-container-right">
        <div class="drawer-container-main-body">
          <el-scrollbar class="horizontal-scrollbar-only">
            <LkTrendsAggregationForm
              ref="TrendsAggregationFormRef"
              :formList="[msTemplate.workOrderReplyForm]"
              :formWidgetData="orderWorkWidgetFormData"
              :operationalFactorData="operationalFactorData"
              :defaultValueActuatorFlag="firstReplyWorkOrder"
              :sccsId="route.query.sccsId"
              :orderId="route.query.orderId"
              :workOrderId="msTemplateData.workOrderId"
              :trendsFormFlag="true"
              :workOrderAllowOperation="[msTemplate.workOrderReplyForm.id]"
              :createUser="{
                userId: workOrderDataRef.creatorId,
                userName: workOrderDataRef.creatorUsername,
                userAvatar: workOrderDataRef.creatorAvatar,
                email: workOrderDataRef.creatorEmail,
                user: true
              }"
              :replayUsers="workOrderDataRef.replyUserList"
            />
          </el-scrollbar>
        </div>
        <div class="drawer-container-footer">
          <el-button plain @click="handleClose">
            {{ t("trade_common_cancel") }}
          </el-button>
          <el-button
            type="primary"
            color="#0070d2"
            :disabled="btnOperationState"
            @click="handleWorkOrderCollection"
          >
            {{ t("trade_common_confirm") }}
          </el-button>
        </div>
      </div>

      <OrderMainFormHeader
        ref="OrderMainFormRef"
        :mainFormData="orderMainForm"
      />
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { computed, markRaw, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { storageLocal } from "@pureadmin/utils";
import dayjs from "dayjs";
import { ReText } from "@/components/ReText";
import { getDefaultConfigWidgetData } from "@/utils/formulasActuator/formulasActuator";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext/index";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import OrderMainFormHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderMainFormHeader.vue";
import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";
import { settingWorkOrderReply } from "@/api/order";
import { emitter } from "@/utils/mitt";

interface RouteParamProp {
  sccsId: string;
  templateId: string;
}

const { t } = useI18n();
const route = useRoute();
const TrendsAggregationFormRef = ref<HTMLElement | any>(null);
const OrderMainFormRef = ref<HTMLElement | any>(null);
const activeNames = ref<string[]>(["1"]);
const drawerVisible = ref<boolean>(false);
const btnOperationState = ref<boolean>(false);
const mainFormVisible = ref<boolean>(false);
const msTemplate = ref<any>({});
const msTemplateData = ref<any>({});
const widgetWorkOrderFormData = ref<any>([]);
const orderWorkWidgetFormData = ref<any>({});
const workOrderCollectId = ref<string>("");
const workOrderType = ref<string>("");
const operationalFactorData = ref<any>({});
const orderMainForm = ref<any>({});
const routeParams = ref<RouteParamProp>({
  sccsId: "",
  templateId: ""
});
const workOrderDataRef = ref<any>({});
const userInfo: any = storageLocal().getItem("user-info");

const emit = defineEmits(["handleUpdateCollection"]);

const dialogTitle = computed(() => {
  const workOrderName = msTemplateData.value.workOrderName;
  return workOrderType.value === "workOrderReply"
    ? `${t("trade_order_taskReply")}_${workOrderName}`
    : `${t("trade_view_work_order")}_${workOrderName}`;
});

const firstEditWorkOrder = computed(() => {
  return (
    msTemplateData.value.editUserList.filter(editUser => !editUser.edited)
      .length > 0
  );
});

const firstReplyWorkOrder = computed(() => {
  return (
    msTemplateData.value.replyUserList.filter(editUser => editUser.edited)
      .length === 0
  );
});

const workOrderCollectState = computed(() => {
  return orderWorkWidgetFormData.value.length === 0;
});

const workOrderTodoState = computed(() => {
  const userInfo: any = storageLocal().getItem("user-info");
  const editUserList = msTemplateData.value.editUserList;
  if (!editUserList) {
    return false;
  }
  const userItem = editUserList.find(
    user => user.teamMemberId === userInfo.latestLoginTeamMemberId
  );

  return userItem ? !userItem.edited : false;
});

const getWorkOrderWidgetData = (widgetData): any => {
  const { widgetList, subWidgetMap, linkedReferenceMap } = widgetData;
  const widgetObject = TransformSubmitDataStructure(
    widgetList,
    subWidgetMap,
    linkedReferenceMap
  );
  return widgetObject;
};

const getTimeLineStampFormat = (dateTime: any) => {
  if (dateTime) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "";
  }
};

const open = (
  workOrderData: any,
  mainFormData: any,
  type?: string,
  replyOrderId?: string,
  customParams?: RouteParamProp
) => {
  workOrderDataRef.value = workOrderData;

  if (type) {
    workOrderType.value = type;
    // dialogType.value = "WORK_ORDER";
  }
  const { msId, workOrderId } = workOrderData;
  const { orderId } = route.query;
  routeParams.value = customParams;
  workOrderCollectId.value = replyOrderId ? replyOrderId : (orderId as string);

  orderMainForm.value = mainFormData;
  getDefaultConfigWidgetData(workOrderCollectId.value, customParams).then(
    async ({ orderTemplateData, orderData }: any) => {
      const { milestoneList } = orderTemplateData;
      msTemplate.value = milestoneList.find(
        milestoneItem => milestoneItem.msId === msId
      );

      msTemplateData.value = orderData.milestoneGroupList
        .find(milestoneItem => milestoneItem.msId === msId)
        .workOrderGroupList.find(
          workOrderGroup => workOrderGroup.workOrderId === workOrderId
        );

      let obj = {};
      let anchorLists = [];
      if (msTemplateData.value.hiddenFormIdList.length > 0) {
        // 当前工单表单有需要隐藏的表单
        widgetWorkOrderFormData.value = msTemplate.value.formList.filter(
          formItem =>
            !msTemplateData.value.hiddenFormIdList.includes(formItem.id)
        );
      } else {
        // 当前工单表单没有需要隐藏的表单
        widgetWorkOrderFormData.value = msTemplate.value.formList;
      }
      if (msTemplate.value.workOrderReplyForm) {
        activeNames.value.push(msTemplate.value.workOrderReplyForm.id);
        workOrderType.value === "workOrderDynamic" &&
          anchorLists.push({
            title: t("trade_order_taskReply"),
            ref: `collaspse-${msTemplate.value.workOrderReplyForm.id}`,
            name: msTemplate.value.workOrderReplyForm.id,
            state: msTemplate.value.workOrderReplyForm.editable,
            stateText: msTemplate.value.workOrderReplyForm.editable
              ? "trade_email_editable"
              : "trade_email_readonly"
          });
      }

      widgetWorkOrderFormData.value.forEach(milestoneItem => {
        activeNames.value.push(milestoneItem.id);
        obj[milestoneItem.id] = {
          editable: milestoneItem.editable,
          formPubMongoId: milestoneItem.id,
          formProperty: "DISPLAY"
        };
        anchorLists.push({
          title: milestoneItem.name,
          ref: `collaspse-${milestoneItem.id}`,
          name: milestoneItem.id,
          state: milestoneItem.editable,
          stateText: milestoneItem.editable
            ? "trade_email_editable"
            : "trade_email_readonly"
        });
      });
      // anchorList.value = anchorLists;

      orderWorkWidgetFormData.value = TransformSubmitDataStructure(
        msTemplateData.value.widgetList,
        msTemplateData.value.subWidgetMap,
        msTemplateData.value.linkedReferenceMap
      );

      operationalFactorData.value = getEntireFormData(
        msTemplateData.value.msId,
        msTemplateData.value.workOrderId
      );

      drawerVisible.value = true;
    }
  );
};

const handleOpenMainFormDialog = () => {
  OrderMainFormRef.value.handleOpenMainFormVisible();
  mainFormVisible.value = !mainFormVisible.value;
};

const handleWorkOrderCollection = () => {
  const currentUserHasEdited = workOrderDataRef.value.replyUserList.some(
    replyUser => replyUser.userId === userInfo.id && replyUser.edited === true
  );

  if (!currentUserHasEdited) {
    ElMessageBox.confirm(
      t("trade_common_workOrderReplyTip"),
      t("trade_order_taskReply"),
      {
        confirmButtonText: t("trade_common_confirm"),
        cancelButtonText: t("trade_common_cancel"),
        confirmButtonClass: "confrim-message-btn-warn-class",
        customClass: "order_confirm_message_box",
        type: "warning",
        icon: markRaw(WarningFilled),
        center: true
      }
    ).then(async () => {
      handleCollectedWorkOrder();
    });
  } else {
    handleCollectedWorkOrder();
  }
};

const handleCollectedWorkOrder = async () => {
  const workOrderWidgetData =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(true);
  const linkedReferenceSourceList =
    TrendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  if (!workOrderWidgetData) {
    return false;
  }

  const sccsId = route.query.sccsId || routeParams.value.sccsId;
  const { workOrderReplyId, msId, replyVersion, workOrderId } =
    msTemplateData.value;

  const { code } = await settingWorkOrderReply({
    sccsId: sccsId,
    orderId: workOrderCollectId.value,
    msId: msId,
    workOrderId: workOrderReplyId,
    version: replyVersion,
    widgetItemDataList: workOrderWidgetData,
    linkedReferenceSaveList: linkedReferenceSourceList
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    drawerVisible.value = false;
    emit("handleUpdateCollection", workOrderId);
  }
};

const handleClose = (): void => {
  // basicInfoDrawer.value = false;
  // emailInfoVisible.value = false;
  drawerVisible.value = false;
  mainFormVisible.value = false;
};

onMounted(() => {
  emitter.on("widgetUploadLoading", (isUploading: boolean) => {
    btnOperationState.value = isUploading;
  });
});

defineExpose({
  open
});
</script>
<style lang="scss">
@use "./style/index.scss";

.work-order-msReply-header {
  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .drawer-header-left {
      font-size: 18px;
      font-weight: bolder;
      line-height: 20px;
      color: #202020;
    }

    .drawer-header-right {
      display: flex;
      align-items: center;

      .drawer-header-btn-next-group {
        position: relative;
        display: flex;
        flex: 1;
        align-items: center;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        .drawer-header-col {
          padding: 3px 10px;
          cursor: pointer;
          border-radius: 4px;

          &:nth-child(2) {
            margin-right: 12px;
          }

          .iconfont {
            font-size: 13px;
            color: #595959;
          }

          .drawer-header-text {
            margin: 0 6px;
            font-size: 14px;
            color: #595959;
          }

          &:hover {
            background: #e5e5e5;
          }
        }
      }

      .drawer-header-btn-group {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 5px;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        .drawer-header-col {
          width: 28px;
          height: 28px;
          margin: 0 4px;
          line-height: 28px;
          text-align: center;
          cursor: pointer;
          border-radius: 4px;

          .iconfont {
            font-size: 14px;
          }

          &.create-work-order-active {
            background: #e1edff;

            .iconfont {
              color: #0070d2;
            }
          }

          &:first-child {
            .iconfont {
              font-size: 16px;
            }
          }

          &:hover {
            background: #e5e5e5;
          }
        }

        &:nth-child(2) {
          margin-right: 10px;
        }
      }
    }
  }
}

.work-order-msReply-container {
  display: flex;
  padding: 0;

  .drawer-container-left {
    flex: 1;
    padding: 16px 12px;
  }

  .drawer-container-right {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 538px;
    min-width: 538px;
    background: #fff;
    border-right: 1px solid #e6eaf0;

    .drawer-container-top {
      display: flex;
      align-items: center;
      height: 45px;
      padding: 0 37px;
    }

    .drawer-container-main-body {
      flex: 1;
      max-height: calc(100% - 60px);
      padding: 0 10px;

      .el-collapse-item {
        border: 0 none;

        .el-collapse-item__header {
          display: none;
        }

        .el-collapse-item__wrap {
          border: 0 none;

          .el-collapse-item__content {
            border: 0 none;
          }
        }
      }
    }

    .drawer-container-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: end;
      width: 100%;
      height: 60px;
      padding: 0 18px;
      background: #fff;
      border-top: 1px solid #e6eaf0;
    }
  }
}
</style>
