<template>
  <el-dialog
    v-model="dialogVisible"
    class="lk-preview-dialog"
    fullscreen
    align-center
    :show-close="false"
  >
    <template #header />
    <div class="preview-header">
      <div class="preview-title">{{ previewTitle }}</div>
      <div class="preview-header-operate">
        <i
          class="iconfont link-import mr20"
          @click="handleDownLoadFile(previewFile)"
        />
        <i class="iconfont link-clean" @click="dialogVisible = false" />
      </div>
    </div>
    <div class="preview-container">
      <el-carousel
        ref="previewCarouselRef"
        height="calc(100vh - 144px)"
        indicator-position="outside"
        arrow="always"
        :autoplay="false"
      >
        <el-carousel-item v-for="file in fileList" :key="file">
          <div
            v-if="
              [
                'bmp',
                'gif',
                'jpeg',
                'jpg',
                'pjpeg',
                'png',
                'tiff',
                'webp'
              ].includes(file.name.split('.')[1].toLocaleLowerCase())
            "
            class="preview-image-container"
          >
            <el-image :src="file.url.split('?')[0]" fit="contain" />
          </div>
          <div
            v-else-if="
              [
                'xlsx',
                'xls',
                'docx',
                'doc',
                'pdf',
                'ppt',
                'pptx',
                'mp4',
                'avi',
                'mov',
                'mkv',
                'wmv',
                'flv',
                '3gp',
                'webm'
              ].includes(file.name.split('.')[1].toLocaleLowerCase())
            "
            class="preview-file-container"
          >
            <VueOfficeExcel
              v-if="
                ['xlsx', 'xls'].includes(
                  file.name.split('.')[1].toLocaleLowerCase()
                )
              "
              :src="file.url"
              style="width: calc(100% - 120px); height: 80vh; margin-top: 20px"
              @rendered="handleRenderedHandler"
              @load="handleRenderedHandler"
            />
            <VueOfficeDocx
              v-if="
                ['docx', 'doc'].includes(
                  file.name.split('.')[1].toLocaleLowerCase()
                )
              "
              :src="file.url"
              style="width: calc(100% - 120px); height: 80vh; margin-top: 20px"
              @rendered="handleRenderedHandler"
              @load="handleRenderedHandler"
            />
            <VueOfficePdf
              v-if="
                ['pdf'].includes(file.name.split('.')[1].toLocaleLowerCase())
              "
              :src="file.url"
              style="width: calc(100% - 120px); height: 80vh; margin-top: 20px"
              @rendered="handleRenderedHandler"
              @load="handleRenderedHandler"
            />
            <VueOfficePptx
              v-if="
                ['ppt', 'pptx'].includes(
                  file.name.split('.')[1].toLocaleLowerCase()
                )
              "
              :src="file.url"
              style="height: 80vh; margin-top: 20px"
              @rendered="handleRenderedHandler"
              @load="handleRenderedHandler"
            />
            <VideoStream
              v-if="
                [
                  'mp4',
                  'avi',
                  'mov',
                  'mkv',
                  'wmv',
                  'flv',
                  '3gp',
                  'webm'
                ].includes(file.name.split('.')[1].toLocaleLowerCase())
              "
              :videoURL="file.url"
            />
          </div>
          <div v-else class="preview-other-container">
            <div class="preview-other-body">
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#link-unknown-format" />
              </svg>
              <div class="preview-other-title">
                {{ t("trade_component_preview_unsupport") }}
              </div>
              <div class="preview-other-tip">
                {{ t("trade_component_preview_download_tip") }}
              </div>
              <el-button
                type="primary"
                @click="handleDownLoadFile(previewFile)"
              >
                {{ t("trade_common_download") }}
              </el-button>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <template #footer />
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { i18n } from "@/plugins/i18n";
import {
  ElButton,
  ElDialog,
  ElImage,
  ElCarousel,
  ElCarouselItem
} from "element-plus";
import VueOfficeExcel from "@vue-office/excel";
import VueOfficeDocx from "@vue-office/docx";
import VueOfficePdf from "@vue-office/pdf";
import VueOfficePptx from "@vue-office/pptx";
import VideoStream from "./videoStream.vue";
import "@vue-office/excel/lib/index.css";
import "@vue-office/docx/lib/index.css";
import axios from "axios";

const dialogComponentName = ref<any>("");
const previewCarouselRef = ref<HTMLElement | any>(null);
const fileUrl = ref<string>("");
const loading = ref<boolean>(false);
const FilePreviewDialogTitle = ref<string>("");
const dialogVisible = ref<boolean>(false);
const { t } = i18n.global as any;

const props = defineProps({
  visible: Boolean,
  componentName: {
    type: String,
    default: ""
  },
  fileData: {
    type: Object,
    default: () => {}
  },
  fileList: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const previewTitle = computed(() => {
  return previewCarouselRef.value
    ? props.fileList[previewCarouselRef.value.activeIndex].name
    : "";
});

const previewFile = computed(() => {
  return previewCarouselRef.value
    ? props.fileList[previewCarouselRef.value.activeIndex]
    : {};
});

watch(
  () => props,
  () => {
    dialogVisible.value = props.visible;
    dialogComponentName.value = props.componentName;
    fileUrl.value = props.fileData.url;
    FilePreviewDialogTitle.value = `预览${props.fileData.name}`;

    if (props.fileData && props.fileList.length > 0) {
      const initialIndex = props.fileList.findIndex(
        (file: any) => file.url === props.fileData.url
      );
      if (initialIndex !== -1) {
        setTimeout(() => {
          previewCarouselRef.value.setActiveItem(initialIndex);
        });
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const handleRenderedHandler = () => {
  loading.value = false;
};

/**
 * 通过Axios下载图片文件
 * @param {string} url - 图片URL (需服务端开启CORS支持)
 * @param {string} [filename] - 自定义文件名（自动补全扩展名）
 * @param {object} [config] - Axios请求配置（如 headers、params 等）
 */
async function downloadImageWithAxios(url, filename, config = {}) {
  try {
    // 1. 发起请求获取二进制数据
    const response = await axios({
      method: "get",
      url,
      responseType: "blob", // 关键：指定响应类型为二进制
      ...config
    });
    // 2. 提取文件类型和扩展名
    const contentType = response.headers["content-type"];
    const extension = contentType.split("/")[1] || "jpg";

    // 3. 生成最终文件名（含时间戳兜底）
    let finalFilename = filename || `download_${Date.now()}`;
    if (!finalFilename.includes(".")) {
      finalFilename += `.${extension}`;
    }
    // 4. 创建Blob链接
    const blob = new Blob([response.data], { type: contentType });
    const blobUrl = URL.createObjectURL(blob);
    // 5. 创建隐藏链接触发下载
    const link = document.createElement("a");
    link.href = blobUrl;
    link.download = finalFilename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    // 6. 释放内存
    URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error("下载失败:", error);
    throw error;
  }
}

const handleDownLoadFile = file => {
  const fileName = file.name.split(".")[1].toLocaleLowerCase();
  if (
    ["bmp", "gif", "jpeg", "jpg", "pjpeg", "png", "tiff", "webp"].includes(
      fileName
    )
  ) {
    downloadImageWithAxios(file.url.split("?")[0], file.name);
  } else {
    window.open(file.url);
  }
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.preview-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 64px;
  background: rgb(0 0 0 / 36%);

  .preview-title {
    font-size: 16px;
    font-weight: bolder;
    line-height: 22px;
    color: #fff;
  }

  .preview-header-operate {
    position: absolute;
    top: 50%;
    right: 60px;
    display: flex;
    align-items: center;
    transform: translateY(-50%);

    .iconfont {
      font-size: 32px;
      color: #8c8c8c;
      cursor: pointer;
    }

    .mr20 {
      margin-right: 60px;
      font-size: 16px;
      color: #fff;
    }
  }
}

.preview-container {
  height: calc(100vh - 64px);
  padding: 40px 90px;

  ::v-deep(.el-carousel) {
    height: 100%;

    .preview-image-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .preview-file-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .preview-other-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      .preview-other-body {
        width: 620px;
        height: 390px;
        text-align: center;
        background: #fff;
        border-radius: 6px;

        .svg-icon {
          width: 37px;
          margin: 0 auto;
        }

        .preview-other-title {
          margin-bottom: 12px;
          font-size: 18px;
          font-weight: bolder;
          line-height: 20px;
          color: #202020;
        }

        .preview-other-tip {
          margin-bottom: 80px;
          font-size: 14px;
          line-height: 20px;
          color: #262626;
        }

        .el-button {
          width: 92px !important;
        }
      }
    }
  }
}
</style>
