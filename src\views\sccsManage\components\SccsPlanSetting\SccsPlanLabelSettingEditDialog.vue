<template>
  <LkDialog
    ref="LkDialogRef"
    :title="editStatus === 'add' ? t('trade_add_tag') : t('trade_edit_tag')"
    class="lk-maximum-dialog"
    @confirm="handleConfirmPlan"
    @close="handleCloseDialog"
  >
    <template #default>
      <div class="create-team-dialog-form">
        <el-form
          ref="ruleFormRef"
          :model="form"
          label-position="top"
          label-width="auto"
        >
          <el-form-item :label="t('trade_common_module')">
            <el-input
              v-if="planSetting.isMainForm"
              style="width: 560px"
              :value="t('trade_order_basicInfo')"
              filterable
              clearable
              disabled
            />
            <el-input
              v-else
              v-model="planSetting.name"
              style="width: 560px"
              filterable
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item :label="t('trade_common_labelName')" required>
            <SccsPlanLabelPopover
              :defaultPlanForm="form"
              :maxlength="20"
              @handleSelectTag="planForm => handlePlanTagForm(form, planForm)"
            />
          </el-form-item>
          <el-form-item :label="t('trade_common_lableTag')" required>
            <div class="table-header-body">
              <span class="sccs-plan-tip-text">
                <i class="iconfont link-tips-warm" />
                {{ t("trade_common_plannedTagTip") }}
              </span>
            </div>
            <div class="table-header-body">
              <span class="sccs-plan-tip-text">
                <i class="iconfont link-tips-warm" />
                <span class="sccs-plan-tip-text-red">
                  {{ t("trade_common_fileds") }}
                </span>
                {{ t("trade_common_planedTip") }}
              </span>
              <span class="sccs-plan-tip-add" @click="handleAddTableData">
                <i class="iconfont link-add" />
                {{ t("trade_common_increase") }}
              </span>
            </div>
            <el-table
              :data="tableData"
              stripe
              :border="true"
              header-row-class-name="table-header-class"
              :tooltip-options="{ showAfter: 500 }"
            >
              <el-table-column
                :label="t('trade_common_sort')"
                prop="name"
                width="70"
              >
                <template #default="{ $index }">
                  <i
                    class="iconfont link-xiangshang font18 mr4"
                    :class="{ 'disabled-icon': $index === 0 }"
                    @click="handleTableRowSort('last', $index, $index === 0)"
                  />
                  <i
                    class="iconfont link-xiangxia font18"
                    :class="{
                      'disabled-icon': $index === tableData.length - 1
                    }"
                    @click="
                      handleTableRowSort(
                        'next',
                        $index,
                        $index === tableData.length - 1
                      )
                    "
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="t('trade_common_tagName')"
                prop="name"
                width="300"
              >
                <template #header>
                  <span class="required-icon">
                    {{ t("trade_common_tagName") }}
                  </span>
                </template>
                <template #default="{ row }">
                  <SccsPlanLabelPopover
                    :defaultPlanForm="{
                      labelName: row.labelValue,
                      labelNameZh: row.labelNameZh,
                      labelNameEn: row.labelValueEn
                    }"
                    :maxlength="20"
                    @handleSelectTag="
                      planForm => handleRowPlanTagForm(row, 'label', planForm)
                    "
                    @handleClearLabelName="
                      planForm => handleClearLabelName(row, 'label', planForm)
                    "
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="t('trade_common_style')"
                prop="name"
                cell-class-name="tag-table-cell"
                width="130"
              >
                <template #header>
                  <span class="required-icon">
                    {{ t("trade_common_style") }}
                  </span>
                </template>
                <template #default="{ row }">
                  <el-popover
                    ref="popoverRef"
                    placement="bottom"
                    :title="t('trade_common_selectcolor')"
                    :width="360"
                    trigger="click"
                    popper-class="popover-tag-color"
                  >
                    <template #reference>
                      <div class="tag-name-row">
                        <span
                          class="tag-name-col"
                          :style="{
                            'border-color': row.style,
                            color: row.style
                          }"
                        >
                          {{ row.labelValue }}
                        </span>
                      </div>
                    </template>
                    <template #default>
                      <ul class="tag-list">
                        <li
                          v-for="item in tagColors"
                          :key="item"
                          class="tag-color-col"
                          :class="[row.style === item ? 'active-tag-li' : '']"
                          @click="handleActiveColor(row, item)"
                        >
                          <span
                            class="tag-li"
                            :style="{ background: item, 'border-color': item }"
                          >
                            <i class="iconfont link-tick" />
                          </span>
                        </li>
                      </ul>
                    </template>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column
                :label="t('trade_common_conditions')"
                width="980"
                prop="conditions"
              >
                <template #header>
                  <span class="required-icon">
                    {{ t("trade_common_conditions") }}
                  </span>
                </template>
                <template #default="{ row }">
                  <SccsPlanLabelTableColumn
                    ref="planTableColumnRef"
                    :sccsId="basicInfo.id"
                    :planSetting="planSetting"
                    :conditions="row.conditions"
                    :planSettingSelect="planSettingSelect"
                    @handleChange="data => handleChange(data, row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="t('trade_common_labelDescribe')"
                width="300"
                prop="name"
              >
                <template #header>
                  <div class="plan-table-column">
                    {{ t("trade_common_labelDescribe") }}
                    <el-tooltip
                      effect="dark"
                      :content="t('trade_planning_tip')"
                      placement="top"
                      :show-after="500"
                    >
                      <i class="iconfont link-explain" />
                    </el-tooltip>
                  </div>
                </template>
                <template #default="{ row }">
                  <SccsPlanLabelPopover
                    :defaultPlanForm="{
                      labelName: row.description,
                      labelNameZh: row.descriptionZh,
                      labelNameEn: row.descriptionEn
                    }"
                    @handleSelectTag="
                      planForm =>
                        handleRowPlanTagForm(row, 'description', planForm)
                    "
                    @handleClearLabelName="
                      planForm =>
                        handleClearLabelName(row, 'description', planForm)
                    "
                  />
                </template>
              </el-table-column>
              <el-table-column prop="name" width="50" fixed="right">
                <template #default="{ $index }">
                  <el-dropdown v-if="$index !== 0" trigger="click">
                    <i class="iconfont link-more-left" />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleDeleteTable($index)">
                          <i class="iconfont link-ashbin colorRed font14" />
                          <span class="colorRed">
                            {{ t("trade_common_delete") }}
                          </span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, markRaw } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import LkDialog from "@/components/lkDialog/index";
import SccsPlanLabelTableColumn from "./SccsPlanLabelTableColumn.vue";
import SccsPlanLabelPopover from "./SccsPlanLabelPopover.vue";
import {
  addSccsLabel,
  getSccsLableDetail,
  editSccsLabel,
  getSccsLableAllFields
} from "@/api/sccs";
import { emitter } from "@/utils/mitt";
import { cloneDeep } from "@pureadmin/utils";

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

let form = ref<any>({
  labelName: "",
  labelNameZh: "",
  labelNameEn: ""
});
const relation = ref<any>({
  id: "",
  relationId: ""
});
const { t } = useI18n();
const route = useRoute();
const planTableColumnRef = ref<HTMLElement | any>(null);
const LkDialogRef = ref<any>(null);
const editStatus = ref<string>("");
const ruleFormRef = ref<any>(null);
const tableData = ref<any[]>([
  {
    style: "#E33E38"
  }
]);
const planSetting = ref<any>({});
const planSettingSelect = ref<any[]>([]);
const popoverRef = ref<any>(null);
const tagColors = ref<string[]>([
  "#E33E38",
  "#FF708C",
  "#FF60DC",
  "#B85CFF",
  "#7B67EE",
  "#B82979",
  "#D99D36",
  "#1295CA",
  "#0DA993",
  "#7C9206",
  "#646A73"
]);

const emit = defineEmits<{
  (e: "handleSuccess"): void;
}>();

const handlePlanTagForm = (formValue: string, planForm: any) => {
  form.value = Object.assign(formValue, planForm);
};

const handleTableRowSort = (
  sortType: string,
  index: number,
  executeBool: boolean
) => {
  if (executeBool) return;
  const tableRow = cloneDeep(tableData.value[index]);
  const tableDeepData = cloneDeep(tableData.value);
  tableDeepData.splice(index, 1);
  tableDeepData.splice(
    sortType === "next" ? index + 1 : index - 1,
    0,
    tableRow
  );
  tableData.value = tableDeepData;
  tableData.value.map(
    (tableRow, tableIndex) => (tableRow["sort"] = tableIndex)
  );
};

const handleCloseDialog = () => {
  form.value = {
    labelName: "",
    labelNameZh: "",
    labelNameEn: ""
  };
  tableData.value = [
    {
      style: "#E33E38"
    }
  ];
};

const handleRowPlanTagForm = (row: any, field: string, planForm: any) => {
  if (field === "label") {
    row["labelValue"] = planForm.labelName;
    row["labelValueEn"] = planForm.labelNameEn;
    row["labelNameZh"] = planForm.labelNameZh;
  } else {
    row["description"] = planForm.labelName;
    row["descriptionZh"] = planForm.labelNameZh;
    row["descriptionEn"] = planForm.labelNameEn;
  }
};

// 清楚标签名称
const handleClearLabelName = (row: any, field: string, planForm: any) => {
  if (field === "label") {
    row["labelValue"] = planForm.labelName;
    row["labelValueEn"] = planForm.labelNameEn;
    row["labelNameZh"] = planForm.labelNameZh;
  } else {
    row["description"] = planForm.labelName;
    row["descriptionZh"] = planForm.labelNameZh;
    row["descriptionEn"] = planForm.labelNameEn;
  }
};

const handleValidatePlanForm = () => {
  const { labelName } = form.value;
  if (!labelName) {
    ElMessage.error(t("trade_common_labelNameNot"));
    return false;
  }
  for (let i = 0, len = tableData.value.length; i < len; i++) {
    const row = tableData.value[i];
    if (!row.labelValue) {
      ElMessage.error(t("trade_common_tagNameNotTip"));
      return false;
    }
    let conditionRowList = [];
    if (!row.conditions) {
      ElMessage.error(t("trade_common_ImproveSettings"));
      return;
    }
    for (let conditionsRow of row.conditions) {
      if (Object.keys(conditionsRow[0]).length === 0) {
        ElMessage.error(t("trade_common_ImproveSettings"));
        return;
      }
      conditionRowList.push(
        ...conditionsRow.filter(condition => condition.validate)
      );
    }
    if (conditionRowList.length !== 0) {
      ElMessage.error(t("trade_common_ImproveSettings"));
      return;
    }
  }
  return true;
};

const handleConfirmPlan = async (): Promise<void> => {
  if (!handleValidatePlanForm()) return;
  const { code } =
    editStatus.value === "add"
      ? await addSccsLabel({
          sccsId: props.basicInfo.id,
          relationId: planSetting.value.id,
          labelDetail: tableData.value,
          isMainForm: planSetting.value.isMainForm,
          ...form.value
        })
      : await editSccsLabel({
          sccsId: props.basicInfo.id,
          relationId: planSetting.value.id,
          labelDetail: tableData.value,
          isMainForm: planSetting.value.isMainForm,
          ...form.value,
          ...relation.value
        });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_dealSuccess"),
      type: "success"
    });
    LkDialogRef.value.close();
    emit("handleSuccess");
    emitter.emit("handleReloadOrderPageData");
  }
};

const handleActiveColor = (row: any, color: string) => {
  row.style = color;
  if (popoverRef.value instanceof Array) {
    popoverRef.value.map(popover => popover.hide());
  } else {
    popoverRef.value.hide();
  }
};

const handleChange = (data, row): void => {
  row.conditions = data;
};

const handleAddTableData = (): void => {
  tableData.value.push({
    style: "#8A8E99"
  });
};

const handleDeleteTable = (index): void => {
  ElMessageBox.confirm(
    t("trade_order_removeTip"),
    t("trade_common_deleteTag"),
    {
      confirmButtonText: t("trade_common_confirm"),
      cancelButtonText: t("trade_common_cancel"),
      confirmButtonClass: "confrim-message-btn-class",
      cancelButtonClass: "cancel-message-btn-class",
      type: "error",
      icon: markRaw(WarningFilled),
      center: true
    }
  ).then(() => {
    tableData.value.splice(index, 1);
  });
};

const open = async (
  rowData: any,
  editStatusStr: string,
  data: any
): Promise<void> => {
  LkDialogRef.value.open();
  planSetting.value = editStatusStr === "edit" ? data : rowData;
  editStatus.value = editStatusStr;
  const { sccsId } = route.query;
  const res = await getSccsLableAllFields({
    sccsId: sccsId as string,
    workOrder: false
  });
  planSettingSelect.value = res.data;
  if (editStatusStr === "edit") {
    const { code, data } = await getSccsLableDetail({
      labelId: rowData.tableId
    });
    if (code === 0) {
      const {
        labelName,
        labelNameZh,
        labelNameEn,
        labelDetail,
        relationId,
        id
      } = data[0];
      form.value.labelName = labelName;
      form.value.labelNameZh = labelNameZh;
      form.value.labelNameEn = labelNameEn;
      relation.value.id = id;
      relation.value.relationId = relationId;
      tableData.value = labelDetail;
    }
  }
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.disabled-icon {
  color: #8c8c8c;
  cursor: not-allowed;
  opacity: 0.5;
}

.el-form {
  ::v-deep(.el-form-item.widthfixRow) {
    width: 100% !important;

    .el-form-item__content {
      width: 100% !important;
    }
  }
}

::v-deep(.el-table) {
  .el-table__row:hover {
    background: transparent !important;

    .el-table__cell {
      background: transparent !important;
    }

    .el-table__cell.el-table-fixed-column--right {
      z-index: 1980 !important;
      background: #fff !important;
    }
  }
}

.table-sort-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 3px;
  text-align: center;
  border: 0.2px solid #8c8c8c;
  border-radius: 50%;

  .el-icon {
    font-size: 14px;
    font-weight: bold;
  }
}

.tag-list {
  display: grid;
  grid-template-columns: 11.1% 11.1% 11.1% 11.1% 11.1% 11.1% 11.1% 11.1% 11.1%;

  .tag-color-col {
    display: inline-grid;
    margin-bottom: 10px;

    .tag-li {
      display: inline-flex;
      width: 22px;
      height: 22px;
      margin: 3px;
      line-height: 22px;
      text-align: center;
      border-radius: 50%;

      .iconfont {
        width: 100%;
        font-size: 10px;
        color: #fff;
        visibility: hidden;
      }
    }

    &:nth-last-child(1) {
      margin-bottom: 0 !important;
    }

    &:nth-last-child(2) {
      margin-bottom: 0 !important;
    }

    &:nth-last-child(3) {
      margin-bottom: 0 !important;
    }

    &:nth-last-child(4) {
      margin-bottom: 0 !important;
    }
  }

  .active-tag-li {
    width: 28px;
    height: 28px;
    text-align: center;
    border: 2px solid #2082ed !important;
    border-radius: 50%;

    .tag-li {
      margin: 1px;

      .iconfont {
        visibility: inherit;
      }
    }
  }
}

.el-table__cell {
  padding: 0 !important;
}

.plan-table-column {
  font-size: 14px;
  color: #8c8c8c;

  .iconfont {
    margin-right: 5px;
    font-size: 12px;
    font-weight: normal;
    cursor: pointer;
  }
}

.tag-name-row {
  box-sizing: content-box !important;
  display: inline-block;
  width: 70px !important;
  height: 20px;
  padding: 6px;
  cursor: pointer;
  border: 1px solid #e0dfe4;
  border-radius: 4px;

  .tag-name-col {
    display: inline-block;
    width: 58px;
    height: 20px;
    padding: 0 5px;
    overflow: hidden;
    font-size: 12px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: transparent;
    border: 1px solid #e33e38;
    border-radius: 11px;
  }

  &:hover {
    background: #f5f5f5;
  }
}

.table-header-body {
  display: flex;
  align-items: center;
  width: 100%;

  .sccs-plan-tip-text {
    flex: 1;
    align-items: center;
    margin-bottom: 14px;
    margin-left: 5px;
    font-size: 12px;
    line-height: 16px;
    color: #8c8c8c;
    text-align: left;

    .iconfont {
      margin-right: 5px;
      font-size: 12px;
      color: #8c8c8c;
    }

    .sccs-plan-tip-text-red {
      margin-right: 6px;
      color: #f56c6c;
      text-decoration: line-through;
    }
  }

  .sccs-plan-tip-add {
    flex: 1;
    align-items: center;
    font-size: 14px;
    color: #0070d2;
    text-align: right;
    cursor: pointer;

    .iconfont {
      font-size: 12px;
    }
  }
}

.itemCenter {
  display: flex;
  align-items: center;
  height: 100%;

  .iconfont {
    cursor: pointer;
  }
}

.required-icon {
  color: #8c8c8c;

  &::before {
    display: inline-block;
    margin-right: 5px;
    font-size: 14px;
    font-weight: bolder;
    color: #e62412;
    content: "*";
  }
}

.sccs-member-manage-col {
  box-sizing: border-box;
  width: 98%;
  // box-shadow: 0px 2px 18px 0px rgba(0, 0, 0, 0.34);
  padding: 13px 11px;
  margin-bottom: 16px;
  background: #f7f7f7;
  border-radius: 4px;

  .sccs-member-manage-col-top {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .sccs-member-manage-col-username {
      padding: 0 5px 0 8px;
      font-size: 14px;
      line-height: 16px;
      color: #262626;
      text-shadow: 0 2px 18px 0 rgb(0 0 0 / 34%);
    }

    .sccs-member-manage-col-account {
      font-size: 14px;
      line-height: 16px;
      color: #595959;
      text-shadow: 0 2px 18px 0 rgb(0 0 0 / 34%);
    }
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.fence-list-tip {
  padding-left: 14px;
  margin-bottom: 14px;
  font-size: 14px;
  // height: 38px;
  line-height: 38px;
  color: #fa8d0a;
  text-align: left;
  background: #fdf6ec;
  border-radius: 2px;

  .iconfont {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
  }
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}

.el-table-fixed-column--right {
  z-index: 1980 !important;
}
</style>
<style lang="scss">
.popover-tag-color {
  padding: 15px 18px !important;

  .el-popover__title {
    font-size: 14px;
    font-weight: bold;
    color: #262626;
  }
}
</style>
