<template>
  <el-popover placement="top" width="auto" @before-enter="handleBeforeEnter">
    <template #reference>
      <div class="triangle-topright" :class="triangleClass" />
    </template>
    <div class="custom-checkbox-popover-body">
      <div
        v-if="Object.keys(relationData).includes('ORDER_BY_CLONE')"
        class="custom-checkbox-popover-desc"
      >
        {{ `R: ${t("trade_order_replayOrderTip")}` }}
      </div>
      <div
        v-if="Object.keys(relationData).includes('ORDER_BY_CLONE')"
        class="custom-checkbox-popover-row"
      >
        <div class="custom-checkbox-popover-title custom-order">
          {{ t("trade_order_original") }}
        </div>
        <div
          class="custom-checkbox-popover-tip"
          @click="gotoDetail(relationData['ORDER_BY_CLONE']['order'][0])"
        >
          {{ relationData["ORDER_BY_CLONE"]["order"][0].toOrderMark }}
        </div>
      </div>
      <!-- 组件优化，循环遍历 -->
      <div
        v-for="type in Object.keys(relationDataFilter)"
        :key="type"
        class="custom-checkbox-popover-row"
      >
        <div
          class="custom-checkbox-popover-title"
          :class="relationTypeObj.get(type).class"
        >
          {{ t(relationTypeObj.get(type).label) }}
        </div>
        <!-- 列表里程碑互用 -->
        <div
          v-if="['MILESTONE_BY_CLONE', 'MILESTONE_CLONE'].includes(type)"
          class="milestone-mark"
        >
          <div
            v-for="(msId, index) in Object.keys(relationDataFilter[type])"
            :key="index"
            class="milestone-mark-item"
          >
            <div class="milestone-name">
              {{ `${relationDataFilter[type][msId][0].fromMsName}:` }}
            </div>
            <div
              v-for="(mark, index) in relationDataFilter[type][msId]"
              :key="index"
              class="custom-checkbox-popover-tip"
              @click="gotoDetail(mark)"
            >
              {{
                relationDataFilter[type][msId].length - 1 === index
                  ? mark.toOrderMark
                  : mark.toOrderMark + "、"
              }}
            </div>
          </div>
        </div>
        <!-- 列表工单互用 -->
        <div
          v-else-if="
            [
              'WORK_ORDER_BY_CLONE',
              'WORK_ORDER_CLONE',
              'WORK_ORDER_BY_MS_CLONE',
              'WORK_ORDER_MS_CLONE'
            ].includes(type)
          "
          class="work-order-mark"
        >
          <div
            v-if="
              ['WORK_ORDER_BY_MS_CLONE', 'WORK_ORDER_MS_CLONE'].includes(type)
            "
            class="milestone-name"
          >
            {{ `${relationDataFilter[type][0].fromMsName}:` }}
          </div>
          <div
            v-for="(mark, index) in relationDataFilter[type]"
            :key="index"
            class="custom-checkbox-popover-tip"
            @click="gotoDetail(mark)"
          >
            {{
              relationDataFilter[type].length - 1 === index
                ? mark.toOrderMark
                : mark.toOrderMark + "、"
            }}
          </div>
        </div>
        <!-- 列表订单关联 -->
        <div v-else class="order-mark">
          <div
            v-for="(mark, index) in relationDataFilter[type]['order']"
            :key="index"
            class="custom-checkbox-popover-tip"
            @click="gotoDetail(mark)"
          >
            {{
              relationDataFilter[type]["order"].length - 1 === index
                ? mark.toOrderMark
                : mark.toOrderMark + "、"
            }}
          </div>
        </div>
      </div>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import {
  getWorkOrderCloneInfo,
  getOrderRelationInfo,
  canGetOrderInfo,
  getTradeSccsRolePermission,
  getTradeCoopSccsRolePermission
} from "@/api/order";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { cloneDeep } from "@pureadmin/utils";
import { useRoute, useRouter } from "vue-router";
import { storageLocal } from "@pureadmin/utils";

const props = defineProps({
  rowData: {
    type: Object,
    default: () => {}
  },
  presentViewInfo: {
    type: Object,
    default: () => {}
  }
});
// 定义互用操作显示的文案和对应展示的样式
const relationTypeObj = new Map([
  // 里程碑互用自
  [
    "MILESTONE_BY_CLONE",
    { label: "trade_order_milestoneUsein", class: "custom-ms-clone-from" }
  ],
  // 里程碑层面互用自工单
  [
    "WORK_ORDER_BY_MS_CLONE",
    { label: "trade_order_milestoneUsein", class: "custom-ms-clone-from" }
  ],
  // 里程碑互用于
  [
    "MILESTONE_CLONE",
    { label: "trade_order_milestoneUse", class: "custom-ms-clone" }
  ],
  // 里程碑层面互用于工单
  [
    "WORK_ORDER_MS_CLONE",
    { label: "trade_order_milestoneUse", class: "custom-ms-clone" }
  ],
  // 关联
  [
    "ORDER_SAME_SCCS_RELATION",
    { label: "trade_common_associationOrder", class: "custom-order-related" }
  ],
  // sccs关联
  [
    "ORDER_CROSS_SCCS_RELATION",
    {
      label: "trade_order_sccsassociation",
      class: "custom-order-related-cross"
    }
  ],
  // 工单互用自
  [
    "WORK_ORDER_BY_CLONE",
    { label: "trade_order_workOrderUsein", class: "custom-ms-clone-from" }
  ],
  // 工单互用于
  [
    "WORK_ORDER_CLONE",
    { label: "trade_order_workOrderUse", class: "custom-ms-clone" }
  ],
  [
    "ORDER_CLONE",
    { label: "trade_common_repeat_order", class: "custom-order-clone" }
  ]
]);

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const relationData = ref({});

const relationDataFilter = computed(() => {
  let filterObj = cloneDeep(relationData.value);
  if (Object.keys(filterObj).includes("ORDER_BY_CLONE")) {
    for (let property in filterObj) {
      if (property === "ORDER_BY_CLONE") {
        delete filterObj["ORDER_BY_CLONE"];
      }
    }
  }
  return filterObj;
});

const triangleClass = computed(() => {
  let classStr = "";
  if (props.rowData.relationIconType) {
    switch (props.rowData.relationIconType) {
      case "CLONE_WORK_ORDER":
        // 工单互用于
        classStr = "triangle-topright-three";
        break;
      case "BY_CLONE_WORK_ORDER":
        // 工单互用自
        classStr = "triangle-topright-two";
        break;
      case "BY_CLONE_ORDER":
        // 订单翻单于（R）
        classStr = "triangle-topright-one";
        break;
      case "OTHER_RELATION_ORDER":
        // 订单其它关联关系（包含订单翻单自，里程碑互用于，里程碑互用自，订单关联）（i）
        classStr = "triangle-topright-three";
        break;
    }
  }
  return classStr;
});

const handleBeforeEnter = async () => {
  const { viewType } = props.presentViewInfo;
  if (viewType === "MILESTONE") {
    const { data } = await getWorkOrderCloneInfo({
      idList: [props.rowData.workOrderId]
    });

    if (Object.keys(data).length > 0) {
      relationData.value = data[props.rowData.workOrderId];
    }
  } else if (viewType === "ORDER") {
    const { data } = await getOrderRelationInfo({
      idList: [props.rowData.orderId]
    });

    if (Object.keys(data).length > 0) {
      relationData.value = data[props.rowData.orderId];
    }
  }
};
const gotoDetail = async orderDta => {
  const { code } = await canGetOrderInfo({
    sccsId: orderDta.toSccsId,
    orderId: orderDta.toOrderId
  });
  code === 0 && handleJumpDetail(orderDta);
};
const handleJumpDetail = async (row): Promise<void> => {
  const { coopTeamMark } = route.query;
  const sessionItem = {
    msId: row.fromMsId,
    workOrderId: row.dataType === "WORK_ORDER" ? row.toId : ""
  };
  // 创建浏览器会话缓存以便详情定位
  sessionStorage.setItem(
    "orderDetailPagePositionInfo",
    JSON.stringify(sessionItem)
  );
  const params = {
    sccsName: row.toSccsName,
    sccsId: row.toSccsId,
    templateId: row.toTemplateId,
    orderId: row.toOrderId,
    orderMark: row.toOrderMark ? row.toOrderMark : row.serialNumber,
    coopTeamMark: coopTeamMark
  };
  let resp: any = {};
  if (coopTeamMark === "2") {
    resp = await getTradeSccsRolePermission({
      sccsId: row.toSccsId,
      orderIdList: [row.toOrderId]
    });
  } else {
    resp = await getTradeCoopSccsRolePermission({
      sccsId: row.toSccsId,
      orderIdList: [row.toOrderId]
    });
  }
  //@ts-ignore
  storageLocal().setItem(
    `${row.toOrderId}_userTeamRole`,
    resp.data[row.toOrderId]
  );
  router.push({ name: "orderDetail", query: params });
};
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
