<template>
  <div class="sccs-manage-body">
    <div class="sccs-manage-group">
      <div v-if="sccsManageList.length === 0" class="sccs-manage-image">
        <el-image :src="emptyNotData" />
        <p class="sccs-manage-message">{{ t("trade_home_sccsEmpty") }}</p>
      </div>
      <div v-for="item in sccsManageList" :key="item" class="sccs-manage-col">
        <div v-show="item.groupName" class="sccs-manage-title">
          {{ item.groupName }}
        </div>
        <div
          class="sccs-manage-group-body"
          :class="[item.groupName ? '' : 'margin-top16']"
        >
          <div
            v-for="sccs in item.sccsList"
            :key="sccs.id"
            class="sccs-manage-group-col"
            @click="handleSccsClick(sccs)"
          >
            <div class="sccs-manage-card">
              <div class="sccs-manage-card-middle">
                <ReText
                  type="info"
                  class="sccs-manage-card-title"
                  :tippyProps="{ delay: 50000 }"
                >
                  {{ sccs.sccsName }}
                </ReText>
                <div class="sccs-manage-card-norm-body">
                  <div class="sccs-manage-card-norm-col">
                    <div class="sccs-manage-card-norm-title">
                      {{ t("trade_home_orderTotal") }}
                    </div>
                    <div class="sccs-manage-card-norm-number">
                      {{ sccs.totalOrderNum }}
                    </div>
                  </div>
                  <div class="sccs-manage-card-norm-col">
                    <div class="sccs-manage-card-norm-title">
                      {{ t("trade_home_inProgress") }}
                    </div>
                    <div class="sccs-manage-card-norm-number primary">
                      {{ sccs.ongoingOrderNum }}
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="sccs-manage-card-footer"
                :class="[sccs.coopTeamMark ? 'coopTeam' : '']"
              >
                <span v-if="sccs.coopTeamMark" class="sccs-footer-flex">
                  <svg class="svg-icon svg-icon-coop" aria-hidden="true">
                    <use xlink:href="#link-coop" />
                  </svg>
                  <ReText
                    type="info"
                    class="sccs-manage-footer-flex-text"
                    :tippyProps="{ delay: 50000 }"
                  >
                    {{ sccs.createTeamName }}
                  </ReText>
                </span>
                <span @click.stop="updateSccsTopById(sccs.id)">
                  <i
                    v-if="!sccs.sccsTopFlag"
                    class="iconfont link-star colorNormal"
                  />
                  <i
                    v-if="sccs.sccsTopFlag"
                    class="iconfont link-star-filled colorOrange"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watchEffect } from "vue";
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";
import { getUserRolePerm } from "@/utils/auth";
import { setSccsTopById } from "@/api/sccs";
import emptyNotData from "@/assets/images/home/<USER>";

const { t } = useI18n();
const sccsManageList = ref([]);
const props = defineProps({
  sccsManage: {
    type: Object as PropType<any>,
    default: () => {}
  },
  isGroup: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const emit = defineEmits<{
  (e: "updateSccsTop"): void;
  (
    e: "handleSccsClick",
    data: {
      id: string;
      groupId: string;
      templateId: string;
      sccsName: string;
      coopTeamMark: boolean;
    }
  ): void;
}>();

const updateSccsTopById = async (sccsId: string): Promise<void> => {
  const { code, data } = await setSccsTopById({ sccsId: sccsId });
  if (code === 0) {
    emit("updateSccsTop");
  }
};

const handleSccsClick = async (sccsRow: any): Promise<void> => {
  const { id, groupId, templateId, sccsName, coopTeamMark } = sccsRow;
  getUserRolePerm(id, coopTeamMark);
  emit("handleSccsClick", { id, groupId, templateId, sccsName, coopTeamMark });
};

watchEffect(() => {
  if (props.isGroup) {
    sccsManageList.value =
      props.sccsManage[0] && props.sccsManage[0].groupName
        ? props.sccsManage
        : [
            {
              groupName: "",
              sccsList: props.sccsManage
            }
          ];
  } else {
    sccsManageList.value =
      props.sccsManage.length > 0
        ? [
            {
              groupName: "",
              sccsList: props.sccsManage
            }
          ]
        : props.sccsManage;
  }
});
</script>
<style lang="scss" scoped>
.sccs-manage-body {
  padding: 0 16px;

  .sccs-manage-group {
    .sccs-manage-col {
      cursor: pointer;

      .sccs-manage-title {
        margin: 16px 0;
        font-size: 16px;
        line-height: 22px;
        color: #595959;
        text-align: left;
      }

      .sccs-manage-group-body {
        display: inline-block;
        width: 100%;

        &.margin-top16 {
          margin-top: 16px;
        }

        .sccs-manage-group-col {
          display: inline-block;
          width: 268px;
          margin-right: 20px;

          .sccs-manage-card {
            height: 142px;
            margin-bottom: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 6px 0 rgb(1 6 62 / 10%);

            .sccs-manage-card-middle {
              height: 106px;
              padding: 12px 14px;

              .sccs-manage-card-title {
                overflow: hidden;
                font-size: 14px;
                font-weight: bold;
                line-height: 20px;
                color: #262626;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .sccs-manage-card-norm-body {
                display: flex;

                .sccs-manage-card-norm-col {
                  flex: 1;
                  text-align: center;

                  .sccs-manage-card-norm-title {
                    margin: 6px 0 8px;
                    font-size: 12px;
                    line-height: 20px;
                    color: #8c8c8c;
                  }

                  .sccs-manage-card-norm-number {
                    font-size: 18px;
                    line-height: 20px;
                    color: #262626;

                    &.primary {
                      color: #0070d2;
                    }

                    &.danger {
                      color: #ff2424;
                    }
                  }
                }
              }
            }

            .sccs-manage-card-footer {
              display: flex;
              align-items: center;
              justify-content: end;
              height: 36px;
              padding: 0 18px 0 16px;
              background: #fafafa;
              border-radius: 0 0 8px 8px;

              &.coopTeam {
                background: #edf3fd;
              }

              .sccs-footer-flex {
                display: flex;
                flex: 1;
                align-items: center;

                .svg-icon {
                  display: inline-block;
                  width: 12px;
                  height: 12px;
                  margin-right: 6px;
                  vertical-align: middle;
                }

                .sccs-manage-footer-flex-text {
                  align-items: center;
                  max-width: 180px;
                  font-size: 12px;
                  color: #595959;
                }
              }

              .colorNormal {
                color: #b8b7b7;
              }

              .colorOrange {
                color: rgb(254 199 60);
              }
            }

            &:hover {
              border-radius: 8px;
              box-shadow: 0 6px 18px 0 rgb(1 6 62 / 14%);
            }
          }
        }
      }
    }

    .sccs-manage-image {
      margin: 20px auto;
      text-align: center;

      .el-image {
        width: 342px;
      }

      .sccs-manage-message {
        font-size: 14px;
        line-height: 30px;
        color: #797979;
        text-align: center;
      }
    }
  }
}
</style>
