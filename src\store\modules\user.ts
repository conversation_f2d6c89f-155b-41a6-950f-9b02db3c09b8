import { defineStore } from "pinia";
import {
  type userType,
  store,
  router,
  resetRouter,
  routerArrays,
  storageLocal
} from "../utils";
import { refreshToken, logoutApi } from "@/api/login";
import type { RefreshTokenResult } from "@/api/type";
import { useMultiTagsStoreHook } from "./multiTags";
import { type DataInfo, setToken, removeToken, userKey } from "@/utils/auth";
import { storageSession } from "@pureadmin/utils";

export const useUserStore = defineStore({
  id: "pure-user",
  state: (): userType => ({
    // 头像
    avatar: storageLocal().getItem<DataInfo<number>>(userKey)?.avatar ?? "",
    // 用户名
    username: storageLocal().getItem<DataInfo<number>>(userKey)?.username ?? "",
    // 昵称
    nickname: storageLocal().getItem<DataInfo<number>>(userKey)?.nickname ?? "",
    // 页面级别权限
    roles: storageLocal().getItem<DataInfo<number>>(userKey)?.roles ?? [],
    // 按钮级别权限
    permissions:
      storageLocal().getItem<DataInfo<number>>(userKey)?.permissions ?? [],
    // 邮箱
    email: storageLocal().getItem<DataInfo<number>>(userKey)?.email ?? "",
    // 最后登录团队ID
    latestLoginTeamId:
      storageLocal().getItem<DataInfo<number>>(userKey)?.latestLoginTeamId ??
      "",
    //最近登录进入的teamMemberId
    latestLoginTeamMemberId:
      storageLocal().getItem<DataInfo<number>>(userKey)
        ?.latestLoginTeamMemberId ?? "",
    // 是否勾选了登录页的免登录
    isRemembered: false,
    // 登录页的免登录存储几天，默认7天
    loginDay: 0
  }),
  actions: {
    /** 存储头像 */
    SET_AVATAR(avatar: string) {
      this.avatar = avatar;
    },
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      this.username = username;
    },
    /** 存储昵称 */
    SET_NICKNAME(nickname: string) {
      this.nickname = nickname;
    },
    /** 存储昵称 */
    SET_EMAIL(email: string) {
      this.email = email;
    },
    /** 存储昵称 */
    SET_LATESTLOGINTEAMID(latestLoginTeamId: string) {
      this.latestLoginTeamId = latestLoginTeamId;
    },
    /** 存储昵称 */
    SET_LATESTLOGINTEAMMEMBERID(latestLoginTeamMemberId: string) {
      this.latestLoginTeamMemberId = latestLoginTeamMemberId;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },
    /** 存储按钮级别权限 */
    SET_PERMS(permissions: Array<string>) {
      this.permissions = permissions;
    },
    /** 存储是否勾选了登录页的免登录 */
    SET_ISREMEMBERED(bool: boolean) {
      this.isRemembered = bool;
    },
    /** 设置登录页的免登录存储几天 */
    SET_LOGINDAY(value: number) {
      this.loginDay = Number(value);
    },
    /** 前端登出（不调用接口） */
    async logOut(url: string) {
      const res = await logoutApi();
      if (res.code === 0) {
        //@ts-ignore
        const storageData = Object.keys(storageLocal().storage).filter(
          storage => storage.indexOf("_userTeamRole") !== -1
        );
        storageData.forEach(storageName => {
          storageLocal().removeItem(storageName);
        });

        storageSession().removeItem("userPresentTeam");
        storageSession().removeItem("userPermission");
        storageSession().removeItem("userMemberTeam");
        storageLocal().removeItem("workOrderProcessorList");
        this.username = "";
        this.roles = [];
        this.permissions = [];
        sessionStorage.removeItem("username");
        removeToken();
        useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
        resetRouter();
        router.push(url ? url : "/login");
      }
    },
    /** 刷新`token` */
    async handRefreshToken(data) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshToken(data)
          .then(res => {
            if (res) {
              setToken(res.data);
              resolve(res.data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
