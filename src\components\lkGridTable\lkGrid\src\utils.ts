import { BigNumber } from "bignumber.js";
import { formatNumberValueOf } from "@/utils/common";

// 判断值是否为空
/**
 * 判断值是否为空
 * @param value 任意类型的值
 * @returns 是否为空
 */
function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true;

  if (typeof value === "string") {
    const str = value.trim();
    return str === "" || str === '""' || str === "''";
  }

  if (Array.isArray(value)) {
    return value.length === 0;
  }

  if (typeof value === "object") {
    return Object.keys(value).length === 0;
  }

  return false;
}

// 查找带千分位分隔符的最大值
function findMaxWithThousandSeparators(numberStrings: any[]): string {
  BigNumber.set({
    FORMAT: {
      groupSeparator: ",",
      decimalSeparator: "."
    }
  });
  const bnArray = numberStrings.map(str => {
    const groupSep = BigNumber.config().FORMAT.groupSeparator;
    const numericStr = `${str}`.replace(new RegExp(`\\${groupSep}`, "g"), "");
    return new BigNumber(numericStr);
  });
  const maxBn = bnArray.reduce(
    (max, current) => (current.gt(max) ? current : max),
    new BigNumber(0)
  );
  return maxBn.toFormat();
}

/**
 * 计算列的统计结果
 * @param column 列配置对象，需包含 name、widgetItemInfo、precision 等属性
 * @param type 统计类型
 * @param selectedRecords 选中的数据行
 * @returns 统计结果字符串
 */
export function computedColumnStatistical(
  column: any,
  type: string,
  selectedRecords: any[]
): string {
  const fieldName = column;
  const widgetItemInfo = column.widgetItemInfo || {};
  const precision = widgetItemInfo.precision || 0;

  if (!selectedRecords?.length) return "";

  const statisticalHandlers = {
    /** 计数 */
    COUNT: () => `${selectedRecords.length}`,
    /** 未填写 */
    NOT_FILLED: () => {
      const isManagerField = fieldName.includes("managerMemberId");
      const result = selectedRecords.filter(record => {
        if (isManagerField) {
          return !record[fieldName]?.username;
        }
        const value = record[fieldName];
        return Array.isArray(value) ? !value.length : isEmpty(value);
      });
      return `${result.length}`;
    },
    /** 已填写 */
    FILLED: () => {
      const isManagerField = fieldName.includes("managerMemberId");
      const result = selectedRecords.filter(record => {
        if (isManagerField) {
          return !!record[fieldName]?.username;
        }
        const value = record[fieldName];
        return Array.isArray(value) ? value.length > 0 : !isEmpty(value);
      });
      return `${result.length}`;
    },
    /** 唯一值 */
    UNIQUE_VALUE: () => {
      const uniqueValues = new Set();
      const notEmptyRecords = selectedRecords.filter(record => {
        const value = record[fieldName];
        return Array.isArray(value) ? value.length > 0 : !isEmpty(value);
      });

      notEmptyRecords.forEach(record => {
        if (record[fieldName]) {
          const value =
            typeof record[fieldName] === "object"
              ? record[fieldName].labelValueId ||
                JSON.stringify(record[fieldName])
              : record[fieldName];
          uniqueValues.add(value);
        }
      });
      return `${uniqueValues.size}`;
    },
    /** 求和 */
    SUM: () => {
      const recordList = selectedRecords
        .filter(record => record[fieldName])
        .map(record => formatNumberValueOf(record[fieldName]));

      const sumResult = recordList.reduce(
        (acc, curr) => acc.plus(curr),
        new BigNumber(0)
      );

      return formatNumberResult(
        sumResult,
        precision,
        widgetItemInfo.useThousandSeparator
      );
    },
    /** 平均值 */
    AVERAGE: () => {
      const recordList = selectedRecords
        .filter(record => record[fieldName])
        .map(record => formatNumberValueOf(record[fieldName]));

      const sumResult = recordList.reduce(
        (acc, curr) => acc.plus(curr),
        new BigNumber(0)
      );
      const average = sumResult.dividedBy(recordList.length);

      return formatNumberResult(
        average,
        precision,
        widgetItemInfo.useThousandSeparator
      );
    },
    /** 最大值 */
    MAX: () => {
      const recordList = selectedRecords
        .filter(record => record[fieldName])
        .map(record => record[fieldName]);
      const result = findMaxWithThousandSeparators(recordList);

      return formatNumberResult(
        new BigNumber(result),
        precision,
        widgetItemInfo.useThousandSeparator
      );
    },
    /** 最小值 */
    MIN: () => {
      const recordList = selectedRecords
        .filter(record => record[fieldName])
        .map(record => formatNumberValueOf(record[fieldName]));
      const result = BigNumber.min(...recordList);

      return formatNumberResult(
        result,
        precision,
        widgetItemInfo.useThousandSeparator
      );
    }
  };

  return statisticalHandlers[type]?.() || "";
}

// 辅助函数：格式化数字结果
function formatNumberResult(
  value: BigNumber,
  precision: number,
  useThousandSeparator: boolean
): string {
  // 检查输入值是否为有效数字
  if (!value || value.isNaN() || !value.isFinite()) {
    return "0";
  }

  const formatOptions = useThousandSeparator
    ? {
        groupSeparator: ",",
        decimalSeparator: ".",
        groupSize: 3
      }
    : {
        groupSeparator: "",
        decimalSeparator: "."
      };

  return value.toFormat(precision, formatOptions);
}

/**
 * fieldName 统计口径转换
 * @param fieldName 原始字段名
 * @param isLocalField 是否是本地数据
 * @returns 本地统计用字段名
 */
export function fieldNameTransformer(
  fieldName: string,
  isLocalField: boolean = false
): string {
  if (fieldName === "orderCreateInfo") {
    return isLocalField ? "creatorUsername" : "createMemberId";
  } else if (fieldName === "orderUpdateInfo") {
    return isLocalField ? "updaterUsername" : "updateMemberId";
  } else if (fieldName.indexOf("_managerMemberId") > -1) {
    return fieldName.replace("_managerMemberId", "_manager");
  } else if (fieldName.indexOf("_replyInfo") > -1) {
    return fieldName.replace("_replyInfo", "_replyMember");
  } else if (fieldName.indexOf("replyMember") > -1 && isLocalField) {
    return fieldName.replace("replyMember", "replyInfo");
  } else if (fieldName === "workOrderCreateInfo") {
    return "workOrderCreator";
  } else if (fieldName === "workOrderCollectInfo") {
    return "collector";
  } else if (fieldName === "workOrderReplyInfo") {
    return "workOrderReplyMemberId";
  } else if (fieldName === "milestoneManager") {
    return isLocalField ? "manager" : "managerMemberId";
  }
  return fieldName;
}
