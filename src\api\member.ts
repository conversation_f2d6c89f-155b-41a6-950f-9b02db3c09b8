import { http } from "@/utils/http";
import type { Result } from "./type";
import type { MemberTeamListProp } from "@/types/member/type";

/**
 * 获取sccs设置-计划管理-里程碑计划列表
 * @param data
 * @returns
 */
export const getMemberList = (params: MemberTeamListProp) => {
  return http.request<Result>("get", "/trade/team-member/page", {
    params
  });
};

/**
 * 邀请我的团队列表
 * @param data
 * @returns
 */
export const inviteMeList = () => {
  return http.request<Result>("get", "/trade/team-member/page");
};

/**
 * 获取团队所有成员
 * @param data
 * @returns
 */
export const getMemberAll = () => {
  return http.request<Result>("get", "/trade/team-member/list");
};

/**
 * 设置团员角色
 * @param data
 * @returns
 */
export const updateRoles = (data: {
  teamMemberId?: string | null;
  teamRoleIds?: string[] | null;
  username?: string;
}) => {
  return http.request<Result>("post", "/trade/team-member/update-roles", {
    data
  });
};

/**
 * 邀请用户
 * @param data
 * @returns
 */
export const inviteMember = (data: {
  email?: string | null;
  teamRoleIds?: string[] | null;
}) => {
  return http.request<Result>("post", "/trade/team-member/invite", {
    data
  });
};

/**
 * 加入团队
 * @param data
 * @returns
 */
export const joinMember = (params: { teamId: string }) => {
  return http.request<Result>("get", "/trade/team-member/join", {
    params
  });
};

/**
 * 删除团队成员
 * @param data
 * @returns
 */
export const deleteMember = (params: { id: string }) => {
  return http.request<Result>("delete", "/trade/team-member/delete", {
    params
  });
};

/**
 * 取当前团队成员所有的团队权限列表
 * @param data
 * @returns
 */
export const getCurPermKeyList = () => {
  return http.request<Result>(
    "get",
    "/trade/team-member/get-cur-perm-key-list"
  );
};
