<template>
  <LkDialog
    ref="LkDialogRef"
    class="lk-dialog-no-footer-container lk-maximum-dialog"
    :title="t('trade_common_history')"
    :show-closse="false"
    destroy-on-close
    :no-footer="true"
    style="height: 968px; padding-right: 0; padding-bottom: 0"
    @confirm="handleChooseConfirm"
    @close="handleClose"
  >
    <template #header>
      <div class="history-dialog-header">
        <div class="header-title">{{ t("trade_common_history") }}</div>
        <div class="header-right">
          <div class="tip-item">
            <span class="delete-style" />
            {{ t("trade_delete_content") }}
          </div>
          <div class="tip-item">
            <span class="add-style" />
            {{ t("trade_new_content") }}
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <div class="history-dialog-body">
        <div class="history-dialog-body-left">
          <el-scrollbar>
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in computedHistoryRecords"
                :key="index"
                placement="top"
                :class="[item.active ? 'active' : '']"
                :color="item.active ? '#0070d2' : ''"
                @click.stop="handleClick(item, index)"
              >
                <div class="timeline-box">
                  <div class="timeline-box-top">
                    <div class="timeline-box-top-left">
                      {{ $formatDate(item.opTime, "YYYY-MM-DD HH:mm") }}
                    </div>
                    <div class="timeline-box-top-right">
                      <LkAvater
                        :size="18"
                        :teamInfo="{
                          avatar: item.opUser.avatar,
                          username: item.opUser.username,
                          coop: item.opUser.coopTeamUser,
                          email: item.opUser.email
                        }"
                      />
                    </div>
                  </div>
                  <div class="timeline-box-bottom">
                    {{ item.operation }}
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </el-scrollbar>

          <span
            class="statistics-btn"
            :class="[statisticsState ? 'active' : '']"
            @click="handleStatisticsStateChange"
          >
            <i class="iconfont link-statistics" />
            <ReText
              :tippyProps="{ delay: 50000 }"
              class="milestone-timeline-title time-line-stamp-name"
            >
              {{ t("trade_statistics_fieldModificationTimes") }}
            </ReText>
          </span>
        </div>
        <div
          v-loading="loading"
          element-loading-text="Loading..."
          class="history-dialog-body-right"
        >
          <StatisticsCard
            v-show="statisticsState"
            :currentMainWorkOrderId="currentMainWorkOrderId"
          />
          <HistoryDetailCard
            v-show="!statisticsState"
            ref="HistoryDetailCardRef"
            :type="type"
            :widgetForm="widgetForm"
            :workOrderForm="workOrderForm"
            :widgetData="widgetFormData"
            :workOrderReplyData="workOrderReplyData"
            :lastHistoryWorkOrderReplyObj="lastHistoryWorkOrderReplyObj"
            :milestoneCard="milestoneCard"
            :milestoneData="milestoneDateData"
            :lastHistoryObj="lastHistoryObj"
          />
        </div>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";
import LkAvater from "@/components/lkAvatar/index";
import { useRoute } from "vue-router";
import { ReText } from "@/components/ReText";
import { getWorkOrderHistoryDetail } from "@/api/history";
import StatisticsCard from "./StatisticsCard.vue";
import HistoryDetailCard from "./HistoryDetailCard.vue";
import { cloneDeep } from "@pureadmin/utils";
import { TransformSubmitDataStructure } from "@/utils/formDesignerUtils.tsx";

const props = defineProps({
  type: {
    type: String as PropType<String>,
    default: () => ""
  },
  historyRecords: {
    type: Array as PropType<any>,
    default: () => []
  },
  currentMainWorkOrderId: {
    type: String as PropType<String>,
    default: () => ""
  },
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneCard: {
    type: Array as PropType<any>,
    default: () => []
  },
  workOrderForm: {
    type: Array as PropType<any>,
    default: () => []
  },
  currentVersion: {
    type: Number as PropType<any>,
    default: () => 0
  }
});

const { t } = useI18n();
const route = useRoute();
const LkDialogRef = ref<any>(null);
const statisticsState = ref<boolean>(false);
const computedHistoryRecords = ref<any>([]);
const widgetFormData = ref<any>([]);
const workOrderReplyData = ref<any>([]);
const currentOrderIndex = ref<number>(0);

const milestoneDateData = ref<any>([]);
const lastHistoryObj = ref<any>({});
const lastHistoryWorkOrderReplyObj = ref<any>({});
const HistoryDetailCardRef = ref<HTMLElement | any>(null);
const loading = ref<boolean>(false);
const open = (historyRecords, version) => {
  computedHistoryRecords.value = cloneDeep(historyRecords);
  computedHistoryRecords.value.forEach((item: any) => {
    item.active = false;
  });
  handleClick(
    computedHistoryRecords.value.find(item => item.version === version),
    computedHistoryRecords.value.findIndex(item => item.version === version)
  );
  LkDialogRef.value.open();
};

const handleClose = (): void => {
  statisticsState.value = false;
};

const handleStatisticsStateChange = (): void => {
  statisticsState.value = !statisticsState.value;
  computedHistoryRecords.value.forEach((item: any) => {
    item.active = false;
  });
  if (!statisticsState.value)
    computedHistoryRecords.value[currentOrderIndex.value].active = true;
};

const handleClick = (item: any, index: number): void => {
  if (item) {
    computedHistoryRecords.value.forEach((item: any) => {
      item.active = false;
    });
    item.active = true;
    currentOrderIndex.value = index;
    statisticsState.value = false;
    const currentId = item.id;
    const previousId =
      index != computedHistoryRecords.value.length - 1
        ? computedHistoryRecords.value[index + 1].id
        : "";
    getDetail(currentId, previousId);
  }
};

const getDetail = (currentId: string, previousId: string) => {
  loading.value = true;
  getWorkOrderHistoryDetail({
    id: currentId,
    lastHistoryId: previousId
  }).then((res: any) => {
    if (res.code === 0) {
      const {
        widgetList,
        linkedReferenceMap,
        milestoneList,
        lastHistoryData,
        workOrderReplyWidgetList,
        workOrderReplySubWidgetMap
      } = res.data;
      let subWidgetMap = [];
      widgetFormData.value = TransformSubmitDataStructure(
        widgetList,
        subWidgetMap,
        linkedReferenceMap
      );
      workOrderReplyData.value = TransformSubmitDataStructure(
        workOrderReplyWidgetList,
        workOrderReplySubWidgetMap,
        linkedReferenceMap
      );
      milestoneDateData.value = milestoneList;
      if (lastHistoryData) {
        lastHistoryObj.value = TransformSubmitDataStructure(
          lastHistoryData.widgetList,
          lastHistoryData.subWidgetMap,
          lastHistoryData.linkedReferenceMap
        );
        lastHistoryWorkOrderReplyObj.value = TransformSubmitDataStructure(
          lastHistoryData.workOrderReplyWidgetList,
          lastHistoryData.workOrderReplySubWidgetMap,
          lastHistoryData.linkedReferenceMap
        );
      }
    }
    loading.value = false;
  });
};
const emit = defineEmits<{
  (e: "handleChooseConfirm", data, type): void;
}>();

const handleChooseConfirm = (): void => {
  LkDialogRef.value.close();
  handleClose();
};

defineExpose({
  open
});
</script>
<style lang="scss">
.lk-dialog-no-footer-container {
  padding-bottom: 0;

  .el-dialog__header.show-close {
    padding-right: calc(
      var(--el-dialog-padding-primary) + var(--el-message-close-size, 16px)
    ) !important;
    padding-bottom: var(--el-dialog-padding-primary) !important;
    border-bottom: none !important;
  }

  .el-dialog__footer {
    display: none;
  }

  .el-dialog__body {
    height: calc(100% - 36px) !important;
    background: none !important;
  }

  .history-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 6px;

    .header-title {
      font-size: 18px;
      font-weight: 500;
      line-height: 20px;
      color: #202020;
    }

    .header-right {
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: normal;
      color: #595959;

      .tip-item {
        display: flex;
        align-items: center;
        margin-right: 36px;

        span {
          width: 24px;
          height: 14px;
          margin-right: 4px;
          border-radius: 4px;

          &.delete-style {
            background: #ffebeb;
            border: 1px solid #f56c6c;
          }

          &.add-style {
            background: #f1ffeb;
            border: 1px solid #67c23a;
          }
        }
      }
    }
  }

  .history-dialog-body {
    display: flex;
    width: 100%;
    height: 100%;

    .history-dialog-body-left {
      position: relative;
      width: 210px;
      background: #fff;
      border-radius: 0 0 0 8px;

      .el-scrollbar {
        height: calc(100% - 96px);
      }

      .el-timeline {
        margin-left: 6px;
      }

      .el-timeline-item {
        padding-left: 20px;

        .el-timeline-item__tail {
          top: 8px;
        }

        .el-timeline-item__node {
          top: 8px;
        }

        .el-timeline-item__wrapper {
          position: relative;
          top: 0;
          width: 165px;
          padding: 8px;
          font-weight: normal;
          cursor: pointer;
          border-radius: 4px;

          .el-timeline-item__timestamp.is-top {
            padding: 0;
            margin: 0;
          }
          // .el-timeline-item__timestamp {
          //   font-size: 12px;
          //   line-height: 17px;
          //   color: #8c8c8c;
          // }

          .el-timeline-item__content {
            display: flex;
            line-height: 20px;
            color: #262626;

            .timeline-box {
              display: flex;
              flex-direction: column;
              width: 100%;

              .timeline-box-top {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;

                .timeline-box-top-left {
                  font-size: 12px;
                }
              }
            }

            .time-line-stamp-info {
              display: flex;
              flex-wrap: wrap;
              margin-left: 4px;

              .el-text.is-truncated {
                max-width: 120px;
              }
            }

            .time-line-stamp-name {
              margin-right: 4px;
              font-size: 14px;
              color: #262626;
            }
          }

          &.active {
            background: #eef8ff;
          }
        }

        .el-timeline-item__wrapper:hover {
          background: #f2f2f2;
        }

        &.active {
          .el-timeline-item__wrapper {
            color: #0070d2;
            background: #eef8ff;

            .el-timeline-item__timestamp {
              font-size: 12px;
              line-height: 17px;
              color: #0070d2;
            }

            .el-timeline-item__content {
              color: #0070d2;
            }

            .time-line-stamp-name {
              color: #0070d2;
            }
          }
        }
      }

      .statistics-btn {
        position: absolute;
        bottom: 32px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 156px;
        height: 32px;
        padding: 0 8px;
        margin-left: 20px;
        font-size: 14px;
        line-height: 20px;
        color: #595959;
        cursor: pointer;
        background: #f2f2f2;
        border-radius: 4px;

        .link-statistics {
          margin-right: 4px;
          font-size: 14px;
        }

        &.active {
          color: #0070d2;
          background: #eef8ff;

          .el-text.is-truncated {
            color: #0070d2 !important;
          }
        }
      }
    }

    .history-dialog-body-right {
      width: calc(100% - 210px);
      height: 100%;
      padding: 16px 20px;
      background: #f6f6f6;
      border-radius: 0 0 8px;
    }
  }
}
</style>
