<template>
  <div class="dc-body">
    <el-row class="dc-title"
      ><el-tooltip
        v-if="widgetForm.info"
        :content="widgetForm.info"
        placement="top"
        :show-after="500"
        popper-class="widget-popper-label-class"
      >
        <i class="iconfont link-explain font12" style="margin-right: 5px" />
      </el-tooltip>
      {{ widgetForm.title }}</el-row
    >
    <el-row v-if="lastVal">
      <span
        class="dc-lastVal"
        :class="judgeValueReturnStatus(currentVal, lastVal)"
      >
        <span
          v-show="
            ['delete', 'edit'].includes(
              judgeValueReturnStatus(currentVal, lastVal)
            )
          "
          class="delete-line"
        />
        <span
          v-if="
            widgetForm.type === 'DrInputNumber' &&
            widgetForm.props &&
            widgetForm.props.unitPosition === 'before'
          "
          class="widget-config-span-unit"
        >
          {{ widgetForm.props.unit }}
        </span>
        {{ lastVal }}
        <span
          v-if="
            widgetForm.type === 'DrInputNumber' &&
            widgetForm.props &&
            widgetForm.props.unitPosition === 'after'
          "
          class="widget-config-span-unit"
        >
          {{ widgetForm.props.unit }}
        </span>
        <span
          v-if="widgetForm.type === 'DrPercentage' && lastVal"
          class="widget-config-span-unit"
        >
          %
        </span>
      </span>
    </el-row>
    <el-row>
      <span
        v-if="judgeValueReturnStatus(currentVal, lastVal) !== 'nochange'"
        class="dc-currentVal"
        :class="judgeValueReturnStatus(currentVal, lastVal)"
      >
        <span
          v-if="
            widgetForm.type === 'DrInputNumber' &&
            widgetForm.props &&
            widgetForm.props.unitPosition === 'before'
          "
          class="widget-config-span-unit"
        >
          {{ widgetForm.props.unit }}
        </span>
        {{ currentVal }}
        <span
          v-if="
            widgetForm.type === 'DrInputNumber' &&
            widgetForm.props &&
            widgetForm.props.unitPosition === 'after'
          "
          class="widget-config-span-unit"
        >
          {{ widgetForm.props.unit }}
        </span>
        <span
          v-if="widgetForm.type === 'DrPercentage' && currentVal"
          class="widget-config-span-unit"
        >
          %
        </span>
      </span>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { useI18n } from "vue-i18n";
const props = defineProps({
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  lastHistoryObj: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();

const lastVal = computed(() => {
  if (props.lastHistoryObj instanceof Array) {
    const widgetData = props.lastHistoryObj.find(
      widgetRow => widgetRow.widgetId === props.widgetForm._fc_id
    );
    return widgetData ? widgetData.label : "";
  } else {
    return "";
  }
});

const currentVal = computed(() => {
  if (props.widgetData instanceof Array) {
    const widgetData = props.widgetData.find(
      widgetRow => widgetRow.widgetId === props.widgetForm._fc_id
    );
    return widgetData ? widgetData.label : "";
  } else {
    return "";
  }
});

const judgeValueReturnStatus = (
  currentValue?: string,
  lastValue?: string
): string => {
  let status = "";
  if (currentValue === lastValue || (!currentValue && !lastValue)) {
    status = "nochange";
  } else if (
    (lastValue === "" || lastValue === null || lastValue === undefined) &&
    currentValue !== "" &&
    currentValue !== null &&
    currentValue !== undefined
  ) {
    status = "add";
  } else if (
    (lastValue !== "" || lastValue !== null || lastValue !== undefined) &&
    (currentValue === null || currentValue === undefined || currentValue === "")
  ) {
    status = "delete";
  } else if (
    lastValue !== null &&
    currentValue !== null &&
    lastValue !== currentValue
  ) {
    status = "edit";
  }
  return status;
};
</script>
<style lang="scss" scoped>
@use "../../style.scss";
</style>
