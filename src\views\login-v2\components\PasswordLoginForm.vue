<template>
  <div class="login-form-container">
    <el-form
      ref="passwordFormRef"
      :model="loginForm"
      :rules="loginRules"
      label-position="top"
      size="large"
    >
      <!-- 用户名输入 -->
      <div class="login-form-item">
        <el-form-item :label="t('trade_login_email')" prop="username">
          <el-input
            v-model="loginForm.username"
            :placeholder="t('trade_login_mailText')"
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
      </div>

      <!-- 密码输入 -->
      <div class="login-form-item">
        <el-form-item :label="t('trade_common_password')" prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            :placeholder="t('trade_login_passwordPlaceholder')"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>
      </div>

      <!-- 错误信息显示 -->
      <UserError
        :respMessage="respMessage"
        :formData="loginForm"
        style="margin-top: -4px; margin-bottom: -40px"
      />

      <!-- 用户服务协议 -->
      <UserAgreement />

      <!-- 登录按钮 -->
      <el-button
        type="primary"
        class="login-button"
        :loading="loginLoading"
        @click="handleLogin"
      >
        {{ t("trade_common_login") }}
      </el-button>

      <!-- 记住我和忘记密码 -->
      <div class="login-form-options">
        <div class="remember-me">
          <el-checkbox
            v-model="rememberMe"
            :label="t('trade_common_rememberMe')"
          />
        </div>
        <div class="forgot-password">
          <el-button
            type="primary"
            link
            @click="() => router.push('/resetPassword')"
          >
            {{ t("trade_common_forgetPassword") }}?
          </el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { isEmail, storageLocal } from "@pureadmin/utils";
import type { FormInstance, FormRules } from "element-plus";
import { loginApi } from "@/api/login";
import type { LoginFormState } from "@/api/type.d";
import UAParser from "ua-parser-js";
import UserAgreement from "./UserAgreement.vue";
import UserError from "./UserError.vue";
import { cacheUsername, getCacheUsername } from "../utils";

const { t } = useI18n();
const router = useRouter();
const route = useRoute();

// 表单引用
const passwordFormRef = ref<FormInstance>();

// 登录状态
const loginLoading = ref<boolean>(false);
const rememberMe = ref<boolean>(false);

// 登录表单数据
const respMessage = ref<string>("");
const loginForm = reactive<LoginFormState>({
  username: getCacheUsername() || "",
  password: "",
  userOs: "",
  userAgent: "",
  userDevice: ""
});

// 表单验证规则
const loginRules = computed<FormRules<LoginFormState>>(() => ({
  username: [
    {
      required: true,
      message: t("trade_login_mailText"),
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        if (!isEmail(value)) {
          callback(new Error(t("trade_login_truemail")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: t("trade_login_passwordPlaceholder"),
      trigger: "blur"
    }
  ]
}));

// 事件定义
const emit = defineEmits<{
  handleLoginSuccess: [data: any];
}>();

// 处理记住我
const handleRememberMe = () => {
  if (rememberMe.value) {
    const base64Str = window.btoa(
      JSON.stringify({
        username: loginForm.username,
        password: loginForm.password
      })
    );
    storageLocal().setItem("tradeUserInfo", base64Str);
  } else {
    storageLocal().removeItem("tradeUserInfo");
  }
};

// 处理登录
const handleLogin = async () => {
  if (!passwordFormRef.value) return;

  try {
    await passwordFormRef.value.validate();
  } catch (errorFields) {
    return;
  }

  loginLoading.value = true;
  respMessage.value = "";

  try {
    // 获取设备信息
    const parser = new UAParser();
    loginForm.userDevice = parser.getDevice().model || "unknown";
    loginForm.userOs = `${parser.getOS().name} ${parser.getOS().version}`;
    loginForm.userAgent = parser.getUA();

    // 调用登录接口
    const response = await loginApi(loginForm);

    if (response.code !== 0) {
      respMessage.value = response.node || response.msg;
    } else {
      // 处理记住我
      handleRememberMe();
      emit("handleLoginSuccess", response.data);
    }
  } catch (error: any) {
    console.error("登录失败:", error);
    const errorMsg = error?.response?.data?.msg || "登录失败";
    respMessage.value = errorMsg;
    ElMessage.error(errorMsg);
  } finally {
    loginLoading.value = false;
  }
};

const refreshUsername = () => {
  const savedUsername = sessionStorage.getItem("username");
  loginForm.username = savedUsername;
};

watch(
  () => loginForm.username,
  newVal => {
    cacheUsername(newVal);
  }
);

// 监听表单变化，清除错误信息
watch(
  () => loginForm,
  () => {
    respMessage.value = "";
  },
  { deep: true }
);

watch(
  () => loginForm,
  () => {
    respMessage.value = "";
    if (rememberMe.value) {
      const { username, password } = loginForm;
      const base64Str = window.btoa(
        JSON.stringify({ username: username, password: password })
      );
      storageLocal().setItem("tradeUserInfo", base64Str);
    } else {
      storageLocal().removeItem("tradeUserInfo");
    }
  },
  { deep: true }
);

onMounted(() => {
  const email = route.query.email;
  const tradeUserInfo: string = storageLocal().getItem("tradeUserInfo");
  if (tradeUserInfo && !email) {
    const { username, password } = JSON.parse(window.atob(tradeUserInfo));
    loginForm.username = username;
    loginForm.password = password;
    rememberMe.value = true;
  }
  if (email) {
    loginForm.username = email;
  }
});

// 暴露表单数据给父组件
defineExpose({
  loginForm,
  refreshUsername
});
</script>

<style lang="scss" scoped>
@use "./index.scss";
</style>
