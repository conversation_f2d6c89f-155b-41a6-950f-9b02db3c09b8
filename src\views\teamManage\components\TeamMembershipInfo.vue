<template>
  <div
    v-loading="loading"
    class="team-member-info-container team-module-area-container"
    :class="[
      !tablePermission ? 'team-module-area-permission-empty-container' : ''
    ]"
  >
    <div v-show="tablePermission" class="team-module-area-header">
      <div class="team-module-area-title">
        {{ t("trade_team_membersManage") }}
      </div>
      <el-button type="primary" color="#0070D2" @click="handleAdd">
        <FontIcon icon="link-add" />
        {{ t("trade_common_invitations") }}
      </el-button>
    </div>
    <div v-show="tablePermission" class="team-module-area-body">
      <el-form
        ref="FormRef"
        label-position="top"
        label-width="auto"
        :model="form"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="`${t('trade_team_inputSearch')}：`">
              <el-input
                v-model="form.keyword"
                clearable
                :placeholder="t('trade_common_searchAccount')"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="`${t('trade_team_teamRole')}：`">
              <el-select-v2
                v-model="form.teamRoleId"
                filterable
                :options="teamRoleList"
                clearable
                :placeholder="t('trade_common_selectText')"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="`${t('trade_common_participatingSCCS')}：`">
              <el-select-v2
                v-model="form.sccsId"
                filterable
                :options="sccsList"
                clearable
                :placeholder="t('trade_common_selectText')"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item style="margin-top: 30px">
              <el-button @click="resetForm">
                {{ t("trade_common_reSet") }}
              </el-button>
              <el-button type="primary" color="#0070D2" @click="handleSearch">
                {{ t("trade_common_query") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        :border="true"
        stripe
        max-height="calc(100vh - 280px)"
        :tooltip-options="{ showAfter: 500 }"
        :empty-text="loading ? '' : t('trade_common_emptyTip')"
      >
        <el-table-column
          :label="t('trade_common_name')"
          width="255"
          prop="username"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="!scope.row.activate" class="el-table-register-tip">
              {{ t("trade_common_unregistered") }}
            </span>
            {{ scope.row.username }}
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_login_email')"
          prop="email"
          width="230"
          show-overflow-tooltip
        />
        <el-table-column
          :label="t('trade_team_teamRole')"
          prop="teamRoleList"
          class-name="el-table-sccs-setting-cell2"
        >
          <template #default="scope">
            <el-tag
              v-for="(item, index) in scope.row.teamRoleList"
              :key="index"
              :class="[
                'tag-col',
                item.manager && item.owner
                  ? 'tag-owner'
                  : item.manager
                    ? 'tag-manager'
                    : ''
              ]"
            >
              <ReText
                type="info"
                class="font12"
                :tippyProps="{ popperOptions: { 'hide-after': 500 } }"
              >
                {{ item.roleName }}
              </ReText>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_common_participatingSCCS')"
          prop="sccsList"
        >
          <template #default="{ row }">
            <!-- <el-popover
              v-for="(item, index) in scope.row.sccsList"
              :key="index"
              placement="top"
              trigger="hover"
              :show-after="500"
              popper-style="min-width: auto;width: auto"
            >
              <template #reference>
                <el-tag
                  :class="[
                    'tag-col',
                    item.manager && item.owner
                      ? 'tag-owner'
                      : item.manager
                        ? 'tag-manager'
                        : ''
                  ]"
                >
                  <span v-if="item.coopTeamMark" class="tag-span-icon">
                    <svg aria-hidden="true" class="svg-icon-coop">
                      <use xlink:href="#link-coop" />
                    </svg>
                  </span>
                  {{ item.sccsName }}
                </el-tag>
              </template>
              <template #default>
                <div style="font-size: 12px; line-height: 17px; color: #262626">
                  {{ item.sccsName }}
                </div>
                <div v-if="item.coopTeamMark">
                  <LkAvatar
                    :size="32"
                    shape="square"
                    fit="cover"
                    :teamInfo="{
                      avatar: item.createTeamAvatar,
                      username: item.createTeamName
                    }"
                  />
                  {{ item.createTeamName }}
                  ({{ item.createTeamShortName }})
                </div>
              </template>
            </el-popover> -->
            <div class="tag-row">
              <div
                v-for="(item, index) in row.sccsList"
                :key="index"
                class="tag-row-item"
              >
                <el-tooltip
                  :content="item.sccsName"
                  :show-after="500"
                  placement="top"
                >
                  <el-tag
                    :class="[
                      'tag-col',
                      item.manager && item.owner
                        ? 'tag-owner'
                        : item.manager
                          ? 'tag-manager'
                          : ''
                    ]"
                    ><span v-if="item.coopTeamMark" class="tag-span-icon">
                      <svg aria-hidden="true" class="svg-icon-coop">
                        <use xlink:href="#link-coop" />
                      </svg>
                    </span>
                    {{ item.sccsName }}</el-tag
                  >
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="70" align="center">
          <template #default="{ row }">
            <LkTableOperate
              v-if="row.owner === 0 && (!row.owner || !row.manager)"
              :tableOpeateLists="
                !row.activate
                  ? [
                      {
                        icon: 'link-invite-members',
                        title: 'trade_team_reinvite',
                        slotName: 'invite'
                      },
                      ...tableOpeateLists
                    ]
                  : tableOpeateLists
              "
              :messageBoxConfirmObject="messageBoxConfirmObject"
              :row="row"
              @handleOperate="handleOperate"
            />
          </template>
        </el-table-column>
      </el-table>
      <LkPagination
        ref="LkPaginationRef"
        :total="total"
        @updatePagination="handleSearch"
      />
      <InviteMembersDialog
        ref="InviteMembersDialogRef"
        @createSuccess="createSuccess"
      />
      <LkDialog
        ref="LkDialogRef"
        :title="t('trade_team_setTeamRole')"
        @confirm="LkDialogComfirm"
      >
        <template #default>
          <el-form label-position="right" label-width="80px">
            <el-form-item :label="t('trade_common_name')">
              <el-text>{{ userInfo.username }}</el-text>
            </el-form-item>
            <el-form-item :label="t('trade_team_teamRole')">
              <el-select-v2
                v-model="userInfo.teamRoleIds"
                filterable
                :options="teamRoleNotOwner"
                clearable
                :placeholder="t('trade_common_selectText')"
                multiple
              />
            </el-form-item>
          </el-form>
        </template>
      </LkDialog>
    </div>
    <LkPermissionEmpty v-show="!tablePermission" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import LkPagination from "@/components/lkPagination/index";
import LkDialog from "@/components/lkDialog/index";
import InviteMembersDialog from "@/views/createTeam/component/inviteMembersDialog.vue";
import LkPermissionEmpty from "@/components/lkPermissionEmpty/index";
import { message } from "@/utils/message";
import { ElMessage } from "element-plus";
import LkTableOperate from "@/components/lkTableOperate";
import { ReText } from "@/components/ReText";
import { deleteMember, updateRoles } from "@/api/member";
import {
  getUserTeamRoleList,
  getTeamMemberPage,
  inviteMember,
  getSccsList
} from "@/api/team";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";

const { t } = useI18n();
const InviteMembersDialogRef = ref<any>(null);
const form = reactive({
  teamRoleId: "",
  keyword: "",
  sccsId: ""
});
const loading = ref<boolean>(true);
let tableData = ref<any[]>([]);
let sccsList = ref<any[]>([]);
let teamRoleList = ref<any[]>([]);
const LkPaginationRef = ref<any>(null);
const tablePermission = ref<boolean>(true);
const LkDialogRef = ref<any>(null);
let total = ref<number>(0);
const userInfo = ref<any>({});
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_common_deleteMemberListTip",
  messageBoxTitle: "trade_team_deleteMembers",
  messageBoxTipArray: ["username"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-setting",
    title: "trade_team_setTeamRole",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);

const teamRoleNotOwner = computed(() => {
  return teamRoleList.value.filter(item => item.label !== "拥有者");
});

const init = (): void => {
  Promise.all([
    getSccsList({ sccsReqEnum: "TEAM_MEMBER_MANAGE" }),
    getUserTeamRoleList({ withOwner: true })
  ]).then(res => {
    sccsList.value = res[0].data;
    teamRoleList.value = res[1].data;
    handleSearch();
  });
};

const resetForm = () => {
  form.teamRoleId = "";
  form.keyword = "";
  form.sccsId = "";
  handleSearch();
};

const handleSearch = async (): Promise<void> => {
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getTeamMemberPage({ ...form, ...pageParams });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
    tablePermission.value = true;
  } else if (res.code === 403) {
    tablePermission.value = false;
  }
  loading.value = false;
};

const createSuccess = (): void => {
  handleSearch();
};

const handleAdd = (): void => {
  InviteMembersDialogRef.value.open();
};

const LkDialogComfirm = async (): Promise<void> => {
  const res = await updateRoles({
    teamRoleIds: userInfo.value.teamRoleIds,
    username: userInfo.value.username,
    teamMemberId: userInfo.value.teamMemberId
  });
  if (res.code === 0) {
    message(t("trade_common_dealSuccess"), {
      customClass: "el",
      type: "success"
    });
    LkDialogRef.value.close();
    handleSearch();
  }
};

const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "invite") {
    handleInviteMembers(row);
  } else if (slotName === "edit") {
    handleSettingRole(row);
  } else {
    handleDelete(row.id);
  }
};

const handleInviteMembers = async (row: any): Promise<void> => {
  let teamRoleIds: string[] = [];
  row.teamRoleList.map(teamRole => teamRoleIds.push(teamRole.id));
  const res = await inviteMember([
    { email: row.email, teamRoleIds: teamRoleIds }
  ]);
  if (res.code === 0) {
    message(t("trade_team_invitationSuccessful2"), {
      customClass: "el",
      type: "success"
    });
  }
};

const handleDelete = async (id: string): Promise<void> => {
  //@ts-ignore
  const { code, node, extend } = await deleteMember({ id: id });
  if (code === 0) {
    message(t("trade_common_deleteSuccess"), {
      customClass: "el",
      type: "success"
    });
    handleSearch();
  } else if (code === 503) {
    ElMessage({
      dangerouslyUseHTMLString: true,
      message: `${t(node)}<br/>${extend.sccsNames}`,
      type: "error"
    });
  }
};

const handleSettingRole = (row: any) => {
  let teamRoleIds: string[] = [];
  if (row.teamRoleList) {
    row.teamRoleList.map(role => teamRoleIds.push(role.id));
  } else {
    teamRoleIds = [];
  }

  userInfo.value.teamRoleIds = teamRoleIds;
  userInfo.value.username = row.email;
  userInfo.value.teamMemberId = row.id;
  LkDialogRef.value.open();
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

::v-deep(.el-table) {
  .el-table__body .el-table__row {
    height: 60px !important;
  }
}
</style>
