<template>
  <el-dialog
    v-model="dialogVisible"
    class="lk-maximum-dialog"
    :title="FilePreviewDialogTitle"
    align-center
  >
    <div>
      <Editor v-model="valueHtml" :defaultConfig="{ readOnly: true }" />
    </div>
    <template #footer />
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { ElDialog } from "element-plus";
import { Editor } from "@wangeditor/editor-for-vue";

const valueHtml = ref<string>("");
const FilePreviewDialogTitle = ref<string>("");
const dialogVisible = ref<boolean>(false);

const props = defineProps({
  visible: <PERSON><PERSON><PERSON>,
  editorValueHtml: { type: String, default: "" }
});

watch(
  () => props,
  () => {
    dialogVisible.value = props.visible;
    valueHtml.value = props.editorValueHtml;
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.file-preview-box-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 3300;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0 / 50%);
}
</style>
