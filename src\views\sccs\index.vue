<template>
  <div class="sccs-body">
    <div class="sccs-body-header">
      <div class="sccs-body-header-radio">
        <div class="sccs-body-header-radio-bg">
          <el-radio-group
            v-model="currentRadio"
            size="large"
            @change="handleChangeRadio"
          >
            <el-radio-button
              v-for="radioItem in radioList"
              :key="radioItem.value"
              :label="t(radioItem.label)"
              :value="radioItem.value"
            />
          </el-radio-group>
        </div>
      </div>
      <div
        v-show="currentRadio === 'sccs_List'"
        class="linkIncrease-work-tabs-flex"
      >
        <ReText type="info" :tippyProps="{ delay: 50000 }" class="radio-span">
          <span
            :class="['tab-item', !sccsGroupId ? 'is-active' : '']"
            @click="handleSelectTab('')"
            >{{ t("trade_home_allSccs") }}</span
          >
        </ReText>
        <ReText
          v-if="tabName"
          type="info"
          :tippyProps="{ delay: 50000 }"
          class="radio-span"
        >
          <span
            v-if="tabName"
            :class="['tab-item', sccsGroupId ? 'is-active' : '']"
            @click="handleSelectTab(tabSccsGroupId)"
          >
            {{ tabName }}
          </span>
        </ReText>
      </div>
      <div
        class="linkIncrease-work-manage"
        :class="[currentRadio !== 'sccs_List' ? 'reverse' : '']"
      >
        <div
          v-show="currentRadio === 'sccs_List'"
          ref="sccsGroupPopoverRef"
          class="linkIncrease-work-btn-hover"
        >
          <el-popover
            :visible="isPopoverVisible"
            placement="bottom-start"
            :width="230"
            trigger="click"
            :popper-style="{ padding: 0 }"
          >
            <template #reference>
              <div v-perms="'sccs_group'" @click="isPopoverVisible = true">
                <span class="sccs-popover-span">
                  {{ t("trade_common_more") }}
                </span>
                <i class="iconfont sccs-popover-icon link-arrow-down" />
              </div>
            </template>

            <div class="sccs-popover-container">
              <div class="sccs-popover-top">
                <div class="sccs-popover-title">
                  {{ t("trade_home_groupList") }}
                </div>
                <el-scrollbar
                  v-if="sccsGroup && sccsGroup.length > 0"
                  max-height="230px"
                >
                  <div
                    v-for="item in sccsGroup"
                    :key="item.id"
                    class="sccs-group-name"
                    :class="[item?.groupName === tabName ? 'is-active' : '']"
                    @click="handleSelectSccsGroup(item)"
                  >
                    {{ item.groupName }}
                    <i class="iconfont link-tick" />
                  </div>
                </el-scrollbar>
                <el-empty
                  v-else
                  :description="t('trade_home_sccsGroupEmpty')"
                  :image-size="114"
                  :image="sccsGroupNoImage"
                />
              </div>
              <div class="sccs-popover-bottom" @click="handleOpenSccsGroup">
                <i class="iconfont link-group-settings" />
                {{ t("trade_home_groupSet") }}
              </div>
            </div>
          </el-popover>
        </div>
        <div class="linkIncrease-work-sccs-group">
          <el-checkbox
            v-show="!sccsGroupId && currentRadio === 'sccs_List'"
            v-model="checked"
            :label="t('trade_common_group')"
            size="large"
          />
          <el-input
            v-model="input"
            style="width: 210px"
            :placeholder="
              currentRadio === 'sccs_List'
                ? t('trade_home_sccsSearchTip')
                : t('trade_common_view_search_placeholder')
            "
            :prefix-icon="Search"
            clearable
            class="linkIncrease-work-sccs-input"
          />
          <el-button
            v-show="currentRadio === 'sccs_List'"
            v-perms="'sccs_create'"
            type="primary"
            color="#0070D2"
            @click="handleAddTemplate"
          >
            <i class="iconfont link-add font12 mr-1" />
            {{ t("trade_home_createSccs") }}
          </el-button>
        </div>
      </div>
    </div>
    <div v-loading="loading" class="sccs-body-container">
      <el-scrollbar ref="scrollbarRef" max-height="100%">
        <SccsManage
          v-if="currentRadio === 'sccs_List'"
          ref="SccsManageRef"
          :sccsManage="sccsManageList"
          :isGroup="checked"
          @updateSccsTop="getSccsList"
          @handleSccsClick="handleSccsClick"
        />
        <SccsCommonViews
          v-else
          :commonViews="commonOrderViewList"
          @handleViewClick="handleViewClick"
          @changeCollect="getOrderViewList"
        />
      </el-scrollbar>
    </div>
    <SccsGroupSettingDialog
      ref="SccsGroupSettingRef"
      @handleClose="handleClose"
    />
    <SccsTemplateDialog
      ref="SccsTemplateDialogRef"
      @createSccsSuccess="createSccsSuccess"
    />
    <SccsManageDrawer ref="SccsManageRef" />
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { onClickOutside } from "@vueuse/core";
import { Search } from "@element-plus/icons-vue";
import SccsManage from "@/views/sccs/components/SccsManage.vue";
import SccsCommonViews from "@/views/sccs/components/SccsCommonViews.vue";
import SccsGroupSettingDialog from "@/views/sccs/components/SccsGroupSettingDialog.vue";
import SccsTemplateDialog from "@/views/sccs/components/SccsTemplateDialog.vue";
import SccsManageDrawer from "@/views/sccsManage/index.vue";
import sccsGroupNoImage from "@/assets/images/sccs/sccs-no-image.png";
import { ReText } from "@/components/ReText";
import { storageLocal, storageSession } from "@pureadmin/utils";
import {
  getSccsListByUser,
  getSccsGroupList,
  getCommonOrderViewList
} from "@/api/sccs";

const props = defineProps({
  hideTeamCard: {
    type: Boolean,
    default: false
  }
});

interface SccsGroupProp {
  groupName: string;
  id: string;
  sort: number;
  sccsIdList: string[];
}

const { t } = useI18n();
const router = useRouter();
const checked = ref<boolean>(true);
const sccsGroupPopoverRef = ref<any | HTMLElement>(null);
const input = ref<string>("");
const sccsGroup = ref<SccsGroupProp[] | any>();
const sccsManageList = ref([]);
const commonOrderViewList = ref<[] | any>();
const loading = ref<boolean>(false);
const tabName = ref<string>("");
const sccsGroupId = ref<string>("");
const tabSccsGroupId = ref<string>("");
const isPopoverVisible = ref<boolean>(false);
const SccsGroupSettingRef = ref<any>(null);
const SccsTemplateDialogRef = ref<any>(null);
const SccsManageRef = ref<any>(null);
const currentRadio = ref<string>("sccs_List");
const scrollbarRef = ref<any>(null);

const radioList = [
  {
    label: "trade_sccs_List",
    value: "sccs_List"
  },
  {
    label: "trade_common_views",
    value: "common_views"
  }
];

interface SccsHandleProp {
  id: string;
  groupId: string;
  templateId: string;
  sccsName: string;
  coopTeamMark: boolean;
}

watch([checked, input], () => {
  if (currentRadio.value === "sccs_List") {
    getSccsList();
  } else {
    getOrderViewList();
  }
});

onClickOutside(sccsGroupPopoverRef, (event: any) => {
  isPopoverVisible.value = false;
});

const handleAddTemplate = (): void => {
  SccsTemplateDialogRef.value.open();
};

const handleChangeRadio = async () => {
  storageLocal().setItem("userInfoSelectedView", currentRadio.value);
  initViewData();
};

const initViewData = () => {
  if (currentRadio.value === "common_views") {
    getOrderViewList();
  } else {
    getSccsList();
  }
  if (scrollbarRef.value) {
    scrollbarRef.value.scrollTo({ top: 0, behavior: "smooth" });
  }
};

const createSccsSuccess = (): void => {
  Promise.all([getSccsList(), getSccsGroup()]);
};

const getOrderViewList = async (): Promise<void> => {
  const { code, data } = await getCommonOrderViewList({
    keyword: input.value
  });
  if (code === 0) {
    commonOrderViewList.value = data;
    loading.value = false;
  }
};

const getSccsList = async (): Promise<void> => {
  loading.value = true;
  const { code, data } = await getSccsListByUser({
    sccsSearch: input.value,
    sccsGroupId: sccsGroupId.value,
    isGroup: checked.value
  });
  if (code === 0) {
    sccsManageList.value = data;
    loading.value = false;
  }
};

const getSccsGroup = async (): Promise<void> => {
  const { code, data } = await getSccsGroupList({
    isContainSccsInfo: false,
    isEmpty: true
  });
  if (code === 0) {
    sccsGroup.value = data;
  }
};

const handleSelectTab = (id: string): void => {
  sccsGroupId.value = id;
  getSccsList();
};

const handleViewClick = (data: any): void => {
  const { viewId, sccsId, templateId, sccsName, coopTeamMark, viewType } = data;
  const parameter = {
    sccsId: sccsId,
    templateId: templateId,
    coopTeamMark: coopTeamMark ? 1 : 2,
    sccsName: sccsName,
    viewId,
    viewType
  };
  router.push({ name: "orderManage", query: parameter });
};

const handleSccsClick = (data: SccsHandleProp): void => {
  const { id, templateId, sccsName, coopTeamMark } = data;
  const parameter = {
    sccsId: id,
    templateId: templateId,
    coopTeamMark: coopTeamMark ? 1 : 2,
    sccsName: sccsName
  };
  storageSession().setItem(
    "coopTeamMark",
    !coopTeamMark ? "mainTeam" : "coopTeam"
  );
  router.push({ name: "orderManage", query: parameter });
};

const handleSelectSccsGroup = (data: any): void => {
  sccsGroupId.value = data.id;
  tabSccsGroupId.value = data.id;
  tabName.value = data.groupName;
  isPopoverVisible.value = false;
  getSccsList();
};

const handleOpenSccsGroup = (): void => {
  isPopoverVisible.value = false;
  SccsGroupSettingRef.value.open();
};

const handleClose = (): void => {
  Promise.all([getSccsList(), getSccsGroup()]);
};

onMounted(() => {
  getSccsGroup();
  const userInfoView =
    (storageLocal().getItem("userInfoSelectedView") as string) || "sccs_List";
  currentRadio.value = userInfoView;
  initViewData();
});
</script>
<style lang="scss" scoped>
.sccs-body {
  height: 100%;

  .sccs-body-header {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0 16px;
    border-bottom: 1px solid rgb(151 151 151 / 19%);

    .sccs-body-header-radio {
      padding: 5px;
      margin-right: 16px;
      background: #f5f5f5;
      border-radius: 4px;

      ::v-deep(.el-radio-button) {
        --el-radio-button-checked-bg-color: none;
        --el-radio-button-checked-text-color: none;
        --el-radio-button-checked-border-color: none;
        --el-radio-button-disabled-checked-fill: none;

        &:first-child {
          margin-right: 5px;
        }

        &.is-active {
          background: #fffdfd;
          border-radius: 4px;

          .el-radio-button__inner {
            font-size: 14px;
            font-weight: 600;
            line-height: 14px;
            color: #0070d2;
            text-align: center;
          }
        }
      }

      ::v-deep(.el-radio-button__inner) {
        padding: 8px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        background: none;
        border: none;
      }
    }

    .linkIncrease-work-tabs-flex {
      display: flex;
      align-items: center;
      height: 60px;
      margin-right: 16px;
      line-height: 60px;

      span {
        display: inline-flex;
        align-items: center;
        max-width: 164px;
        height: 100%;
        padding: 0 4px;
        font-size: 14px;
        color: #808080;
        text-align: left;
        cursor: pointer;

        &.is-active {
          color: #0070d2;
          border-bottom: 3px solid #0070d2;
        }
      }
    }

    .linkIncrease-work-manage {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;

      &.reverse {
        justify-content: flex-end;

        .linkIncrease-work-sccs-group {
          .linkIncrease-work-sccs-input {
            margin: 0;
          }
        }
      }

      .linkIncrease-work-sccs-group {
        .el-checkbox {
          vertical-align: middle;
        }

        .linkIncrease-work-sccs-input {
          margin-right: 12px;
          margin-left: 16px;
        }
      }
    }
  }

  .sccs-body-container {
    height: calc(100% - 60px);
    overflow: hidden;
  }
}

.sccs-popover-span {
  font-size: 14px;
  line-height: 22px;
  color: #a8abb2;
  text-align: left;
  cursor: pointer;
}

.sccs-popover-icon {
  margin-left: 6px;
  font-size: 12px;
  line-height: 20px;
  color: #a8abb2;
  text-align: left;
  cursor: pointer;
}

.sccs-popover-container {
  .sccs-popover-top {
    // margin-top: 8px;
    .sccs-popover-title {
      padding-left: 16px;
      font-size: 12px;
      line-height: 36px;
      color: #797979;
    }
  }

  .sccs-popover-bottom {
    height: 50px;
    padding: 0 22px;
    line-height: 50px;
    text-align: left !important;
    cursor: pointer;
    border-top: 1px solid rgb(151 151 151 / 17%);

    &:hover {
      background: #f5f5f5;
    }
  }

  .sccs-group-name {
    position: relative;
    height: 36px;
    padding: 0 18px;
    overflow: hidden;
    line-height: 36px;
    color: #262626;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;

    .iconfont {
      position: absolute;
      top: 50%;
      right: 18px;
      display: none;
      font-size: 10px;
      transform: translateY(-50%);
    }

    &:hover {
      background: #f5f5f5;
    }

    &.is-active {
      color: #2082ed;

      .iconfont {
        display: block;
      }
    }
  }
}
</style>
