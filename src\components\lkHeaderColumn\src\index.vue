<template>
  <div
    class="subtable-column-body"
    :style="{ width: `${width}px` }"
    style="pointer-events: auto; cursor: pointer"
  >
    <el-tooltip
      v-if="column.info"
      effect="light"
      :content="infoInternationalization"
      placement="top"
      :show-after="500"
    >
      <i
        class="iconfont link-explain"
        style="margin-right: 3px; font-size: 12px; color: #262626"
      />
    </el-tooltip>
    <span v-if="column.$required" class="subtable-column-require" />
    <ReText
      class="subtable-column-title"
      type="info"
      :tippyProps="{ delay: 50000 }"
    >
      {{ titleInternationalization }}
      {{ column.props?.unit ? `(${column.props?.unit})` : "" }}
    </ReText>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { ReText } from "@/components/ReText";
import { storageLocal } from "@pureadmin/utils";

const props = defineProps({
  column: {
    type: Object as PropType<any>,
    default: () => {}
  },
  width: {
    type: String as PropType<string>,
    default: ""
  }
});

const titleInternationalization = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  return translationLang === "en"
    ? props.column.props?.titleEn
      ? props.column.props.titleEn
      : props.column.title
    : props.column.title;
});

const infoInternationalization = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  return translationLang === "en"
    ? props.column.props?.infoEn
      ? props.column.props.infoEn
      : props.column.info
    : props.column.info;
});
</script>
<style lang="scss" scoped>
.subtable-column-body {
  display: flex;
  align-items: center;

  .subtable-column-require {
    margin-right: 3px;

    &::after {
      font-size: 14px;
      color: #e62412;
      content: "*";
    }
  }

  .subtable-column-title {
    font-size: 14px;
  }
}
</style>
