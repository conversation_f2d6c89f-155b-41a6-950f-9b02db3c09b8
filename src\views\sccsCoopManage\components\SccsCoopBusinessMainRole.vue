<template>
  <div class="team-module-area-body">
    <el-form
      ref="FormRef"
      label-position="top"
      label-width="auto"
      :model="form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_inputSearch')}：`">
            <el-input
              v-model="form.roleName"
              clearable
              :placeholder="t('trade_sccs_roleName')"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_members')}：`">
            <LkTeamSelectV2
              v-model="form.teamMemberId"
              filterable
              :options="sccsList"
              clearable
              :placeholder="t('trade_common_selectText')"
              :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
              :props="{ label: 'username', value: 'teamMemberId' }"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item style="margin-top: 30px">
            <el-button @click="resetForm">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchTable"
            >
              {{ t("trade_common_query") }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="tableData"
      :border="true"
      stripe
      :tooltip-options="{ showAfter: 500 }"
      :empty-text="
        loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
      "
    >
      <el-table-column
        :label="t('trade_sccs_roleName')"
        width="255"
        prop="name"
        class-name="el-table-sccs-setting-cell"
      >
        <template #default="{ row }">
          <ReText
            :key="row.name"
            class="tag-col"
            :tippyProps="{ delay: 50000 }"
          >
            <svg class="iconfont svg-icon svg-icon-coop" aria-hidden="true">
              <use xlink:href="#link-coop" />
            </svg>
            {{ row.name }}
          </ReText>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('trade_team_members')"
        prop="teamMemberInfoList"
        show-overflow-tooltip
      >
        <template #default="scope">
          <el-tag
            v-for="item in scope.row.teamMemberInfoList"
            :key="item.teamMemberId"
            class="tag-col"
          >
            <span v-if="!item.activate" class="tag-register-tip">
              ({{ t("trade_common_unregistered") }})
            </span>
            <LkAvatar
              :size="18"
              fit="fill"
              :teamInfo="{
                avatar: item.avatar,
                username: item.username
              }"
            />
            {{ item.username }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column width="70">
        <template #default="scope">
          <el-dropdown trigger="click">
            <i class="iconfont link-more-left" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleEdit(scope.row)">
                  <i class="iconfont link-edit font14" />
                  {{ t("trade_common_edit") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <LkPagination
      ref="LkPaginationRef"
      :total="total"
      @updatePagination="handleSearchTable"
    />
    <SccsCoopMainRoleDialog
      ref="SccsCollaboratorsRoleRef"
      :basicInfo="basicInfo"
      @updateSccsSuccess="handleSearchTable"
    />
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { reactive, ref, watch } from "vue";
import LkAvatar from "@/components/lkAvatar/index";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import LkPagination from "@/components/lkPagination/index";
import SccsCoopMainRoleDialog from "../components/SccsCoopTeamMainRole/SccsCoopMainRoleDialog.vue";
import { getSccsCoopTeamRoleInCoopTeam, getSccsMemberList } from "@/api/sccs";

const { t } = useI18n();
const LkPaginationRef = ref<any>(null);
const FormRef = ref<any>(null);
const SccsCollaboratorsRoleRef = ref<any>(null);
let tableData = ref<any[]>([]);
let sccsList = ref<any[]>([]);
let total = ref<number>(0);
const loading = ref<boolean>(false);
const form = reactive({
  roleName: "",
  teamMemberId: ""
});

const resetForm = () => {
  form.roleName = "";
  form.teamMemberId = "";
  handleSearchTable();
};

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleSearchTable = async (): Promise<void> => {
  loading.value = true;
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getSccsCoopTeamRoleInCoopTeam({
    sccsId: props.basicInfo.id,
    ...pageParams,
    ...form
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleEdit = (row: any): void => {
  SccsCollaboratorsRoleRef.value.open(row);
};

const handleCreateTeamRole = (): void => {};

const init = (): void => {
  loading.value = true;
  Promise.all([getSccsMemberList({ sccsId: props.basicInfo.id })])
    .then(res => {
      sccsList.value = res[0].data;
      handleSearchTable();
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 300);
    });
};

watch(
  () => props.basicInfo.id,
  () => {
    if (props.basicInfo && props.basicInfo.id) {
      init();
    }
  },
  {
    immediate: true
  }
);

defineExpose({
  handleCreateTeamRole
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.team-module-area-body {
  margin-top: 0 !important;

  .team-module-area-btn {
    position: absolute;
    top: 0;
    right: 0;
  }

  ::v-deep(.tag-col) {
    padding: 3px 6px;
    margin-right: 4px;
    overflow: hidden;
    font-size: 12px;
    line-height: 20px;
    color: #595959;
    text-overflow: ellipsis;
    word-wrap: break-word;
    background: #f0f0f0;
    border: 0 none;
    border-radius: 4px;
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  overflow: hidden;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  text-overflow: ellipsis;
  word-wrap: break-word;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
