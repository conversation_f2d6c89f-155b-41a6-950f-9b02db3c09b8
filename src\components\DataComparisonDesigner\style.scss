.dc-body {
  width: 100%;

  .el-row {
    align-items: center;
    min-height: 24px;
    margin-bottom: 10px;
  }

  .dc-title,
  .dc-lastVal,
  .dc-currentVal {
    box-sizing: border-box;
    display: inline-flex;
    flex-wrap: wrap;
    align-items: flex-end;

    .preview-sub-table-container {
      width: 100%;
      min-height: 400px;

      ::v-deep(.table-component-container) {
        width: 100% !important;
        height: 100% !important;
      }
    }

    .dc-lastVal-label-select,
    .dc-currentVal-label-select {
      .select-v2-text {
        display: inline-block;
        max-width: 100%;
        height: 20px;
        padding: 0 6px;
        overflow: hidden;
        line-height: 20px;
        color: #262626 !important;
        text-overflow: ellipsis;
        word-wrap: break-word;
        border-radius: 3px;
      }
    }
  }

  .dc-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #707177;
  }

  .dc-title.is-required {
    &::before {
      margin-left: 4px;
      color: #f56c6c;
      content: "*";
    }
  }

  .dc-lastVal,
  .dc-currentVal {
    font-size: 14px;
    color: #000;
  }

  .dc-lastVal {
    position: relative;

    &.delete,
    &.edit {
      padding: 0 6px;
      background: #fff1f0;
      border: 1px solid #f56c6c;
    }

    .delete-line {
      position: absolute;
      top: 50%;
      left: 0;
      z-index: 99;
      width: 100%;
      height: 1px;
      background: #262626;
    }
  }

  .dc-currentVal {
    &.add,
    &.edit {
      padding: 0 6px;
      background: #f1ffeb;
      border: 1px solid #67c23a;
    }
  }
}
