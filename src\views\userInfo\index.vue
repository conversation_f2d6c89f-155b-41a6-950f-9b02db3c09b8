<template>
  <el-drawer
    v-model="drawerVisible"
    :with-header="false"
    direction="rtl"
    :size="590"
    :append-to-body="true"
    class="userInfo-drawer-class"
    destroy-on-close
  >
    <div class="userInfo-body">
      <div class="userInfo-body-top">
        <div class="userInfo-body-title">
          {{ t("trade_perCenter_personalInformation") }}
        </div>
        <div class="userInfo-body-container">
          <div class="userInfo-body-col">
            <div class="userInfo-body-avater">
              <div class="userInfo-body-avater-left">
                <!-- <LkUpload :avatarSize="58" mode="avatar" /> -->
                <LKUploadAvatar
                  :teamInfo="{
                    id: userInfo.id,
                    teamAvatar: userInfo.avatar,
                    teamName: userInfo.username
                  }"
                  class="avatarfont16"
                  shape="circle"
                  ossFileType="TRADE_USER_AVATAR "
                  @onSuccessUpdateAvatar="onSuccessUpdateAvatar"
                />
              </div>
              <div class="userInfo-body-avater-right">
                <div class="userInfo-body-avater-userName">
                  {{ userInfo.username }}
                </div>
                <div class="userInfo-body-avater-sex">
                  <FontIcon
                    v-if="userInfo.sex && userInfo.sex === 1"
                    icon="link-male"
                    style="color: #0070d2"
                  />
                  <FontIcon
                    v-if="userInfo.sex && userInfo.sex === 2"
                    icon="link-female"
                  />
                </div>
              </div>
            </div>
            <div class="userInfo-body-edit">
              <el-button @click="openEditUserInfo">
                {{ t("trade_common_edit") }}
              </el-button>
            </div>
          </div>
          <div class="userInfo-body-col">
            <FontIcon icon="link-email" />
            <span class="userInfo-body-email">{{ userInfo.email }}</span>
          </div>
          <div class="userInfo-body-col">
            <el-button type="text" @click="handleResetPasswordDialog">
              {{ t("trade_common_resetPassword") }}
            </el-button>
          </div>
        </div>
      </div>
      <div class="userInfo-body-bottom">
        <div class="userInfo-body-bottom-top">
          <div class="userInfo-body-title">
            {{ t("trade_perCenter_myTeam") }}
          </div>
          <el-button type="primary" color="#0070D2" @click="addTeam">
            <FontIcon icon="link-add" />
            {{ t("trade_perCenter_createTeam") }}
          </el-button>
        </div>
        <el-scrollbar class="userInfo-invite-team-list">
          <div
            v-for="teamItem in teamList"
            :key="teamItem.id"
            class="userInfo-invite-team-li"
          >
            <div class="userInfo-invite-avater">
              <LkAvatar
                class="colorOrange avatarFont16"
                shape="square"
                :size="44"
                fit="cover"
                :teamInfo="{
                  avatar: teamItem.teamAvatar,
                  username: teamItem.teamName
                }"
              />
            </div>
            <div class="userInfo-invite-team-msg">
              <div class="userInfo-invite-team-name">
                <!-- <div class="userInfo-invite-team-text">
                  {{ teamItem.teamName }}
                </div> -->
                <ReText
                  type="info"
                  class="userInfo-invite-team-text"
                  :tippyProps="{ popperOptions: { 'hide-after': 500 } }"
                >
                  {{ teamItem.teamName }}
                </ReText>
                <el-tag v-if="teamItem.ownerFlag" type="primary">
                  {{ t("trade_common_owner") }}
                </el-tag>
              </div>
              <div class="userInfo-invite-team-code">
                {{ t("trade_team_teamCode") }}：{{ teamItem.teamCode }}
                <span class="userInfo-invite-team-main">
                  <el-tooltip
                    effect="dark"
                    :content="t('trade_common_copy')"
                    :show-after="500"
                    placement="top"
                  >
                    <i
                      v-copy:click="teamItem.teamCode"
                      class="iconfont link-copy"
                    />
                  </el-tooltip>
                </span>
              </div>
            </div>
            <div class="userInfo-invite-button-area">
              <FontIcon
                v-if="userInfo.latestLoginTeamId === teamItem.id"
                icon="link-tick"
                style="color: #0070d2"
              />
              <span
                v-else
                class="userInfo-invite-team-main"
                @click="handleSwitchTeam(teamItem.id)"
              >
                <el-tooltip
                  effect="dark"
                  :content="t('trade_team_switchTeam')"
                  :show-after="500"
                  placement="top"
                >
                  <i class="iconfont link-switch-teams" />
                </el-tooltip>
              </span>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <CreateTeamDialog
        ref="CreateTeamDialogRef"
        @createSuccess="createSuccess"
      />
      <EditUserInfoDialog
        ref="EditUserInfoDialogRef"
        :userInfo="userInfo"
        @updateUserInfo="updateUserInfo"
      />
      <ResetPasswordDialog ref="ResetPasswordDialogRef" :userInfo="userInfo" />
    </div>
  </el-drawer>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { getMyTeamList } from "@/api/common";
import { storageLocal } from "@pureadmin/utils";
import LkAvatar from "@/components/lkAvatar/index";
import LKUploadAvatar from "@/components/lkUploadAvatar/index";
import CreateTeamDialog from "../createTeam/component/createTeamDialog.vue";
import EditUserInfoDialog from "./components/editUserInfoDialog.vue";
import ResetPasswordDialog from "./components/resetPasswordDialog.vue";
import { switchTeam, getNewUserInfo } from "@/utils/auth";
import { ReText } from "@/components/ReText";
import { emitter } from "@/utils/mitt";

interface TeamProp {
  id: string;
  invitedUnHandleCount: number;
  msgUnreadCnt: number;
  ownerFlag: boolean;
  teamAvatar: string | null;
  teamCode: string;
  teamName: string;
  teamOwner: string;
  teamShortName: string;
}

const { t } = useI18n();
const drawerVisible = ref<boolean>(false);
const CreateTeamDialogRef = ref<any>(null);
const EditUserInfoDialogRef = ref<any>(null);
const ResetPasswordDialogRef = ref<any>(null);
let teamList = ref<TeamProp[]>([
  {
    id: "",
    invitedUnHandleCount: 0,
    msgUnreadCnt: 0,
    ownerFlag: false,
    teamAvatar: "",
    teamCode: "",
    teamName: "",
    teamOwner: "",
    teamShortName: ""
  }
]);
let userInfo = ref<any>({});

const open = (): void => {
  drawerVisible.value = true;
};

const addTeam = (): void => {
  CreateTeamDialogRef.value.open();
};

const getTeamList = async (): Promise<void> => {
  const res = await getMyTeamList();
  if (res.code === 0) {
    teamList.value = res.data;
  }
};

const handleSwitchTeam = async (id: string): Promise<void> => {
  switchTeam(id);
};

const handleResetPasswordDialog = (): void => {
  ResetPasswordDialogRef.value.open();
};

const openEditUserInfo = (): void => {
  EditUserInfoDialogRef.value.open();
};

const init = (): void => {
  userInfo.value = storageLocal().getItem("user-info");
  getTeamList();
};

const createSuccess = (): void => {
  init();
};

const onSuccessUpdateAvatar = (): void => {
  getNewUserInfo().then(res => {
    userInfo.value = res;
    getTeamList();
    //@ts-ignore
    emitter.emit("updateUserInfoAvatar", res);
    emitter.emit("updateUserInfo");
  });
};

const updateUserInfo = (): void => {
  emitter.emit("updateUserInfo");
  init();
};

onMounted(() => {
  init();
});

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.userInfo-body {
  display: flex;
  flex-direction: column;
  height: 100%;

  .userInfo-body-top {
    height: 230px;
    background: #fff;

    .userInfo-body-title {
      height: 48px;
      padding: 0 23px 0 18px;
      font-size: 18px;
      font-weight: bolder;
      line-height: 48px;
      color: #303133;
      text-align: left;
      border-bottom: 1px solid #e3e5e9;
    }

    .userInfo-body-container {
      height: 182px;
      padding: 28px 23px 28px 32px;

      .userInfo-body-col {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        // margin-top: 22px;
        .userInfo-body-avater {
          display: flex;
          flex: 1;
          align-items: center;

          .userInfo-body-avater-right {
            flex: 1;
            margin-left: 8px;

            .userInfo-body-avater-userName {
              font-size: 14px;
              line-height: 20px;
              color: #262626;
            }

            .userInfo-body-avater-sex {
              .iconfont {
                font-size: 12px;
                color: #ff2a6c;
              }
            }
          }
        }

        .userInfo-body-edit {
          align-items: center;
          width: 60px;
        }

        .iconfont {
          font-size: 12px;
          color: #8c8c8c;
        }

        .userInfo-body-email {
          margin-left: 19px;
        }
      }
    }
  }

  .userInfo-body-bottom {
    flex: 1;
    margin-top: 16px;
    background: #fff;

    .userInfo-body-bottom-top {
      display: flex;
      align-items: center;
      padding: 0 23px 0 18px;
      border-bottom: 1px solid #e3e5e9;

      .userInfo-body-title {
        flex: 1;
        align-items: center;
        height: 48px;
        font-size: 18px;
        font-weight: bolder;
        line-height: 48px;
        color: #303133;
        text-align: left;
      }

      .el-button {
        .iconfont {
          margin-right: 3px;
          font-size: 14px;
        }
      }
    }

    .userInfo-invite-team-list {
      max-height: calc(100vh - 295px);
      overflow-y: auto;

      .userInfo-invite-team-li {
        display: flex;
        align-items: center;
        height: 58px;
        padding: 7px 18px 7px 28px;

        &:hover {
          background: #f5f5f5;

          .userInfo-invite-team-main {
            background: #e5e5e5;
            border-radius: 3px;

            &:hover {
              background: #e5e5e5;
              border-radius: 3px;
            }
          }
        }

        .userInfo-invite-avater {
          width: 55px;

          .el-avatar {
            vertical-align: middle;
            border-radius: 8px;

            .team-avater {
              width: 100%;
              height: 100%;
              line-height: 44px;
              background: #ec7840;
              box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%);
            }
          }
        }

        .userInfo-invite-team-msg {
          flex: 1;
          max-width: calc(100% - 70px);

          .userInfo-invite-team-name {
            display: flex;
            margin-bottom: 4px;

            .userInfo-invite-team-text {
              max-width: 380px;
              overflow: hidden;
              font-size: 14px;
              line-height: 20px;
              color: #262626;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .el-tag {
              height: 20px;
              margin-left: 6px;
              font-size: 12px;
              color: #0070d2;
              border: 0 none;
            }
          }

          .userInfo-invite-team-code {
            font-size: 12px;
            line-height: 17px;
            color: #8c8c8c;

            .userInfo-invite-team-main {
              display: inline-block;
              width: 18px;
              height: 18px;
              text-align: center;
              cursor: pointer;
              border-radius: 3px;

              .iconfont {
                font-size: 12px;
                line-height: 17px;
                color: #8c8c8c;
              }
            }
          }
        }

        .userInfo-invite-button-area {
          .userInfo-invite-team-main {
            display: inline-block;
            width: 22px;
            height: 22px;
            text-align: center;

            .iconfont {
              font-size: 14px;
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.userInfo-drawer-class {
  .el-drawer__body {
    padding: 0 !important;
    background: #f2f2f2;
    box-shadow: -2px 0 20px 0 rgb(0 0 0 / 25%);
  }
}
</style>
