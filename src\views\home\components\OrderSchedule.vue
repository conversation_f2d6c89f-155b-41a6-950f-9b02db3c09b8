<!-- eslint-disable vue/valid-v-memo -->
<template>
  <div
    class="linkIncrease-work-card"
    style="max-height: calc(100vh - 73px); padding-bottom: 16px"
  >
    <el-calendar
      ref="calendar"
      v-model:model-value="dateValue"
      :class="[isExpand ? 'is-expand' : 'is-retract']"
      :range="currentWeeks"
    >
      <template #header>
        <div class="calendar-btn-body">
          <div class="linkIncrease-work-header-name">
            <svg class="svg-icon svg-icon-order-schedule">
              <use xlink:href="#link-order-schedule" />
            </svg>
            <span>{{ t("trade_home_orderSchedule") }}</span>
          </div>
          <div class="linkIncrease-work-header-oprate">
            <el-button type="info" plain @click="selectDate('today')">
              {{ t("trade_now_day") }}
            </el-button>
            <el-input-number
              v-model="defaultYear"
              clearable
              style="width: 120px"
            >
              <template #decrease-icon>
                <i
                  class="iconfont link-arrow-left"
                  @click="selectDate('prev-year')"
                />
              </template>
              <template #increase-icon>
                <i
                  class="iconfont link-arrow-right"
                  @click="selectDate('next-year')"
                />
              </template>
            </el-input-number>
          </div>
        </div>
        <ul class="calendar-year-body">
          <li
            v-for="item in monthList"
            :key="item.monthName"
            class="calendar-day-col"
            :class="
              Number(currentMonth) === Number(item.month) ? 'is-active' : ''
            "
            @click="handleMonthClick(item)"
          >
            <div class="calendar-day-col-item">
              <div
                v-if="item.plannedStartDate || item.plannedEndDate"
                class="calendar-day-col-flex"
              >
                <span
                  v-show="item.plannedStartDate"
                  class="calendar-day-col-item-point-blue"
                />
                <span
                  v-show="item.plannedEndDate"
                  class="calendar-day-col-item-point-red"
                />
              </div>
              <span>{{ t(item.monthName) }}</span>
            </div>
          </li>
        </ul>
      </template>
      <template #date-cell="{ data }">
        <el-tooltip
          placement="top"
          :show-after="500"
          :offset="15"
          :disabled="
            !setDayMark(data.day) ||
            (setDayMark(data.day).planedEndDateCount === 0 &&
              setDayMark(data.day).planedStartDateCount === 0)
          "
        >
          <template #content>
            <div class="tooltip-content">
              <div
                v-if="
                  setDayMark(data.day) &&
                  setDayMark(data.day).planedEndDateCount > 0
                "
                class="tooltip-content-item"
              >
                <span class="tooltip-content-item-point red" />
                <span class="tooltip-content-item-text">{{
                  t("trade_schedule_tooltip_end")
                }}</span>
                <span>{{ setDayMark(data.day).planedEndDateCount }}</span>
              </div>
              <div
                v-if="
                  setDayMark(data.day) &&
                  setDayMark(data.day).planedStartDateCount > 0
                "
                class="tooltip-content-item"
              >
                <span class="tooltip-content-item-point blue" />
                <span class="tooltip-content-item-text">{{
                  t("trade_schedule_tooltip_start")
                }}</span>
                <span>{{ setDayMark(data.day).planedStartDateCount }}</span>
              </div>
            </div>
          </template>
          <div
            :class="[
              data.isSelected ? 'is-selected' : 'el-calendar-day-default'
            ]"
            @click="handleDateClick(data)"
          >
            <span class="el-calendar-day-text">{{
              data.day.split("-").slice(2).join("")
            }}</span>
            <div class="el-calendar-day-decs">
              <span
                v-if="
                  setDayMark(data.day) &&
                  setDayMark(data.day).planedEndDateCount > 0
                "
                class="calendar-day-col-item-point-red large"
              />
              <span
                v-if="
                  setDayMark(data.day) &&
                  setDayMark(data.day).planedStartDateCount > 0
                "
                class="calendar-day-col-item-point-blue large"
              />
            </div>
          </div>
        </el-tooltip>
      </template>
    </el-calendar>
    <div class="expand-btn">
      <!-- 展开 -->
      <span v-if="!isExpand" @click="changeExpand(true)"
        >{{ t("trade_expand") }} <i class="iconfont link-arrow-down"
      /></span>
      <!-- 收齐 -->
      <span v-else @click="changeExpand(false)"
        >{{ t("trade_retract") }} <i class="iconfont link-arrow-up"
      /></span>
    </div>
    <div
      class="home-current-date-list"
      :class="[isExpand ? 'is-expand' : 'is-retract']"
    >
      <el-scrollbar
        ref="HomeScheduleScrollbarRef"
        :max-height="isExpand ? 'calc(100vh - 507px)' : 'calc(100vh - 327px)'"
        style="padding: 0 16px"
        @scroll="handleScroll"
      >
        <div class="home-current-date-list-title">
          {{ dayjs(dateValue).format("YYYY-MM-DD") }}
        </div>
        <el-collapse
          v-if="sccsList.length > 0"
          v-model="activeCollapseNames"
          v-loading="sccsListLoading"
          class="home-current-date-list-collapse"
        >
          <el-collapse-item
            v-for="(list, listIndex) in sccsList"
            :key="list.sccsId"
            :name="listIndex"
          >
            <template #title>
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                <span class="el-collapse-item-name">{{ list.sccsName }}</span>
              </ReText>
            </template>
            <div
              v-if="list.msPageByPlannedEndDate.total > 0"
              class="el-collapse-item"
            >
              <div class="el-collapse-item-title">
                <span style="display: inline-flex; align-items: center">
                  {{ t("trade_home_planEndsToday")
                  }}<span class="el-collapse-item-title-badge">{{
                    list.msPageByPlannedEndDate.total
                  }}</span>
                </span>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :show-after="500"
                  :content="`1/3: 1 ${t('trade_home_order_list_schedule_tip')};3 ${t('trade_home_order_list_schedule_tip_2')}`"
                  placement="top"
                >
                  <i class="iconfont link-explain" />
                </el-tooltip>
              </div>
              <div class="el-collapse-item-content">
                <div class="el-collapse-item-content-box">
                  <div
                    v-for="(item, index) in list.msPageByPlannedEndDate.list"
                    :key="index"
                    class="el-collapse-item-content-item"
                    @click="handleItemClick(item, list)"
                  >
                    <el-row :gutter="5">
                      <el-col :span="10">
                        <ReText type="info" :tippyProps="{ delay: 50000 }">
                          <span class="el-collapse-item-content-item-title">{{
                            item.orderMark
                          }}</span>
                        </ReText>
                      </el-col>
                      <el-col class="align-right" :span="8">
                        <ReText type="info" :tippyProps="{ delay: 50000 }">
                          <span
                            v-if="item.haveWorkOrderReply"
                            class="el-collapse-item-content-item-reply"
                          >
                            {{ t("trade_order_taskReplyComplete") }}
                          </span>
                        </ReText>
                      </el-col>
                      <el-col class="align-right" :span="6">
                        <ReText type="info" :tippyProps="{ delay: 50000 }">
                          <span class="el-collapse-item-content-item-complete">
                            {{ t("trade_work_order_completed") }}
                          </span>
                        </ReText>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="10">
                        <ReText type="info" :tippyProps="{ delay: 50000 }">
                          <span class="el-collapse-item-content-item-msName">
                            {{ item.name }}
                          </span>
                        </ReText>
                      </el-col>
                      <el-col class="align-right" :span="8">
                        <div
                          v-if="item.haveWorkOrderReply"
                          class="el-collapse-item-content-item-replyValue"
                        >
                          <span class="complete-count">{{
                            item.completeReplyWorkOrderCount
                          }}</span
                          >/<span>{{ item.workOrderCount }}</span>
                        </div>
                      </el-col>
                      <el-col class="align-right" :span="6">
                        <div
                          class="el-collapse-item-content-item-completeValue"
                        >
                          <span class="complete-count">{{
                            item.completeEditWorkOrderCount
                          }}</span
                          >/<span>{{ item.workOrderCount }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <div
                v-if="list.msPageByPlannedEndDate.total > 5"
                :key="list.sccsId"
                class="el-collapse-item-content-pagination"
              >
                <el-pagination
                  v-model:page-size="
                    sccsPageMap.get(list.sccsId).planEndsTodayPageInfo.pageSize
                  "
                  v-model:current-page="
                    sccsPageMap.get(list.sccsId).planEndsTodayPageInfo
                      .currentPage
                  "
                  :page-size-options="[5, 10, 20, 50]"
                  :pager-count="6"
                  layout="prev, pager, next"
                  :total="list.msPageByPlannedEndDate.total"
                  @change="
                    handlePageChange('PLANNED_END_DATE', list.sccsId, listIndex)
                  "
                />
              </div>
            </div>
            <div
              v-if="
                list.msPageByPlannedEndDate.total > 0 &&
                list.msPageByPlannedStartDate.total > 0
              "
              class="el-collapse-item-line"
            />
            <div
              v-if="list.msPageByPlannedStartDate.total > 0"
              class="el-collapse-item"
            >
              <div class="el-collapse-item-title blue">
                <span style="display: inline-flex; align-items: center">
                  {{ t("trade_home_planStartsToday")
                  }}<span class="el-collapse-item-title-badge blue">{{
                    list.msPageByPlannedStartDate.total
                  }}</span>
                </span>
              </div>
              <div class="el-collapse-item-content">
                <div class="el-collapse-item-content-box">
                  <div
                    v-for="(item, index) in list.msPageByPlannedStartDate.list"
                    :key="index"
                    class="el-collapse-item-content-item"
                    @click="handleItemClick(item, list)"
                  >
                    <el-row :gutter="5">
                      <el-col :span="10">
                        <ReText type="info" :tippyProps="{ delay: 50000 }">
                          <span class="el-collapse-item-content-item-title">{{
                            item.orderMark
                          }}</span>
                        </ReText>
                      </el-col>
                      <el-col :span="14">
                        <ReText type="info" :tippyProps="{ delay: 50000 }">
                          <span class="el-collapse-item-content-item-msName">
                            {{ item.name }}
                          </span>
                        </ReText>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
              <div
                v-if="list.msPageByPlannedStartDate.total > 5"
                :key="list.sccsId"
                class="el-collapse-item-content-pagination"
              >
                <el-pagination
                  v-model:page-size="
                    sccsPageMap.get(list.sccsId).planStartTodayPageInfo.pageSize
                  "
                  v-model:current-page="
                    sccsPageMap.get(list.sccsId).planStartTodayPageInfo
                      .currentPage
                  "
                  :page-size-options="[5, 10, 20, 50]"
                  :pager-count="6"
                  layout="prev, pager, next"
                  :total="list.msPageByPlannedStartDate.total"
                  @change="
                    handlePageChange(
                      'PLANNED_START_DATE',
                      list.sccsId,
                      listIndex
                    )
                  "
                />
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-empty
          v-else
          :description="t('trade_home_empty_sccs_list_tip')"
          :image-size="342"
          :image="sccsGroupNoImage"
        />
      </el-scrollbar>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  computed,
  ref,
  watch,
  nextTick,
  onMounted,
  onBeforeUnmount,
  reactive
} from "vue";
import { useI18n } from "vue-i18n";
import {
  getTradeSccsRolePermission,
  getTradeCoopSccsRolePermission
} from "@/api/order";
import {
  getSccsInfoListForPlannedDate,
  getPlannedDateScopeStatistic
} from "@/api/orderSchedule";
import dayjs from "dayjs";
import type { CalendarDateType, CalendarInstance } from "element-plus";
import sccsGroupNoImage from "@/assets/images/sccs/sccs-no-image.png";
import { ReText } from "@/components/ReText";
import { useRouter } from "vue-router";
import { storageLocal } from "@pureadmin/utils";

interface MonthItem {
  year: string;
  month: string;
  monthName: string;
  plannedStartDate?: boolean;
  plannedEndDate?: boolean;
  dateItemList?: any;
}
interface DateItem {
  planedStartDateCount: number;
  planedEndDateCount: number;
}

const { t } = useI18n();
const router = useRouter();
const calendar = ref<CalendarInstance>();
const defaultYear = ref(null);
const scheduleData = ref<any>();
// 默认年
defaultYear.value = dayjs(new Date()).format("YYYY");
// 当前日期
const dateValue = ref(new Date());
// 当前月份
const currentMonth = ref<string>(dayjs(new Date()).format("M"));

// 月份列表
const monthList = ref<MonthItem[]>([]);

const isExpand = ref(false);

const HomeScheduleScrollbarRef = ref<any>(null);
const changeExpand = (val: boolean) => {
  if (val) {
    currentWeeks.value = null;
  } else {
    nextTick(() => {
      getWeeksRange(dateValue.value);
    });
  }
  isExpand.value = val;
};
const activeCollapseNames = ref([]);
const sccsList = ref<any>([]);
const sccsListLoading = ref(false);

const sccsPageMap = reactive(new Map());

const screenWidth = ref(window.innerWidth);
const monthListLength = computed(() => {
  if (screenWidth.value >= 1920) {
    return 11;
  } else {
    return 7;
  }
});

const handleScroll = (event: { scrollTop: number; scrollLeft: number }) => {
  if (event.scrollTop > 0) {
    isExpand.value = false;
    getWeeksRange(dateValue.value);
  }
};

const handlePageChange = (type: string, sccsId?: any, index?: any) => {
  getSccsList(type, sccsId);
};

const selectDate = (val: CalendarDateType): void => {
  if (!calendar.value) return;
  if (val === "today") {
    defaultYear.value = dayjs(new Date()).format("YYYY");
    currentMonth.value = dayjs(new Date()).format("M");
    calendar.value.selectDate(val);
    // 选择月份后需要重新生成月份排序并且标记
    initScheduleView(
      monthListLength.value,
      Number(defaultYear.value),
      Number(currentMonth.value)
    );
  } else {
    dateValue.value = new Date(
      defaultYear.value +
        "-" +
        currentMonth.value +
        "-" +
        dayjs(dateValue.value).format("DD")
    );
    initScheduleView(
      monthListLength.value,
      Number(defaultYear.value),
      Number(currentMonth.value)
    );
  }
  if (!isExpand.value) {
    nextTick(() => {
      getWeeksRange(dateValue.value);
    });
  } else {
    currentWeeks.value = null;
  }
};

const setDayMark = computed(() => {
  let dateItemObject: DateItem = {
    planedStartDateCount: 0,
    planedEndDateCount: 0
  };
  return timeStamp => {
    if (scheduleData.value) {
      const currentYearAndMonth = dayjs(timeStamp).format("YYYY-M");
      if (
        scheduleData.value.get(currentYearAndMonth) &&
        scheduleData.value.get(currentYearAndMonth).dateItemList
      ) {
        const dateItemList =
          scheduleData.value.get(currentYearAndMonth).dateItemList;
        const monthDayMap = new Map();
        for (let dateItem of dateItemList) {
          monthDayMap.set(dateItem.dateStr, dateItem);
        }
        if (monthDayMap.get(timeStamp)) {
          dateItemObject = {
            planedStartDateCount:
              monthDayMap.get(timeStamp).planedStartDateCount,
            planedEndDateCount: monthDayMap.get(timeStamp).planedEndDateCount
          };
          return dateItemObject;
        } else {
          dateItemObject = {
            planedStartDateCount: 0,
            planedEndDateCount: 0
          };
          return dateItemObject;
        }
      } else {
        dateItemObject = {
          planedStartDateCount: 0,
          planedEndDateCount: 0
        };
        return dateItemObject;
      }
    } else {
      dateItemObject = {
        planedStartDateCount: 0,
        planedEndDateCount: 0
      };
      return dateItemObject;
    }
  };
});

const handleMonthClick = (item: MonthItem) => {
  // 选择月份后需要重新生成月份排序并且标记
  // 关联组件赋值
  currentMonth.value = item.month;
  defaultYear.value = item.year;
  let currentDate = null;
  if (Number(item.month) < 10) {
    currentDate =
      item.year + "-0" + item.month + "-" + dayjs(dateValue.value).format("DD");
  } else {
    currentDate =
      item.year + "-" + item.month + "-" + dayjs(dateValue.value).format("DD");
  }
  dateValue.value = new Date(currentDate);
  initScheduleView(
    monthListLength.value,
    Number(item.year),
    Number(item.month)
  );
  if (!isExpand.value) {
    nextTick(() => {
      getWeeksRange(dateValue.value);
    });
  } else {
    currentWeeks.value = null;
  }
};

const handleDateClick = data => {
  defaultYear.value = dayjs(data.date).format("YYYY");
  currentMonth.value = dayjs(data.date).format("M");
  initScheduleView(
    monthListLength.value,
    Number(defaultYear.value),
    Number(currentMonth.value)
  );
};

const handleItemClick = (item, list) => {
  // 创建浏览器会话缓存以便详情定位
  sessionStorage.setItem("orderDetailPagePositionInfo", JSON.stringify(item));
  handleJumpDetail(item, list);
};
const handleJumpDetail = async (row, list): Promise<void> => {
  const { sccsId, coopTeamMark } = row;
  const params = {
    sccsName: list.sccsName,
    sccsId: sccsId,
    templateId: list.templateId,
    orderId: row.orderId,
    orderMark: row.orderMark ? row.orderMark : row.serialNumber,
    coopTeamMark: !coopTeamMark ? "2" : coopTeamMark
  };
  let resp: any = {};
  if (params.coopTeamMark === "2") {
    resp = await getTradeSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  } else {
    resp = await getTradeCoopSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  }
  //@ts-ignore
  storageLocal().setItem(`${row.orderId}_userTeamRole`, resp.data[row.orderId]);
  router.push({ name: "orderDetail", query: params });
};

const getSccsList = async (type?: string, sccsId?: any) => {
  let params = {
    dateStr: dayjs(dateValue.value).format("YYYY-MM-DD"),
    pageSize: 5,
    pageNo: 1,
    sccsId: "",
    queryType: null
  };
  if (type === "PLANNED_START_DATE") {
    params.pageNo = sccsPageMap.get(sccsId).planStartTodayPageInfo.currentPage;
    params.pageSize = sccsPageMap.get(sccsId).planStartTodayPageInfo.pageSize;
    params.queryType = "PLANNED_START_DATE";
    params.sccsId = sccsId;
  } else if (type === "PLANNED_END_DATE") {
    params.pageNo = sccsPageMap.get(sccsId).planEndsTodayPageInfo.currentPage;
    params.pageSize = sccsPageMap.get(sccsId).planEndsTodayPageInfo.pageSize;
    params.queryType = "PLANNED_END_DATE";
    params.sccsId = sccsId;
  }
  sccsListLoading.value = true;
  const res = await getSccsInfoListForPlannedDate(params);
  if (res.code === 0 && res.data.length > 0) {
    if (type === "PLANNED_START_DATE") {
      sccsList.value.find(e => e.sccsId === sccsId).msPageByPlannedStartDate =
        res.data.filter(e => e.sccsId === sccsId)[0].msPageByPlannedStartDate;
    } else if (type === "PLANNED_END_DATE") {
      sccsList.value.find(e => e.sccsId === sccsId).msPageByPlannedEndDate =
        res.data.filter(e => e.sccsId === sccsId)[0].msPageByPlannedEndDate;
    } else {
      sccsList.value = res.data;
    }
    activeCollapseNames.value = [];
    sccsList.value.forEach((item, index) => {
      activeCollapseNames.value.push(index);
    });

    if (!type && !sccsId) {
      for (const sccsItem of sccsList.value) {
        sccsPageMap.set(sccsItem.sccsId, {
          planStartTodayPageInfo: {
            pageSize: 5,
            currentPage: 1
          },
          planEndsTodayPageInfo: {
            pageSize: 5,
            currentPage: 1
          }
        });
      }
    }
    sccsListLoading.value = false;
  } else {
    sccsList.value = [];
    sccsListLoading.value = false;
  }
};

// 根据传入的年月生成数组，并且传入的年月在数组中间
// params：length 数组长度, year 年份, month 月份
const generateYearMonthArray = (
  arrayLength: number,
  inputYear: number,
  inputMonth: number
) => {
  // 定义月份英文翻译数组（国际化）
  const monthNames = [
    "trade_January", // 1月
    "trade_February", // 2月
    "trade_March", // 3月
    "trade_April", // 4月
    "trade_May", // 5月
    "trade_June", // 6月
    "trade_July", // 7月
    "trade_August", // 8月
    "trade_September", // 9月
    "trade_October", // 10月
    "trade_November", // 11月
    "trade_December" // 12月
  ];
  // 检查输入的月份是否在有效范围内（1 - 12）
  if (inputMonth < 1 || inputMonth > 12) {
    throw new Error("输入的月份必须在 1 到 12 之间。");
  }
  // 计算前后推的月数
  const halfLength = Math.floor(arrayLength / 2);
  const result = [];

  // 往前推月份
  for (let i = halfLength; i > 0; i--) {
    let year = inputYear;
    let month = inputMonth - i;
    // 处理月份小于 1 的情况，即跨年
    if (month < 1) {
      year--;
      month += 12;
    }
    result.push({
      year: year,
      month: month,
      monthName: monthNames[month - 1]
    });
  }

  // 添加传入的年月
  result.push({
    year: inputYear,
    month: inputMonth,
    monthName: monthNames[inputMonth - 1]
  });

  // 往后推月份
  for (let i = 1; i < arrayLength - halfLength; i++) {
    let year = inputYear;
    let month = inputMonth + i;
    // 处理月份大于 12 的情况，即跨年
    if (month > 12) {
      year++;
      month -= 12;
    }
    result.push({
      year: year,
      month: month,
      monthName: monthNames[month - 1]
    });
  }

  return result;
};
const currentWeeks = ref<[Date, Date]>(null);
const getCurrentWeekDates = currentDate => {
  // 获取当前日期是本周的第几天（0 代表周日，1 代表周一，以此类推）
  const currentDay = currentDate.getDay();

  // 计算本周周日（起始日）的日期
  const startOfWeek = new Date(currentDate);
  startOfWeek.setDate(currentDate.getDate() - currentDay);

  const weekDates = [];
  for (let i = 0; i < 7; i++) {
    const date = new Date(startOfWeek);
    date.setDate(startOfWeek.getDate() + i);
    weekDates.push(date);
  }

  return weekDates;
};
const getWeeksRange = (date: Date) => {
  const currentWeeksList = getCurrentWeekDates(date);
  const length = currentWeeksList.length;
  const startDate = currentWeeksList[0];
  const endDate = currentWeeksList[length - 1];
  currentWeeks.value = [startDate, endDate];
};
// 月份标记
const setMounthMark = () => {
  for (const [key, value] of scheduleData.value) {
    monthList.value.map(e => {
      if (`${e.year}-${e.month}` === key) {
        e.plannedEndDate = value.plannedEndDate;
        e.plannedStartDate = value.plannedStartDate;
        e.dateItemList = value.dateItemList;
      }
    });
  }
};
const getPlannedDateScopeStatistics = async () => {
  try {
    const res = await getPlannedDateScopeStatistic({
      beginYear: monthList.value[0].year, // 起始年
      beginMonth: monthList.value[0].month, // 起始月
      endYear: monthList.value[monthList.value.length - 1].year, // 结束年
      endMonth: monthList.value[monthList.value.length - 1].month // 结束月
    });
    const dataList = res.data;
    const dateValueMap = new Map();
    for (let dateItemData in dataList) {
      dateValueMap.set(dateItemData, dataList[dateItemData]);
    }
    scheduleData.value = dateValueMap;
  } catch (error) {
    console.log(error);
  }
};
// 初始化日程组件
const initScheduleView = async (
  arrayLength: number,
  year: number,
  month: number
) => {
  // 1.初始化月份
  monthList.value = generateYearMonthArray(arrayLength, year, month);
  // 2.根据月份获取数据
  await getPlannedDateScopeStatistics();
  // 3.标记月份
  setMounthMark();
  // 4.获取周
};
const handleResize = () => {
  screenWidth.value = window.innerWidth;
  initScheduleView(
    monthListLength.value,
    defaultYear.value,
    Number(currentMonth.value)
  );
};

onMounted(() => {
  window.addEventListener("resize", handleResize);

  initScheduleView(
    monthListLength.value,
    defaultYear.value,
    Number(currentMonth.value)
  );
  getWeeksRange(dateValue.value);
});
onBeforeUnmount(() => {
  // 在组件卸载前移除 resize 事件监听器，避免内存泄漏
  window.removeEventListener("resize", handleResize);
});

watch(
  () => dateValue.value,
  () => {
    getSccsList();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
@use "index.scss";

.tooltip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px;

  .tooltip-content-item {
    display: inline-flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    line-height: 16px;
    color: #fff;

    &:last-child {
      margin-bottom: 0;
    }

    .tooltip-content-item-point {
      width: 8px;
      height: 8px;
      margin-right: 8px;
      border-radius: 50%;

      &.blue {
        background: #409eff;
      }

      &.red {
        background: #f56c6c;
      }
    }

    .tooltip-content-item-text {
      margin-right: 16px;
    }
  }
}
</style>
