<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div
    v-if="showMark"
    class="order-detail-body-relations"
    :class="hideBackgroudColor ? 'hideBackgroudColor' : ''"
  >
    <!-- 原单 -->
    <div
      v-if="Object.keys(orderRelationData).includes('ORDER_BY_CLONE')"
      class="custom-checkbox-popover-row"
    >
      <div class="custom-checkbox-popover-title custom-order">
        {{ t("trade_order_original") }}
      </div>

      <div class="custom-checkbox-popover-tip">
        <span class="custom-checkbox-popover-mark-clean">
          <span
            @click="gotoDetail(orderRelationData['ORDER_BY_CLONE']['order'][0])"
            >{{
              orderRelationData["ORDER_BY_CLONE"]["order"][0].toOrderMark
            }}</span
          >
          <i
            v-if="hasCancelPermission('ORDER_BY_CLONE')"
            class="iconfont link-clean"
            @click="
              cancelRelation(orderRelationData['ORDER_BY_CLONE']['order'][0])
            "
          />
        </span>
      </div>
    </div>
    <!-- 组件优化，循环遍历 -->
    <div
      v-for="type in Object.keys(orderRelationData).filter(
        e => e !== 'ORDER_BY_CLONE'
      )"
      :key="type"
      class="custom-checkbox-popover-row"
    >
      <div
        v-if="
          orderRelationData[type][msId]?.length > 0 ||
          orderRelationData[type].length > 0
        "
        class="custom-checkbox-popover-title"
        :class="relationTypeObj.get(type).class"
      >
        {{ t(relationTypeObj.get(type).label) }}
      </div>
      <!-- 详情-里程碑 -->
      <div
        v-if="
          ['MILESTONE_BY_CLONE', 'MILESTONE_CLONE'].includes(type) &&
          orderRelationData[type][msId]?.length > 0
        "
        class="milestone-mark"
      >
        <div
          v-for="(mark, index) in orderRelationData[type][msId]"
          :key="index"
          class="custom-checkbox-popover-tip"
        >
          <span class="custom-checkbox-popover-mark-clean">
            <span @click="gotoDetail(mark)">{{ mark.toOrderMark }}</span>
            <i
              v-if="hasCancelPermission(type) && !hideBackgroudColor"
              class="iconfont link-clean"
              @click="cancelRelation(mark)"
            />
          </span>
          <span v-if="orderRelationData[type][msId].length - 1 !== index"
            >、</span
          >
        </div>
      </div>
      <!-- 详情-工单 -->
      <div
        v-else-if="['WORK_ORDER_BY_CLONE', 'WORK_ORDER_CLONE'].includes(type)"
        class="milestone-mark"
      >
        <div
          v-for="(mark, index) in orderRelationData[type]"
          :key="index"
          class="custom-checkbox-popover-tip"
        >
          <span class="custom-checkbox-popover-mark-clean">
            <span @click="gotoDetail(mark)">{{ mark.toOrderMark }}</span>
            <i
              v-if="hasCancelPermission(type)"
              class="iconfont link-clean"
              @click="cancelRelation(mark)"
            />
          </span>
          <span v-if="orderRelationData[type].length - 1 !== index">、</span>
        </div>
      </div>
      <!-- 详情-订单 -->
      <div v-else class="milestone-mark">
        <div
          v-for="(mark, index) in orderRelationData[type]['order']"
          :key="index"
          class="custom-checkbox-popover-tip"
        >
          <span class="custom-checkbox-popover-mark-clean">
            <span @click="gotoDetail(mark)">{{ mark.toOrderMark }}</span>
            <i
              v-if="hasCancelPermission(type) && !hideBackgroudColor"
              class="iconfont link-clean"
              @click="cancelRelation(mark)"
            />
          </span>
          <span v-if="orderRelationData[type]['order'].length - 1 !== index"
            >、</span
          >
        </div>
      </div>
    </div>
  </div>
  <el-dialog
    v-model="cancelMsDialogVisible"
    class="cancel-ms-dialog"
    :close-on-click-modal="false"
    width="480"
  >
    <template #header>
      <div class="dialog-title">
        <i class="iconfont link-tips-warm" />
        {{ t("trade_order_cancelMutuallyUse") }}
      </div>
    </template>
    <span class="cancel-ms-dialog-tip">
      {{ `${t("trade_cancel_ms_clone_tip")}？` }}
    </span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseDialog">
          {{ t("trade_common_notYetCancel") }}
        </el-button>
        <el-button
          type="primary"
          plain
          color="#0070D2"
          @click="handleCancelMilestone('CLEAN_DATA')"
        >
          {{ t("trade_common_cancelCleanButton") }}
        </el-button>
        <el-button
          type="primary"
          color="#0070D2"
          @click="handleCancelMilestone('KEEP_DATA')"
        >
          {{ t("trade_common_cancelSaveButton") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, markRaw, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { cloneDeep, storageLocal } from "@pureadmin/utils";
import {
  cancelCloneMs,
  cancelCloneWorkOrder,
  getTradeCoopSccsRolePermission,
  getTradeSccsRolePermission,
  canGetOrderInfo
} from "@/api/order";

const props = defineProps({
  orderRelationMap: {
    type: Object,
    default: () => {}
  },
  type: {
    type: String,
    default: ""
  },
  msId: {
    type: String,
    default: ""
  },
  orderId: {
    type: String,
    default: ""
  },
  hideBackgroudColor: {
    type: Boolean,
    default: false
  },
  permissionList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(["updateOrderRelation", "gotoDetail"]);

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 定义互用操作显示的文案和对应展示的样式
const relationTypeObj = new Map([
  // 里程碑互用自
  [
    "MILESTONE_BY_CLONE",
    { label: "trade_order_milestoneUsein", class: "custom-ms-clone-from" }
  ],
  // 里程碑层面互用自工单
  [
    "WORK_ORDER_BY_MS_CLONE",
    { label: "trade_order_milestoneUsein", class: "custom-ms-clone-from" }
  ],
  // 里程碑互用于
  [
    "MILESTONE_CLONE",
    { label: "trade_order_milestoneUse", class: "custom-ms-clone" }
  ],
  // 里程碑层面互用于工单
  [
    "WORK_ORDER_MS_CLONE",
    { label: "trade_order_milestoneUse", class: "custom-ms-clone" }
  ],
  // 关联
  [
    "ORDER_SAME_SCCS_RELATION",
    { label: "trade_common_associationOrder", class: "custom-order-related" }
  ],
  // sccs关联
  [
    "ORDER_CROSS_SCCS_RELATION",
    {
      label: "trade_order_sccsassociation",
      class: "custom-order-related-cross"
    }
  ],
  // 工单互用自
  [
    "WORK_ORDER_BY_CLONE",
    { label: "trade_order_workOrderUsein", class: "custom-ms-clone-from" }
  ],
  // 工单互用于
  [
    "WORK_ORDER_CLONE",
    { label: "trade_order_workOrderUse", class: "custom-ms-clone" }
  ],
  // 翻单
  [
    "ORDER_CLONE",
    { label: "trade_common_repeat_order", class: "custom-order-clone" }
  ]
]);
const cancelMsDialogVisible = ref(false);

const currentCloseOrder = ref(null);

// 是否显示当前标识
const showMark = computed(() => {
  let flag = false;
  let cloneDeepObj = cloneDeep(props.orderRelationMap);
  if (cloneDeepObj && Object.keys(cloneDeepObj).length === 0) {
    flag = false;
  } else {
    if (props.type === "ORDER") {
      // 当前是订单详情头部

      Object.keys(cloneDeepObj).forEach(key => {
        if (cloneDeepObj[key]["order"].length > 0) {
          cloneDeepObj[key]["isShowTypeTag"] = true;
        } else {
          cloneDeepObj[key]["isShowTypeTag"] = false;
        }
      });
    } else if (props.type === "MILESTONE") {
      // 当前是里程碑详情头部
      Object.keys(cloneDeepObj).forEach(key => {
        if (
          cloneDeepObj[key][props.msId] &&
          cloneDeepObj[key][props.msId].length > 0
        ) {
          cloneDeepObj[key]["isShowTypeTag"] = true;
        } else {
          cloneDeepObj[key]["isShowTypeTag"] = false;
        }
      });
    } else if (props.type === "WORKORDER") {
      // 当前是工单详情头部
      Object.keys(cloneDeepObj).forEach(key => {
        if (cloneDeepObj[key].length > 0) {
          cloneDeepObj[key]["isShowTypeTag"] = true;
        } else {
          cloneDeepObj[key]["isShowTypeTag"] = false;
        }
      });
    }

    let keys = Object.keys(cloneDeepObj).find(
      key => cloneDeepObj[key]["isShowTypeTag"]
    );
    if (keys && keys.length > 0) {
      flag = true;
    } else {
      flag = false;
    }
  }
  return flag;
});

const orderRelationData = ref({});

const handleCloseDialog = (): void => {
  cancelMsDialogVisible.value = false;
};

const handleCancelMilestone = async (type: string) => {
  const { code } = await cancelCloneMs({
    id: currentCloseOrder.value.id,
    cancelType: type
  });
  operateAfter(code);
};
const operateAfter = code => {
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    cancelMsDialogVisible.value = false;
    emit("updateOrderRelation");
  }
};
const cancelRelation = orderDta => {
  if (orderDta.dataType === "WORK_ORDER") {
    ElMessageBox.confirm(
      `${t("trade_order_cancelMutuallyUseTip")}？`,
      t("trade_order_cancelMutuallyUse"),
      {
        confirmButtonText: t("trade_confirm_cancellation"),
        cancelButtonText: t("trade_common_notYetCancel"),
        confirmButtonClass: "confrim-message-btn-warn-class",
        customClass: "order_confirm_message_box",
        type: "warning",
        icon: markRaw(WarningFilled),
        center: true
      }
    )
      .then(async () => {
        // 工单互用取消
        const { code } = await cancelCloneWorkOrder({
          id: orderDta.id
        });
        operateAfter(code);
      })
      .catch(e => {
        return;
      });
  } else if (orderDta.dataType === "MILESTONE") {
    currentCloseOrder.value = orderDta;
    cancelMsDialogVisible.value = true;
  }
};
const gotoDetail = async orderDta => {
  const { code } = await canGetOrderInfo({
    sccsId: orderDta.toSccsId,
    orderId: orderDta.toOrderId
  });
  code === 0 && handleJumpDetail(orderDta);
};
const handleJumpDetail = async (row): Promise<void> => {
  const sessionItem = {
    msId: row.fromMsId,
    workOrderId: row.dataType === "WORK_ORDER" ? row.toId : ""
  };
  // 创建浏览器会话缓存以便详情定位
  sessionStorage.setItem(
    "orderDetailPagePositionInfo",
    JSON.stringify(sessionItem)
  );
  const { coopTeamMark } = route.query;
  const params = {
    sccsName: row.toSccsName,
    sccsId: row.toSccsId,
    templateId: row.toTemplateId,
    orderId: row.toOrderId,
    orderMark: row.toOrderMark ? row.toOrderMark : row.serialNumber,
    coopTeamMark: coopTeamMark
  };
  let resp: any = {};
  if (coopTeamMark === "2") {
    resp = await getTradeSccsRolePermission({
      sccsId: row.toSccsId,
      orderIdList: [row.toOrderId]
    });
  } else {
    resp = await getTradeCoopSccsRolePermission({
      sccsId: row.toSccsId,
      orderIdList: [row.toOrderId]
    });
  }
  //@ts-ignore
  storageLocal().setItem(
    `${row.toOrderId}_userTeamRole`,
    resp.data[row.toOrderId]
  );
  emit("gotoDetail");
  router.push({ name: "orderDetail", query: params });
};
// 判断当前是否有取消互用权限
const hasCancelPermission = computed(() => {
  return tagType => {
    let permissionList = [];
    if (props.orderId) {
      permissionList = props.permissionList;
    } else if (route.query.orderId) {
      permissionList = storageLocal().getItem(
        `${route.query.orderId}_userTeamRole`
      )[props.msId ? props.msId : "order"];
    } else {
      permissionList = [];
    }
    if (permissionList.length > 0) {
      switch (props.type) {
        case "ORDER":
          if (tagType === "ORDER_SAME_SCCS_RELATION") {
            return permissionList.includes("relate_order");
          } else if (tagType === "ORDER_CROSS_SCCS_RELATION") {
            return permissionList.includes("relate_sccs_order");
          } else if (
            tagType === "ORDER_CLONE" ||
            tagType === "ORDER_BY_CLONE"
          ) {
            return permissionList.includes("clone_order");
          }
        case "MILESTONE":
          if (
            tagType === "MILESTONE_CLONE" ||
            tagType === "MILESTONE_BY_CLONE"
          ) {
            return permissionList.includes("clone_milestone");
          } else {
            return false;
          }
        case "WORKORDER":
          return permissionList.includes("clone_work_order");
        default:
          return false;
      }
    } else {
      return false;
    }
  };
});

watch(
  () => props.orderRelationMap,
  () => {
    orderRelationData.value = {
      ...props.orderRelationMap,
      isShowTypeTag: false
    };
  },
  {
    immediate: true
  }
);

const handleReset = () => {
  orderRelationData.value = {};
};
defineExpose({
  orderRelationData,
  handleReset
});
</script>
<style lang="scss">
.order-detail-body-relations {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  padding: 8px 15px;
  background: #f0f1f6;
  border-radius: 4px 4px 0 0;

  &.hideBackgroudColor {
    padding: 5px 0;
    background: none;
    border-radius: none;
  }

  .custom-checkbox-popover-row {
    display: flex;
    align-items: baseline;
    justify-content: flex-start;

    .custom-checkbox-popover-title {
      flex-shrink: 0;
      height: fit-content;
      padding: 2px 6px;
      margin: 6px 0;
      font-size: 12px;
      line-height: 20px;
      border-radius: 2px;
      // 原单
      &.custom-order {
        color: #0070d2;
        background: #ddefff;
      }
      // 翻单
      &.custom-order-clone {
        color: #2b69f7;
        background: #dde1ff;
      }
      // 关联（同一个sccs关联）
      &.custom-order-related {
        color: #fa8d0a;
        background: #ffedd1;
      }
      // 跨sccs关联
      &.custom-order-related-cross {
        color: #00b699;
        background: #d9f4f0;
      }
      // 里程碑互用于/工单互用于
      &.custom-ms-clone {
        color: #9870fa;
        background: #e6dcff;
      }
      // 里程碑互用自/工单互用自
      &.custom-ms-clone-from {
        color: #fa8d0a;
        background: #fff1cd;
      }
    }

    .milestone-mark {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin: 0 5px;
    }

    .custom-checkbox-popover-tip {
      display: inline-flex;
      align-items: center;
      padding: 5px 0;
      margin-left: 6px;
      color: #616266;
      cursor: pointer;

      &:hover {
        color: #0070d2;
      }

      .custom-checkbox-popover-mark-clean {
        display: inline-flex;
        align-items: center;
        font-size: 14px;

        span {
          white-space: nowrap;
        }
      }
    }

    .link-clean {
      margin: 0 4px;
      font-size: 12px;
      color: #babec6;
      cursor: pointer;
    }
  }
}

.cancel-ms-dialog {
  top: 50%;
  height: 196px;
  margin-top: -98px;

  .dialog-title {
    display: flex;
    align-items: center;
    padding: 4px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #262626;

    .link-tips-warm {
      margin-right: 10px;
      font-size: 24px;
      color: #fa8d0a;
    }
  }

  .el-dialog__headerbtn {
    top: 10px;
    right: 10px;
  }

  .cancel-ms-dialog-tip {
    margin-left: 37px;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    color: #595959;
  }

  .dialog-footer {
    position: absolute;
    right: 16px;
    bottom: 12px;
  }
}
</style>
