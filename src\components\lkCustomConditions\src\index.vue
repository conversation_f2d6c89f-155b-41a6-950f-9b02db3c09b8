<template>
  <div class="custom-conditions-control-row">
    <LkSearchConditions
      ref="searchConditions"
      :condition="condition"
      :conditionList="conditionList"
      :teleported="true"
      @handleConditionFirstData="handleConditionFirstData"
      @handleChangeConditions="handleChangeConditions"
    >
      <template #fieldTypeSlot>
        <el-select-v2
          v-if="
            !['IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
              searchConditionsForm.operator
            )
          "
          v-model="searchConditionsForm.fieldTypeEnum"
          class="custom-conditions-operator-control"
          filterable
          :options="FieldTypeSelectOptions"
          clearable
          @change="handleSelectFieldTypeEnum"
        >
          <template #label="{ label }">
            {{ t(label) }}
          </template>
          <template #default="{ item }">
            {{ t(item.label) }}
          </template>
        </el-select-v2>
        <el-cascader
          v-if="searchConditionsForm.fieldTypeEnum === 'OTHER_FIELD'"
          ref="TwoCascaderRef"
          v-model="searchConditionsForm.twoData"
          style="width: 120px; margin-right: 5px"
          :options="planSettingTwoSelect"
          :show-all-levels="false"
          @change="handleSelectTwoCascader"
        />
      </template>
      <template #workOrderSlot>
        <!-- 第五个控件：工单范围 -->
        <el-select-v2
          v-show="workOrderRangeVisible"
          v-model="searchConditionsForm.workOrderRange"
          class="custom-conditions-operator-control"
          filterable
          :options="WorkOrderRangeOptions"
          clearable
        >
          <template #label="{ label }">
            {{ t(label) }}
          </template>
          <template #default="{ item }">
            {{ t(item.label) }}
          </template>
        </el-select-v2>
      </template>
    </LkSearchConditions>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, nextTick, watchEffect, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import LkSearchConditions from "@/components/lkSearchConditions";
import {
  WorkOrderRangeOptions,
  MathOperatorOptions,
  FieldTypeSelectOptions
} from "@/components/lkSearchConditions/src/enum";
import { getSccsLableAllFields, getWidgetItem } from "@/api/sccs";
import { computedAsync } from "@vueuse/core";

const props = defineProps({
  condition: {
    type: Object as PropType<any>,
    default: () => {}
  },
  conditionList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  labelConditionMap: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();
const route = useRoute();
const searchConditions = ref<HTMLElement | any>(null);
const TwoCascaderRef = ref<HTMLElement | any>(null);
const searchConditionsForm = ref<any>({
  firstData: [],
  firstDataObject: {},
  twoData: [],
  twoDataObject: {},
  operator: "",
  fieldTypeEnum: "CUSTOM",
  workOrderRange: null,
  rangeDateType: null,
  customerDateType: null,
  value: ""
});
const MathOperatorSelectOptions = ref<any>([]);
const firstOpertatorSelectControl = ref<boolean>(false);
const planSettingSelect = ref<any[]>([]);
const widgetForm = ref<any>({});
const firstCascaderData = ref<any>({});
const workOrderRangeVisible = ref<boolean>(false);
const firstFormPubId = ref<string>("");

const emit = defineEmits<{
  (e: "handleChange", data: any): void;
  (e: "handleChangeConditions", data: any): void;
}>();

const fetchData = async () => {
  return new Promise(async (resolve, reject) => {
    const { sccsId } = route.query;
    const { firstDataObject, fieldTypeEnum } = props.condition;
    if (firstDataObject) {
      const { data } = await getSccsLableAllFields({
        sccsId: sccsId as string,
        fieldType: firstDataObject.fieldType,
        workOrder: false
      });
      resolve(data);
    }
  });
};

const planSettingTwoSelect = computedAsync(async () => {
  const { sccsId } = route.query;
  const { firstDataObject } = props.condition;
  if (firstDataObject) {
    const { data } = await getSccsLableAllFields({
      sccsId: sccsId as string,
      fieldType: firstDataObject.fieldType,
      workOrder: false
    });
    return data;
  }
}, []);

const handleConditionFirstData = (
  bool: boolean,
  data: any,
  formPubId: string,
  workOrderRangeVal?: string
): void => {
  nextTick(() => {
    workOrderRangeVisible.value = bool;
    firstCascaderData.value = data;
    firstFormPubId.value = formPubId;

    if (workOrderRangeVal) {
      searchConditionsForm.value.workOrderRange = workOrderRangeVal;
    }
  });
};

const handleChangeConditions = (conditionData: any) => {
  Object.assign(searchConditionsForm.value, conditionData);
};

/**
 * 第三个控件切换值
 */
const handleSelectFieldTypeEnum = async () => {
  handleGetSccsLabelFields();
  searchConditionsForm.value.twoData = [];
  searchConditionsForm.value.workOrderRange = null;
  searchConditionsForm.value.rangeDateType = null;
  searchConditionsForm.value.customerDateType = null;
  searchConditionsForm.value.value = "";
  const { fieldTypeEnum } = searchConditionsForm.value;
  const { field } = firstCascaderData.value.data;
  if (fieldTypeEnum === "CUSTOM" && workOrderRangeVisible.value) {
    const { data } = await getWidgetItem({
      widgetId: field,
      formPubId: firstFormPubId.value
    });
    widgetForm.value = data;
  }
  searchConditions.value.handleUpdateFieldTypeEnum(
    searchConditionsForm.value.fieldTypeEnum
  );
};

/**
 * 获取第二个级联控件允许的下拉值
 */
const handleGetSccsLabelFields = async () => {
  const { sccsId } = route.query;
  const { firstData, fieldTypeEnum } = searchConditionsForm.value;
  const { fieldType } = firstCascaderData.value.data;
  if (firstData.length > 0 && fieldTypeEnum === "OTHER_FIELD") {
    const { data } = await getSccsLableAllFields({
      sccsId: sccsId as string,
      fieldType: fieldType,
      workOrder: false
    });
    planSettingTwoSelect.value = data;
  }
};

/**
 * 第二个级联控件数据选择
 */
const handleSelectTwoCascader = () => {
  searchConditionsForm.value.twoDataObject =
    TwoCascaderRef.value.getCheckedNodes()[0].data;
};

watch(
  searchConditionsForm,
  async () => {
    handleSetTableRowConditions();
  },
  {
    deep: true
  }
);

watch(
  props.condition,
  newVal => {
    if (newVal && newVal.firstDataObject && Object.keys(newVal).length > 0) {
      searchConditionsForm.value = newVal;
      nextTick(async () => {
        const { fieldType } = newVal.firstDataObject;

        if (["DrInputNumber", "DrRate", "DrPercentage"].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions["number"];
          firstOpertatorSelectControl.value = false;
        } else if (["DrDatePicker"].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions["date"];
          firstOpertatorSelectControl.value = true;
        } else {
          MathOperatorSelectOptions.value = MathOperatorOptions["widget"];
          firstOpertatorSelectControl.value = false;
        }
      });
    }
  },
  { deep: true, immediate: true }
);

watchEffect(() => {
  if (props.conditionList) {
    planSettingSelect.value = props.conditionList;
  }
});

const handleSetTableRowConditions = () => {
  emit("handleChangeConditions", searchConditionsForm.value);
};

defineExpose({
  searchConditionsForm: searchConditionsForm.value
});
</script>
<style lang="scss" scoped>
.custom-conditions-control-row {
  display: flex;

  ::v-deep(.el-cascader) {
    width: 140px;
    margin-right: 10px;
  }

  ::v-deep(.el-select) {
    margin-right: 10px;
  }

  ::v-deep(.el-date-editor) {
    margin-right: 10px;
  }

  ::v-deep(.el-input) {
    margin-right: 10px;
  }

  .conditions-form-input {
    width: 100px;

    ::v-deep(.el-input-group__append) {
      padding: 0 10px;
    }
  }

  .custom-conditions-operator-control {
    display: inline-flex;
    width: 110px;
  }

  .custom-conditions-operator-small-control {
    width: 80px;
  }

  .custom-conditions-col {
    display: inline-flex;
  }
}
</style>
