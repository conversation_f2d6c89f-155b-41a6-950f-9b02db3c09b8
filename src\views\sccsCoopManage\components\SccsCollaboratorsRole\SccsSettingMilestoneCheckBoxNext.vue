<template>
  <div class="role-per-col">
    <el-checkbox
      v-model="msCheckAll"
      class="checkbox-parent checkbox-group-col"
      :indeterminate="msIndeterminate"
      @change="handleMsCheckAll"
    >
      <div class="checkbox-span-body">
        <div class="checkbox-flex">{{ milestoneToolPerm.label }}</div>
        <div class="checkbox-flex">
          <el-radio-group
            v-model="msPermissionKey"
            :disabled="orderScope !== 'ALL_COOP_ORDER'"
          >
            <el-radio
              v-for="msTool in milestoneToolPerm.orderScopeList"
              :key="msTool.value"
              :value="msTool.value"
            >
              {{ msTool.label }}
            </el-radio>
          </el-radio-group>
        </div>
      </div>
    </el-checkbox>

    <div class="milestone-setting-body">
      <div
        v-for="msToolPerm in milestoneToolPerm.childrenList"
        :key="msToolPerm.key"
        class="milestone-setting-col"
      >
        <el-collapse v-model="msCollapseActive">
          <el-collapse-item :title="msToolPerm.label" :name="msToolPerm.key">
            <template #title>
              <div @click.stop>
                <el-checkbox
                  v-model="msToolPermObject[msToolPerm.key].msCheckAll"
                  class="checkbox-parent checkbox-group-col"
                  :indeterminate="
                    msToolPermObject[msToolPerm.key].msIndeterminate
                  "
                  @change="
                    bool => handleMilestoneCheckAll(bool, msToolPerm.key)
                  "
                >
                  <div class="checkbox-span-body">
                    <div class="checkbox-flex">{{ msToolPerm.label }}</div>
                  </div>
                </el-checkbox>
              </div>
            </template>

            <el-checkbox-group
              v-model="msToolPermObject[msToolPerm.key].msPermissionKeyList"
              @change="
                milestoneKey =>
                  handleChangeMilestoneChecked(milestoneKey, msToolPerm.key)
              "
            >
              <el-row :gutter="20">
                <el-col
                  v-for="msPermissionItem in msToolPerm.childrenList"
                  :key="msPermissionItem.key"
                  :span="8"
                >
                  <el-checkbox
                    class="checkbox-group-col"
                    :label="msPermissionItem.label"
                    :value="msPermissionItem.key"
                    @change="
                      checked =>
                        handleChangeMilestoneItemChecked(
                          checked,
                          msToolPerm.key,
                          msPermissionItem.key
                        )
                    "
                  >
                    <div class="checkbox-span">
                      {{ msPermissionItem.label }}
                      <el-tooltip
                        v-if="msPermissionItem.remark"
                        effect="dark"
                        :content="msPermissionItem.remark"
                        placement="top"
                        :show-after="500"
                      >
                        <i class="iconfont link-explain font14 color80" />
                      </el-tooltip>
                    </div>
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { cloneDeep } from "lodash-es";

const msCheckAll = ref<boolean>(false);
const msIndeterminate = ref<boolean>(false);
const msPermissionKey = ref<string>("");
const msCollapseActive = ref<string[]>([""]);
const msToolPermObject = ref<any>({});

const emit = defineEmits(["handleUpdateMsPermissionKey"]);

const props = defineProps({
  sccsSettingData: {
    type: Object as PropType<any>
  },
  msRolePermData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderScope: {
    type: String as PropType<string>,
    default: ""
  }
});

const milestoneToolPerm = computed(() => {
  return props.sccsSettingData;
});

/**
 * 里程碑最大权限
 */
const milestonePermItem = computed(() => {
  return {
    permissionKey: milestoneToolPerm.value.key,
    orderScope: msPermissionKey.value
  };
});

const handleMsCheckAll = (bool: boolean) => {
  for (let [msId, msTool] of Object.entries(msToolPermObject.value)) {
    const msItemData = milestoneToolPerm.value.childrenList.find(
      msData => msData.key === msId
    );
    const msPermissionKeyLists = msItemData.childrenList.map(
      msToolData => msToolData.key
    );
    msToolPermObject.value[msId] = {
      msCheckAll: bool,
      msIndeterminate: false,
      msPermissionKeyList: bool ? msPermissionKeyLists : []
    };
  }
  emit("handleUpdateMsPermissionKey", bool);
};

/**
 * 单个里程碑权限勾选
 */
const handleMilestoneCheckAll = (checked: any, msId: any) => {
  const msItemData = milestoneToolPerm.value.childrenList.find(
    msData => msData.key === msId
  );
  const msPermissionKeyLists = msItemData.childrenList.map(
    msToolData => msToolData.key
  );
  msToolPermObject.value[msId].msPermissionKeyList = checked
    ? msPermissionKeyLists
    : [];

  msToolPermObject.value[msId].msCheckAll =
    msItemData.childrenList.length ===
    msToolPermObject.value[msId].msPermissionKeyList.length;
  msToolPermObject.value[msId].msIndeterminate =
    msItemData.childrenList.length !==
      msToolPermObject.value[msId].msPermissionKeyList.length &&
    msToolPermObject.value[msId].msPermissionKeyList.length !== 0;
};

/**
 * 里程碑下的单个权限勾选
 */
const handleChangeMilestoneChecked = (value: any, msId: string) => {
  msToolPermObject.value[msId].msPermissionKeyList = value;
};

const handleChangeMilestoneItemChecked = (
  checked: any,
  msId: string,
  value: any
) => {
  const msPermissionKeyList = cloneDeep(
    msToolPermObject.value[msId].msPermissionKeyList
  );

  checked
    ? handleCheckedLinkageRules(msPermissionKeyList, msId)
    : handleUnCheckedLinkageRules(value, msId);

  const msItemData = milestoneToolPerm.value.childrenList.find(
    msData => msData.key === msId
  );

  msToolPermObject.value[msId].msCheckAll =
    msItemData.childrenList.length ===
    msToolPermObject.value[msId].msPermissionKeyList.length;
  msToolPermObject.value[msId].msIndeterminate =
    msItemData.childrenList.length !==
      msToolPermObject.value[msId].msPermissionKeyList.length &&
    msToolPermObject.value[msId].msPermissionKeyList.length !== 0;
};

const handleUnCheckedLinkageRules = (value: any, msId: string) => {
  if (value === "view_work_order") {
    msToolPermObject.value[msId].msPermissionKeyList = [];
  } else if (value === "view_ms_reply") {
    const index = msToolPermObject.value[msId].msPermissionKeyList.findIndex(
      msKey => msKey === "assign_milestone_approval"
    );
    msToolPermObject.value[msId].msPermissionKeyList.splice(index, 1);
  }
};

const handleCheckedLinkageRules = (value: any, msId: string) => {
  if (
    value.includes("assign_milestone_approval") &&
    !value.includes("view_ms_reply")
  ) {
    // 有里程碑批复/指派的权限但是没有查看里程碑批复的权限
    msToolPermObject.value[msId].msPermissionKeyList.push("view_ms_reply");
    if (!value.includes("view_work_order")) {
      // 判断是否有查看工单的权限，没有则加入
      msToolPermObject.value[msId].msPermissionKeyList.push("view_work_order");
    }
  }

  if (!value.includes("view_work_order") && value.includes("view_ms_reply")) {
    // 有查看里程碑批复的权限但是没有查看工单的权限则加入
    msToolPermObject.value[msId].msPermissionKeyList.push("view_work_order");
  }

  if (
    (value.includes("assign_work_order_approval") ||
      value.includes("assign_work_order_collection")) &&
    !value.includes("view_work_order")
  ) {
    // 有工单批复/指派，采集/指派的权限但是没有查看工单的权限则加入
    msToolPermObject.value[msId].msPermissionKeyList.push("view_work_order");
  }
};

const handleOtainMilestoneRoleData = () => {
  const milestoneRolePermItemList = Object.entries(msToolPermObject.value).map(
    (msToolData: any) => {
      return {
        msId: msToolData[0],
        permissionKeyList: msToolData[1].msPermissionKeyList
      };
    }
  );
  return {
    milestoneRolePermItemList: milestoneRolePermItemList,
    milestonePermItem: milestonePermItem.value
  };
};

watch(
  () => props.sccsSettingData.childrenList,
  () => {
    if (
      props.sccsSettingData.childrenList &&
      props.sccsSettingData.childrenList.length > 0
    ) {
      let msPermissionObject = {};
      for (let msPermission of props.sccsSettingData.childrenList) {
        msPermissionObject[msPermission.key] = {
          msCheckAll: false,
          msIndeterminate: false,
          msPermissionKeyList: []
        };
        msCollapseActive.value.push(msPermission.key);
      }
      msToolPermObject.value = msPermissionObject;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => msToolPermObject,
  () => {
    let msToolPermObjectData = cloneDeep(Object.values(msToolPermObject.value));
    const isMsToolChecked = msToolPermObjectData
      .map((msTool: any) => msTool.msCheckAll)
      .reduce((acc, curr) => acc && curr, true);

    if (isMsToolChecked) {
      msIndeterminate.value = false;
      msCheckAll.value = true;
    } else {
      const MsToolUnChecked = msToolPermObjectData
        .map((msTool: any) => msTool.msCheckAll)
        .every(msToolBool => msToolBool === false);

      const MsToolIndeterminate = msToolPermObjectData
        .map((msTool: any) => msTool.msIndeterminate)
        .every(msToolBool => msToolBool === false);

      if (MsToolUnChecked && MsToolIndeterminate) {
        msIndeterminate.value = false;
        msCheckAll.value = false;
      } else {
        msIndeterminate.value = true;
      }
    }

    let msPermissionKeyLists = [];
    for (let msToolRow of msToolPermObjectData) {
      msPermissionKeyLists = msPermissionKeyLists.concat(
        //@ts-ignore
        msToolRow.msPermissionKeyList
      );
    }
    emit("handleUpdateMsPermissionKey", msPermissionKeyLists.length > 0);
  },
  {
    deep: true
  }
);

watch(
  () => props.orderScope,
  () => {
    msPermissionKey.value = props.orderScope;
    if (!props.orderScope) {
      msPermissionKey.value = undefined;
      Object.values(msToolPermObject.value).forEach((msTool: any) => {
        msTool.msCheckAll = false;
        msTool.msIndeterminate = false;
        msTool.msPermissionKeyList = [];
      });
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.msRolePermData,
  () => {
    msPermissionKey.value = props.msRolePermData?.milestonePermItem?.orderScope;
    for (let msRole of props.msRolePermData.milestoneRolePermItemList) {
      const msItemData = milestoneToolPerm.value.childrenList.find(
        msData => msData.key === msRole.msId
      );
      msToolPermObject.value[msRole.msId].msPermissionKeyList =
        msRole.permissionKeyList;
      msToolPermObject.value[msRole.msId].msIndeterminate =
        msRole.permissionKeyList.length !== msItemData.childrenList;

      if (msRole.permissionKeyList.length === 0) {
        msToolPermObject.value[msRole.msId].msCheckAll = false;
        msToolPermObject.value[msRole.msId].msIndeterminate = false;
      } else {
        msToolPermObject.value[msRole.msId].msCheckAll =
          msRole.permissionKeyList.length === msItemData.childrenList;
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => msToolPermObject,
  () => {
    const hasCheckedValue = Object.values(msToolPermObject.value).some(
      (value: any) => {
        return value.msPermissionKeyList.length > 0;
      }
    );
    if (hasCheckedValue) {
      msPermissionKey.value = props.orderScope;
    } else {
      msPermissionKey.value = undefined;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  handleOtainMilestoneRoleData
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  margin-bottom: 10px;
  font-weight: bolder;
}

.role-per-col {
  box-sizing: border-box;
}

.checkbox-group-col {
  width: 100% !important;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  ::v-deep(.el-checkbox__label) {
    flex: 1 !important;

    .checkbox-span-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .checkbox-flex {
        flex: 1;

        &:nth-child(2) {
          padding-right: 20px;
          text-align: right;
        }
      }
    }
  }
}

.milestone-setting-body {
  .milestone-setting-col {
    padding: 0 20px;

    ::v-deep(.el-collapse) {
      border: 0 none !important;

      .el-collapse-item {
        .el-collapse-item__wrap {
          border: 0 none !important;

          .el-collapse-item__content {
            padding: 0 20px 10px !important;
          }
        }
      }
    }
  }
}
</style>
