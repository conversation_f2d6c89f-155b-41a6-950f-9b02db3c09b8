<template>
  <div class="order-file-preview-body">
    <el-tooltip
      effect="dark"
      :content="fileName"
      placement="top"
      :show-after="500"
    >
      <svg
        v-if="['docx', 'doc'].includes(fileType)"
        class="icon svg-icon"
        aria-hidden="true"
        :show-after="500"
        @click="handleOpenFilePreview('VueOfficeDocx')"
      >
        <use xlink:href="#link-word" />
      </svg>
      <svg
        v-else-if="['ppt', 'pptx'].includes(fileType)"
        class="icon svg-icon"
        aria-hidden="true"
        @click="handleOpenFilePreview('VueOfficePptx')"
      >
        <use xlink:href="#link-ppt" />
      </svg>
      <svg
        v-else-if="
          [
            'bmp',
            'gif',
            'jpeg',
            'jpg',
            'pjpeg',
            'png',
            'tiff',
            'webp'
          ].includes(fileType)
        "
        class="icon svg-icon"
        aria-hidden="true"
        @click="handleOpenFilePreview('ElImage')"
      >
        <use xlink:href="#link-photo" />
      </svg>
      <svg
        v-else-if="['pdf'].includes(fileType)"
        class="icon svg-icon"
        aria-hidden="true"
        @click="handleOpenFilePreview('VueOfficePdf')"
      >
        <use xlink:href="#link-pdf" />
      </svg>
      <svg
        v-else-if="['xlsx', 'xls'].includes(fileType)"
        class="icon svg-icon"
        aria-hidden="true"
        @click="handleOpenFilePreview('VueOfficeExcel')"
      >
        <use xlink:href="#link-excel1" />
      </svg>
      <svg
        v-else-if="
          ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', '3gp', 'webm'].includes(
            fileType
          )
        "
        class="icon svg-icon"
        aria-hidden="true"
        @click="handleOpenFilePreview('VueOfficeExcel')"
      >
        <use xlink:href="#link-video" />
      </svg>
      <svg
        v-else
        class="icon svg-icon"
        aria-hidden="true"
        @click="handleOpenFilePreview()"
      >
        <use xlink:href="#link-unknown-format" />
      </svg>
    </el-tooltip>
  </div>
</template>
<script setup lang="ts">
import { ref, watchEffect, getCurrentInstance } from "vue";
import { ElTooltip } from "element-plus";

const fileType = ref<string>("");
const fileName = ref<string>("");
const props = defineProps({
  filePreview: {
    type: Object as PropType<any>,
    default: () => {}
  },
  fileList: {
    type: Array as PropType<any>,
    default: () => []
  }
});

watchEffect(() => {
  fileName.value = props.filePreview.name;
  fileType.value = fileName.value
    ? fileName.value.split(".").slice(-1)[0].toLocaleLowerCase()
    : "";
});

const { proxy } = getCurrentInstance();

// todo 验厂结束后优化，需要梳理为什么文件上传格式异常
const handleOpenFilePreview = async (componentName?: any) => {
  // @ts-ignore
  await proxy.$filePreview.preview({
    componentName: componentName,
    fileData: props.filePreview.url
      ? props.filePreview
      : { name: props.filePreview.name, url: props.filePreview.response.data },
    fileList: props.fileList
  });
};
</script>
<style lang="scss">
.order-file-preview-body {
  display: inline-flex;
  cursor: pointer;

  .svg-icon {
    align-items: center;
    width: 24px;
    height: 24px;
  }
}
</style>
