<template>
  <div>
    <div
      v-if="widget.props && widget.props.header && widgetCardConfig"
      class="widget-card-title"
    >
      {{ widgetHeader(widget) }}
    </div>
    <el-row
      :gutter="widget.props && widget.props.gutter ? widget.props.gutter : 20"
    >
      <el-col
        v-for="widgetChild in widgetChildrenList"
        :key="widgetChild['_fc_id']"
        :span="widgetChild.props.span"
        :offset="widgetChild.props.offset"
        :push="widgetChild.props.push"
        :pull="widgetChild.props.pull"
      >
        <div
          v-for="widgetChildItem in widgetChild?.children"
          :key="widgetChildItem.id"
          style="margin-bottom: 14px"
        >
          <div
            v-show="
              !(
                visibleFormExpression.hasOwnProperty(widgetChildItem._fc_id) &&
                visibleFormExpression[widgetChildItem._fc_id] === false
              )
            "
          >
            <div v-if="widgetChildItem" class="widget-form-label">
              <el-tooltip
                v-if="widgetChildItem.info"
                :content="widgetInfoLange(widgetChildItem)"
                placement="top"
                :show-after="500"
                popper-class="widget-popper-label-class"
              >
                <i class="iconfont link-explain font12" />
              </el-tooltip>
              {{ widgetTitleLange(widgetChildItem) }}
            </div>
            <div v-if="widgetChild.children" class="widget-form-value">
              <div
                v-if="widgetChildItem.type === 'DrPlaceholder'"
                :style="{
                  display: 'inline-block',
                  width: '100%',
                  height: `${widgetChildItem.props.height}px`
                }"
              />
              <WidgetCard
                v-if="widgetChildItem.type === 'DrCard'"
                :widgetData="widgetData"
                :widget="widgetChild.children"
                :visibleFormExpression="visibleFormExpression"
              />
              <LkWidgetForm
                v-else
                :widgetData="
                  getWidgetControlData(
                    widgetChildItem['_fc_id'],
                    widgetChildItem
                  )
                "
                :widgetDataRow="
                  getWidgetControlDataVal(widgetChildItem['_fc_id'])
                "
                :widgetTableData="
                  getWidgetControlDataVal(widgetChildItem._fc_id)
                "
                :widgetConfig="widgetChildItem"
                :visibleFormExpression="visibleFormExpression"
              />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import LkWidgetForm from "@/components/lkWidgetForm";
import { storageLocal } from "@pureadmin/utils";

const props = defineProps({
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widget: {
    type: Object as PropType<any>,
    default: () => {}
  },
  visibleFormExpression: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const widgetCardVisible = ref<boolean>(true);

const widgetInfoLange = computed(() => {
  return widget => {
    const translationLang = storageLocal().getItem("translationLang");
    return translationLang === "en"
      ? widget.props?.infoEn || widget.info
      : widget.info;
  };
});

const widgetTitleLange = computed(() => {
  return widget => {
    const translationLang = storageLocal().getItem("translationLang");
    return translationLang === "en"
      ? widget.props?.titleEn || widget.title
      : widget.title;
  };
});

const widgetHeader = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  return widget => {
    return translationLang === "en"
      ? widget.props?.headerEn || widget.props?.header
      : widget.props?.header;
  };
});

const widgetCardConfig = computed(() => {
  return !(
    props.widget.children.filter(
      child => child.children && child.children.length > 0
    )?.length === 0
  );
});

const getWidgetControlData = computed(() => {
  return (widgetId: string, widget: any): string => {
    if (props.widgetData instanceof Array) {
      const widgetData = props.widgetData.find(
        widgetRow => widgetRow.widgetId === widgetId
      );

      return widget && widgetData
        ? [
            "DrFilesUpload",
            "DrImagesUpload",
            "DrExchangeRates",
            "DrCheckbox",
            "DrRelateCard"
          ].includes(widget.type)
          ? widgetData.obj
          : widgetData.label
        : "";
    } else {
      return "";
    }
  };
});

const getWidgetControlDataVal = computed(() => {
  return (widgetId: string): any => {
    if (props.widgetData instanceof Array) {
      const widgetData = props.widgetData.find(
        widgetRow => widgetRow.widgetId === widgetId
      );
      return widgetData ? widgetData : "";
    } else {
      return "";
    }
  };
});

const widgetChildrenList = computed(() => {
  return props.widget.children;
});

watch(
  () => props.visibleFormExpression,
  () => {
    const cardColFormItemIds = props.widget.children
      .map(col => col.children && col.children[0] && col.children[0]._fc_id)
      .filter(col => !!col);
    for (let colId of cardColFormItemIds) {
      if (
        !props.visibleFormExpression.hasOwnProperty(colId) ||
        !!props.visibleFormExpression[colId]
      ) {
        widgetCardVisible.value = true;
        return;
      }
    }
    widgetCardVisible.value = false;
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.widget-card-title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bolder;
  line-height: 24px;
  color: #262626;
  border-bottom: 1px solid #e6e6e6;
}
</style>
