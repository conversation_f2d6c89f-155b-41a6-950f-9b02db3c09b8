<template>
  <div class="create-team-dialog">
    <LkDialog
      ref="LkDialogRef"
      :title="
        props.editStatus === 'add'
          ? t('trade_perCenter_createTeam')
          : t('trade_team_editTeamInfo')
      "
      @confirm="confirm"
      @close="handleClose"
    >
      <template #default>
        <div v-if="props.editStatus === 'add'" class="create-team-dialog-title">
          {{ t("trade_team_createTeamTip") }}
        </div>
        <div class="create-team-dialog-form">
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="rules"
            label-position="top"
            label-width="auto"
          >
            <el-form-item :label="t('trade_team_teamName')" prop="teamName">
              <el-input
                v-model="ruleForm.teamName"
                :placeholder="t('trade_team_enterTeamName')"
                show-word-limit
                maxlength="100"
                clearable
              />
            </el-form-item>
            <el-form-item
              :label="t('trade_team_teamShortName')"
              prop="teamShortName"
            >
              <el-input
                v-model="ruleForm.teamShortName"
                show-word-limit
                :placeholder="t('trade_team_enterTeamShortName')"
                maxlength="20"
                clearable
              />
            </el-form-item>
            <el-form-item
              v-if="props.editStatus === 'add'"
              :label="t('trade_team_teamHeader')"
            >
              <LKUpload ref="LKUploadRef" />
            </el-form-item>
          </el-form>
        </div>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import LkDialog from "@/components/lkDialog/index";
import LKUpload from "@/components/lkUpload/index";
import { TeamCreateType, ApiTeamCreateType } from "../types/types.d";
import type { FormInstance, FormRules } from "element-plus";
import { createTeam, updateTeam } from "@/api/team";
import { useI18n } from "vue-i18n";
import { message } from "@/utils/message";

interface PropsType {
  editStatus?: string | null;
  teamInfo?: ApiTeamCreateType;
}

const props = withDefaults(defineProps<PropsType>(), {
  editStatus: "add",
  teamInfo: () => ({
    teamName: "",
    teamShortName: "",
    teamAvatar: ""
  })
});

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const LKUploadRef = ref<any>(null);
const ruleFormRef = ref<FormInstance>();
let ruleForm = reactive<ApiTeamCreateType>({
  teamName: "",
  teamShortName: "",
  teamAvatar: ""
});
const rules = reactive<FormRules<TeamCreateType>>({
  teamName: [
    {
      required: true,
      message: t("trade_team_enterTeamName"),
      trigger: "blur"
    }
  ],
  teamShortName: [
    {
      required: true,
      message: t("trade_team_enterTeamShortName"),
      trigger: "blur"
    }
  ]
});

const emit = defineEmits<{
  (e: "createSuccess", data: any): void;
}>();

const open = (): void => {
  if (props.editStatus === "edit") {
    // ruleForm = reactive(props.teamInfo);
    ruleForm.teamName = props.teamInfo.teamName;
    ruleForm.teamShortName = props.teamInfo.teamShortName;
    ruleForm.id = props.teamInfo.id;
  }
  LkDialogRef.value.open();
};

const confirm = (): void => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      if (props.editStatus === "add") {
        ruleForm.teamAvatar = LKUploadRef.value.imageUrl;
      }

      const response =
        props.editStatus === "add"
          ? await createTeam(ruleForm)
          : await updateTeam(ruleForm);
      if (response.code === 0) {
        message(t("trade_common_dealSuccess"), {
          customClass: "el",
          type: "success"
        });
        ruleFormRef.value.resetFields();
        LkDialogRef.value.close();
        emit("createSuccess", response.data);
      }
    }
  });
};

const handleClose = (): void => {
  ruleForm.teamName = "";
  ruleForm.teamShortName = "";
  ruleForm.teamAvatar = "";
  ruleFormRef.value.clearValidate();
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.create-team-dialog {
  .create-team-dialog-title {
    padding: 12px;
    margin-bottom: 32px;
    font-size: 14px;
    line-height: 20px;
    color: #fa8d0a;
    background: #fdf6ec;
    border-radius: 4px;
  }
}
</style>
