<template>
  <div
    class="order-manage-body"
    :style="{ height: useAppStoreHook().viewportSize.height - 41 + 'px' }"
  >
    <div
      class="order-manage-left"
      :class="[
        hide ? 'hideLeft' : '',
        screenWidth <= 1366 ? 'Screen_1366' : ''
      ]"
    >
      <OrderManageView
        ref="orderManageViewRef"
        @handleLoadedView="handleInitPageData"
        @handleLoadedAnalysis="handleLoadedAnalysis"
        @handleClearTableSelectedRecords="
          () => orderTableRef.handleGetCheckboxRecords([])
        "
      />
      <div class="anchor-icon-left" @click="hideLeft">
        <i class="iconfont link-pack-up" />
      </div>
    </div>
    <div class="order-manage-right">
      <div v-if="hide" class="anchor-icon-right" @click="hideLeft">
        <i class="iconfont link-expand" />
      </div>
      <div
        v-show="!showIframe"
        id="order-manage-table-container"
        v-loading="loading"
        :element-loading-text="`${t('trade_common_loadingText')}...`"
        class="order-manage-table-container"
      >
        <OrderGridTable
          ref="orderTableRef"
          :tableConfig="orderWidgetFields"
          :tableTotal="tableTotal"
          :relationType="relationType"
          :relationTodo="relationTodo"
          :milestoneIdList="milestoneIdList"
          :colStatisticsItemList="colStatisticsItemList"
          :colStatisticsSearchCondition="colStatisticsSearchCondition"
          :presentView="presentView"
          :presentViewInfo="presentViewInfo"
          :currentScreenWidth="screenWidth"
          :orderPermissionList="orderPermissionList"
          @handleKeyWordSearch="handleKeyWordSearch"
          @handleEditOrderDetail="handleEditOrderDetail"
          @handlePageSizeChanged="handlePageSizeChanged"
          @handleReloadTableData="handleInitTable"
          @handleSearchTableByConditions="handleSearchTableByConditions"
          @handleAddOrderView="handleAddOrderView"
          @handleSortTable="handleSortTable"
          @handleUpdateTableConfig="handleSortTable"
          @handleChangeColumnVisible="handleChangeColumnVisible"
        >
          <template #batchOperate="{ tableSelectedNumber, tableSelectedList }">
            <OrderBatchOperate
              :currentSccsId="currentSccsId"
              :currentScreenWidth="screenWidth"
              :presentView="presentView"
              :selectedNumber="tableSelectedNumber"
              :tableSelectedList="tableSelectedList"
              @handleReloadTableData="handleInitTable"
            />
          </template>
          <template #orderOperate>
            <el-tooltip
              :content="t('trade_order_createOrder')"
              :disabled="buttonTextVisible"
            >
              <el-button
                v-if="getUserPermission('create_order')"
                class="order-manage-right-table-btn"
                type="primary"
                color="#0070D2"
                @click="handleAddOrder()"
              >
                <i
                  class="iconfont link-add"
                  :class="{ 'no-margin': screenWidth <= 1366 }"
                />{{ buttonTextVisible ? t("trade_order_createOrder") : "" }}
              </el-button>
            </el-tooltip>
            <!-- <span class="order-manage-btn">
              <i class="iconfont link-import" />{{ t("trade_common_import") }}
            </span> -->
            <el-tooltip
              :content="t('trade_common_export')"
              :disabled="buttonTextVisible"
            >
              <el-dropdown
                trigger="click"
                class="order-manage-btn"
                @command="handleOrderImport"
              >
                <span class="el-dropdown-link">
                  <i class="iconfont link-export" />
                  {{ buttonTextVisible ? t("trade_common_export") : "" }}
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="all_order">
                      <i class="iconfont link-export" />
                      {{ t("trade_sccs_orderView") }}
                    </el-dropdown-item>
                    <el-dropdown-item command="order_view">
                      <i class="iconfont link-export" />
                      {{ t("trade_sccs_thatorderView") }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-tooltip>
          </template>
        </OrderGridTable>
      </div>
      <div v-show="showIframe" class="order-manage-iframe-container">
        <iframe :src="currentAnalysisPanelUrl" frameborder="0" />
      </div>
    </div>
  </div>
  <OrderViewRenameDialog ref="OrderViewRenameDialogRef" />
  <OrderViewTemplateDialog
    ref="OrderViewTemplateDialogRef"
    @handleCreateOrder="handleCreateOrder"
  />
</template>
<script lang="ts" setup>
import { computed, provide, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { cloneDeep, debounce, storageSession } from "@pureadmin/utils";
import { getUserRolePerm } from "@/utils/auth";
import { useAppStoreHook } from "@/store/modules/app";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import OrderGridTable from "./components/OrderGridTable.vue";
import OrderManageView from "./components/OrderManageView.vue";
import OrderViewRenameDialog from "./components/OrderViewRenameDialog.vue";
import OrderViewTemplateDialog from "./components/OrderViewTemplateDialog.vue";
import OrderBatchOperate from "./components/orderManageList/OrderBatchOperate.vue";
import {
  getOrderList,
  getOrderViewById,
  exportExcel,
  getWorkOrderList,
  getWorkOrderColumnStatistics,
  getOrderColumnStatistics
} from "@/api/order";
import { getOrderPermission } from "@/utils/common";
import { useResizeObserver } from "@vueuse/core";

const { t, locale } = useI18n();
const route = useRoute();
const router = useRouter();
const orderTableRef = ref<any | HTMLElement>(null);
const orderManageViewRef = ref<any | HTMLElement>(null);
const OrderViewRenameDialogRef = ref<any | HTMLElement>(null);
const loading = ref<boolean>(true);
const orderWidgetFields = ref<any>([]);
const tableTotal = ref<number>(0); // 表格分页的总条数
const OrderViewTemplateDialogRef = ref<any>(null);
const presentView = ref<any>({}); //当前选中的视图数据
const presentViewInfo = ref<any>({}); // 当前视图的配置信息
const keyword = ref<string>(""); // 搜索的关键字
const pageInfo = ref<any>({}); // 表格的分页数据
const tableSortList = ref<any>([]); // 表格的排序数据
const sccsConditionList = ref<any>([]); // 表格的过滤数据
const milestoneIdList = ref<string[]>([]); // 里程碑的id集合
const currentSccsId = ref<string>(""); // 当前sccsId
const presentHiddenWidgetNames = ref<string[]>([]); // 字段配置隐藏的字段名集合
const orderViewList = ref<any>([]);
const hide = ref(false);
const buttonTextVisible = ref(false);
const showWhite = ref(false);
const relationTodo = ref<boolean>(true); // 是互用于还是互用自
const colStatisticsItemList = ref<any[]>([]);
const userRolePermission = ref<string[]>([]);
const orderPermissionList = ref<any>({}); // 是一个订单为key的对象
const currentAnalysisPanelUrl = ref<string>(""); // 当前选中的统计面板URL
const currentAnalysisPanelId = ref<string>(""); // 当前选中的统计面板ID
const hideLeft = () => {
  hide.value = !hide.value;
  showWhite.value = false;
};
// 初始化页面数据判断是否从详情页面跳转过来的
const relationType = ref<any>("");

const getUserPermission = computed(() => {
  return permissionKey => {
    return userRolePermission.value.includes(permissionKey);
  };
});

const colStatisticsSearchCondition = computed(() => {
  return {
    keyword: keyword.value,
    sccsConditionList: sccsConditionList.value,
    sortList: tableSortList.value
  };
});

const showIframe = computed(() => {
  return !!currentAnalysisPanelUrl.value;
});

const handleAddOrder = debounce(
  (): void => {
    OrderViewTemplateDialogRef.value.open();
  },
  1000,
  true
);

const handleKeyWordSearch = (orderKeyWord: string, tableRef: any) => {
  keyword.value = orderKeyWord;
  handleInitTable(tableRef);
};

const handlePageSizeChanged = (pageConfig: any) => {
  pageInfo.value = pageConfig;
  handleInitTable();
};

const handleChangeColumnVisible = (columnList: any) => {
  presentHiddenWidgetNames.value = columnList;
};

const handleSortTable = (sortList: any) => {
  tableSortList.value = sortList;
  handleInitTable();
};

const handleEditOrderDetail = (orderId: string) => {
  OrderViewTemplateDialogRef.value.open(orderId);
};

// 添加视图成功
const handleAddOrderView = () => {
  orderManageViewRef.value.handleGetUserOrderView();
};

const handleLoadedAnalysis = (panelUrl: string, panelId: string) => {
  currentAnalysisPanelUrl.value = panelUrl;
  currentAnalysisPanelId.value = panelId;
  presentView.value = {}; // 清除表格视图的高亮
};

const handleOrderImport = async command => {
  ElMessage({
    message: t("trade_common_exportTip"),
    type: "success",
    plain: true
  });
  let msViewIds = [];
  let viewName = "";
  const { viewType, id, title } = presentView.value;
  let searchCondition = {};
  // 所有订单视图数据
  if (command === "all_order") {
    const defaultView = orderViewList.value.orderViewList.find(
      orderView => orderView.defaultView
    );
    msViewIds.push(defaultView.id);
    orderViewList.value.msViewList.forEach(msView => {
      msView.workOrderViewList.forEach(msWorkOrderView => {
        if (msWorkOrderView.defaultView) {
          msViewIds.push(msWorkOrderView.id);
        }
      });
    });
  } else {
    if (viewType === "ORDER") {
      msViewIds.push(id);
      orderViewList.value.msViewList.forEach(msView => {
        msView.workOrderViewList.forEach(msWorkOrderView => {
          if (msWorkOrderView.defaultView) {
            msViewIds.push(msWorkOrderView.id);
          }
        });
      });
      viewName = title;
    } else {
      msViewIds.push(id);
    }
    searchCondition = {
      keyword: keyword.value,
      sccsConditionList: sccsConditionList.value,
      sortList: tableSortList.value
    };
  }

  const { sccsId, sccsName } = route.query;
  const { code, data } = await exportExcel({
    viewType: viewType,
    viewIdList: msViewIds,
    sccsId: sccsId,
    sccsName: sccsName,
    viewName: viewName,
    ...searchCondition
  });
  if (code === 0) {
    window.open(data);
    ElMessage({
      message: t("trade_common_exportSuccess"),
      type: "success"
    });
  }
};

const handleSearchTableByConditions = (conditions: any) => {
  sccsConditionList.value = conditions;
  handleInitTable();
};

const initRouter = (): void => {
  const { sccsName } = route.query;
  const params = route.query;
  useMultiTagsStoreHook().handleTags("push", {
    path: `/orderManage`,
    name: "orderManage",
    query: params,
    meta: {
      title: {
        zh: `${sccsName}`
      },
      dynamicLevel: 3
    }
  });
  // 路由跳转
  router.push({ name: "orderManage", query: params });
};

initRouter();

const handleCreateOrder = (): void => {
  handleInitTable();
};

const handleInitTable = async (
  tableRef?: any,
  showMutuallyOprate?: boolean
): Promise<void> => {
  loading.value = true;
  const { sccsId, type } = route.query || {};
  relationType.value = type || "";

  if (!sccsId) return;

  let resp;
  if (!sccsId) {
    return;
  }
  if (presentView.value.viewType === "MILESTONE") {
    const params = {
      sccsId: sccsId,
      keyword: keyword.value || "",
      msId: presentView.value.msId,
      sccsConditionList: sccsConditionList.value,
      sortList: tableSortList.value,
      hiddenColumnList: presentHiddenWidgetNames.value,
      // 判断是否是从详情跳转过来的参数，如是的话，需要传递sourceOrderId和relationOperationType
      // 否则不传
      ...pageInfo.value
    };
    if (relationType.value && !showMutuallyOprate) {
      params.sourceOrderId = route.query.orderId;
      params.relationOperationType = route.query.typeValue;
    } else {
      params.sourceOrderId = "";
      params.relationOperationType = null;
    }
    resp = await getWorkOrderList(params);
  } else {
    const params = {
      sccsId: sccsId,
      keyword: keyword.value,
      msId: presentView.value.msId,
      sccsConditionList: sccsConditionList.value,
      sortList: tableSortList.value,
      hiddenColumnList: presentHiddenWidgetNames.value,
      // 判断是否是从详情跳转过来的参数，如是的话，需要传递原orderId和relationOperationType
      // 否则不传
      // sourceWorkOrderId: route.query.workOrderId || "",
      // sourceOrderId: relationType.value ? route.query.orderId : null,
      // sourceMsId: route.query.msId || "",
      // sourceSccsId: route.query.sccsId || "",
      // relationOperationType: route.query.typeValue || null,
      // todo: route.query.todo === "1",
      ...pageInfo.value
    };
    if (relationType.value && !showMutuallyOprate) {
      params.sourceWorkOrderId = route.query.workOrderId;
      params.sourceOrderId = route.query.orderId;
      params.sourceMsId = route.query.msId;
      params.sourceSccsId = route.query.sccsId;
      params.relationOperationType = route.query.typeValue;
      params.todo = route.query.todo === "1";
    } else {
      params.sourceWorkOrderId = "";
      params.sourceOrderId = "";
      params.sourceMsId = "";
      params.sourceSccsId = "";
      params.relationOperationType = null;
      params.todo = null;
    }

    if (params.todo === null) {
      relationTodo.value = true;
    } else {
      relationTodo.value = params.todo;
    }

    resp = await getOrderList(params);
  }

  if (resp.code === 0) {
    tableTotal.value = resp.data.total;
    // 深拷贝当前表格选中行数据
    let selectedList = null;
    if (orderTableRef.value.tableSelectedList.length > 0) {
      selectedList = cloneDeep(orderTableRef.value.tableSelectedList);
    }
    orderTableRef.value.handleRenderTableData(
      resp.data.list,
      presentView.value.viewType
    );
    // orderTableRef.value.handleGetCheckboxRecords([]); // 筛选目前需求是不需要取消已选数据
    if (selectedList && selectedList.length > 0) {
      orderTableRef.value.patchTableCheckboxStatus(selectedList);
    }
    if (!!tableRef) {
      orderTableRef.value?.executeStatisticalResult?.();
      tableRef.handleListTableSearch(keyword.value);
    }

    let orderIdList = resp.data.list.map(record => record.orderId);
    if (orderIdList.length > 0) {
      orderPermissionList.value = await getOrderPermission(sccsId, orderIdList);
    }
  }
  loading.value = false;
};

provide("reloadTableData", () => {
  handleInitTable();
});

/**
 * 初始化页面数据
 * @param id
 */
const handleInitPageData = async (
  orderViewData: any,
  selectedViewData: any
) => {
  loading.value = true;
  orderViewList.value = orderViewData;
  currentSccsId.value = route.query.sccsId as string;
  const sccsId = route.query.sccsId as string;
  const { code, data } = await getOrderViewById({
    sccsId: sccsId,
    orderListViewId: selectedViewData.id
  });
  const coopTeamName: string = storageSession().getItem("coopTeamMark");
  const resp = await getUserRolePerm(sccsId, coopTeamName !== "mainTeam");
  userRolePermission.value = resp as string[];
  if (code === 0) {
    orderWidgetFields.value = data.fields;
    const milestoneFields = data.fields.filter(
      field => field.type === "MILESTONE"
    );
    milestoneFields.forEach(milestoneField => {
      if (!milestoneIdList.value.includes(milestoneField.name.split("_")[0])) {
        milestoneIdList.value.push(milestoneField.name.split("_")[0]);
      }
    });
    presentView.value = selectedViewData;
    presentViewInfo.value = data;
    sccsConditionList.value = data.conditionList;
    keyword.value = data.keyWords;
    pageInfo.value = { pageSize: data.pageSize, pageNo: 1 };
    presentHiddenWidgetNames.value = data.fields
      .filter(field => !field.show)
      .map(dataField => dataField.name);
    handleInitTable();
    orderManageViewRef.value.handleObtainSccsDropDownList();
    handleDefaultStatisticsData();
    // 清除统计面板的高亮和 iframe 显示
    currentAnalysisPanelUrl.value = "";
    currentAnalysisPanelId.value = "";
  }
};

/**
 * 根据视图类型默认执行统计逻辑
 */
const handleDefaultStatisticsData = async () => {
  const sccsId = route.query.sccsId as string;
  let widgetList;
  let widgetStatisticsList;
  if (presentView.value.defaultView) {
    const NumberFields = orderWidgetFields.value.filter(
      widgetField => widgetField.widgetType === "INPUT_NUMBER"
    );
    widgetList = NumberFields.map(widgetField => widgetField.name);
    widgetStatisticsList = NumberFields.map(widgetField => {
      return {
        type: "SUM",
        widgetId: widgetField.name
      };
    });
  } else {
    widgetList = presentViewInfo.value.colStatisticsItemList.map(
      widgetField => widgetField.colOptWidgetId
    );
    widgetStatisticsList = presentViewInfo.value.colStatisticsItemList.map(
      widgetField => {
        return {
          type: widgetField.colStatisticType,
          widgetId: widgetField.colOptWidgetId
        };
      }
    );
  }
  const resp =
    presentView.value.viewType === "MILESTONE"
      ? await getWorkOrderColumnStatistics({
          sccsId: sccsId,
          sourceIdList: [],
          milestoneId: presentView.value.msId,
          widgetList: widgetList,
          milestone: false,
          sccsConditionList: sccsConditionList.value,
          sortList: tableSortList.value,
          keyword: keyword.value,
          widgetStatisticsList: widgetStatisticsList
        })
      : await getOrderColumnStatistics({
          sccsId: sccsId,
          sourceIdList: [],
          widgetList: widgetList,
          milestone: false,
          sccsConditionList: sccsConditionList.value,
          keyword: keyword.value,
          sortList: tableSortList.value,
          widgetStatisticsList: widgetStatisticsList
        });
  if (resp.code === 0) {
    colStatisticsItemList.value = resp.data.map(col => {
      const widgetItem = widgetStatisticsList.find(
        widget => widget.widgetId === col.widgetId
      );
      return Object.assign(col, { type: widgetItem.type });
    });
  }
};

const screenWidth = ref(window.innerWidth);
useResizeObserver(document.body, entries => {
  const entry = entries[0];
  const { width } = entry.contentRect;
  screenWidth.value = width;
  hide.value = width <= 1366;
  buttonTextVisible.value = width > (locale.value === "en" ? 1700 : 1540);
});
provide("buttonTextVisible", buttonTextVisible);
</script>

<style lang="scss" scoped>
@use "./components/index.scss";

/* 自定义 v-loading 样式 */
.order-manage-body {
  ::v-deep(.el-loading-mask) {
    z-index: 1000 !important;
    background-color: rgb(255 255 255 / 45%); /* 背景颜色 */
  }

  .el-loading-spinner .path {
    stroke: #409eff; /* 加载动画的颜色 */
  }

  .el-loading-spinner .el-loading-text {
    font-size: 16px; /* 加载文本的字体大小 */
    color: #409eff; /* 加载文本的颜色 */
  }
}

.order-manage-iframe-container {
  width: 100%;
  height: 100%;

  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
