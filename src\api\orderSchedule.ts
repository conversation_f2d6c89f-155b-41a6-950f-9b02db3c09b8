import { http } from "@/utils/http";
import type { Result } from "./type";

/**
 * 取某个日期符合条件的sccs列表
 * @param data
 * @returns
 */
export const getSccsInfoListForPlannedDate = data => {
  return http.request<Result>(
    "post",
    "/trade/sccs-milestone-planned/get-sccs-info-list-for-planned-date",
    { data }
  );
};

/**
 * 获得里程碑计划年度统计数据
 * @param params
 * @returns
 */
export const getMilestonePlannedDateStatistics = params => {
  return http.request<Result>(
    "get",
    "/trade/sccs-milestone-planned/get-milestone-planned-date-statistics",
    { params }
  );
};

/**
 * 获得里程碑计划范围统计数据
 * @param params
 * @returns
 */
export const getPlannedDateScopeStatistic = params => {
  return http.request<Result>(
    "get",
    "/trade/sccs-milestone-planned/get-planned-date-scope-statistic",
    { params }
  );
};
