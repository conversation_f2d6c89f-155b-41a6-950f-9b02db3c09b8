<template>
  <div class="wangeditor">
    <Toolbar
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
      style="border-bottom: 1px solid #ccc"
    />
    <Editor
      v-model="editorValue"
      :defaultConfig="editorConfig"
      :mode="mode"
      style="height: 500px; overflow-y: hidden"
      @onCreated="handleCreated"
    />
  </div>
</template>
<script setup lang="ts">
import { onBeforeUnmount, shallowRef, watch, ref } from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { uploadFile } from "@/api/common";
import { useRoute } from "vue-router";
import { createFormData } from "@pureadmin/utils";

const route = useRoute();
const mode = "default";
const editorRef = shallowRef();
const editorValue = ref<string>("");
const toolbarConfig: any = {
  excludeKeys: [
    "insertVideo", // 去掉插入视频功能
    "uploadVideo", // 去掉上传视频功能
    "group-video",
    "fullScreen", // 去掉全屏功能
    "emotion"
  ]
};
const editorConfig = {
  readOnly: false,
  maxLength: 2000,
  MENU_CONF: {
    uploadImage: {
      async customUpload(file: File, insertFn: any) {
        const formDataParams = createFormData({
          sccsId: route.query.sccsId,
          ossFileType: "TRADE_SCCS",
          file: file
        });
        const { code, data } = await uploadFile(formDataParams);
        if (code === 0) {
          insertFn(data, file.name, "");
        }
      },

      onBeforeUpload(files: any) {
        console.log("onBeforeUpload", files);

        return files; // 返回哪些文件可以上传
      },
      onProgress(progress: any) {
        console.log("onProgress", progress);
      },
      onSuccess(file: any, res: any) {
        console.log("onSuccess", file, res);
      },
      onFailed(file: any, res: { message: any }) {
        alert(res.message);
        console.log("onFailed", file, res);
      },
      onError(file: any, err: { message: any }, res: any) {
        alert(err.message);
        console.error("onError", file, err, res);
      }
    }
  }
};

const emit = defineEmits(["handleChange"]);

const handleCreated = editor => {
  editorRef.value = editor;
};

watch(editorValue, () => {
  const editor = editorRef.value;
  const html = editor.getHtml();
  const text = editor.getText();
  emit("handleChange", editorValue.value, { html, text });
});

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>
<style lang="scss">
@use "@wangeditor/editor/dist/css/style.css";

.wangeditor {
  max-width: 100%;
  border: 1px solid var(--el-input-border-color, var(--el-border-color));
  border-radius: 4px;
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color))
    inset;
}
</style>
