import Axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type CustomParamsSerializer
} from "axios";
import type {
  PureHttpError,
  RequestMethods,
  PureHttpResponse,
  PureHttpRequestConfig
} from "./types.d";
import { stringify } from "qs";
import { getToken, formatToken } from "@/utils/auth";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal } from "@pureadmin/utils";
import { removeToken } from "@/utils/auth";
import { ElMessage } from "element-plus";
import { transformI18n } from "@/plugins/i18n";
import { getAuthRefreshToken } from "@/api/common";

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 100000,
  baseURL:
    import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_TRADE_PREFIX,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
};

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** `token`过期后，暂存待执行的请求 */
  private static requests = [];

  /** 缓存的请求队列 */
  private static cacheRequest = {};

  /** 防止重复刷新`token` */
  private static isRefreshing = false;

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {};

  /** 保存当前`Axios`实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  // 删除缓存队列中的请求
  private abortCacheRequest(reqKey): void {
    if (PureHttp.cacheRequest[reqKey]) {
      // 通过AbortController实例上的abort来进行请求的取消
      PureHttp.cacheRequest[reqKey].abort();
      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete PureHttp.cacheRequest[reqKey];
    }
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        let systemLanguage = storageLocal().getItem<StorageConfigs>(
          `${responsiveStorageNameSpace()}locale`
        )?.locale;
        if (systemLanguage === "system") {
          systemLanguage = navigator.language === "en" ? "en-US" : "zh-CN";
        }

        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }
        /** 请求白名单，放置一些不需要`token`的接口（通过设置请求白名单，防止`token`过期后再请求造成的死循环问题） */
        const whiteList = [
          "/refresh-token",
          "/login",
          "/mail-login",
          "/send-verify-code",
          "/check-mail-code",
          "/register",
          "/reset-psw-by-email",
          "/file/upload"
        ];
        return whiteList.some(url => config.url.endsWith(url))
          ? config
          : new Promise(async resolve => {
              const data: any = getToken();
              if (data) {
                config.headers["Authorization"] = formatToken(data.accessToken);
                config.headers["Accept-Language"] = systemLanguage;
                const now = new Date().getTime();
                const expired = parseInt(data.expires) - now <= 0;
                if (expired) {
                  if (!PureHttp.isRefreshing) {
                    PureHttp.isRefreshing = true;
                    const userInfo: any = storageLocal().getItem("user-info");
                    // token过期刷新
                    getAuthRefreshToken({
                      refreshToken: data.refreshToken
                    })
                      .then(res => {
                        const { accessToken, refreshToken, expiresTime } =
                          res.data;
                        const PureUserInfo = Object.assign(userInfo, {
                          accessToken: accessToken,
                          refreshToken: refreshToken,
                          expires: expiresTime
                        });
                        storageLocal().setItem("user-info", PureUserInfo);
                        config.headers["Authorization"] =
                          formatToken(accessToken);
                        PureHttp.requests.forEach(cb => cb(accessToken));
                        PureHttp.requests = [];
                      })
                      .catch(() => {
                        removeToken();
                        window.location.href = "/login";
                      })
                      .finally(() => {
                        PureHttp.isRefreshing = false;
                      });
                  }
                  resolve(PureHttp.retryOriginalRequest(config));
                } else {
                  config.headers["Authorization"] = formatToken(
                    data.accessToken
                  );
                  resolve(config);
                }
              } else {
                resolve(config);
              }
            });
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      async (response: PureHttpResponse) => {
        const $config = response.config;
        const responseWhiteCode = [
          0, 503, 1000, 1101, 1102, 1103, 1104, 1105, 1106, 504
        ];
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        if (response.data.code === 401) {
          removeToken();
          window.location.href = "/login";
        }
        if (!responseWhiteCode.includes(response.data.code)) {
          ElMessage.error(transformI18n(response.data.node));
        }
        // 504 错误提示直接用后端返回的msg
        if (response.data.code === 504) {
          ElMessage.error(response.data.msg);
        }
        return response.data;
      },
      (error: PureHttpError) => {
        // if (Axios.isCancel(error)) {
        //   return Promise.reject({
        //     message: "重复请求，已取消"
        //   });
        // }
        const $error = error;
        //@ts-ignore
        $error.isCancelRequest = Axios.isCancel($error);
        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject($error);
      }
    );
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as PureHttpRequestConfig;

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** 单独抽离的`post`工具函数 */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("post", url, params, config);
  }

  /** 单独抽离的`get`工具函数 */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("get", url, params, config);
  }
}

export const http = new PureHttp();
