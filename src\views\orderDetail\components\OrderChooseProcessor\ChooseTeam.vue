<template>
  <div class="choose-coop-team-container">
    <div v-if="stepNumber === 0" class="choose-coop-team-first">
      <div
        v-for="item in coopTeamList"
        :key="item.id"
        class="choose-coop-team-col"
        @click="handleSelectCoopTeam(item)"
      >
        <LkNoPopoverAvatar
          :size="32"
          class="colorOrange"
          shape="square"
          :teamInfo="{
            avatar: item.teamAvatar,
            username: item.teamName
          }"
        />
        <div class="choose-processor-msg-box">
          <div class="choose-processor-msg-text">
            {{ item.teamName }}
          </div>
          <div class="choose-processor-msg-next-text">
            {{ item.teamShortName }}
          </div>
        </div>

        <div class="choose-processor-number-container">
          <div
            v-if="coopTeamData.hasOwnProperty(item.id)"
            class="choose-processor-number-title"
          >
            {{ t("trade_email_selected") }}
          </div>
          <div class="choose-processor-number-title">
            {{ renderCoopTeamText(item.id) }}
          </div>
        </div>
        <i class="iconfont link-arrow-right choose-processor-icon" />
      </div>
    </div>
    <div v-else class="choose-coop-team-second">
      <div class="choose-coop-team-second-header" @click="handleReturnLastStep">
        <el-icon><ArrowLeft /></el-icon>
        <div class="choose-coop-team-second-header-name">
          {{ selectCoopTeamData.teamName }}
          ({{ selectCoopTeamData.teamShortName }})
        </div>
      </div>
      <div class="choose-coop-team-second-container">
        <el-radio-group
          v-model="coopTeamRadioValue"
          class="choose-coop-team-radio-container"
        >
          <el-collapse v-model="collapseActive" accordion>
            <el-collapse-item name="1">
              <template #icon>
                <span />
              </template>
              <template #title>
                <el-radio
                  value="assignedTeamId"
                  @click.prevent="handleCancelRadio('assignedTeamId')"
                >
                  {{ t("trade_order_AssignToThePersonCharge") }}
                </el-radio>
              </template>
            </el-collapse-item>
            <el-collapse-item name="2">
              <template #title>
                <el-radio
                  value="assignedTeamMemberId"
                  @click.prevent="handleCancelRadio('assignedTeamMemberId')"
                >
                  {{ t("trade_order_AssignToThePersonCharge2") }}
                </el-radio>
              </template>
              <el-checkbox-group v-model="chooseProcessorChecked">
                <ChooseProcessor
                  v-if="coopTeamMemberList.length > 0"
                  :chooseProcessorList="coopTeamMemberList"
                  @handleSelectChoose="handleSelectChoose"
                />
                <el-empty
                  v-else
                  :image-size="100"
                  :description="t('trade_common_no_search_data')"
                />
              </el-checkbox-group>
            </el-collapse-item>
          </el-collapse>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowLeft } from "@element-plus/icons-vue";
import LkNoPopoverAvatar from "@/components/lkNoPopoverAvatar/index";
import ChooseProcessor from "@/views/orderDetail/components/OrderChooseProcessor/ChooseProcessor.vue";

const props = defineProps({
  coopTeamList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  coopTeamData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  keyword: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
const selectCoopTeamData = ref<any>({});
const stepNumber = ref<number>(0);
const collapseActive = ref<string[]>([]);
const coopTeamRadioValue = ref<string>("");
const selectChoosePersonList = ref<any[]>([]);
const chooseProcessorChecked = ref<any[]>([]);

const emit = defineEmits(["handleGetAssignedTeamData"]);

const coopTeamMemberList = computed(() => {
  return selectCoopTeamData.value.teamMemberList
    .filter(teamMember => {
      return (
        teamMember.username?.includes(props.keyword) ||
        teamMember.teamName?.includes(props.keyword)
      );
    })
    .map(teamMember => {
      return Object.assign(teamMember, {
        user: true,
        coop: true,
        id: teamMember.teamMemberId
      });
    });
});

const renderCoopTeamText = (teamId: string) => {
  let renderText = "";
  if (props.coopTeamData.hasOwnProperty(teamId)) {
    if (props.coopTeamData[teamId].coopType === "assignedTeamMemberId") {
      renderText = props.coopTeamData[teamId].teamMemberData.length;
    } else if (props.coopTeamData[teamId].coopType === "assignedTeamId") {
      renderText = t("trade_common_directorPerson");
    }
  }
  return renderText;
};

const handleCancelRadio = (value: string) => {
  coopTeamRadioValue.value = value === coopTeamRadioValue.value ? "" : value;
  if (value === "assignedTeamId") {
    selectChoosePersonList.value = [];
    chooseProcessorChecked.value = [];

    emit(
      "handleGetAssignedTeamData",
      coopTeamRadioValue.value,
      selectCoopTeamData.value,
      selectChoosePersonList.value
    );
  } else if (value === "assignedTeamMemberId") {
    emit(
      "handleGetAssignedTeamData",
      coopTeamRadioValue.value,
      selectCoopTeamData.value,
      selectChoosePersonList.value
    );
  }
};

const handleSelectCoopTeam = (coopTeamRow: any) => {
  selectCoopTeamData.value = coopTeamRow;
  if (props.coopTeamData.hasOwnProperty(coopTeamRow.id)) {
    coopTeamRadioValue.value = props.coopTeamData[coopTeamRow.id].coopType;
    if (
      props.coopTeamData[coopTeamRow.id].coopType === "assignedTeamMemberId"
    ) {
      collapseActive.value = ["2"];
      chooseProcessorChecked.value = props.coopTeamData[
        coopTeamRow.id
      ].teamMemberData.map(team => team.teamMemberId);
      props.coopTeamData[coopTeamRow.id].teamMemberData.forEach(member => {
        if (
          !selectChoosePersonList.value.some(
            item => item.userId === member.userId
          )
        ) {
          selectChoosePersonList.value.push(member);
        }
      });
    } else {
      collapseActive.value = [];
    }
  } else {
    coopTeamRadioValue.value = "";
    collapseActive.value = [];
  }

  stepNumber.value = 1;
};

const handleReturnLastStep = () => {
  stepNumber.value = 0;
  selectCoopTeamData.value = {};
  selectChoosePersonList.value = [];
  chooseProcessorChecked.value = [];
};

const handleSetChoosePersonnList = (chooseList: any) => {
  chooseProcessorChecked.value = chooseList.map(team => team.teamMemberId);
  selectChoosePersonList.value = chooseList;
};

const handleSelectChoose = (chooseData: any, checked: boolean): void => {
  const teamId = chooseData.teamId;
  if (checked) {
    selectChoosePersonList.value.push(chooseData);
  } else {
    const index = selectChoosePersonList.value.findIndex(
      choose => choose.userId === chooseData.userId
    );
    if (index !== -1) {
      selectChoosePersonList.value.splice(index, 1);
    }
  }
  emit(
    "handleGetAssignedTeamData",
    coopTeamRadioValue.value,
    selectCoopTeamData.value,
    selectChoosePersonList.value
  );
};

defineExpose({
  handleSetChoosePersonnList
});
</script>
<style lang="scss" scoped>
.choose-coop-team-container {
  width: calc(100% - 10px);

  .choose-coop-team-first {
    .choose-coop-team-col {
      display: flex;
      align-items: center;
      width: 100%;
      height: 48px;
      padding: 8px 12px;
      margin-top: 5px;
      cursor: pointer;
      border-radius: 4px;

      .choose-processor-msg-box {
        display: flex;
        flex: 1;
        flex-direction: column;
        margin-left: 8px;

        .choose-processor-msg-text {
          font-size: 12px;
          font-weight: normal;
          color: #262626;
          text-align: left;
        }

        .choose-processor-msg-next-text {
          font-size: 12px;
          font-weight: normal;
          color: #595959;
          text-align: left;
        }
      }

      .choose-processor-number-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 30px;

        .choose-processor-number-title {
          font-size: 12px;
          font-weight: normal;
          color: #262626;
          text-align: center;
        }
      }

      .choose-processor-icon {
        font-size: 10px;
        color: #8c8c8c;
      }

      &:hover {
        background: #f2f2f2;
      }
    }
  }

  .choose-coop-team-second {
    .choose-coop-team-second-header {
      display: flex;
      align-items: center;
      height: 32px;
      cursor: pointer;

      .choose-coop-team-second-header-name {
        margin-left: 4px;
      }
    }

    .choose-coop-team-second-container {
      ::v-deep(.choose-coop-team-radio-container) {
        width: 100%;

        .el-radio {
          display: flex;
          width: 100%;
          height: 40px;
          font-size: 14px;
          font-weight: normal;
          color: #262626;
          text-align: left;

          &.is-checked {
            .el-radio__label {
              font-size: 14px;
              font-weight: normal;
              line-height: 14px;
              color: #262626;
              text-align: left;
            }
          }
        }

        .el-collapse {
          width: 100%;
          border: 0 none;

          .el-collapse-item {
            .el-collapse-item__header {
              height: 40px;
              border: 0 none;
            }

            .el-collapse-item__wrap {
              border: 0 none;

              .el-collapse-item__content {
                padding: 0 !important;
                padding-left: 10px !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>
