<template>
  <div class="dc-body">
    <el-row class="dc-title"
      ><el-tooltip
        v-if="widgetForm.info"
        :content="widgetForm.info"
        placement="top"
        :show-after="500"
        popper-class="widget-popper-label-class"
      >
        <i class="iconfont link-explain font12" style="margin-right: 5px" />
      </el-tooltip>
      {{ widgetForm.title }}</el-row
    >
    <el-row v-if="lastVal">
      <div
        class="dc-lastVal"
        :class="judgeValueReturnStatus(currentVal, lastVal)"
      >
        <span
          v-show="
            ['delete', 'edit'].includes(
              judgeValueReturnStatus(currentVal, lastVal)
            )
          "
          class="delete-line"
        />
        <div class="dc-lastVal-label-select" v-html="lastVal" />
      </div>
    </el-row>
    <el-row>
      <div
        v-if="judgeValueReturnStatus(currentVal, lastVal) !== 'nochange'"
        class="dc-currentVal"
        :class="judgeValueReturnStatus(currentVal, lastVal)"
      >
        <div class="dc-currentVal-label-select" v-html="currentVal" />
      </div>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";

const props = defineProps({
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  lastHistoryObj: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const lastVal = computed(() => {
  if (props.lastHistoryObj instanceof Array) {
    const widgetData = props.lastHistoryObj.find(
      widgetRow => widgetRow.widgetId === props.widgetForm._fc_id
    );
    return widgetData ? widgetData.label.replace(/,/g, "") : "";
  } else {
    return "";
  }
});

const currentVal = computed(() => {
  if (props.widgetData instanceof Array) {
    const widgetData = props.widgetData.find(
      widgetRow => widgetRow.widgetId === props.widgetForm._fc_id
    );
    return widgetData ? widgetData.label.replace(/,/g, "") : "";
  } else {
    return "";
  }
});

const judgeValueReturnStatus = (
  currentValue?: string,
  lastValue?: string
): string => {
  let status = "";
  if (currentValue === lastValue || (!currentValue && !lastValue)) {
    status = "nochange";
  } else if (
    (lastValue === "" || lastValue === null || lastValue === undefined) &&
    currentValue !== "" &&
    currentValue !== null &&
    currentValue !== undefined
  ) {
    status = "add";
  } else if (
    (lastValue !== "" || lastValue !== null || lastValue !== undefined) &&
    (currentValue === null || currentValue === undefined || currentValue === "")
  ) {
    status = "delete";
  } else if (
    lastValue !== null &&
    currentValue !== null &&
    lastValue !== currentValue
  ) {
    status = "edit";
  }
  return status;
};
</script>
<style lang="scss" scoped>
@use "../../style.scss";

.dc-currentVal-label-select,
.dc-lastVal-label-select {
  ::v-deep(.select-v2-text) {
    display: inline-block;
    padding: 5px;
    margin-right: 10px;
    margin-bottom: 5px;
    font-size: 12px;
    line-height: 12px;
    color: #262626;
    background: #f2f2f2;
    border-radius: 3px;
  }
}
</style>
