<template>
  <div class="team-more-operation-container">
    <div class="team-more-operation-container-top">
      <div class="sccs-more-title">
        {{ t("trade_common_Orderidentificationsettings") }}
      </div>
      <div class="sccs-more-tip">
        {{ t("trade_common_OrderidentificationsettingsTip") }}
      </div>
      <span class="sccs-plan-tip-text">
        <i class="iconfont link-tips-warm" />
        <span class="sccs-plan-tip-text-red">
          {{ t("trade_common_fileds") }}
        </span>
        {{ t("trade_common_planedTip") }}
      </span>
      <div class="sccs-setting-container">
        <draggable
          :list="form"
          :disabled="!enabled"
          item-key="groupName"
          group="freeGroup"
          class="sccs-setting-body"
          ghost-class="ghost"
          chosen-class="chosen"
          animation="300"
          @start="dragging = true"
          @end="handleSettingTag"
        >
          <template #item="{ element, index }">
            <div
              class="sccs-setting-col"
              :class="{ 'not-draggable': !enabled }"
              :data-draggable-name="element.name"
            >
              <n-tag :key="element.id" :class="{ 'not-draggable': !enabled }">
                <i class="iconfont link-drag" />
                <el-select-v2
                  v-model="element.widgetId"
                  filterable
                  :options="sccsSettingOptions"
                  :placeholder="t('trade_sccs_moreOpretaionTip')"
                  @change="handleSettingTag"
                >
                  <template #label="{ value }">
                    <span
                      :class="[
                        getWidgetClass(value)?.deleted ? 'widget-deleted' : ''
                      ]"
                    >
                      {{ getWidgetClass(value)?.label }}
                    </span>
                  </template>
                  <template #default="data">
                    <ReText type="info" :tippyProps="{ delay: 50000 }">
                      <span style="color: #262626">{{ data.item.label }}</span>
                    </ReText>
                  </template>
                </el-select-v2>
                <i
                  v-if="index !== 0"
                  class="iconfont link-clean-up sccs-remove-icon"
                  @click="handleDeleteSettingCol(index)"
                />
              </n-tag>
            </div>
          </template>
        </draggable>
        <el-icon
          v-if="form.length < 3"
          class="sccs-tag-add-icon"
          @click="handleAddSettingCol"
        >
          <CirclePlus />
        </el-icon>
      </div>
      <div v-if="sccsSettingVisible" class="sccs-setting-btn-group">
        <el-button type="primary" color="#0070D2" @click="handleSaveOpertaion">
          {{ t("trade_common_save") }}
        </el-button>
      </div>
    </div>
    <div class="team-more-operation-container-bottom">
      <div class="sccs-more-title">
        {{ t("trade_order_Reasonforcancellation") }}
      </div>
      <div class="sccs-more-tip">
        <i class="iconfont link-tips-warm font14" />
        {{ t("trade_common_orderoperationsTip2") }}
      </div>
      <el-form label-position="top" label-width="auto" :model="form">
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="6">
            <el-form-item>
              <el-input
                v-model="orderCancelReason"
                clearable
                :placeholder="t('trade_order_Reasoncancellation')"
                @change="handleSearchTableData"
                @keydown.enter.prevent="handleSearchTableData"
                @blur="handleSearchTableData"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button @click="handleSearchTableData">
                {{ t("trade_common_query") }}
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="alignRigth">
              <el-button
                type="text"
                class="color0070d2"
                @click="handleEdit('add')"
              >
                <i class="iconfont link-add" />
                {{ t("trade_common_increase") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        :border="true"
        stripe
        :tooltip-options="{ showAfter: 500 }"
        max-height="calc(100vh - 459px)"
        :empty-text="
          loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
        "
      >
        <el-table-column
          :label="t('trade_order_Reasoncancellation')"
          prop="orderCancelReason"
          show-overflow-tooltip
        />
        <el-table-column width="70" align="center">
          <template #default="{ row }">
            <LkTableOperate
              :tableOpeateLists="tableOpeateLists"
              :messageBoxConfirmObject="messageBoxConfirmObject"
              :row="row"
              @handleOperate="handleOperate"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <LkDialog
      ref="LkDialogRef"
      :title="
        dialogStatus === 'add'
          ? t('trade_order_addReasoncancellation')
          : t('trade_order_editReasoncancellation')
      "
      destroy-on-close
      @confirm="handleConfirm"
      @close="handleClose"
    >
      <template #default>
        <el-form
          ref="FormRef"
          label-position="top"
          label-width="auto"
          :rules="rules"
          :model="formValue"
        >
          <el-form-item
            :label="t('trade_order_Reasoncancellation')"
            required
            prop="orderCancelReasonValue"
          >
            <el-input
              v-model="formValue.orderCancelReasonValue"
              clearable
              :placeholder="t('trade_common_inputText')"
              show-word-limit
              maxlength="100"
            />
          </el-form-item>
        </el-form>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, reactive, inject } from "vue";
import { useI18n } from "vue-i18n";
import draggable from "vuedraggable";
import { ElMessage } from "element-plus";
import { CirclePlus } from "@element-plus/icons-vue";
import type { FormRules, FormInstance } from "element-plus";
import LkDialog from "@/components/lkDialog/index";
import LkTableOperate from "@/components/lkTableOperate/index";
import { ReText } from "@/components/ReText";
import {
  getMainFormFields,
  saveOrderMarkWidget,
  getOrderMarkWidget,
  getSccsOrderCancelReason,
  createSccsOrderCancelReason,
  updateSccsOrderCancelReason,
  deleteSccsOrderCancelReason
} from "@/api/sccs";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";

const { t } = useI18n();
const enabled = ref<boolean>(true);
const dragging = ref<boolean>(false);
const sccsSettingOptions = ref<any[]>([]);
const sccsSettingCloneOptions = ref<any>([]);
const sccsSettingVisible = ref<boolean>(false);
const form = ref<any[]>([{ widgetId: "" }]);
const tableData = ref<any[]>([]);
const orderCancelReason = ref<string>("");
const LkDialogRef = ref<any>(null);
const FormRef = ref<FormInstance>();
const dialogStatus = ref<string>("");
const tableRow = ref<string>("");
const loading = ref<boolean>(false);
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_order_removeReasoncancellationTip",
  messageBoxTitle: "trade_order_removeReasoncancellation",
  messageBoxTipArray: ["orderCancelReason"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-edit",
    title: "trade_common_edit",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const reloadTableData = inject("reloadTableData") as () => void;

const formValue = ref<any>({
  orderCancelReasonValue: ""
});

const rules = reactive<FormRules<any>>({
  orderCancelReasonValue: [
    {
      required: true,
      message: t("trade_common_cancelReasonTip"),
      trigger: "blur"
    }
  ]
});

const getWidgetClass = (val: string) => {
  const index = sccsSettingOptions.value.findIndex(
    widget => widget.value === val
  );
  if (index === -1) {
    const item = sccsSettingCloneOptions.value.find(
      widget => widget.value === val
    );
    return item;
  } else {
    const item = sccsSettingOptions.value.find(widget => widget.value === val);
    return item;
  }
};

const handleConfirm = (): void => {
  if (!FormRef.value) return;
  FormRef.value.validate(async valid => {
    if (valid) {
      const { code } =
        dialogStatus.value === "add"
          ? await createSccsOrderCancelReason({
              sccsId: props.basicInfo.id,
              orderCancelReason: formValue.value.orderCancelReasonValue
            })
          : await updateSccsOrderCancelReason({
              id: tableRow.value,
              sccsId: props.basicInfo.id,
              orderCancelReason: formValue.value.orderCancelReasonValue
            });
      if (code === 0) {
        ElMessage({
          message: t("trade_common_dealSuccess"),
          type: "success"
        });
        LkDialogRef.value.close();
        handleClose();
        const { data } = await getSccsOrderCancelReason({
          sccsId: props.basicInfo.id,
          orderCancelReason: orderCancelReason.value
        });
        tableData.value = data;
      }
    }
  });
};

const handleSettingTag = (): void => {
  sccsSettingVisible.value = true;
};

const handleClose = (): void => {
  formValue.value.orderCancelReasonValue = "";
  FormRef.value.resetFields();
};

const handleSearchTableData = async (): Promise<void> => {
  loading.value = true;
  const { data } = await getSccsOrderCancelReason({
    sccsId: props.basicInfo.id,
    orderCancelReason: orderCancelReason.value
  });
  tableData.value = data;
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleEdit = (status: string, row?: any): void => {
  LkDialogRef.value.open();
  dialogStatus.value = status;
  if (status === "edit") {
    formValue.value.orderCancelReasonValue = row.orderCancelReason;
    tableRow.value = row.id;
  }
};

const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "edit") {
    LkDialogRef.value.open();
    dialogStatus.value = status;
    formValue.value.orderCancelReasonValue = row.orderCancelReason;
    tableRow.value = row.id;
  } else {
    handleDeleteTable(row);
  }
};

const handleDeleteTable = async (row?: any): Promise<void> => {
  const { code } = await deleteSccsOrderCancelReason({
    id: row.id,
    sccsId: props.basicInfo.id
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_deleteSuccess"),
      type: "success"
    });
    const { data } = await getSccsOrderCancelReason({
      sccsId: props.basicInfo.id,
      orderCancelReason: orderCancelReason.value
    });
    tableData.value = data;
  }
};

const handleAddSettingCol = (): void => {
  form.value.push({ widgetId: "" });
  handleSettingTag();
};

const handleDeleteSettingCol = (index: number): void => {
  form.value.splice(index, 1);
  handleSettingTag();
};

const handleSaveOpertaion = async (): Promise<void> => {
  let widgetIdList = [];
  for (let i = 0, len = form.value.length; i < len; i++) {
    const widget = form.value[i];
    if (!widget.widgetId) {
      ElMessage.error(t("trade_sccs_moreOpretaionTip"));
      return;
    }
    widgetIdList.push(widget.widgetId);
  }
  const { code } = await saveOrderMarkWidget({
    sccsId: props.basicInfo.id,
    widgetIdList: widgetIdList
  });
  if (code === 0) {
    sccsSettingVisible.value = false;
    reloadTableData();
    ElMessage({
      message: t("trade_common_dealSuccess"),
      type: "success"
    });
  }
};

onMounted(() => {
  const sccsId = props.basicInfo.id;
  loading.value = true;
  Promise.all([
    getMainFormFields({ sccsId: sccsId }),
    getOrderMarkWidget({ sccsId: sccsId }),
    getSccsOrderCancelReason({
      sccsId: sccsId,
      orderCancelReason: orderCancelReason.value
    })
  ])
    .then(res => {
      sccsSettingCloneOptions.value = res[0].data;
      sccsSettingOptions.value = res[0].data.filter(
        dataRow => !dataRow.deleted
      );
      if (res[1].data && res[1].data.length > 0) {
        form.value = [];
        res[1].data.map(item =>
          form.value.push({ widgetId: item["widgetId"] })
        );
      }
      tableData.value = res[2].data;
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 300);
    });
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.team-more-operation-container {
  padding: 20px 26px;

  .team-more-operation-container-top {
    height: 240px;

    .sccs-more-title {
      font-size: 16px;
      font-weight: bolder;
      line-height: 22px;
      color: #303133;
      text-align: left;
    }

    .sccs-more-tip {
      margin-top: 25px;
      font-size: 14px;
      line-height: 20px;
      color: #595959;
      text-align: left;
    }

    .sccs-setting-body {
      display: flex;
      align-items: center;

      .sccs-setting-col {
        position: relative;
        display: flex;
        align-items: center;
        padding: 6px 7px 6px 0;
        margin-right: 12px;
        background: #f5f7fa;
        border-radius: 4px;

        .el-select {
          width: 220px !important;
        }

        .iconfont {
          margin: 0 8px;
          font-size: 12px;
          color: #8c8c8c;
          vertical-align: middle;
          cursor: pointer;
        }

        .sccs-remove-icon {
          position: absolute;
          top: -7px;
          right: -5px;
          margin: 0;
          font-size: 14px;
        }
      }

      .el-icon {
        font-size: 18px;
        color: #0070d2;
        vertical-align: middle;
        cursor: pointer;
      }
    }

    .sccs-setting-btn-group {
      margin-top: 23px;
    }

    .sccs-setting-container {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .sccs-tag-add-icon {
        font-size: 16px;
        color: #0070d2;
        cursor: pointer;
      }
    }
  }

  .team-more-operation-container-bottom {
    .sccs-more-title {
      font-size: 16px;
      font-weight: bolder;
      line-height: 22px;
      color: #303133;
      text-align: left;
    }

    .sccs-more-tip {
      margin-top: 25px;
      font-size: 14px;
      line-height: 20px;
      color: #595959;
      text-align: left;
    }
  }
}

.sccs-plan-tip-text {
  display: inline-block;
  margin: 10px 0;
  font-size: 12px;
  line-height: 16px;
  color: #8c8c8c;
  text-align: left;

  .iconfont {
    margin-right: 5px;
    font-size: 12px;
  }

  .sccs-plan-tip-text-red {
    margin-right: 6px;
    color: #f56c6c;
    text-decoration: line-through;
  }
}

.widget-deleted {
  color: #f56c6c;
  text-decoration: line-through;
}
</style>
