<template>
  <div
    class="team-basic-info-container"
    :class="[
      !tabPermission ? 'team-module-area-permission-empty-container' : ''
    ]"
  >
    <template v-if="tabPermission">
      <template v-if="teamInfo?.teamCode">
        <div class="team-basic-info-top has-border">
          <div class="team-basic-info-li">
            <span class="team-basic-info-name">
              {{ t("trade_team_teamHeader") }}
            </span>
            <span
              class="team-basic-info-describe team-basic-info-describle-flex team-basic-info-describle-just-center"
            >
              <LKUploadAvatar
                :teamInfo="teamInfo"
                @onSuccessUpdateAvatar="onSuccessUpdateAvatar"
              />
              <el-button @click="handleOpenTeamEdit">
                {{ t("trade_common_edit") }}
              </el-button>
            </span>
          </div>
          <div class="team-basic-info-li">
            <span class="team-basic-info-name">
              {{ t("trade_perCenter_teamCode") }}
            </span>
            <span class="team-basic-info-describe">
              {{ teamInfo.teamCode }}
              <el-tooltip
                :show-after="500"
                effect="dark"
                :content="t('trade_team_useCodeJoin')"
                placement="bottom"
              >
                <template #default>
                  <i class="iconfont link-explain" />
                </template>
              </el-tooltip>
              <el-tooltip
                :show-after="500"
                effect="dark"
                :content="t('trade_common_copy')"
                placement="top"
              >
                <template #default>
                  <span
                    class="teamCode-col"
                    @click="handleCopy(teamInfo.teamCode)"
                  >
                    <i class="iconfont link-copy" />
                  </span>
                </template>
              </el-tooltip>
            </span>
          </div>
          <div class="team-basic-info-li">
            <span class="team-basic-info-name">{{
              t("trade_team_teamName")
            }}</span>
            <span
              class="team-basic-info-describe team-basic-info-describe-max-width"
              >{{ teamInfo.teamName }}</span
            >
          </div>
          <div class="team-basic-info-li">
            <span class="team-basic-info-name">
              {{ t("trade_team_teamShortName") }}
            </span>
            <span class="team-basic-info-describe">
              {{ teamInfo.teamShortName }}
            </span>
          </div>
        </div>
        <div class="team-basic-info-top">
          <div class="team-basic-info-li">
            <span class="team-basic-info-name">{{
              t("trade_team_teamOwner")
            }}</span>
            <span
              class="team-basic-info-describe team-basic-info-describle-flex"
            >
              <span class="team-basic-info-row">
                <LKAvatar
                  :teamInfo="teamInfo.ownerInfo"
                  :size="22"
                  fit="cover"
                />
                <span class="team-basic-info-row-name">{{
                  teamInfo.ownerInfo?.username
                }}</span>
              </span>
              <!-- <el-button>{{ t("trade_common_transfe") }}</el-button> -->
            </span>
          </div>
        </div>
        <CreateTeamDialog
          ref="createTeamDialogRef"
          editStatus="edit"
          :teamInfo="teamInfo"
          @createSuccess="handleUpdateTeam"
        />
      </template>
    </template>
    <LkPermissionEmpty v-else />
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { onMounted, ref, unref } from "vue";
import { getTeamDetail } from "@/api/common";
import { teamCooperationCache } from "@/utils/cooperatioTeam";
import CreateTeamDialog from "../../createTeam/component/createTeamDialog.vue";
import LKUploadAvatar from "@/components/lkUploadAvatar/index";
import LKAvatar from "@/components/lkAvatar/index";
import { useClipboard } from "@vueuse/core";
import { ElMessage } from "element-plus";
import LkPermissionEmpty from "@/components/lkPermissionEmpty";

interface ownerInfoProp {
  email: string;
  mobile: string | null;
  avatar: string | null;
  username: string;
}

interface TeamInfoProp {
  ownerInfo?: ownerInfoProp;
  teamAvatar: string;
  teamCode: string;
  teamName: string;
  teamOwner: string;
  teamShortName: string;
}

const { copy } = useClipboard();
const { t } = useI18n();
let currentTeam = ref<any>({});
const createTeamDialogRef = ref<any>(null);
let teamCode: string = unref("");
let teamInfo = ref<TeamInfoProp>({
  teamAvatar: "",
  teamCode: "",
  teamName: "",
  teamOwner: "",
  teamShortName: ""
});
const tabPermission = ref(true);

const handleOpenTeamEdit = (): void => {
  createTeamDialogRef.value.open();
};

const handleCopy = (code: string): void => {
  copy(code);
  ElMessage({
    message: t("trade_common_copySuccess"),
    type: "success"
  });
};

const initTeamInfo = async (): Promise<void> => {
  currentTeam.value = await teamCooperationCache.currentlyUsedTeam();
  const res = await getTeamDetail({ id: currentTeam.value.id });
  if (res.code === 0) {
    teamInfo.value = res.data;
    teamCode = res.data.teamCode;
  } else if (res.code === 403) {
    tabPermission.value = false;
  }
};

const handleUpdateTeam = (): void => {
  initTeamInfo();
};

const onSuccessUpdateAvatar = (): void => {
  initTeamInfo();
};

onMounted(async () => {
  initTeamInfo();
});
</script>
<style lang="scss" scoped>
.team-basic-info-container {
  height: 100%;

  &.team-module-area-permission-empty-container {
    align-content: center;
  }

  .team-basic-info-top {
    padding: 42px 0 48px;
    margin: 0 24px 0 28px;

    &.has-border {
      border-bottom: 1px solid #e6e6e6;
    }

    .team-basic-info-li {
      display: flex;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .team-basic-info-name {
        display: inline-block;
        width: 120px;
        font-size: 14px;
        line-height: 20px;
        color: #8c8c8c;
        text-align: right;
      }

      .team-basic-info-describe {
        flex: 1;
        margin-left: 17px;
        font-size: 14px;
        line-height: 20px;
        color: #262626;
        text-align: left;
        line-break: anywhere;

        &.team-basic-info-describe-max-width {
          max-width: 658px;
        }

        .teamCode-col {
          display: inline-block;
          width: 18px;
          height: 18px;
          margin-left: 11px;
          text-align: center;
          border-radius: 3px;

          &:hover {
            background: #e5e5e5;
          }

          i {
            font-size: 12px;
          }
        }

        .el-button {
          text-align: right;
        }

        .team-basic-info-row {
          display: flex;

          .team-basic-info-row-name {
            margin-left: 5px;
          }
        }
      }

      .team-basic-info-describle-flex {
        display: flex;
        justify-content: space-between;
      }

      .team-basic-info-describle-just-center {
        align-items: center;
      }
    }
  }
}
</style>
