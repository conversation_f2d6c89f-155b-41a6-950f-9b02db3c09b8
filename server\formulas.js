//@ts-nocheck
import * as acorn from "acorn";
import * as acornWalk from "acorn-walk";
import dayjs from "dayjs";
import { BigNumber } from "bignumber.js";
import { cloneDeep } from "@pureadmin/utils";
import {
  SUM,
  AVERAGE,
  MAX,
  MIN,
  CEILING,
  FLOOR,
  INT,
  SUMIF,
  SUMIFS,
  COUNTIF,
  COUNTIFS,
  CONCATENATE,
  LEFT,
  RIGHT,
  SEARCH,
  YEAR,
  MONTH,
  DAY,
  HOUR,
  MINUTE,
  SECOND,
  DAYS,
  DATEDIF,
  WORKDAY,
  NETWORKDAYS,
  IF,
  IFS,
  AND,
  OR,
  TODAY,
  COUNTA,
  TEXTJOIN
} from "@formulajs/formulajs";

/**
 * 公式主执行器，将公式语句转化成ast语句
 * @param formulas 公式语句
 * @param formulasObject 公式计算因子的值
 * @returns
 */
export const calculateFormulasWidget = (formulas, formulasObjectData) => {
  const ast = acorn.parse(formulas, { ecmaVersion: 2020 });
  let acornWalkData;
  const formulasObject = cloneDeep(formulasObjectData);

  acornWalk.simple(ast, {
    Literal(node) {
      if (node.value && (node.value + "").indexOf("@@") > -1) {
        const dataRoute = node.value.split("@@");
        acornWalkData =
          getNestedValue(formulasObject, dataRoute) !== null &&
          getNestedValue(formulasObject, dataRoute) !== undefined
            ? getNestedValue(formulasObject, dataRoute)
            : "";
      } else {
        acornWalkData = node.value;
      }
    },
    BinaryExpression(node) {
      acornWalkData = deepBinaryCalculate(node, formulasObject);
    },
    CallExpression(node) {
      acornWalkData = deepCalculate(node, formulasObject);
    }
  });

  return acornWalkData;
};

export const transformFormulasValue = formulasValue => {
  return typeof formulasValue === "string"
    ? formulasValue.replace(/&quot;/g, `"`).replace(/&&amp;/g, `\\`)
    : formulasValue;
};

export const getNestedValue = (obj, path) => {
  if (path[0] === "Formulas") {
    let detailFormList = Object.values(obj);
    for (let detailItem of detailFormList) {
      if (detailItem.hasOwnProperty(path[1])) {
        return detailItem[path[1]];
      } else {
        continue;
      }
    }
  } else {
    if (path.length === 2) {
      return path.reduce(
        (currentObj, key) =>
          currentObj !== null && currentObj !== undefined
            ? currentObj[key]
            : undefined,
        obj
      );
    } else if (path.length === 3) {
      if (!obj[path[0]]) return [];
      const pathValue =
        obj[path[0]][path[1]] instanceof Array
          ? obj[path[0]][path[1]].reduce((acc, obj) => {
              for (const key in obj) {
                if (acc[key]) {
                  // 如果结果对象中已有该属性，合并数组
                  acc[key] =
                    obj[key] instanceof Array
                      ? [...acc[key], ...obj[key]]
                      : [...acc[key], obj[key]];
                } else {
                  // 否则直接赋值
                  acc[key] =
                    obj[key] instanceof Array ? [...obj[key]] : [obj[key]];
                }
              }
              return acc;
            }, {})
          : obj[path[0]][path[1]];
      return pathValue ? pathValue[path[2]] : [];
    }
  }
};

export const handleObtainDynamicDefaultValue = (
  formWidgets,
  widgetDefaultData
) => {
  let widgetObject = {};
  for (let i = 0, len = formWidgets.length; i < len; i++) {
    const widget = formWidgets[i];
    if (widget.type === "DrDivider") {
      continue;
    } else if (widget.type === "DrCard") {
      widget.children.forEach(widgetChild => {
        if (widgetChild.children) {
          widgetChild.children.forEach(widgetChildItem => {
            if (
              ["DrInputNumber", "DrPercentage"].includes(widgetChildItem.type)
            ) {
              widgetObject[widgetChildItem._fc_id] = getFormulasWidgetValues(
                widgetChildItem,
                widgetDefaultData
              )
                ? getFormulasWidgetValues(widgetChildItem, widgetDefaultData)
                : 0;
            } else {
              widgetObject[widgetChildItem._fc_id] = getFormulasWidgetValues(
                widgetChildItem,
                widgetDefaultData
              );
            }
          });
        }
      });
    } else if (widget.type === "DrTableForm") {
      if (widgetDefaultData) {
        const widgetItem = widgetDefaultData.find(
          widgetChildData => widgetChildData.widgetId === widget._fc_id
        );

        const childrenWidgetIds = widget.children
          .slice(1)
          .map(childWidget => childWidget.children[0]?._fc_id);

        let subTableObject = {};
        if (widgetItem?.childrenList) {
          for (let widgetId of childrenWidgetIds) {
            let widgetDataList = [];
            for (let childRow of widgetItem?.childrenList) {
              const widgetColData = childRow.find(
                childCol => childCol.widgetId === widgetId
              );
              widgetDataList.push(widgetColData?.obj);
            }
            subTableObject[widgetId] = widgetDataList;
          }
        }

        widgetObject[widget._fc_id] = subTableObject;
      }
    } else {
      if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
        widgetObject[widget._fc_id] = getFormulasWidgetValues(
          widget,
          widgetDefaultData
        )
          ? getFormulasWidgetValues(widget, widgetDefaultData)
          : 0;
      } else {
        widgetObject[widget._fc_id] = getFormulasWidgetValues(
          widget,
          widgetDefaultData
        );
      }
    }
  }

  return widgetObject;
};

export const getFormulasWidgetValues = (widget, defaultData) => {
  if (defaultData && defaultData.length !== 0) {
    const widgetItem = defaultData.find(
      widgetItem => widgetItem.widgetId === widget._fc_id
    );
    if (widgetItem) {
      if (widget.type === "DrRate") {
        return widget.props?.starCount > widgetItem.obj
          ? widgetItem.obj
          : widget.props?.starCount;
      } else if (widget.type === "DrInputNumber") {
        if (!widget.props.min && !widget.props.max) {
          return widgetItem.obj ? widgetItem.obj : 0;
        }
        if (widget.props.min && widget.props.min > widgetItem.obj) {
          return widget.props.min;
        }
        if (widget.props.max && widget.props.max < widgetItem.obj) {
          return widget.props.max;
        }
      } else if (widget.type === "DrCheckbox") {
        let checkboxHtml = "";
        for (let widgetItemId of widgetItem.obj) {
          const widgetOptionItem = widget.props.options.find(
            option => option.value === widgetItemId
          );
          if (widgetOptionItem) {
            checkboxHtml += `,${widgetOptionItem.label}`;
          }
        }
        return checkboxHtml.substr(1);
      } else if (widget.type === "DrRadio") {
        const widgetOptionItem = widget.props.options.find(
          option => option.value === widgetItem.obj
        );
        return widgetOptionItem ? widgetOptionItem.label : "";
      } else if (widget.type === "DrAddress") {
        return widgetItem.label ? widgetItem.label : "";
      } else if (widget.type === "DrExchangeRates") {
        return widgetItem.obj ? widgetItem.obj : {};
      } else if (widget.type === "DrDatePicker") {
        return widgetItem.obj ? widgetItem.obj : "";
      } else if (
        ["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(widget.type)
      ) {
        return widgetItem.obj ? widgetItem.obj : {};
      } else {
        return widgetItem.obj;
      }
    } else {
      if (widget.type === "DrCheckbox") {
        return [];
      } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
        return null;
      } else if (widget.type === "DrAddress") {
        return "";
      } else {
        return "";
      }
    }
  } else {
    if (widget.type === "DrCheckbox") {
      return [];
    } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
      return null;
    } else if (widget.type === "DrExchangeRates") {
      return {};
    } else {
      return "";
    }
  }
};

/**
 * 日期时间控件：前端传递的是时间戳，但是后端返回的是字符串，前端在传递进入控件之前，需要进行类型转化
 * @param valData
 * @param timeStamp
 * @returns
 */
export const matchDateFormatStr = (valData, timeStamp) => {
  if (!valData) return "";
  const dateFormatAll = valData.split(" ").length > 1;
  if (dateFormatAll) {
    return dayjs(new Date(timeStamp)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    if (valData.split(" ")[0].split("-").length === 1) {
      return dayjs(new Date(timeStamp)).format("YYYY");
    } else {
      return dayjs(new Date(timeStamp)).format("YYYY-MM-DD");
    }
  }
};

/**
 * 根据表单类型转化控件值
 * @param widget
 * @returns
 */
export const getWidgetBindValue = widget => {
  if (widget.widgetType === "DATE_PICKER") {
    return matchDateFormatStr(widget.label, widget.obj);
  } else {
    if (typeof widget.obj === "string") {
      return widget.obj.replace(/\\/g, "&&amp;");
    }
    return widget.obj;
  }
};

/**
 * 1.接口数据转化成表单需要的值
 * @param widgetList 控件值
 * @param subWidgetMap 子表控件的值
 * @param linkedReferenceMap 关联引用控件的值
 * @returns
 */
export const TransformSubmitDataStructure = (
  widgetList,
  subWidgetMap,
  linkedReferenceMap
) => {
  let widgetFormData = [];
  if (!widgetList) return;
  for (let i = 0, len = widgetList.length; i < len; i++) {
    const widgetData = widgetList[i];
    if (!widgetData.parentWidgetId) {
      if (!["TABLE_FORM", "DR_RELATE_CARD"].includes(widgetData.widgetType)) {
        const index = widgetFormData.findIndex(
          widgetItem => widgetItem.widgetId === widgetData.widgetId
        );
        if (index === -1) {
          widgetFormData.push({
            label: widgetData.label,
            obj: getWidgetBindValue(widgetData),
            showObj: widgetData.showObj,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId
          });
        } else {
          widgetFormData.splice(index, 1, {
            label: widgetData.label,
            obj: getWidgetBindValue(widgetData),
            showObj: widgetData.showObj,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId
          });
        }
      } else if (widgetData.widgetType === "DR_RELATE_CARD") {
        if (
          linkedReferenceMap &&
          linkedReferenceMap.hasOwnProperty(widgetData.widgetId)
        ) {
          const linkedReferenceMapData = linkedReferenceMap[
            widgetData.widgetId
          ].map(linkReference => {
            if (linkReference.widgetType === "TABLE_FORM") {
              const subTableAssemblyData = Object.values(
                subWidgetMap[linkReference.widgetId].reduce((res, item) => {
                  res[item.rowIndex]
                    ? res[item.rowIndex].push(item)
                    : (res[item.rowIndex] = [item]);
                  return res;
                }, {})
              );
              subTableAssemblyData.forEach(subTableRowData => {
                subTableRowData.forEach(subTableChildRowData =>
                  getWidgetBindValue(subTableChildRowData)
                );
              });
              return Object.assign(linkReference, {
                childrenList: subTableAssemblyData
              });
            } else {
              return linkReference;
            }
          });

          const index = widgetFormData.findIndex(
            widgetItem => widgetItem.widgetId === widgetData.widgetId
          );
          if (index === -1) {
            widgetFormData.push({
              label: null,
              obj: linkedReferenceMapData,
              formId: widgetData.formId,
              widgetId: widgetData.widgetId
            });
          } else {
            widgetFormData.splice(index, 1, {
              label: null,
              obj: linkedReferenceMapData,
              formId: widgetData.formId,
              widgetId: widgetData.widgetId
            });
          }
        }
      } else {
        if (subWidgetMap[widgetData.widgetId]) {
          const subTableAssemblyData = Object.values(
            subWidgetMap[widgetData.widgetId].reduce((res, item) => {
              res[item.rowIndex]
                ? res[item.rowIndex].push(item)
                : (res[item.rowIndex] = [item]);
              return res;
            }, {})
          );
          subTableAssemblyData.forEach(subTableRowData => {
            subTableRowData.forEach(subTableChildRowData =>
              getWidgetBindValue(subTableChildRowData)
            );
          });
          widgetFormData.push({
            label: null,
            obj: null,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId,
            childrenList: subTableAssemblyData
          });
        }
      }
    }
  }
  return widgetFormData;
};

/**
 * 根据传递进来的字符串进行转化，判断当前字符串是否是个日期
 * @param str
 * @returns
 */
const isDate = str => {
  if (!str) return false;
  if (str === "" || str === "-1") return false;
  if (str instanceof Array || typeof str === "boolean") return false;
  if (str instanceof Date) return true;
  if (typeof str === "number") return false;
  // 检查是否是数字时间戳
  if (/^\d+$/.test(str)) {
    if (`${str}`.length !== 10 || `${str}`.length !== 13) {
      return false;
    }
    const num = Number(str);
    return !isNaN(new Date(num).getTime());
  }

  if (typeof str === "object") {
    return new BigNumber(str).isNegative() || !!str.$isDayjsObject;
  }

  const regex = /[.,\/#!$%\^&\*;:{}=\_`~()]/g;
  if (typeof str === "string" && !!regex.test(str)) return false;

  // 检查是否符合ISO日期格式（YYYY-MM-DD）
  const isoMatch = str.match(/^(\d{4})-(\d{2})-(\d{2})$/);
  if (isoMatch) {
    const year = parseInt(isoMatch[1], 10);
    const month = parseInt(isoMatch[2], 10) - 1; // 月份从0开始
    const day = parseInt(isoMatch[3], 10);
    const date = new Date(year, month, day);
    // 确保解析后的年月日与输入完全一致，排除被自动调整的情况
    const valid =
      date.getFullYear() === year &&
      date.getMonth() === month &&
      date.getDate() === day;
    return valid;
  }

  // 其他情况使用Date.parse解析
  return !isNaN(Date.parse(str));
};

export const renderWidget = (widgetJsonList, widgetId, milestoneItemData) => {
  let workOrderWidgetReplyObject = {};
  let workOrderReplyArrayObject = {};
  const workOrderWidgetIdList = widgetJsonList.map(
    workOrderReplyWidget => workOrderReplyWidget._fc_id
  );
  let workOrderWidgetList = {};
  for (let widgetName of workOrderWidgetIdList) {
    workOrderWidgetList[widgetName] = [];
  }

  if (milestoneItemData && milestoneItemData.workOrderGroupList) {
    for (let workOrderItem of milestoneItemData.workOrderGroupList) {
      const milestoneData = handleObtainDynamicDefaultValue(
        widgetJsonList,
        TransformSubmitDataStructure(
          workOrderItem.widgetList,
          workOrderItem.subWidgetMap,
          workOrderItem.linkedReferenceMap
        )
      );

      for (let milestoneName in milestoneData) {
        let workOrderList = workOrderWidgetList[milestoneName];
        if (!(workOrderWidgetList[milestoneName] instanceof Array)) {
          workOrderList = [];
        }

        workOrderList.push(milestoneData[milestoneName]);
      }

      workOrderReplyArrayObject[workOrderItem.workOrderId] = milestoneData;
    }
  }

  workOrderWidgetReplyObject[widgetId] = {
    flatMapData: workOrderReplyArrayObject,
    nestMapData: workOrderWidgetList
  };
  return workOrderWidgetReplyObject;
};

export const handleAccordingToFormIdObtainData = (data, templateData) => {
  return new Promise(async resolve => {
    // 1.主表单
    const mainFormData = handleObtainDynamicDefaultValue(
      templateData.mainForm.widgetJsonList,
      TransformSubmitDataStructure(
        data.widgetList,
        data.subWidgetMap,
        data.linkedReferenceMap
      )
    );

    let milestoneReplyObject = {};
    let workOrderReplyObject = {};
    let milestoneWidgetReplyObject = {};
    let workOrderWidgetReplyObject = {};
    let workOrderList = {};

    for (let milestone of templateData.milestoneList) {
      const milestoneItemData = data.milestoneGroupList.find(
        milestoneData => milestoneData.msId === milestone.msId
      );

      // 2.里程碑批复的数据
      if (
        milestoneItemData &&
        milestone &&
        milestone.replyForm &&
        milestone.replyForm.widgetJsonList.length > 0
      ) {
        milestoneReplyObject[milestone.msId] = handleObtainDynamicDefaultValue(
          milestone.replyForm.widgetJsonList,
          TransformSubmitDataStructure(
            milestoneItemData.replyWidgetList,
            milestoneItemData.subWidgetMap,
            milestoneItemData.linkedReferenceMap
          )
        );
        milestoneWidgetReplyObject[milestone.replyForm.id] =
          handleObtainDynamicDefaultValue(
            milestone.replyForm.widgetJsonList,
            TransformSubmitDataStructure(
              milestoneItemData.replyWidgetList,
              milestoneItemData.subWidgetMap,
              milestoneItemData.linkedReferenceMap
            )
          );
      }

      // 3.工单类型的数据
      let workOrderObject = {};
      if (milestoneItemData && milestoneItemData.workOrderGroupList) {
        for (let workOrderItem of milestoneItemData.workOrderGroupList) {
          if (milestone.workOrderReplyForm?.widgetJsonList) {
            let milestoneFormJsonList = [];
            let workOrderFormJsonList = [];
            milestone.formList.forEach(milestoneForm => {
              milestoneFormJsonList.push(...milestoneForm.widgetJsonList);
            });
            if (milestone.workOrderReplyForm?.widgetJsonList) {
              workOrderFormJsonList.push(
                ...milestone.workOrderReplyForm?.widgetJsonList
              );
            }

            workOrderObject[workOrderItem.workOrderId] = {
              widgetList: handleObtainDynamicDefaultValue(
                milestoneFormJsonList,
                TransformSubmitDataStructure(
                  workOrderItem.widgetList,
                  workOrderItem.subWidgetMap,
                  workOrderItem.linkedReferenceMap
                )
              ),
              workOrderReplyWidgetList: handleObtainDynamicDefaultValue(
                workOrderFormJsonList,
                TransformSubmitDataStructure(
                  workOrderItem.widgetList,
                  workOrderItem.subWidgetMap,
                  workOrderItem.linkedReferenceMap
                )
              )
            };
          }
        }
      }
      workOrderReplyObject[milestone.msId] = workOrderObject;
    }

    for (let milestone of templateData.milestoneList) {
      const milestoneItemData = data.milestoneGroupList.find(
        milestoneData => milestoneData.msId === milestone.msId
      );

      // 2.里程碑批复的数据
      if (
        milestoneItemData &&
        milestone &&
        milestone.replyForm &&
        milestone.replyForm.widgetJsonList.length > 0
      ) {
        milestoneWidgetReplyObject[milestone.replyForm.id] =
          handleObtainDynamicDefaultValue(
            milestone.replyForm.widgetJsonList,
            TransformSubmitDataStructure(
              milestoneItemData.replyWidgetList,
              milestoneItemData.subWidgetMap,
              milestoneItemData.linkedReferenceMap
            )
          );
      }

      // 3.工单类型的数据
      if (milestone.workOrderReplyForm) {
        workOrderWidgetReplyObject = renderWidget(
          milestone.workOrderReplyForm.widgetJsonList,
          milestone.workOrderReplyForm.id,
          milestoneItemData
        );
      }

      for (let workOrder of milestone.formList) {
        workOrderList[workOrder.id] = renderWidget(
          workOrder.widgetJsonList,
          workOrder.id,
          milestoneItemData
        );
      }
    }

    let mainFormObject = {};
    mainFormObject[templateData.mainForm.id] = mainFormData;
    // 表单显隐数据设置
    let formVisibleSettingObject = {};
    let mainFormVisibleObject = {};
    mainFormVisibleObject[templateData.mainForm.id] = mainFormData;
    formVisibleSettingObject["mainForm"] = mainFormVisibleObject;

    for (let milestone of templateData.milestoneList) {
      let msReplyObject = {};
      if (milestone.replyForm) {
        msReplyObject[milestone.replyForm.id] =
          milestoneReplyObject[milestone.msId];
      }

      formVisibleSettingObject[milestone.msId] = Object.assign(msReplyObject, {
        workOrderData: workOrderReplyObject[milestone.msId]
      });
    }

    resolve({
      formVisibleObject: formVisibleSettingObject,
      orderDetailData: data,
      formulaObject: Object.assign(
        mainFormObject,
        workOrderWidgetReplyObject,
        workOrderList,
        milestoneWidgetReplyObject
      )
    });
  });
};

/**
 * 计算执行语句
 * @returns
 */
export const deepBinaryCalculate = (node, formulasObject) => {
  if (node.type === "BinaryExpression") {
    let leftData;
    let rightData;
    if (node.left.type === "BinaryExpression") {
      leftData = deepBinaryCalculate(node.left, formulasObject);
    } else {
      leftData = deepCalculate(node.left, formulasObject);
    }
    if (node.right.type === "BinaryExpression") {
      rightData = deepBinaryCalculate(node.right, formulasObject);
    } else {
      rightData = deepCalculate(node.right, formulasObject);
    }

    return calculateFormulasLeftAndRightData(leftData, rightData, node);
  }
};

/**
 * 函数执行语句
 * @returns
 */
export const deepCalculate = (node, formulasObject) => {
  let nodeParams = [];

  if (node.type === "CallExpression") {
    let nodeParams = [];
    if (node.arguments.length > 0) {
      for (let childNode of node.arguments) {
        if (childNode.type === "Literal") {
          let paramsValue =
            (childNode.value + "").indexOf("@@") > -1
              ? getNestedValue(formulasObject, childNode.value.split("@@"))
              : childNode.value;

          if (paramsValue) {
            nodeParams.push(paramsValue);
          } else {
            nodeParams.push(null);
          }
        } else {
          const childDeepData = deepCalculate(childNode, formulasObject);
          nodeParams.push(childDeepData);
        }
      }
      return calculateFormulasValue(node.callee.name, nodeParams);
    } else {
      return calculateFormulasValue(node.callee.name, nodeParams);
    }
  } else if (node.type === "ArrayExpression") {
    // return node.elements;
    let nodeArrayElement = [];
    node.elements.forEach(element => {
      nodeArrayElement.push(deepCalculate(element, formulasObject));
    });
    return nodeArrayElement;
  } else if (node.type === "BinaryExpression") {
    let leftData;
    let rightData;
    if (node.left.type === "BinaryExpression") {
      leftData = deepBinaryCalculate(node.left, formulasObject);
    } else {
      leftData = deepCalculate(node.left, formulasObject);
    }
    if (node.right.type === "BinaryExpression") {
      rightData = deepBinaryCalculate(node.right, formulasObject);
    } else {
      rightData = deepCalculate(node.right, formulasObject);
    }
    return calculateFormulasLeftAndRightData(leftData, rightData, node);
  } else if (node.type === "Literal") {
    return (node.value + "").indexOf("@@") > -1
      ? getNestedValue(formulasObject, node.value.split("@@"))
      : node.value;
  } else if (node.type === "UnaryExpression") {
    return `${node.operator}${node.argument.value}`;
  }
  if (nodeParams.includes("Error: #VALUE!")) {
    return "Error: #VALUE!";
  } else {
    return calculateFormulasValue(node.callee?.name, nodeParams);
  }
};

function calculateFormulasLeftAndRightData(leftData, rightData, node) {
  if (
    leftData !== null &&
    leftData !== undefined &&
    leftData !== "null" &&
    leftData !== "undefined" &&
    rightData !== null &&
    rightData !== undefined &&
    rightData !== "null" &&
    rightData !== "undefined"
  ) {
    if (isDate(leftData) || isDate(rightData)) {
      if (isDate(leftData) && isDate(rightData)) {
        if (node.operator === "==") {
          return dayjs(leftData).isSame(dayjs(rightData), "day");
        } else {
          const diffDays = dayjs(leftData).diff(dayjs(rightData), "day");
          return diffDays;
        }
      } else {
        if (node.operator === "==") {
          return dayjs(leftData).isSame(dayjs(rightData), "day");
        } else if (node.operator === "+") {
          if (isDate(leftData)) {
            return dayjs(leftData).add(rightData, "day");
          } else {
            return dayjs(rightData).add(leftData, "day");
          }
        } else {
          if (isDate(leftData)) {
            return dayjs(leftData).subtract(rightData, "day");
          } else {
            return dayjs(rightData).subtract(leftData, "day");
          }
        }
      }
    } else {
      if (isDate(leftData) || isDate(rightData)) {
        if (isDate(leftData) && isDate(rightData)) {
          const diffDays = dayjs(leftData).diff(dayjs(rightData), "day");
          return diffDays;
        } else {
          if (node.operator === "==") {
            return dayjs(leftData).isSame(dayjs(rightData), "day");
          } else if (node.operator === "+") {
            if (isDate(leftData)) {
              const rightResult = rightData ? rightData : 0;
              return dayjs(leftData).add(rightResult, "day");
            } else {
              const leftResult = leftData ? leftData : 0;
              return dayjs(rightData).add(leftResult, "day");
            }
          } else {
            return dayjs(leftData).subtract(rightData, "day");
          }
        }
      } else {
        let leftResult = `"${
          typeof leftData === "string"
            ? leftData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : leftData
              ? leftData
              : ""
        }"`;
        let rightResult = `"${
          typeof rightData === "string"
            ? rightData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : rightData
              ? rightData
              : ""
        }"`;

        if (typeof leftData === "string" && typeof rightData === "string") {
          return eval(`${leftResult}${node.operator}${rightResult}`);
        } else {
          if (
            (leftData === null || leftData === undefined) &&
            (rightData === null || rightData === undefined)
          ) {
            return undefined;
          } else {
            return eval(
              `${Number.isNaN(Number(leftData)) ? leftResult : Number(leftData)}${node.operator}${Number.isNaN(Number(rightData)) ? rightResult : Number(rightData)}`
            );
          }
        }
      }
    }
  } else {
    if (
      leftData !== null &&
      leftData !== undefined &&
      leftData !== "null" &&
      leftData !== "undefined" &&
      rightData !== null &&
      rightData !== undefined &&
      rightData !== "null" &&
      rightData !== "undefined"
    ) {
      return "";
    } else {
      if (isDate(leftData) || isDate(rightData)) {
        if (isDate(leftData) && isDate(rightData)) {
          const diffDays = dayjs(leftData).diff(dayjs(rightData), "day");
          return diffDays;
        } else {
          if (node.operator === "==") {
            return dayjs(leftData).isSame(dayjs(rightData), "day");
          } else if (node.operator === "+") {
            if (isDate(leftData)) {
              const rightResult = rightData ? rightData : 0;
              return dayjs(leftData).add(rightResult, "day");
            } else {
              const leftResult = leftData ? leftData : 0;
              return dayjs(rightData).add(leftResult, "day");
            }
          } else {
            if (isDate(leftData)) {
              return dayjs(leftData).subtract(rightData, "day");
            } else {
              return dayjs(rightData).subtract(leftData, "day");
            }
          }
        }
      } else {
        let leftResult = `"${
          typeof leftData === "string"
            ? leftData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : leftData
              ? leftData
              : ""
        }"`;
        let rightResult = `"${
          typeof rightData === "string"
            ? rightData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : rightData
              ? rightData
              : ""
        }"`;

        if (typeof leftData === "string" && typeof rightData === "string") {
          return eval(`${leftResult}${node.operator}${rightResult}`);
        } else {
          if (
            (leftData === null || leftData === undefined) &&
            (rightData === null || rightData === undefined)
          ) {
            return undefined;
          } else {
            return eval(
              `${Number.isNaN(Number(leftData)) ? leftResult : Number(leftData)}${node.operator}${Number.isNaN(Number(rightData)) ? rightResult : Number(rightData)}`
            );
          }
        }
      }
    }
  }
}

export const calculateFormulasValue = (formulas, params) => {
  const formulasValue = calculateFormulas(formulas, params);
  return formulasValue;
};

export const calculateFormulas = (formulas, params) => {
  let paramsList = [];
  for (let param of params) {
    if (param === "!==") {
      paramsList.push("<>");
    } else {
      paramsList.push(param);
    }
  }
  switch (formulas) {
    case "SUM":
      const sumParams = paramsList.filter(param => {
        return param !== null && param !== undefined;
      });
      return SUM(...sumParams);
    case "AVERAGE":
      const averageParams = paramsList.filter(param => {
        return param !== null && param !== undefined;
      });
      return AVERAGE(...averageParams);
    case "INT":
      const intParam = paramsList[0];
      if (intParam === null || intParam === undefined) {
        return 0;
      } else {
        return INT(intParam);
      }
    case "MAX":
      const timeParams = paramsList
        .filter(param => {
          return param !== null && param !== undefined;
        })
        .flat()
        .map(param => {
          return /^(\d{4}-\d{2}-\d{2})(?: (\d{2}:\d{2})(?::\d{2})?)?$/.test(
            param
          )
            ? new Date(param).getTime()
            : param;
        });

      return MAX(...timeParams);
    case "MIN":
      const minTimeParams = paramsList
        .filter(param => {
          return param !== null && param !== undefined;
        })
        .flat()
        .map(param => {
          return /^(\d{4}-\d{2}-\d{2})(?: (\d{2}:\d{2})(?::\d{2})?)?$/.test(
            param
          )
            ? new Date(param).getTime()
            : param;
        });

      return MIN(...minTimeParams);
    case "CEILING":
      const ceilingParamOne = paramsList[0];
      const ceilingParamTwo = paramsList[1];
      if (ceilingParamOne === null || ceilingParamOne === undefined) {
        return 0;
      } else if (ceilingParamTwo === null || ceilingParamTwo === undefined) {
        return "#VALUE!";
      } else {
        return CEILING(...paramsList);
      }
    case "FLOOR":
      const floorParamOne = paramsList[0];
      const floorParamTwo = paramsList[1];
      if (floorParamOne === null || floorParamOne === undefined) {
        return 0;
      } else if (floorParamTwo === null || floorParamTwo === undefined) {
        return "#VALUE!";
      } else {
        return FLOOR(...paramsList);
      }
    case "SUMIF":
      const sumIfParam1 = paramsList[0];
      const sumIfParam2 = paramsList[1];
      const sumIfParam3 = paramsList[2];

      return SUMIF(
        sumIfParam1.flat(),
        sumIfParam2,
        sumIfParam3.flat().map(number => parseFloat(number))
      );
    case "SUMIFS":
      return SUMIFS(...paramsList);
    case "COUNTIF":
      let countIfParam1;
      if (paramsList[0] instanceof Array) {
        countIfParam1 = paramsList[0].map(param => {
          if (param === null || param === undefined || param === "") {
            return " ";
          } else {
            return param;
          }
        });
      } else {
        countIfParam1 =
          paramsList[0] === null || paramsList[0] === undefined
            ? " "
            : paramsList[0];
      }
      const countIfParam2 =
        paramsList[1] === null || paramsList[1] === undefined
          ? " "
          : paramsList[1];

      return COUNTIF(countIfParam1, countIfParam2);
    case "COUNTIFS":
      return COUNTIFS(...paramsList);
    case "CONCATENATE":
      const concatEnateParams = paramsList.filter(
        param => param !== null && param !== undefined
      );
      return CONCATENATE(...concatEnateParams);
    case "TEXTJOIN":
      const textJoinParams = paramsList.filter(
        param => param !== null && param !== undefined
      );
      return TEXTJOIN(...textJoinParams);
    case "LEFT":
      const leftParamOne = paramsList[0];
      const leftParamTwo = paramsList[1];
      if (leftParamOne === null || leftParamOne === undefined) {
        return " ";
      } else if (leftParamTwo === null || leftParamTwo === undefined) {
        return LEFT(leftParamOne, 0);
      } else {
        return LEFT(...paramsList);
      }
    case "RIGHT":
      const rightParamOne = paramsList[0];
      const rightParamTwo = paramsList[1];
      if (rightParamOne === null || rightParamOne === undefined) {
        return " ";
      } else if (rightParamTwo === null || rightParamTwo === undefined) {
        return RIGHT(rightParamOne, 0);
      } else {
        return RIGHT(...paramsList);
      }
    case "SEARCH":
      const searchParamsIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (searchParamsIndex === -1) {
        return "#VALUE!";
      }
      return SEARCH(...paramsList);
    case "DAYS":
      const daysIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (daysIndex === -1) {
        return "#VALUE!";
      }
      return DAYS(...paramsList);
    case "DATEDIF":
      const dateDifIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (dateDifIndex === -1) {
        return "#VALUE!";
      }
      return DATEDIF(...paramsList);
    case "YEAR":
      const yearIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (yearIndex === -1) {
        return "#VALUE!";
      }
      return YEAR(...paramsList);
    case "MONTH":
      const monthIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (monthIndex === -1) {
        return "#VALUE!";
      }
      return MONTH(...paramsList);
    case "DAY":
      const dayIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (dayIndex === -1) {
        return "#VALUE!";
      }
      return DAY(...paramsList);
    case "HOUR":
      const hourIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (hourIndex === -1) {
        return "#VALUE!";
      }
      return HOUR(...paramsList);
    case "MINUTE":
      const minuteIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (minuteIndex === -1) {
        return "#VALUE!";
      }
      return MINUTE(...paramsList);
    case "SECOND":
      const secondIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (secondIndex === -1) {
        return "#VALUE!";
      }
      return SECOND(...paramsList);
    case "WORKDAY":
      const workDayIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (workDayIndex === -1) {
        return "#VALUE!";
      }
      return WORKDAY(paramsList[0], paramsList[1], paramsList[2].flat());
    case "NETWORKDAYS":
      const netWorkDaysIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (netWorkDaysIndex === -1) {
        return "#VALUE!";
      }
      return NETWORKDAYS(paramsList[0], paramsList[1], paramsList[2].flat());
    case "IF":
      const ifParams = paramsList.map((param, index) => {
        if (param === null || param === undefined || param === "") {
          return index === 0 ? false : " ";
        } else {
          return index === 0 ? !!param : param;
        }
      });
      return IF(...ifParams);
    case "IFS":
      const ifsParams = paramsList.map((param, index) => {
        if (param === null || param === undefined || param === "") {
          return index % 2 ? false : " ";
        } else {
          return index % 2 ? !!param : param;
        }
      });
      return IFS(...ifsParams);
    case "AND":
      const andParams = paramsList.map(param => {
        if (param === null || params === undefined) {
          return false;
        } else {
          return param;
        }
      });
      return AND(...andParams);
    case "OR":
      const orParams = paramsList.map(param => {
        if (param === null || params === undefined) {
          return false;
        } else {
          return param;
        }
      });
      return OR(...orParams);
    case "TODAY":
      return TODAY();
    case "COUNTA":
      return COUNTA(...paramsList);
  }
};

/**
 * 获取当前订单的整个表单数据源
 * @param milestoneId 里程碑id 允许为空
 * @param workOrderId 工单id 允许为空
 * @returns
 */
export const getEntireFormData = (
  widgetConfigData,
  milestoneId,
  workOrderId
) => {
  const { entrieTemplateForm, templateData } = widgetConfigData;
  const detailData = templateData.orderDetailData;
  let orderDetailObject = {};
  let checkboxList = [];
  let widgetList = [];
  const mainFormData = TransformSubmitDataStructure(
    detailData.widgetList,
    detailData.subWidgetMap,
    detailData.linkedReferenceMap
  );
  const mainFormObject = handleObtainDynamicDefaultValue(
    entrieTemplateForm.mainForm.widgetJsonList,
    mainFormData
  );

  orderDetailObject[entrieTemplateForm.mainForm.id] = mainFormObject;
  checkboxList = checkboxList.concat(
    getFormListWidgetJsonList(
      entrieTemplateForm.mainForm.widgetJsonList
    ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
  );
  widgetList = widgetList.concat(
    getFormListWidgetJsonList(entrieTemplateForm.mainForm.widgetJsonList)
  );

  if (
    detailData.milestoneGroupList &&
    milestoneId &&
    entrieTemplateForm.milestoneList.length !== 0
  ) {
    for (let msFormDetail of detailData.milestoneGroupList) {
      const { msId, subWidgetMap, replyWidgetList, linkedReferenceMap } =
        msFormDetail;
      const msTemplateConfig = entrieTemplateForm.milestoneList.find(
        msConfig => msConfig.msId === msId
      );

      if (msTemplateConfig.replyForm) {
        const msReplyObject = handleObtainDynamicDefaultValue(
          msTemplateConfig.replyForm.widgetJsonList,
          TransformSubmitDataStructure(
            replyWidgetList,
            subWidgetMap,
            linkedReferenceMap
          )
        );

        orderDetailObject[msTemplateConfig.replyForm.id] = msReplyObject;
        checkboxList = checkboxList.concat(
          getFormListWidgetJsonList(
            msTemplateConfig.replyForm.widgetJsonList
          ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
        );
        widgetList = widgetList.concat(
          getFormListWidgetJsonList(msTemplateConfig.replyForm.widgetJsonList)
        );
      }

      let msFormWidgetList = [];
      for (let msForm of msTemplateConfig.formList) {
        msFormWidgetList = msFormWidgetList.concat(msForm.widgetJsonList);
      }

      if (msId === milestoneId) {
        const currentWorkOrderForm = msFormDetail.workOrderGroupList.find(
          workOrderData => workOrderData.workOrderId === workOrderId
        );

        for (let msFormItem of msTemplateConfig.formList) {
          const msFormWidgetList = getFormListWidgetJsonList(
            msFormItem.widgetJsonList
          );

          const value = currentWorkOrderForm
            ? handleObtainDynamicDefaultValue(
                msFormWidgetList,
                TransformSubmitDataStructure(
                  currentWorkOrderForm.widgetList,
                  currentWorkOrderForm.subWidgetMap,
                  currentWorkOrderForm.linkedReferenceMap
                )
              )
            : getWorkOrderGroupData(
                msFormDetail.workOrderGroupList.map(workOrder => {
                  return handleObtainDynamicDefaultValue(
                    msFormWidgetList,
                    TransformSubmitDataStructure(
                      workOrder.widgetList,
                      workOrder.subWidgetMap,
                      workOrder.linkedReferenceMap
                    )
                  );
                })
              );

          orderDetailObject[msFormItem.id] = value;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList).filter(
              widget => ["DrCheckbox", "DrRadio"].includes(widget.type)
            )
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList)
          );
        }

        if (msTemplateConfig.workOrderReplyForm) {
          const msReplyWidgetList = getFormListWidgetJsonList(
            msTemplateConfig.workOrderReplyForm.widgetJsonList
          );

          const value = currentWorkOrderForm
            ? handleObtainDynamicDefaultValue(
                msReplyWidgetList,
                TransformSubmitDataStructure(
                  currentWorkOrderForm.widgetList,
                  currentWorkOrderForm.subWidgetMap,
                  currentWorkOrderForm.linkedReferenceMap
                )
              )
            : handleObtainDynamicDefaultValue(
                msReplyWidgetList,
                TransformSubmitDataStructure([], [])
              );

          orderDetailObject[msTemplateConfig.workOrderReplyForm.id] = value;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            )
          );
        }
      } else {
        for (let msFormItem of msTemplateConfig.formList) {
          const msFormWidgetList = getFormListWidgetJsonList(
            msFormItem.widgetJsonList
          );
          let msFormObject = {};
          for (let msFormWidgetItem of msFormWidgetList) {
            msFormObject[msFormWidgetItem._fc_id] = [];
            for (let workOrderDetail of msFormDetail.workOrderGroupList) {
              const value = handleObtainDynamicDefaultValue(
                [msFormWidgetItem],
                TransformSubmitDataStructure(
                  workOrderDetail.widgetList,
                  workOrderDetail.subWidgetMap,
                  workOrderDetail.linkedReferenceMap
                )
              );
              msFormObject[msFormWidgetItem._fc_id].push(
                ...Object.values(value)
              );
            }
          }
          orderDetailObject[msFormItem.id] = msFormObject;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList).filter(
              widget => ["DrCheckbox", "DrRadio"].includes(widget.type)
            )
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(msFormItem.widgetJsonList)
          );
        }

        if (msTemplateConfig.workOrderReplyForm) {
          const workOrderReplyWidgetList = getFormListWidgetJsonList(
            msTemplateConfig.workOrderReplyForm.widgetJsonList
          );
          let msFormObject = {};
          for (let msFormWidgetItem of workOrderReplyWidgetList) {
            msFormObject[msFormWidgetItem._fc_id] = [];
            for (let workOrderDetail of msFormDetail.workOrderGroupList) {
              const value = handleObtainDynamicDefaultValue(
                [msFormWidgetItem],
                TransformSubmitDataStructure(
                  workOrderDetail.widgetList,
                  workOrderDetail.subWidgetMap,
                  workOrderDetail.linkedReferenceMap
                )
              );
              msFormObject[msFormWidgetItem._fc_id].push(
                ...Object.values(value)
              );
            }
          }
          orderDetailObject[msTemplateConfig.workOrderReplyForm.id] =
            msFormObject;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            )
          );
        }
      }
    }
  }

  return {
    checkboxList: checkboxList,
    entireFormObject: Object.assign({}, ...Object.values(orderDetailObject)),
    entireDetail: orderDetailObject,
    widgetList: widgetList
  };
};

const getFormListWidgetJsonList = widgetJsonList => {
  let widgetList = [];
  for (let widgetJson of widgetJsonList) {
    if (widgetJson.type === "DrCard") {
      const cardChilds = widgetJson.children
        .filter(col => col.children)
        .map(col => col.children[0]);
      widgetList.push(...getFormListWidgetJsonList(cardChilds));
    } else {
      widgetList.push(widgetJson);
    }
  }
  return widgetList;
};

const getWorkOrderGroupData = workOrderData => {
  if (workOrderData.length === 0) {
    return {};
  }
  let workOrderKeys = Object.keys(workOrderData[0]);
  let workOrderObject = {};
  for (let workOrderKey of workOrderKeys) {
    workOrderObject[workOrderKey] = workOrderData.map(
      workOrder => workOrder[workOrderKey]
    );
  }
  return workOrderObject;
};
