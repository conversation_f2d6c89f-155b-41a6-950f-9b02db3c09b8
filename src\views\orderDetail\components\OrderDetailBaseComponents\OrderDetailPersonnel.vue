<template>
  <div class="milestone-reply-form-text">
    <div class="milestone-reply-form-text-col">
      <span class="milestone-reply-form-tip">
        {{ t("trade_common_creator") }}
      </span>
      <div class="milestone-reply-form-span">
        <LkAvater
          :teamInfo="{
            avatar: workOrder.creatorAvatar,
            username: workOrder.creatorUsername,
            email: workOrder.creatorEmail
          }"
          :size="22"
        />
        <span v-if="!hiddenTime" class="milestone-reply-form-span-text">
          {{ getTimeLineStampFormat(workOrder.createTime) }}
        </span>
      </div>
    </div>
    <div class="milestone-reply-form-text-col">
      <span class="milestone-reply-form-tip">
        {{ t("trade_collectors") }}
      </span>
      <div class="milestone-reply-form-span">
        <LkAvatarGroupNext
          :size="22"
          :avatarListGroup="workOrder.editUserList"
          :maxAvatar="4"
        />
        <span
          v-if="workOrder.edited && !hiddenTime"
          class="milestone-reply-form-span-text"
        >
          {{ getTimeLineStampFormat(workOrder.editTime) }}
        </span>
        <span
          v-if="!workOrder.edited"
          class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
        >
          {{ t("trade_order_notCollection") }}
        </span>
      </div>
    </div>
    <div
      v-if="workOrder.replyUserList.length > 0"
      class="milestone-reply-form-text-col"
    >
      <span class="milestone-reply-form-tip">
        {{ t("trade_work_order_approver") }}
      </span>
      <div class="milestone-reply-form-span">
        <LkAvatarGroupNext
          :size="22"
          :avatarListGroup="workOrder.replyUserList"
          :maxAvatar="4"
          mode="reply"
        />
        <span
          v-if="workOrder.replied && !hiddenTime"
          class="milestone-reply-form-span-text"
        >
          {{ getTimeLineStampFormat(workOrder.replyTime) }}
        </span>
        <span
          v-if="!workOrder.replied"
          class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
        >
          {{ t("trade_order_notSubmit") }}
        </span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import LkAvater from "@/components/lkAvatar/index";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext/index";

const { t } = useI18n();

defineProps({
  workOrder: {
    type: Object as PropType<any>,
    default: () => {}
  },
  hiddenTime: {
    type: Boolean,
    default: false
  }
});

const getTimeLineStampFormat = (dateTime: any) => {
  if (dateTime) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "";
  }
};
</script>
<style lang="scss" scoped>
@use "../style/index.scss";
</style>
