<template>
  <el-card class="statistics-card">
    <el-form
      ref="FormRef"
      label-position="top"
      label-width="auto"
      :model="form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item>
            <el-input
              v-model="form.keyword"
              clearable
              :placeholder="t('trade_statistics_fieldName')"
              :prefix-icon="Search"
              @input="handleFilter"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-scrollbar style="height: calc(100% - 50px)">
      <el-table
        :data="tableFliterData"
        :border="true"
        :tooltip-options="{ showAfter: 500 }"
        :header-cell-style="{ background: '#f7f7f7' }"
      >
        <el-table-column :label="t('trade_common_fileds')" prop="name" />
        <el-table-column :label="t('trade_common_numberChange')" prop="value" />
      </el-table>
    </el-scrollbar>
  </el-card>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { getWorkOrderFiledStatsList } from "@/api/history";
import { Search } from "@element-plus/icons-vue";

const props = defineProps({
  currentMainWorkOrderId: {
    type: String as PropType<String>,
    default: () => ""
  }
});
const { t } = useI18n();
const LkPaginationRef = ref<any>(null);
let total = ref<number>(0);
const form = reactive({
  keyword: ""
});
let tableDataClone = ref<any[]>([]);
let tableFliterData = ref<any[]>([]);

const initData = async (): Promise<void> => {
  const res = await getWorkOrderFiledStatsList({
    workOrderId: props.currentMainWorkOrderId
  });
  if (res.code === 0) {
    tableFliterData.value = res.data;
    tableDataClone.value = res.data;
  }
};
// 前端过滤数据
const handleFilter = async (): Promise<void> => {
  let temp = tableDataClone.value;
  if (form.keyword) {
    temp = temp.filter((item: any) => {
      return item.name.includes(form.keyword);
    });
    tableFliterData.value = temp;
  } else {
    initData();
  }
};

onMounted(() => {
  initData();
});
</script>
<style lang="scss" scoped>
.statistics-card {
  width: 100%;
  height: 100%;
  background: #fff;
  border: 1px solid #e6eaf0;
  border-radius: 2px;

  ::v-deep(.el-card__body) {
    height: 100%;
  }
}
</style>
