// 工单类型：任意工单，全部工单
export const WorkOrderRangeOptions = [
  {
    label: "trade_common_arbitrarily",
    value: "ARBITRARILY"
  },
  {
    label: "trade_common_allGongdan",
    value: "ALL"
  }
];

// 运算符
export const MathOperatorOptions = {
  widget: [
    {
      label: "trade_common_eq",
      value: "EQ"
    },
    {
      label: "trade_common_ne",
      value: "NE"
    },
    {
      label: "trade_common_in",
      value: "IN"
    },
    {
      label: "trade_common_notin",
      value: "NOT_IN"
    },
    {
      label: "trade_common_null",
      value: "IS_NULL"
    },
    {
      label: "trade_common_notnull",
      value: "IS_NOT_NULL"
    }
  ],
  number: [
    {
      label: "=",
      value: "EQ"
    },
    {
      label: "≠",
      value: "NE"
    },
    {
      label: "<",
      value: "LT"
    },
    {
      label: ">",
      value: "GT"
    },
    {
      label: "≤",
      value: "LE"
    },
    {
      label: "≥",
      value: "GE"
    },
    {
      label: "trade_common_null",
      value: "IS_NULL"
    },
    {
      label: "trade_common_notnull",
      value: "IS_NOT_NULL"
    }
  ],
  date: [
    {
      label: "trade_common_eq",
      value: "EQ"
    },
    {
      label: "trade_common_before2",
      value: "BEFORE"
    },
    {
      label: "trade_common_after",
      value: "AFTER"
    },
    {
      label: "trade_common_between",
      value: "BETWEEN"
    },
    {
      label: "trade_common_null",
      value: "IS_NULL"
    },
    {
      label: "trade_common_notnull",
      value: "IS_NOT_NULL"
    }
  ],
  file: [
    {
      label: "trade_common_null",
      value: "IS_NULL"
    },
    {
      label: "trade_common_notnull",
      value: "IS_NOT_NULL"
    }
  ]
};

export const FieldTypeSelectOptions = [
  {
    label: "trade_common_custom",
    value: "CUSTOM"
  },
  {
    label: "trade_common_otherField",
    value: "OTHER_FIELD"
  }
];

// 日期控件范围类型
export const DateRangeOptions = [
  {
    label: "指定范围",
    value: "DATE_RANGE"
  },
  {
    label: "过去",
    value: "LAST"
  },
  {
    label: "未来",
    value: "NEXT"
  },
  {
    label: "本周",
    value: "THIS_WEEK"
  },
  {
    label: "上周",
    value: "LAST_WEEK"
  },
  {
    label: "下周",
    value: "NEXT_WEEK"
  },
  {
    label: "本月",
    value: "THIS_MONTH"
  },
  {
    label: "上月",
    value: "LAST_MONTH"
  },
  {
    label: "下月",
    value: "NEXT_MONTH"
  },
  {
    label: "今年",
    value: "THIS_YEAR"
  },
  {
    label: "去年",
    value: "LAST_YEAR"
  },
  {
    label: "明年",
    value: "NEXT_YEAR"
  }
];

// 日期控件早于、晚于,等于类型
export const DatePickerOptions = [
  {
    label: "指定日期",
    value: "ASSIGN"
  },
  {
    label: "前",
    value: "BEFORE"
  },
  {
    label: "后",
    value: "AFTER"
  },
  {
    label: "今天",
    value: "TODAY"
  },
  {
    label: "昨天",
    value: "YESTERDAY"
  },
  {
    label: "明天",
    value: "TOMORROW"
  },
  {
    label: "本月",
    value: "THIS_MONTH"
  },
  {
    label: "上月",
    value: "LAST_MONTH"
  },
  {
    label: "下月",
    value: "NEXT_MONTH"
  },
  {
    label: "今年",
    value: "THIS_YEAR"
  },
  {
    label: "去年",
    value: "LAST_YEAR"
  },
  {
    label: "明年",
    value: "NEXT_YEAR"
  }
];

// 日期控件早于、晚于,等于类型
export const DayPickerOptions = [
  {
    label: "前",
    value: "BEFORE"
  },
  {
    label: "后",
    value: "AFTER"
  },
  {
    label: "当天",
    value: "TODAY"
  }
];
