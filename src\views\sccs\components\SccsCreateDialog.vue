<template>
  <div class="sccs-group-body">
    <LkDialog
      ref="LkDialogRef"
      :noFooter="true"
      :title="t('trade_home_createSccs')"
      :append-to-body="true"
    >
      <template #default>
        <el-form
          ref="formRef"
          :model="sizeForm"
          :rules="rules"
          label-position="top"
          label-width="100"
        >
          <el-form-item
            :label="t('trade_home_sccsName')"
            required
            prop="sccsName"
          >
            <el-input
              v-model="sizeForm.sccsName"
              show-word-limit
              maxlength="100"
              clearable
            />
          </el-form-item>
          <el-form-item
            :label="t('trade_home_sccsNo')"
            required
            prop="sccsCode"
          >
            <el-input
              v-model="sizeForm.sccsCode"
              show-word-limit
              maxlength="8"
              clearable
              style="width: 94%; margin-right: 10px"
              @input="handleInput"
            />
            <el-tooltip
              effect="dark"
              :content="t('trade_home_codeTip')"
              placement="top"
              :show-after="500"
            >
              <i class="iconfont link-explain" />
            </el-tooltip>
          </el-form-item>
          <el-form-item :label="t('trade_home_sccsGroup')" prop="groupId">
            <el-select-v2
              v-model="sizeForm.groupId"
              :options="sccsGroupList"
              :props="{ label: 'groupName', value: 'id' }"
            >
              <template #header>
                <div class="sccs-group-editable-body">
                  <div
                    v-if="!createSccsGroupInSelect"
                    class="sccs-group-editable-text"
                    @click="createSccsGroupInSelect = true"
                  >
                    <i class="iconfont link-add" />
                    {{ t("trade_home_addGroup") }}
                  </div>
                  <div v-else class="sccs-group-editable-btn">
                    <el-input
                      v-model="sccsGroupName"
                      show-word-limit
                      maxlength="20"
                      clearable
                      :placeholder="t('trade_home_groupNameInputTip')"
                      style="width: 67%; margin-right: 10px"
                    />
                    <el-button
                      type="primary"
                      color="#0070D2"
                      @click="handleCreateSccsGroup"
                      >{{ t("trade_common_confirm") }}</el-button
                    >
                    <el-button plain @click="createSccsGroupInSelect = false">{{
                      t("trade_common_cancel")
                    }}</el-button>
                  </div>
                </div>
              </template>
            </el-select-v2>
          </el-form-item>
          <el-form-item class="dialog-footer-btn-group">
            <el-button @click="resetForm(formRef)">
              {{ t("trade_common_cancel") }}
            </el-button>
            <el-button
              :loading="btnLoading"
              type="primary"
              color="#0070D2"
              @click="onSubmit(formRef)"
              >{{ t("trade_common_confirm") }}</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </LkDialog>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog";
import type { FormInstance, FormRules } from "element-plus";
import {
  createSccs,
  getNewSccsCode,
  getSccsGroupList,
  createSccsGroup
} from "@/api/sccs";
import { ElMessage } from "element-plus";

const { t } = useI18n();
const rules = reactive<FormRules>({
  sccsName: [
    {
      required: true,
      message: t("trade_home_sccsSearchTip"),
      trigger: "change"
    }
  ],
  sccsCode: [
    {
      required: true,
      message: t("trade_home_sccsNoTip"),
      trigger: "change"
    }
  ]
});
const btnLoading = ref(false);
const LkDialogRef = ref<any>(null);
const formRef = ref<any>(null);
const sccsGroupName = ref<string>("");
const createSccsGroupInSelect = ref<boolean>(false);
const templateId = ref<string>("");
const sizeForm = reactive({
  sccsName: "",
  sccsCode: "",
  groupId: ""
});
const sccsGroupList = ref<any[]>([]);

const emit = defineEmits<{
  (e: "updateSccsGroup"): void;
}>();

const open = (id: string): void => {
  templateId.value = id;
  Promise.all([
    getNewSccsCode(),
    getSccsGroupList({ isContainSccsInfo: false })
  ]).then(res => {
    if (res[0].code === 0) {
      sizeForm.sccsCode = res[0].data;
    }
    if (res[1].code === 0) {
      const groupArray = res[1].data.filter(
        sccsGroupRow => !sccsGroupRow.editable
      );
      sizeForm.groupId =
        groupArray[0] && groupArray[0].id ? groupArray[0].id : "";
      sccsGroupList.value = res[1].data;
    }
    LkDialogRef.value.open();
  });
};

const handleInput = (value: any): void => {
  const regex = /^[A-Za-z]+$/;
  const ChineseRegex = /[\u4e00-\u9fa5]/g;
  if (ChineseRegex.test(value)) {
    sizeForm.sccsCode = sizeForm.sccsCode.replace(ChineseRegex, "");
  } else if (!regex.test(value)) {
    // 如果输入值不是字母，则将inputValue设置为上一个值，即阻止输入
    sizeForm.sccsCode = sizeForm.sccsCode.slice(0, -1);
  } else {
    sizeForm.sccsCode = sizeForm.sccsCode.toUpperCase();
  }
};

const handleCreateSccsGroup = async () => {
  if (!sccsGroupName.value) {
    ElMessage({
      message: t("trade_home_groupNameInputTip"),
      type: "error"
    });
    return;
  }
  const { code, data: codeData } = await createSccsGroup({
    id: "",
    groupName: sccsGroupName.value
  });
  if (code === 0) {
    sccsGroupName.value = "";
    createSccsGroupInSelect.value = false;
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    const { data } = await getSccsGroupList({ isContainSccsInfo: false });
    sizeForm.groupId = codeData;
    sccsGroupList.value = data;
  }
};

const onSubmit = (formEl: FormInstance | undefined): void => {
  if (!formEl) return;
  btnLoading.value = true;
  formEl.validate(async valid => {
    if (valid) {
      const { code } = await createSccs({
        id: "",
        templateId: templateId.value,
        ...sizeForm
      });
      if (code === 0) {
        btnLoading.value = false;
        ElMessage({
          message: t("trade_common_updateSuccess"),
          type: "success"
        });
        LkDialogRef.value.close();
        formEl.resetFields();
        emit("updateSccsGroup");
      }
    } else {
      btnLoading.value = false;
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  LkDialogRef.value.close();
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.sccs-group-editable-body {
  .sccs-group-editable-text {
    font-size: 14px;
    color: #606266;
    cursor: pointer;

    .iconfont {
      font-size: 12px;
    }
  }
}
</style>
