<template>
  <div class="search-input-container">
    <el-input
      ref="inputRef"
      v-model="keyword"
      type="text"
      class="order-manage-right-table-input"
      :placeholder="t('trade_order_searchOrderTip')"
      :prefix-icon="Search"
      clearable
      @focus="handleInputFocus"
      @blur="handleInputBlur"
      @input="handleInputChange"
      @change="handleKeyWordSearch"
      @clear="handleClear"
    >
      <template v-if="searchResult?.results.length" #append>
        <div class="order-manage-input-jump-body">
          <el-icon @click="handleJumpTableColumn('prev')">
            <ArrowLeft />
          </el-icon>
          {{ searchResult?.index + 1 }} / {{ searchResult?.results.length }}
          <el-icon @click="handleJumpTableColumn('next')">
            <ArrowRight />
          </el-icon>
        </div>
      </template>
    </el-input>

    <!-- 历史搜索下拉框 -->
    <div v-show="showHistoryDropdown" class="search-history-dropdown">
      <div v-if="searchHistory.length > 0" class="search-history-content">
        <div class="search-history-header">
          <span class="search-history-title">{{
            t("trade_common_recentSearch")
          }}</span>
        </div>
        <div class="search-history-list">
          <div
            v-for="(item, index) in searchHistory"
            :key="index"
            class="search-history-item"
            :title="item"
            @mousedown="handleSelectHistory(item)"
          >
            {{ item }}
          </div>
        </div>
        <div class="search-history-footer">
          <span class="search-history-clear" @mousedown="handleClearHistory">
            {{ t("trade_common_clear") }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { Search, ArrowLeft, ArrowRight } from "@element-plus/icons-vue";

// 接口定义
interface SearchResult {
  index: number;
  results: any[];
}

// Props定义
const props = defineProps<{
  modelValue: string;
  searchResult?: SearchResult;
}>();

// Emits定义
const emit = defineEmits<{
  "update:modelValue": [value: string];
  search: [value: string];
  clear: [];
  jumpTableColumn: [type: string];
}>();

// 国际化
const { t } = useI18n();

// 响应式数据
const inputRef = ref();
const keyword = ref(props.modelValue);
const showHistoryDropdown = ref(false);
const searchHistory = ref<string[]>([]);
const isInputFocused = ref(false);

// 常量
const STORAGE_KEY = "order_search_history";
const MAX_HISTORY_COUNT = 5;
const BLUR_DELAY = 200; // 失焦延迟时间

// 计算属性
const hasSearchResults = computed(() =>
  Boolean(props.searchResult?.results.length)
);

const shouldShowDropdown = computed(
  () =>
    isInputFocused.value &&
    !keyword.value?.trim() &&
    searchHistory.value.length > 0
);

// 搜索历史管理
const searchHistoryManager = {
  load: (): string[] => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn("加载搜索历史失败:", error);
      return [];
    }
  },

  save: (history: string[]): void => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      console.warn("保存搜索历史失败:", error);
    }
  },

  add: (searchValue: string): void => {
    const trimmedValue = searchValue.trim();
    if (!trimmedValue) return;

    // 移除重复项并添加到开头
    const filteredHistory = searchHistory.value.filter(
      item => item !== trimmedValue
    );
    filteredHistory.unshift(trimmedValue);

    // 限制数量
    if (filteredHistory.length > MAX_HISTORY_COUNT) {
      filteredHistory.splice(MAX_HISTORY_COUNT);
    }

    searchHistory.value = filteredHistory;
    searchHistoryManager.save(filteredHistory);
  },

  clear: (): void => {
    searchHistory.value = [];
    localStorage.removeItem(STORAGE_KEY);
  }
};

// 事件处理器
const handleInputChange = (value: string): void => {
  keyword.value = value;
  emit("update:modelValue", value);
};

const handleInputFocus = (): void => {
  isInputFocused.value = true;
  if (!keyword.value?.trim()) {
    searchHistory.value = searchHistoryManager.load();
  }
};

const handleInputBlur = (): void => {
  isInputFocused.value = false;
  // 延迟隐藏，确保点击事件能够触发
  setTimeout(() => {
    if (!isInputFocused.value) {
      showHistoryDropdown.value = false;
    }
  }, BLUR_DELAY);
};

const handleKeyWordSearch = (): void => {
  const searchValue = keyword.value?.trim();
  if (searchValue) {
    searchHistoryManager.add(searchValue);
  }
  emit("search", searchValue || "");
  showHistoryDropdown.value = false;
};

const handleClear = (): void => {
  keyword.value = "";
  emit("update:modelValue", "");
  emit("search", "");

  if (isInputFocused.value) {
    nextTick(() => {
      searchHistory.value = searchHistoryManager.load();
    });
  }
};

const handleJumpTableColumn = (type: string): void => {
  emit("jumpTableColumn", type);
};

const handleSelectHistory = (historyItem: string): void => {
  keyword.value = historyItem;
  emit("update:modelValue", historyItem);
  showHistoryDropdown.value = false;
  // 触发搜索
  nextTick(() => {
    handleKeyWordSearch();
  });
};

const handleClearHistory = (): void => {
  searchHistoryManager.clear();
  showHistoryDropdown.value = false;
};

// 点击外部区域处理
const handleClickOutside = (event: Event): void => {
  const target = event.target as HTMLElement;
  const container = target.closest(".search-input-container");

  if (!container) {
    isInputFocused.value = false;
    showHistoryDropdown.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

// 监听器
watch(
  () => props.modelValue,
  newValue => {
    keyword.value = newValue;
  }
);

watch(shouldShowDropdown, show => {
  showHistoryDropdown.value = show;
});
</script>

<style lang="scss" scoped>
.search-input-container {
  position: relative;
  display: inline-block;

  .search-history-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    z-index: 3000;
    margin-top: 4px;
    text-align: left;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);

    .search-history-content {
      padding: 0;

      .search-history-header {
        padding: 8px 16px 0;

        .search-history-title {
          font-size: 14px;
          color: #909399;
        }
      }

      .search-history-list {
        padding: 4px 0;
        margin: 0 8px;

        .search-history-item {
          padding: 8px;
          overflow: hidden;
          font-size: 14px;
          color: #606266;
          text-align: left;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }
        }
      }

      .search-history-footer {
        padding: 8px 16px;
        text-align: left;
        border-top: 1px solid #f0f0f0;

        .search-history-clear {
          display: block;
          font-size: 14px;
          color: #606266;
          cursor: pointer;
          transition: color 0.3s;

          &:hover {
            color: #005bb5;
          }
        }
      }
    }
  }
}

// 继承原有样式
:deep(.order-manage-right-table-input) {
  width: 240px;

  .el-input__wrapper {
    border-radius: 2px;
  }

  .el-input-group__append {
    padding: 0 8px;
    background: #fafafa;
    border-left: 1px solid #dcdfe6;

    .order-manage-input-jump-body {
      display: flex;
      gap: 8px;
      align-items: center;
      font-size: 12px;
      color: #606266;

      .el-icon {
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #0070d2;
        }
      }
    }
  }
}
</style>
