import { createApp, h } from "vue";
import LkElMessageBox from "./src/index.vue";

const MessageBox = {
  open(options) {
    return new Promise(resolve => {
      // 创建挂载容器
      const mountNode = document.createElement("div");
      document.body.appendChild(mountNode);
      // 清除组件实例
      let messageBoxInstance = null;

      // 关闭处理
      const close = () => {
        messageBoxInstance.unmount();
        document.body.removeChild(mountNode);
      };

      // 动态创建组件
      messageBoxInstance = createApp({
        render() {
          return h(LkElMessageBox, {
            visible: true,
            ...options,
            // 监听确认/取消事件
            onConfirm: () => resolve("confirm"),
            onCancel: () => resolve("cancel"),
            onNextConfirm: () => resolve("nextConfirm"),
            "onUpdate:visible": val => {
              if (!val) close();
            }
          });
        }
      });
      messageBoxInstance.mount(mountNode);
    });
  },
  confirm(message, options) {
    return this.open({
      message,
      ...options
    });
  }
};

export default {
  install(app) {
    app.config.globalProperties.$submitMessageBox = MessageBox;
  }
};
