import Cookies from "js-cookie";
import { useUserStoreHook } from "@/store/modules/user";
import {
  storageLocal,
  isString,
  isIncludeAllChildren,
  storageSession
} from "@pureadmin/utils";
import { getUserInfo } from "@/api/login";
import {
  getTeamHome,
  getMyTeamList,
  getTeamMemberPermission,
  getSccsRolePermSet,
  getCoopSccsRolePermSet
} from "@/api/common";
import { ElMessage } from "element-plus";
import { router } from "@/router";

export interface DataInfo<T> {
  /** token */
  accessToken: string;
  /** `accessToken`的过期时间（时间戳） */
  expiresTime: T;
  /** 用于调用刷新accessToken的接口时所需的token */
  refreshToken: string;
  /** 用户id */
  userId: string;
  /** 头像 */
  avatar?: string;
  /** 用户名 */
  username?: string;
  /** 昵称 */
  nickname?: string;
  /** 当前登录用户的角色 */
  roles?: Array<string>;
  /** 当前登录用户的按钮级别权限 */
  permissions?: Array<string>;
  email?: string;
  latestLoginTeamId?: string;
  latestLoginTeamMemberId?: string;
  sex?: string | null;
}

export const userKey = "user-info";
export const TokenKey = "authorized-token";
/**
 * 通过`multiple-tabs`是否在`cookie`中，判断用户是否已经登录系统，
 * 从而支持多标签页打开已经登录的系统后无需再登录。
 * 浏览器完全关闭后`multiple-tabs`将自动从`cookie`中销毁，
 * 再次打开浏览器需要重新登录系统
 * */
export const multipleTabsKey = "multiple-tabs";

/** 获取`token` */
export function getToken(): DataInfo<number> {
  // 此处与`TokenKey`相同，此写法解决初始化时`Cookies`中不存在`TokenKey`报错
  return Cookies.get(TokenKey)
    ? JSON.parse(Cookies.get(TokenKey))
    : storageLocal().getItem(userKey);
}

/**
 * @description 设置`token`以及一些必要信息并采用无感刷新`token`方案
 * 无感刷新：后端返回`accessToken`（访问接口使用的`token`）、`refreshToken`（用于调用刷新`accessToken`的接口时所需的`token`，`refreshToken`的过期时间（比如30天）应大于`accessToken`的过期时间（比如2小时））、`expires`（`accessToken`的过期时间）
 * 将`accessToken`、`expires`、`refreshToken`这三条信息放在key值为authorized-token的cookie里（过期自动销毁）
 * 将`avatar`、`username`、`nickname`、`roles`、`permissions`、`refreshToken`、`expires`这七条信息放在key值为`user-info`的localStorage里（利用`multipleTabsKey`当浏览器完全关闭后自动销毁）
 */
export function setToken(data: DataInfo<number>): Promise<void> {
  return new Promise(resolve => {
    let expires = 0;
    const { accessToken, refreshToken } = data;
    expires = data.expiresTime; // 如果后端直接设置时间戳，将此处代码改为expires = data.expires，然后把上面的DataInfo<Date>改成DataInfo<number>即可
    const cookieString = JSON.stringify({ accessToken, expires, refreshToken });

    expires > 0
      ? Cookies.set(TokenKey, cookieString, {
          expires: (expires - Date.now()) / 86400000
        })
      : Cookies.set(TokenKey, cookieString);

    getSystemUserInfo({ accessToken, refreshToken, expires }).then(res => {
      resolve(res);
    });
  });
}

export const setUserRole = (teamId: string): any => {
  return new Promise(resolve => {
    Promise.all([getMyTeamList(), getTeamMemberPermission()]).then(data => {
      const teamData = data[0].data;
      const teamMemberPermission = data[1].data;
      const presentTeam = teamData.find(teamItem => teamItem.id === teamId);

      // 注意：消息中心的未读数量现在由 store 管理，不需要在这里设置
      storageSession().setItem("userPermission", teamMemberPermission);
      storageSession().setItem("userPresentTeam", presentTeam);
      storageSession().setItem("userMemberTeam", teamData);
      resolve("");
    });
  });
};

export async function getSystemUserInfo({
  accessToken,
  refreshToken,
  expires
}): Promise<void> {
  return new Promise(reslove => {
    getUserInfo().then(res => {
      if (res.code === 0) {
        function setUserKey({
          avatar,
          username,
          email,
          latestLoginTeamId,
          latestLoginTeamMemberId,
          sex,
          id,
          userSign
        }) {
          useUserStoreHook().SET_AVATAR(avatar);
          useUserStoreHook().SET_USERNAME(username);
          useUserStoreHook().SET_EMAIL(email);
          useUserStoreHook().SET_LATESTLOGINTEAMID(latestLoginTeamId);
          useUserStoreHook().SET_LATESTLOGINTEAMMEMBERID(
            latestLoginTeamMemberId
          );
          useUserStoreHook().SET_ROLES([]);
          useUserStoreHook().SET_PERMS([]);
          storageLocal().setItem(userKey, {
            id,
            accessToken,
            refreshToken,
            expires,
            avatar,
            username,
            email,
            sex,
            latestLoginTeamId,
            latestLoginTeamMemberId,
            userSign
          });
        }
        setUserRole(res.data.latestLoginTeamId).then(() => {
          setUserKey(res.data);
          reslove(res.data);
        });
      }
    });
  });
}

/**
 * 切换团队
 * @param teamId 团队id
 * @param isJump 是否需要进行链接跳转
 */
export async function switchTeam(
  teamId,
  isJump: boolean = true,
  jumpUrl?: string
) {
  const { code, data } = await getUserInfo();
  const { latestLoginTeamId, latestLoginTeamMemberId } = data;
  if (code === 0) {
    const res = await getTeamHome({ teamId: teamId });
    if (res.code === 0 && res.data.teamInfo) {
      useUserStoreHook().SET_LATESTLOGINTEAMID(latestLoginTeamId);
      useUserStoreHook().SET_LATESTLOGINTEAMMEMBERID(latestLoginTeamMemberId);
      const userInfo: any = storageLocal().getItem(userKey);
      userInfo.latestLoginTeamId = latestLoginTeamId;
      userInfo.latestLoginTeamMemberId = latestLoginTeamMemberId;
      storageLocal().setItem(userKey, userInfo);
      await setUserRole(teamId);
      if (isJump) {
        if (location.pathname === "/") {
          location.href = "/";
        } else {
          router.push(jumpUrl || "/");
        }
      }
      return true;
    } else if (latestLoginTeamId === null || latestLoginTeamId === teamId) {
      isJump && router.push("/selectTeam");
      return false;
    } else {
      ElMessage.error(res.msg || "团队不存在");
      if (isJump) {
        if (location.pathname === "/") {
          location.href = "/";
        } else {
          router.push("/");
        }
      }
      return false;
    }
  } else {
    return false;
  }
}

export function getNewUserInfo() {
  return new Promise(async resolve => {
    const res = await getUserInfo();
    if (res.code === 0) {
      const {
        avatar,
        username,
        email,
        latestLoginTeamId,
        latestLoginTeamMemberId,
        sex,
        userSign
      } = res.data as any;
      useUserStoreHook().SET_AVATAR(avatar);
      useUserStoreHook().SET_USERNAME(username);
      useUserStoreHook().SET_EMAIL(email);
      useUserStoreHook().SET_LATESTLOGINTEAMID(latestLoginTeamId);
      useUserStoreHook().SET_LATESTLOGINTEAMMEMBERID(latestLoginTeamMemberId);
      useUserStoreHook().SET_ROLES([]);
      useUserStoreHook().SET_PERMS([]);
      const userInfo: any = storageLocal().getItem(userKey);
      userInfo.avatar = avatar;
      userInfo.username = username;
      userInfo.email = email;
      userInfo.sex = sex;
      userInfo.latestLoginTeamId = latestLoginTeamId;
      userInfo.latestLoginTeamMemberId = latestLoginTeamMemberId;
      userInfo.userSign = userSign;
      storageLocal().setItem(userKey, userInfo);
      setUserRole(latestLoginTeamId).then(() => {
        resolve(userInfo);
      });
    }
  });
}

/** 删除`token`以及key值为`user-info`的localStorage信息 */
export function removeToken() {
  Cookies.remove(TokenKey);
  Cookies.remove(multipleTabsKey);
  storageLocal().removeItem(userKey);
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return "Bearer " + token;
};

/** 是否有按钮级别的权限（根据登录接口返回的`permissions`字段进行判断）*/
export const hasPerms = (value: string | Array<string>): boolean => {
  if (!value) return false;
  const allPerms = "*:*:*";
  const { permissions } = useUserStoreHook();
  if (!permissions) return false;
  if (permissions.length === 1 && permissions[0] === allPerms) return true;
  const isAuths = isString(value)
    ? permissions.includes(value)
    : isIncludeAllChildren(value, permissions);
  return isAuths ? true : false;
};

/** 获取用户的sccs和订单没有关联的权限 */
export const getUserRolePerm = async (sccsId: string, coopTeam: boolean) => {
  return new Promise(async resolve => {
    const { code, data } = !coopTeam
      ? await getSccsRolePermSet({ sccsId: sccsId })
      : await getCoopSccsRolePermSet({ sccsId: sccsId });
    if (code === 0) {
      storageSession().setItem("userSccsPerm", data);
      resolve(data);
    }
  });
};
