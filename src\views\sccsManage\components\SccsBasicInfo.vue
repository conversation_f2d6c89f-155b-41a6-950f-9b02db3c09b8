<template>
  <div class="team-basic-info-container">
    <div class="team-basic-info-top">
      <div class="team-basic-info-li">
        <span class="team-basic-info-name">{{ t("trade_home_sccsName") }}</span>
        <span
          class="team-basic-info-describe team-basic-info-describe-max-width"
        >
          {{ basicInfo.sccsName }}
        </span>
        <el-button @click="handleOpenTeamEdit">
          {{ t("trade_common_edit") }}
        </el-button>
      </div>
      <div class="team-basic-info-li">
        <span class="team-basic-info-name">{{ t("trade_home_sccsNo") }}</span>
        <span class="team-basic-info-describe">
          {{ basicInfo.sccsCode }}
          <el-tooltip
            :show-after="500"
            effect="dark"
            :content="t('trade_home_codeTip')"
            placement="bottom"
          >
            <template #default>
              <i class="iconfont link-explain" />
            </template>
          </el-tooltip>
        </span>
      </div>
    </div>
    <LkDialog
      ref="LkDialogRef"
      :title="t('trade_sccs_editSccs')"
      @confirm="confirm"
      @close="handleClose"
    >
      <template #default>
        <div class="create-team-dialog-form">
          <el-form
            ref="ruleFormRef"
            label-position="top"
            :model="ruleForm"
            :rules="rules"
            label-width="auto"
          >
            <el-form-item :label="t('trade_home_sccsName')" prop="sccsName">
              <el-input
                v-model="ruleForm.sccsName"
                show-word-limit
                maxlength="100"
                clearable
              />
            </el-form-item>
            <el-form-item
              :label="t('trade_home_sccsNo')"
              prop="sccsCode"
              class="formItemHasTips"
            >
              <el-input
                v-model="ruleForm.sccsCode"
                show-word-limit
                maxlength="8"
                clearable
                style="width: 94%; margin-right: 10px"
                @input="handleInput"
              />
              <el-tooltip
                effect="dark"
                :content="t('trade_home_codeTip')"
                placement="top"
                :show-after="500"
              >
                <i class="iconfont link-explain" />
              </el-tooltip>
            </el-form-item>
            <div class="tips">
              <span>{{ t("trade_sccs_editSccsCodeTip") }}</span>
            </div>
          </el-form>
        </div>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, watchEffect, inject } from "vue";
import { useI18n } from "vue-i18n";
import type { FormInstance, FormRules } from "element-plus";
import LkDialog from "@/components/lkDialog/index";
import { updateSccs } from "@/api/sccs";
import { ElMessage } from "element-plus";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

interface SccsBasicInfoProp {
  sccsName: string;
  sccsCode: string;
  templateId?: string;
  id?: string;
  groupId?: string;
}

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const ruleFormRef = ref<FormInstance>();
const reloadTableData = inject("reloadTableData") as () => void;
const multiTagsStore = useMultiTagsStoreHook();

let ruleForm = reactive<SccsBasicInfoProp>({
  id: "",
  sccsName: "",
  sccsCode: "",
  templateId: "",
  groupId: ""
});

// 保存原始的 SCCS 编号，用于比较是否发生了变化
const originalSccsCode = ref<string>("");

watchEffect(() => {
  const { sccsName, sccsCode, id, templateId } = props.basicInfo;
  ruleForm.sccsName = sccsName;
  ruleForm.sccsCode = sccsCode;
  ruleForm.id = id;
  ruleForm.templateId = templateId;
  ruleForm.groupId = props.groupId;

  // 保存原始的 SCCS 编号
  originalSccsCode.value = sccsCode;
});

const emit = defineEmits<{
  (e: "updateSccsSuccess", id: string): void;
}>();

const handleClose = (): void => {
  ruleForm.sccsName = "";
  ruleForm.sccsCode = "";
  ruleFormRef.value.resetFields();
};

const handleInput = (value: any): void => {
  const regex = /^[A-Za-z]+$/;
  const ChineseRegex = /[\u4e00-\u9fa5]/g;
  if (ChineseRegex.test(value)) {
    ruleForm.sccsCode = ruleForm.sccsCode.replace(ChineseRegex, "");
  } else if (!regex.test(value)) {
    // 如果输入值不是字母，则将inputValue设置为上一个值，即阻止输入
    ruleForm.sccsCode = ruleForm.sccsCode.slice(0, -1);
  } else {
    ruleForm.sccsCode = ruleForm.sccsCode.toUpperCase();
  }
};

const rules = reactive<FormRules<SccsBasicInfoProp>>({
  sccsName: [
    {
      required: true,
      message: t("trade_home_sccsSearchTip"),
      trigger: "blur"
    }
  ],
  sccsCode: [
    {
      required: true,
      message: t("trade_home_sccsNoTip"),
      trigger: "blur"
    }
  ]
});

const handleOpenTeamEdit = (): void => {
  LkDialogRef.value.open();
};

const confirm = async (): Promise<void> => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async valid => {
    if (valid) {
      //@ts-ignore
      const { code, data } = await updateSccs(ruleForm);
      if (code === 0) {
        ElMessage({
          message: t("trade_common_updateSuccess"),
          type: "success"
        });

        // 检查 SCCS 编号是否发生了变化
        const sccsCodeChanged = originalSccsCode.value !== ruleForm.sccsCode;

        // 如果 SCCS 编号发生了变化，更新相关的订单详情标签页
        if (sccsCodeChanged && ruleForm.id) {
          try {
            multiTagsStore.updateOrderDetailTabsBySccs(
              ruleForm.id,
              ruleForm.sccsCode
            );
          } catch (error) {
            console.error("Failed to update related order detail tabs:", error);
          }
        }

        LkDialogRef.value.close();
        handleClose();
        emit("updateSccsSuccess", ruleForm.id);
        reloadTableData?.();
      }
    }
  });
};
</script>
<style lang="scss" scoped>
.team-basic-info-container {
  .team-basic-info-top {
    padding: 42px 0 48px;
    margin: 0 24px 0 28px;

    &.has-border {
      border-bottom: 1px solid #e6e6e6;
    }

    .team-basic-info-li {
      display: flex;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .team-basic-info-name {
        display: inline-block;
        width: 120px;
        font-size: 14px;
        line-height: 20px;
        color: #8c8c8c;
        text-align: right;
      }

      .team-basic-info-describe {
        flex: 1;
        margin-left: 17px;
        font-size: 14px;
        line-height: 20px;
        color: #262626;
        text-align: left;
        line-break: anywhere;

        &.team-basic-info-describe-max-width {
          max-width: 658px;
          margin-right: 100px;
        }

        i {
          margin-right: 14px;
          font-size: 12px;
        }

        .el-button {
          text-align: right;
        }

        .team-basic-info-row {
          display: flex;

          .team-basic-info-row-name {
            margin-left: 5px;
          }
        }
      }

      .team-basic-info-describle-flex {
        display: flex;
        justify-content: space-between;
      }

      .team-basic-info-describle-just-center {
        align-items: center;
      }
    }
  }
}

.tips {
  margin-top: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.formItemHasTips {
  margin-bottom: 0;

  ::v-deep(.el-form-item__error) {
    position: relative;
  }
}
</style>
