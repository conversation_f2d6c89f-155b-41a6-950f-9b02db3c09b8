<template>
  <el-input-number
    ref="DrPercentageRef"
    v-model="widgetFormData[widgetConfigure._fc_id]"
    v-thousands="widgetConfigure.props.useThousandSeparator"
    :precision="widgetConfigure.props.precision"
    :min="widgetMinValue"
    :max="widgetMaxValue"
    controls-position="right"
    clearable
    :placeholder="placeholder"
    @change="handleChange"
  >
    <template #suffix>
      <span>%</span>
    </template>
  </el-input-number>
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, ref, watch } from "vue";
import { ElInputNumber } from "element-plus";
import { cloneDeep } from "@pureadmin/utils";
import { formatThousandNumber } from "@/utils/common";
import { thousands } from "@/directives/thousands";

const vThousands = thousands;
const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  isRenderDefaultValue: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  formConfig: {
    type: Object as PropType<any>,
    default: () => {}
  },
  currentFormWidgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetFormEncapsulatedData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const placeholder = computed(() => {
  return (
    props.widgetConfigure.props?.placeholderEn ||
    props.widgetConfigure.props?.placeholder
  );
});

const DrPercentageRef = ref<HTMLElement | any>(null);
const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const actuatorDefaultValue: any = inject("ActuatorDefaultValue", null);
const invocationDefaultValueExecutor: any = inject(
  "invocationDefaultValueExecutor",
  null
);

const emit = defineEmits([
  "handleSubTableWidgetValueChange",
  "handleRenderFinish"
]);

const widgetMaxValue = computed(() => {
  const widgetConfigureMax = props.widgetConfigure.props.max;
  return widgetConfigureMax !== null && widgetConfigureMax !== undefined
    ? widgetConfigureMax
    : Infinity;
});

const widgetMinValue = computed(() => {
  const widgetConfigureMin = props.widgetConfigure.props.min;
  return widgetConfigureMin !== null && widgetConfigureMin !== undefined
    ? widgetConfigureMin
    : -Infinity;
});

watch(
  () => ({ ...props.trendsForm }),
  (newVal, oldVal) => {
    widgetFormData.value[props.widgetConfigure._fc_id] =
      typeof cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id] ===
        "string" &&
      cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id] === ""
        ? null
        : cloneDeep(props.trendsForm)[props.widgetConfigure._fc_id];

    if (actuatorDefaultValue) {
      const defaultValueConfig =
        props.widgetConfigure.props.defaultValueConfig || {};

      if (
        defaultValueConfig.type === "relate" &&
        newVal &&
        oldVal &&
        newVal.hasOwnProperty(defaultValueConfig.content) &&
        oldVal.hasOwnProperty(defaultValueConfig.content) &&
        newVal[defaultValueConfig.content] !==
          oldVal[defaultValueConfig.content]
      ) {
        widgetFormData.value[props.widgetConfigure._fc_id] =
          invocationDefaultValueExecutor(
            props.widgetConfigure.props.defaultValueConfig,
            props.widgetConfigure
          );
        handleChange();
      } else if (defaultValueConfig.type === "formula") {
        const formulasParams = defaultValueConfig.content.formulasParams.map(
          param => param.split("@@")[1]
        );

        for (let formulasParam of formulasParams) {
          if (
            newVal &&
            oldVal &&
            newVal.hasOwnProperty(formulasParam) &&
            oldVal.hasOwnProperty(formulasParam) &&
            newVal[formulasParam] !== oldVal[formulasParam]
          ) {
            widgetFormData.value[props.widgetConfigure._fc_id] =
              invocationDefaultValueExecutor(
                props.widgetConfigure.props.defaultValueConfig,
                props.widgetConfigure
              );
            handleChange();
            return;
          }
        }
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const handleChange = () => {
  const widgetId = props.widgetConfigure._fc_id;
  const widgetValue = widgetFormData.value[widgetId];
  const widgetLabelText = props.widgetConfigure.props.useThousandSeparator
    ? formatThousandNumber(widgetValue)
    : widgetValue;

  if (props.widgetRowIndex !== -1) {
    emit(
      "handleSubTableWidgetValueChange",
      widgetId,
      {
        label: widgetLabelText,
        obj: widgetFormData.value[widgetId],
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  } else {
    handleWidgetFormsValue(
      widgetId,
      {
        label: widgetLabelText,
        obj: widgetFormData.value[widgetId],
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  }
};

onMounted(() => {
  emit("handleRenderFinish", DrPercentageRef.value);
});
</script>
