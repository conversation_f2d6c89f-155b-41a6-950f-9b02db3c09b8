// 多组件库的国际化和本地项目国际化兼容
import { type I18n, createI18n } from "vue-i18n";
import type { App, WritableComputedRef } from "vue";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal } from "@pureadmin/utils";

// element-plus国际化
import enLocale from "element-plus/es/locale/lang/en";
import zhLocale from "element-plus/es/locale/lang/zh-cn";

export let localesConfigs = {
  zh: {
    ...zhLocale
  },
  en: {
    ...enLocale
  }
};

/**
 * 获取实际应该使用的语言
 * 统一处理语言选择逻辑，支持多种配置来源
 * @param fallbackLocale 回退语言，默认为 "zh"
 * @returns 实际的语言代码 ("zh" | "en")
 */
export function getActualLocale(fallbackLocale: string = "zh"): string {
  const translationLang = storageLocal().getItem("translationLang") as string;

  // 优先使用 translationLang，如果存在且不为 "system"
  if (translationLang && translationLang !== "system") {
    return translationLang;
  }

  // 如果 translationLang 为 "system" 或不存在，根据浏览器语言决定
  if (translationLang === "system" || !translationLang) {
    return navigator.language.indexOf("zh") > -1 ? "zh" : "en";
  }

  // 最后回退到传入的 fallbackLocale
  return fallbackLocale || "zh";
}

/**
 * 国际化转换工具函数（自动读取根目录locales文件夹下文件进行国际化匹配）
 * @param message message
 * @returns 转化后的message
 */
export function transformI18n(message: any = "") {
  if (!message) {
    return "";
  }

  // 处理存储动态路由的title,格式 {zh:"",en:""}
  if (typeof message === "object") {
    const locale: string | WritableComputedRef<string> | any =
      i18n.global.locale;
    return message[locale?.value] ? message[locale?.value] : message.zh;
  }

  const key = message.match(/(\S*)\./)?.input;
  if (key) {
    return i18n.global.t.call(i18n.global.locale, message);
  } else if (!key) {
    // 兼容非嵌套形式的国际化写法
    const lang =
      storageLocal().getItem("translationLang") &&
      storageLocal().getItem("translationLang") !== "system"
        ? storageLocal().getItem("translationLang")
        : "zh";
    //@ts-ignore
    return localesConfigs[lang][message];
  } else {
    //@ts-ignore
    return localesConfigs[i18n.global.locale.value][message];
  }
}

/** 此函数只是配合i18n Ally插件来进行国际化智能提示，并无实际意义（只对提示起作用），如果不需要国际化可删除 */
export const $t = (key: string) => key;

export const i18n: I18n = createI18n({
  legacy: false,
  locale: getActualLocale(
    storageLocal().getItem<StorageConfigs>(
      `${responsiveStorageNameSpace()}locale`
    )?.locale ?? "zh"
  ),
  fallbackLocale: "zh",
  messages: localesConfigs,
  missingWarn: false,
  fallbackWarn: false,
  silentFallbackWarn: true,
  silentTranslationWarn: true
});

export async function useI18n(app: App, data: any) {
  app.use(
    createI18n({
      legacy: false,
      locale: getActualLocale(
        storageLocal().getItem<StorageConfigs>(
          `${responsiveStorageNameSpace()}locale`
        )?.locale ?? "zh"
      ),
      fallbackLocale: "zh",
      messages: Object.assign(localesConfigs, {
        zh: { ...data["zh-CN"] },
        en: { ...data["en-US"] }
      }),
      globalInjection: true,
      missingWarn: false,
      fallbackWarn: false,
      silentFallbackWarn: true,
      silentTranslationWarn: true
    })
  );
}
