<template>
  <div class="dr-relateCard-body">
    <div class="dr-relatedCard-header">
      <span class="dr-relatedCard-add-btn" @click="handleAddRelateCard">
        <i class="iconfont link-add" />
        {{
          t("trade_common_addRelateCard", {
            params0: widgetConfigure.props.relatedValue.formLabel
          })
        }}
      </span>
    </div>
    <div v-if="relateCardState" class="dr-relateCard-bottom-container">
      <div class="dr-relateCard-container-header">
        <i
          class="iconfont link-clean-up dr-relateCard-container-header-icon"
          @click="handleRemoveSubTableRelateSource"
        />
      </div>
      <div class="dr-relateCard-container">
        <el-row :gutter="20">
          <el-col
            v-for="widgetChild in widgetConfigure.props.relatedValue.rules"
            :key="widgetChild['_fc_id']"
            :span="widgetConfigure.props.colSpan"
          >
            <OrderDetailDescWidget
              :widgetForm="[widgetChild]"
              :widgetData="tableSourceWidgetData"
            />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
  <Teleport to="body">
    <el-dialog v-model="dialogVisible" class="lk-middle-dialog" align-center>
      <LKSubTable
        ref="subTableRef"
        :widgetConfig="widgetSubTableOptions"
        :widgetData="tableWidgetData"
        :modeRadioValue="radioValue"
        tableFirstType="rowRadio"
      />
      <template #footer>
        <el-button @click="dialogVisible = false">
          {{ t("trade_common_cancel") }}
        </el-button>
        <el-button type="primary" @click="handleSaveSubTableRelateSource">
          {{ t("trade_common_sure") }}
        </el-button>
      </template>
    </el-dialog>
  </Teleport>
</template>
<script lang="ts" setup>
import { ref, computed, inject, watch } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import LKSubTable from "@/components/lkWidgetForm/src/lkSubTable.vue";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import { cloneDeep, isEqual } from "@pureadmin/utils";
import { linkedDataReference } from "@/api/common.ts";
import {
  handleObtainDynamicDefaultValue,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  sccsId: {
    type: String as PropType<string>,
    default: ""
  },
  orderId: {
    type: String as PropType<string>,
    default: ""
  },
  workOrderId: {
    type: String as PropType<string>,
    default: ""
  },
  createState: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const { t } = useI18n();
const route = useRoute();
const subTableRef = ref<HTMLElement | any>(null);
const dialogVisible = ref<boolean>(false);
const radioValue = ref<any>();
const tableWidgetData = ref<any>([]);
const tableCacheWidgetData = ref<any[]>([]);
const tableSourceWidgetData = ref<any[]>([]);
const relateCardState = ref<boolean>(false);

const getFormIdByWidgetId: any = inject("getFormIdByWidgetId", null);
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const handleSaveLinkedReferenceSource: any = inject(
  "handleSaveLinkedReferenceSource",
  null
);

const widgetSubTableOptions = computed(() => {
  let widgetList = [{}];
  props.widgetConfigure.props.relatedValue.rules.map(rule => {
    let relatedColObject = {};
    relatedColObject["children"] = [];
    relatedColObject["children"].push(rule);
    widgetList.push(relatedColObject);
  });
  return { children: widgetList };
});

const handleAddRelateCard = async () => {
  const sccsId = route.query.sccsId || props.sccsId;
  const orderId = route.query.orderId || props.orderId;

  if (orderId) {
    const { code, data } = await linkedDataReference({
      sccsId: sccsId,
      orderId: orderId,
      workOrderId: props.workOrderId,
      create: props.createState,
      linkedId: props.widgetConfigure._fc_id
    });
    if (code === 0) {
      tableCacheWidgetData.value = data;

      tableWidgetData.value = data.map(row => {
        let widgetObject = {};
        for (let widget of row.widgetList) {
          widgetObject[widget.widgetId] = widget;
        }
        for (let subWidgetKey in row.subWidgetMap) {
          const widgetData = TransformSubmitDataStructure(
            row.widgetList,
            row.subWidgetMap,
            {}
          );
          const subWidgetItemData = widgetData.find(
            widget => widget.widgetId === subWidgetKey
          );
          if (subWidgetItemData) {
            widgetObject[subWidgetKey] = subWidgetItemData.childrenList;
          }
        }
        return Object.assign(
          {
            checked: row.source,
            sourceId: row.sourceId
          },
          widgetObject
        );
      });

      radioValue.value = data.findIndex(sourceData => !!sourceData.source);
    }
  }
  dialogVisible.value = true;
};

const handleSaveSubTableRelateSource = async () => {
  const radioRowData = subTableRef.value.getRadioCheckedRow();
  const sourceId = radioRowData?.sourceId || "";
  const orderId = route.query.orderId || props.orderId;

  if (sourceId && orderId) {
    const tableRecord = tableCacheWidgetData.value.find(
      cacheData => cacheData.sourceId === sourceId
    );

    const widgetData = TransformSubmitDataStructure(
      tableRecord.widgetList,
      tableRecord.subWidgetMap,
      {}
    );
    tableSourceWidgetData.value = widgetData;
    relateCardState.value = true;
    const widgetId = props.widgetConfigure._fc_id;
    const formId = getFormIdByWidgetId(widgetId);

    let factorRelateCardData = {
      [props.widgetConfigure._fc_id]: handleObtainDynamicDefaultValue(
        props.widgetConfigure.props.relatedValue.rules,
        widgetData
      )
    };

    handleSaveLinkedReferenceSource({
      sourceId: sourceId,
      linkedId: widgetId,
      widgetList: widgetData,
      factoryObject: {
        factoryWidgetId: widgetId,
        factoryData: factorRelateCardData,
        factoryFormData: formId
      }
    });
    handleWidgetFormsValue(widgetId, {
      widgetId: widgetId,
      widgetType: "DrRelateCard",
      label: null,
      obj: null
    });
    dialogVisible.value = false;
  }
};

const handleRemoveSubTableRelateSource = async () => {
  const sccsId = route.query.sccsId || props.sccsId;
  const orderId = route.query.orderId || props.orderId;
  if (!sccsId || !orderId) return;

  const formId = getFormIdByWidgetId(props.widgetConfigure._fc_id);
  let factorRelateCardData = {
    [props.widgetConfigure._fc_id]: handleObtainDynamicDefaultValue(
      props.widgetConfigure.props.relatedValue.rules,
      []
    )
  };

  handleSaveLinkedReferenceSource(
    {
      sccsId: sccsId,
      sourceId: "",
      workOrderId: props.workOrderId,
      orderId: orderId,
      linkedId: props.widgetConfigure._fc_id,
      factoryObject: {
        factoryWidgetId: props.widgetConfigure._fc_id,
        factoryData: factorRelateCardData,
        factoryFormData: formId
      },
      widgetList: []
    },
    "delete"
  );
  tableSourceWidgetData.value = [];
  relateCardState.value = false;
};

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    if (props.trendsForm[props.widgetConfigure._fc_id]) {
      tableSourceWidgetData.value = cloneDeep(newVal);
      relateCardState.value = !!newVal?.length;
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.dr-relateCard-body {
  width: 100%;

  .dr-relatedCard-header {
    margin-bottom: 10px;

    .dr-relatedCard-add-btn {
      display: inline-flex;
      font-size: 14px;
      color: #0070d2;
      cursor: pointer;

      .iconfont {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }

  .dr-relateCard-bottom-container {
    position: relative;

    .dr-relateCard-container-header {
      position: absolute;
      top: -14px;
      right: -6px;
      cursor: pointer;

      .iconfont {
        color: #999;
      }
    }

    .dr-relateCard-container {
      padding: 22px 14px;
      background: #f6f8fa;
      border-radius: 4px;

      ::v-deep(.readonly-textarea) {
        .el-textarea__inner {
          background: transparent !important;
        }
      }
    }
  }
}
</style>
