<template>
  <div v-if="loading" v-loading="loading" style="height: 20vh" />
  <div v-if="tableData.length === 0 && !loading" class="team-empty-body">
    <el-empty
      :description="t('trade_common_notPlanTip')"
      :image-size="342"
      :image="tradeSccsImage"
    />
  </div>
  <div v-else-if="!loading" class="team-module-area-body">
    <el-scrollbar
      ref="containerRef"
      max-height="calc(100vh - 140px)"
      :noresize="true"
      :always="true"
    >
      <div class="fence-list-tip" :class="`collaspse-${tableData[0].id}`">
        <i class="iconfont link-tips-warm" />
        {{ t("trade_common_plannedSetting") }}
      </div>
      <span class="sccs-plan-tip-text">
        <i class="iconfont link-tips-warm" />
        <span class="sccs-plan-tip-text-red">
          {{ t("trade_common_fileds") }}
        </span>
        {{ t("trade_common_planedTip") }}
      </span>
      <div
        v-for="item in tableData"
        :key="item.id"
        :class="['sccs-planned-label-table-body', `collaspse-${item.id}`]"
      >
        <div class="sccs-planned-label-title">
          <div class="sccs-planned-label-flex">
            <span
              v-if="item.isMainForm === 1"
              class="sccs-planned-label-row-text"
            >
              {{ t("trade_common_orderBasicInfo") }}
            </span>
            <ReText
              v-else
              class="sccs-planned-label-row-text"
              type="info"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.name }}
            </ReText>
            <span class="sccs-planned-label-tip">
              {{ item.labelList.length }}
            </span>
          </div>
          <div class="sccs-planned-label-flex-right">
            <div @click="handleAdd(item)">
              <i class="iconfont link-add" />
              {{ t("trade_common_increase") }}
            </div>
          </div>
        </div>
        <el-table
          :data="getTableData(item.labelList)"
          stripe
          :border="true"
          :span-method="
            ({ row, column, rowIndex, columnIndex }) =>
              objectSpanMethod(
                { row, column, rowIndex, columnIndex },
                getTableData(item.labelList)
              )
          "
          header-row-class-name="table-header-class"
          :tooltip-options="{ showAfter: 500 }"
          :empty-text="
            loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
          "
        >
          <el-table-column
            :label="t('trade_common_labelName')"
            prop="tableName"
            width="160"
            show-overflow-tooltip
          />
          <el-table-column
            :label="t('trade_common_lableTag')"
            prop="labelValue"
            width="130"
          >
            <template #default="{ row }">
              <span
                class="table-column-label-tag"
                :style="{ 'border-color': row.style, color: row.style }"
              >
                {{ row.labelValue }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :label="t('trade_common_labelCondition')"
            prop="labelConditions"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div
                v-for="item in getLabelConditions(row.labelConditions)"
                :key="item"
                class="label-conditions-tag"
              >
                <div
                  class="label-conditions-tag-row"
                  v-html="getLabelConditionCol(item)"
                />
                <span class="label-conditions-tag-tip">
                  {{ t("trade_common_or") }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="t('trade_common_labelDescribe')"
            prop="description"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column width="70">
            <template #default="scope">
              <el-dropdown trigger="click">
                <i class="iconfont link-more-left" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleEdit(scope.row, item)">
                      <i class="iconfont link-edit font14" />
                      {{ t("trade_common_edit") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      @click="
                        handleDelete(scope.row, item.name, scope.row.tableId)
                      "
                    >
                      <i class="iconfont link-ashbin colorRed font14" />
                      <span class="colorRed">
                        {{ t("trade_common_delete") }}
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-scrollbar>
  </div>
  <SccsPlanLabelSettingEditDialog
    ref="SccsPlanLabelSettingEditDialogRef"
    :basicInfo="basicInfo"
    @handleSuccess="handleSearchTable()"
  />
  <LKAnchor
    class="anchor-container"
    :anchorList="anchorList"
    :container="containerRef?.wrapRef"
  />
</template>
<script lang="ts" setup>
import { ref, markRaw, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { ReText } from "@/components/ReText";
import { emitter } from "@/utils/mitt";
import LKAnchor from "@/components/lkAnchor";
import { getSccsTradeLableList, deleteSccsLabel } from "@/api/sccs";
import tradeSccsImage from "@/assets/images/home/<USER>";
import SccsPlanLabelSettingEditDialog from "./SccsPlanLabelSettingEditDialog.vue";

const { t } = useI18n();
const loading = ref<boolean>(false);
let tableData = ref<any[]>([]);
const anchorList = ref<any>([]);
const SccsPlanLabelSettingEditDialogRef = ref<any>(null);
const containerRef = ref<any>(null);

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const emit = defineEmits<{
  (e: "handleChange"): void;
}>();

const handleSearchTable = async (): Promise<void> => {
  loading.value = true;
  const { code, data } = await getSccsTradeLableList({
    sccsId: props.basicInfo.id
  });
  if (code === 0) {
    tableData.value = data;
    anchorList.value = data.map(anchor => {
      return {
        title: anchor.name || "订单基本信息",
        ref: `collaspse-${anchor.id}`,
        name: anchor.id
      };
    });
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleEdit = (data: any, dataRow: any): void => {
  SccsPlanLabelSettingEditDialogRef.value.open(data, "edit", dataRow);
};

const handleDelete = (data: any, milname: string, deleteId: string): void => {
  const removeTitle = `${t("trade_common_removeTip")}${milname ? milname : "订单基本信息"}${t("trade_sccs_labeltip")}「${data.tableName}」？`;
  ElMessageBox.confirm(removeTitle, t("trade_common_deleteTag"), {
    confirmButtonText: t("trade_common_confirm"),
    cancelButtonText: t("trade_common_cancel"),
    confirmButtonClass: "confrim-message-btn-class",
    cancelButtonClass: "cancel-message-btn-class",
    type: "error",
    icon: markRaw(WarningFilled),
    center: true
  }).then(async () => {
    const { code } = await deleteSccsLabel({ id: deleteId });
    if (code === 0) {
      handleSearchTable();
      ElMessage({
        message: t("trade_common_deleteSuccess"),
        type: "success"
      });
      emitter.emit("handleReloadOrderPageData");
    }
  });
};

const getLabelConditions = (labelConditions: any) => {
  return labelConditions.split("</br>或</br>");
};

const getLabelConditionCol = (labelConditionCol: string) => {
  const labelColList = labelConditionCol.split("</br>且</br>");
  let labelHtml = "";
  labelColList.forEach(labelText => {
    labelHtml += `<br />且<span class="label-conditions-tag-text">${labelText}</span>`;
  });
  return labelHtml.substr(7);
};

const getTableData = (tableData: any) => {
  let labelDetail = [];
  tableData.map(tableRow => {
    tableRow.labelDetail.map(tableDetail =>
      labelDetail.push(
        Object.assign(tableDetail, {
          tableName: tableRow.name,
          tableId: tableRow.id,
          isMainForm: tableRow.isMainForm,
          name: tableRow.name
        })
      )
    );
  });
  return labelDetail;
};

const objectSpanMethod = (
  { row, column, rowIndex, columnIndex },
  tableData
) => {
  if (columnIndex === 0 || columnIndex === 4) {
    // 假设我们只根据第一列内容合并
    if (rowIndex > 0 && row.tableName === tableData[rowIndex - 1].tableName) {
      return { rowspan: 0, colspan: 1 };
    } else {
      let rowspan = 1;
      for (let i = rowIndex + 1; i < tableData.length; i++) {
        if (tableData[i].tableName === row.tableName) {
          rowspan++;
        } else {
          break;
        }
      }
      return { rowspan, colspan: 1 };
    }
  }
};

const handleAdd = async (data: any): Promise<void> => {
  SccsPlanLabelSettingEditDialogRef.value.open(data, "add");
};

watch(
  () => props.basicInfo.id,
  () => {
    if (props.basicInfo && props.basicInfo.id) {
      handleSearchTable();
    }
  },
  {
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
@use "../index.scss";

.team-module-area-body {
  margin-top: 0 !important;
  margin-bottom: 0 !important;

  .team-module-area-btn {
    position: absolute;
    top: 0;
    right: 0;
  }
}

.label-conditions-tag {
  .label-conditions-tag-row {
    box-sizing: border-box;
    display: inline-block;
    padding: 0 9px;
    font-size: 12px;
    color: #262626;
    background: #f2f2f2;
    border-radius: 3px;

    ::v-deep(.delete-field) {
      color: #f56c6c;
      text-decoration: line-through;
    }
  }

  .label-conditions-tag-tip {
    display: block;
    margin: 5px 0;
    font-size: 14px;
    line-height: 20px;
    color: #262626;
    text-align: left;
  }

  &:last-child {
    .label-conditions-tag-tip {
      display: none;
    }
  }
}

.sccs-plan-tip-text {
  display: inline-block;
  margin: 10px 0;
  font-size: 12px;
  line-height: 16px;
  color: #8c8c8c;
  text-align: left;

  .iconfont {
    margin-right: 5px;
    font-size: 12px;
  }

  .sccs-plan-tip-text-red {
    margin-right: 6px;
    color: #f56c6c;
    text-decoration: line-through;
  }
}

::v-deep(.el-table) {
  .table-header-class {
    .el-table__cell {
      background: #f7f7f7 !important;
    }
  }

  .el-table__row:hover {
    background: transparent !important;

    .el-table__cell {
      background: transparent !important;
    }
  }

  .el-popper {
    max-width: 600px !important;
  }
}

.sccs-planned-row-tip {
  margin: 10px 0;
  font-size: 12px;
  line-height: 14px;
  color: #808080;
  text-align: left;
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}

.fence-list-tip {
  // padding-left: 14px;
  padding: 0 14px;
  font-size: 14px;
  // height: 38px;
  line-height: 38px;
  color: #fa8d0a;
  text-align: left;
  background: #fdf6ec;
  border-radius: 2px;

  .iconfont {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
  }
}

.sccs-planned-label-table-body {
  width: calc(100% - 16px);

  .sccs-planned-label-title {
    display: flex;
    align-items: center;
    margin: 30px 0 13px;
    font-size: 14px;
    font-weight: bold;
    line-height: 16px;
    color: #262626;
    text-align: left;

    .sccs-planned-label-flex {
      display: inline-flex;
      flex: 2;
      align-items: center;
      max-width: calc(100% - 70px);

      .sccs-planned-label-row-text {
        align-items: center;
        font-size: 14px;
        font-weight: bolder;
        color: #262626;
        text-align: left;
      }

      .sccs-planned-label-tip {
        position: relative;
        margin-left: 6px;
        font-size: 14px;
        font-weight: bolder;
        color: #262626;
        text-align: left;

        &::before {
          position: absolute;
          top: 50%;
          left: -4px;
          display: inline-block;
          width: 2px;
          height: 2px;
          content: "";
          background: #262626;
          border-radius: 50%;
        }
      }
    }

    .sccs-planned-label-flex-right {
      flex: none;
      width: 70px;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      color: #0070d2;
      text-align: right;
      cursor: pointer;

      .iconfont {
        font-size: 14px;
      }
    }
  }
}

.table-column-label-tag {
  display: inline-block;
  max-width: 100px;
  padding: 0 8px;
  overflow: hidden;
  font-size: 12px;
  line-height: 20px;
  color: #e33e38;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  background: transparent;
  border: 1px solid #e33e38;
  border-radius: 11px;
}

.team-empty-body {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}
</style>
<style lang="scss">
.anchor-container {
  position: fixed !important;
  top: 130px !important;

  .iconfont {
    color: #0070d2;
  }
}
</style>
