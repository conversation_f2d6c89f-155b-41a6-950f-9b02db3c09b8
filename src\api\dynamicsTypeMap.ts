export const dynamicsTypeMap = [
  // 工单变更
  {
    CREATE_WORK_ORDER: "创建工单",
    icon: "link-add"
  },
  {
    IMPORT_WORK_ORDER: "导入工单",
    icon: "link-import"
  },
  ,
  {
    EDIT_WORK_ORDER: "编辑工单",
    icon: "link-edit"
  },
  {
    REPLY_WORK_ORDER: "批复工单",
    icon: "link-reply"
  },
  //   工单操作
  {
    UPDATE_COLLECT_USER: "修改采集人",
    icon: "link-modify-processor"
  },
  {
    UPDATE_REPLY_USER: "修改批复人",
    icon: "link-modify-processor"
  },

  {
    ASSIGN_WORK_ORDER: "协作方指派采集",
    icon: "link-distribute"
  },
  {
    ASSIGN_WORK_ORDER_REPLY: "协作方指派批复",
    icon: "link-reply"
  },
  {
    COOP_UPDATE_COLLECT_USER: "协作方修改采集人",
    icon: "link-modify-processor"
  },
  {
    COOP_UPDATE_REPLY_USER: "协作方修改批复人",
    icon: "link-modify-processor"
  },

  {
    STOP_WORK_ORDER: "终止采集",
    icon: "link-zhongzhi"
  },
  {
    CLONE_TO_WORK_ORDER: "工单互用于",
    icon: "link-shared-in"
  },
  {
    CANCEL_CLONE_TO_WORK_ORDER: "取消互用",
    icon: "link-cancelled"
  },
  // 里程碑批复动态类型
  {
    CREATE_MILESTONE_REPLY: "创建里程碑批复",
    icon: "link-add"
  },
  {
    EDIT_MILESTONE_REPLY: "编辑里程碑批复",
    icon: "link-edit"
  },
  {
    UPDATE_MILESTONE_REPLY_USER: "修改里程碑批复人",
    icon: "link-modify-processor"
  },

  {
    ASSIGN_MILESTONE_REPLY: "协作方指派里程碑批复",
    icon: "link-distribute"
  },
  {
    COOP_UPDATE_MILESTONE_REPLY_USER: "协作方修改里程碑批复人",
    icon: "link-edit"
  }
];
