import { watch, onUnmounted, type WatchSource, type WatchOptions } from "vue";

/**
 * 监听依赖变更，满足条件时仅执行一次函数，然后自动销毁监听
 * @param source 要监听的响应式数据源
 * @param conditionFn 判断条件的函数，返回true时执行回调
 * @param callbackFn 满足条件时执行的回调函数
 * @param options watch的选项
 */
export default function useOnceWhen<
  T = any,
  S extends WatchSource<T> | WatchSource<T>[] | (() => T) | Array<() => T> =
    | WatchSource<T>
    | WatchSource<T>[]
    | (() => T)
    | Array<() => T>
>(
  source: S,
  conditionFn: (...args: any[]) => boolean,
  callbackFn: (...args: any[]) => void,
  options: WatchOptions = {}
): () => void {
  let unwatch: (() => void) | null = null;

  unwatch = watch(
    source as any,
    (...args) => {
      if (conditionFn(...args)) {
        // 先停止监听，防止回调中的操作再次触发
        if (unwatch) {
          unwatch();
          unwatch = null;
        }

        // 执行回调
        callbackFn(...args);
      }
    },
    { immediate: true, deep: true, ...options }
  );

  // 组件卸载时自动清理
  onUnmounted(() => {
    if (unwatch) {
      unwatch();
      unwatch = null;
    }
  });

  // 返回手动停止监听的函数
  return () => {
    if (unwatch) {
      unwatch();
      unwatch = null;
    }
  };
}
