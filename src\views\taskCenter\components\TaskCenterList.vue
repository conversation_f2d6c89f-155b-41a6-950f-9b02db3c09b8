<template>
  <div class="infinite-list-wrapper" style="overflow: auto">
    <ul
      v-if="currentMenuTabList.length > 0"
      v-infinite-scroll="loadList"
      class="list"
      :infinite-scroll-disabled="disabled"
    >
      <li
        v-for="(taskItem, index) in currentMenuTabList"
        :key="index"
        class="list-item"
      >
        <TaskCenterCard
          :itemData="taskItem"
          @handleConfirmSuccess="handleConfirmSuccess"
        />
      </li>
      <div
        v-if="currentMenuTabList.length > 0 && loading"
        class="list-tip loading"
      >
        {{ `${t("trade_common_loadingText")}...` }}
      </div>
      <div v-if="currentMenuTabList.length > 0 && noMore" class="list-tip">
        {{ t("trade_no_more_tip") }}
      </div>
    </ul>
    <el-empty
      v-else-if="!currentMenuTabList.length && !loading"
      :description="t('trade_no_task')"
      :image-size="342"
      :image="taskNoImage"
    />
  </div>
</template>
<script lang="ts" setup>
import TaskCenterCard from "@/views/taskCenter/components/TaskCenterCard.vue";
import taskNoImage from "@/assets/images/taskCenter/task-center-no-task.png";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const emit = defineEmits(["loadList", "update"]);
const props = defineProps({
  currentMenuTabList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pageInfo: {
    type: Object,
    default: () => ({})
  },
  disabled: {
    type: Boolean,
    default: false
  },
  noMore: {
    type: Boolean,
    default: false
  }
});

const loadList = () => {
  if (props.loading || props.noMore) return;
  emit("loadList");
};
const handleConfirmSuccess = () => {
  emit("update");
};
</script>
<style lang="scss" scoped>
.infinite-list-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-height: 1100px;
  padding: 2px 12px 20px 20px;
  background: #f5f6fa;

  ::-webkit-scrollbar-track {
    background-color: #f5f6fa;
  }

  .list-tip {
    width: 100%;
    margin-top: 20px;
    font-size: 14px;
    color: #bfbfbf;
    text-align: center;

    &.loading {
      color: #409eff;
    }
  }
}

.infinite-list-wrapper::-webkit-scrollbar-track {
  background-color: #f5f6fa;
}

.infinite-list-wrapper .list {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
}

.infinite-list-wrapper .list-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.infinite-list-wrapper .list-item + .list-item {
  margin-top: 20px;
}
</style>
