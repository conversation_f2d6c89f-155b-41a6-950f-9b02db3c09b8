<template>
  <div class="team-module-area-body">
    <el-form
      ref="FormRef"
      label-position="top"
      label-width="auto"
      :model="form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_inputSearch')}：`">
            <el-input
              v-model="form.dataFenceName"
              clearable
              :placeholder="t('trade_sccs_dataFenceNameTip')"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="`${t('trade_sccs_restrictingMembers')}：`">
            <LkTeamSelectV2
              v-model="form.userId"
              filterable
              :options="sccsList"
              clearable
              :placeholder="t('trade_common_selectText')"
              :props="{ label: 'username', value: 'userId' }"
              :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            :label="`${t('trade_sccs_restrictingCollaborativeTeams')}：`"
          >
            <LkTeamSelectV2
              v-model="form.coopTeamId"
              filterable
              :options="sccsCoopTeamList"
              clearable
              :placeholder="t('trade_common_selectText')"
              :props="{ label: 'teamName', value: 'teamId' }"
              :teamRenderFields="{ avatar: 'teamAvatar', label: 'teamName' }"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="margin-top: 30px">
            <el-button @click="resetForm">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchTable"
            >
              {{ t("trade_common_query") }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="fence-list-tip">{{ t("trade_sccs_restrictingTip") }}</div>
    <el-table
      :data="tableData"
      :border="true"
      stripe
      :tooltip-options="{ showAfter: 500 }"
      :empty-text="
        loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
      "
    >
      <el-table-column
        :label="t('trade_sccs_dataFenceName')"
        width="255"
        prop="dataFenceName"
        show-overflow-tooltip
      />
      <el-table-column
        :label="t('trade_sccs_restrictingMembers')"
        prop="userList"
        class-name="el-table-sccs-setting-cell"
      >
        <template #default="scope">
          <ReText
            v-for="item in scope.row.userList"
            :key="item.memberId"
            class="tag-col"
            :tippyProps="{ delay: 50000 }"
          >
            <span v-if="!item.activate" class="tag-register-tip">
              ({{ t("trade_common_unregistered") }})
            </span>
            <LkAvatar
              :size="18"
              fit="cover"
              :teamInfo="{
                avatar: item.avatar,
                username: item.username,
                email: item.email || item.account
              }"
            />
            {{ item.username }}
          </ReText>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('trade_sccs_restrictingCollaborativeTeams')"
        prop="coopTeamList"
        class-name="el-table-sccs-setting-cell"
      >
        <template #default="scope">
          <ReText
            v-for="item in scope.row.coopTeamList"
            :key="item.memberId"
            class="tag-col"
            :tippyProps="{ delay: 50000 }"
          >
            <LkAvatar
              :size="18"
              fit="square"
              class="colorOrange"
              :teamInfo="{
                avatar: item.teamAvatar,
                username: item.teamName
              }"
            />
            {{ item.teamName }}
          </ReText>
        </template>
      </el-table-column>
      <el-table-column width="70" align="center">
        <template #default="{ row }">
          <LkTableOperate
            :tableOpeateLists="tableOpeateLists"
            :messageBoxConfirmObject="messageBoxConfirmObject"
            :row="row"
            @handleOperate="handleOperate"
          />
        </template>
      </el-table-column>
    </el-table>
    <LkPagination
      ref="LkPaginationRef"
      :total="total"
      @updatePagination="handleSearchTable"
    />
    <LkDialog
      ref="LkDialogRef"
      class="lk-middle-dialog"
      :title="dialogTitle"
      @confirm="handleConfirmDialog"
      @close="handleClose"
    >
      <template #default>
        <div class="create-team-dialog-form">
          <el-form
            ref="ruleFormRef"
            label-position="top"
            :model="ruleForm"
            :rules="rules"
            label-width="auto"
          >
            <div class="fence-list-tip2">
              <p>{{ t("trade_sccs_dataFenceTip1") }}</p>
              <p>{{ t("trade_sccs_dataFenceTip2") }}</p>
            </div>
            <el-form-item
              :label="t('trade_sccs_dataFenceName')"
              prop="dataFenceName"
            >
              <el-input
                v-model="ruleForm.dataFenceName"
                clearable
                :placeholder="t('trade_sccs_dataFenceNameTip')"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            <el-form-item :label="`${t('trade_sccs_restrictingMembers')}：`">
              <LkTeamSelectV2
                v-model="ruleForm.userIdList"
                filterable
                :options="sccsList"
                clearable
                :placeholder="t('trade_common_selectText')"
                multiple
                :props="{ label: 'username', value: 'userId' }"
                value-id="userId"
                :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
              />
            </el-form-item>
            <el-form-item
              :label="`${t('trade_sccs_restrictingCollaborativeTeams')}：`"
            >
              <LkTeamSelectV2
                v-model="ruleForm.coopTeamIdList"
                filterable
                :options="sccsCoopTeamList"
                clearable
                :placeholder="t('trade_common_selectText')"
                multiple
                :props="{ label: 'teamName', value: 'teamId' }"
                :teamRenderFields="{ avatar: 'teamAvatar', label: 'teamName' }"
              />
            </el-form-item>
            <el-form-item :label="`${t('trade_sccs_setWidgetList')}：`">
              <el-scrollbar max-height="calc(100% - 250px)" style="width: 100%">
                <SccsDataFenceFields
                  :dataFenceWidgetHiddenIds="ruleForm.widgetHiddenIdList"
                  @handleWidgetHiddenIds="handleGetDataFenceWidgetHiddenIds"
                />
              </el-scrollbar>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { computed, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { message } from "@/utils/message";
import type { FormRules } from "element-plus";
import LkDialog from "@/components/lkDialog/index";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import LkAvatar from "@/components/lkAvatar/index";
import LkPagination from "@/components/lkPagination/index";
import LkTableOperate from "@/components/lkTableOperate/index";
import SccsDataFenceFields from "@/views/sccsManage/components/SccsDataFence/SccsDataFenceFields.vue";
import { ReText } from "@/components/ReText";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";
import {
  getSccsCoopTeamList,
  getSccsMemberList,
  deleteDataFence,
  getDataFenceList,
  saveDataFence,
  getSccsDataFenceLabelList
} from "@/api/sccs";

const { t } = useI18n();
const FormRef = ref<any | HTMLElement>(null);
const LkPaginationRef = ref<any | HTMLElement>(null);
const SccsCollaboratorsRoleRef = ref<any | HTMLElement>(null);
const loading = ref<boolean>(false);
const tableData = ref<any[]>([]);
const sccsList = ref<any[]>([]);
const sccsCoopTeamList = ref<any[]>([]);
const total = ref<number>(0);
const dialogState = ref<string>("add");
const LkDialogRef = ref<any>(null);
const ruleFormRef = ref<any>(null);
const dataFenceId = ref<string>("");
const dataFenceLabelList = ref<any>([]);
const widgetHiddenIdLists = ref<string[]>([]);
const ruleForm = reactive<any>({
  dataFenceName: [],
  userIdList: [],
  coopTeamIdList: [],
  widgetHiddenIdList: []
});
const form = reactive({
  dataFenceName: "",
  userId: "",
  coopTeamId: ""
});
const rules = reactive<FormRules<any>>({
  dataFenceName: [
    {
      required: true,
      message: t("trade_sccs_dataFenceNameTip"),
      trigger: "blur"
    }
  ]
});
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_sccs_deleteDataFence",
  messageBoxTitle: "trade_sccs_delDataFence",
  messageBoxTipArray: ["dataFenceName"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-edit",
    title: "trade_common_edit",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const dialogTitle = computed(() => {
  return dialogState.value === "add"
    ? t("trade_sccs_addDataFence")
    : t("trade_sccs_editDataFence");
});

const resetForm = () => {
  form.dataFenceName = "";
  form.userId = "";
  form.coopTeamId = "";
  handleSearchTable();
};

const handleClose = (): void => {
  ruleForm.dataFenceName = [];
  ruleForm.userIdList = [];
  ruleForm.coopTeamIdList = [];
  ruleForm.widgetHiddenIdList = [];
  ruleFormRef.value.clearValidate();
};

const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "edit") {
    handleEdit(row);
  } else {
    handleDeleteTable(row.id);
  }
};

const handleGetDataFenceWidgetHiddenIds = (dataFenceWidgetHiddenIds: any[]) => {
  widgetHiddenIdLists.value = dataFenceWidgetHiddenIds;
};

const handleSearchTable = async (): Promise<void> => {
  loading.value = true;
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getDataFenceList({
    sccsId: props.basicInfo.id,
    ...pageParams,
    ...form
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleEdit = (rowData: any): void => {
  const { id, dataFenceName, userIdList, coopTeamIdList, widgetList } = rowData;
  dataFenceId.value = id;
  ruleForm.dataFenceName = dataFenceName;
  ruleForm.userIdList = userIdList;
  ruleForm.coopTeamIdList = coopTeamIdList;
  ruleForm.widgetHiddenIdList = widgetList;
  dialogState.value = "edit";
  LkDialogRef.value.open();
};

const handleConfirmDialog = async (): Promise<void> => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      let dataFenceIdObject = {};
      if (dataFenceId.value) {
        dataFenceIdObject["id"] = dataFenceId.value;
      }

      const { code } = await saveDataFence({
        ...dataFenceIdObject,
        sccsId: props.basicInfo.id,
        widgetList: widgetHiddenIdLists.value,
        ...ruleForm
      });
      if (code === 0) {
        message(t("trade_common_dealSuccess"), {
          customClass: "el",
          type: "success"
        });
        LkDialogRef.value.close();
        ruleFormRef.value.resetFields();
        handleSearchTable();
      }
    }
  });
};

const handleDeleteTable = async (id): Promise<void> => {
  const res = await deleteDataFence({ id: id, sccsId: props.basicInfo.id });
  if (res.code === 0) {
    message(t("trade_common_deleteSuccess"), {
      customClass: "el",
      type: "success"
    });
    handleSearchTable();
  }
};

const handleCreateTeamRole = (): void => {};

const init = (): void => {
  loading.value = true;
  const sccsId = props.basicInfo.id;
  Promise.all([
    getSccsCoopTeamList({ sccsId: sccsId }),
    getSccsMemberList({ sccsId: sccsId }),
    getSccsDataFenceLabelList({
      sccsId: sccsId
    })
  ])
    .then(res => {
      sccsCoopTeamList.value = res[0].data;
      sccsList.value = res[1].data;
      dataFenceLabelList.value = res[2].data;
      handleSearchTable();
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 300);
    });
};

const addSccsBusinessRole = (): void => {
  SccsCollaboratorsRoleRef.value.open();
};

const findUniqueCommonElements = (arrA: any[], arrB: any[]) => {
  const setB = new Set(arrB);
  const setAInB = new Set(arrA.filter(element => setB.has(element)));
  return Array.from(setAInB);
};

watch(
  () => props.basicInfo.id,
  () => {
    if (props.basicInfo && props.basicInfo.id) {
      init();
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const open = () => {
  dialogState.value = "add";
  LkDialogRef.value.open();
};

defineExpose({
  dataFenceId,
  open,
  handleCreateTeamRole,
  addSccsBusinessRole
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.team-module-area-body {
  margin-top: 0 !important;

  .team-module-area-btn {
    position: absolute;
    top: 0;
    right: 0;
  }

  ::v-deep(.tag-col) {
    padding: 3px 6px;
    margin-right: 4px;
    font-size: 12px;
    line-height: 20px;
    color: #595959;
    background: #f0f0f0;
    border: 0 none;
    border-radius: 4px;
  }
}

.create-team-dialog-form {
  height: 100%;
}

.data-fence-label-body {
  margin-bottom: 16px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;

  .data-fence-label-header {
    height: 48px;
    margin: 0 15px;
    line-height: 48px;
    border-bottom: 1px solid #eee;

    .data-fence-label-title {
      margin-right: 27px;
      font-size: 16px;
      font-weight: bolder;
      line-height: 16px;
      color: #262626;
      text-align: left;
    }
  }

  .data-fence-label-container {
    margin: 15px;

    .label-col {
      margin-bottom: 20px;

      .data-fence-label-name {
        display: block;
        font-size: 14px;
        line-height: 16px;
        color: #262626;
        text-align: left;
      }

      .el-checkbox {
        ::v-deep(.el-checkbox__label) {
          font-size: 12px;
          color: #262626;
        }
      }
    }
  }
}

.fence-list-tip {
  height: 38px;
  padding-left: 14px;
  margin-bottom: 14px;
  font-size: 14px;
  line-height: 38px;
  color: #fa8d0a;
  text-align: left;
  background: #fdf6ec;
  border-radius: 2px;
}

.fence-list-tip2 {
  padding: 12px 14px;
  margin-bottom: 14px;
  background: #fdf6ec;
  border-radius: 2px;

  p {
    font-size: 14px;
    line-height: 20px;
    color: #fa8d0a;
    text-align: left;
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
