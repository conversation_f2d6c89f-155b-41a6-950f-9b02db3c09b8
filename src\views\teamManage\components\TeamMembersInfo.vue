<template>
  <div
    v-loading="loading"
    class="team-member-info-container team-module-area-container"
    :class="[
      !tablePermission ? 'team-module-area-permission-empty-container' : ''
    ]"
  >
    <div v-show="tablePermission" class="team-module-area-header">
      <div class="team-module-area-title">
        {{ t("trade_team_teamRoleManagement") }}
      </div>
      <el-button type="primary" color="#0070D2" @click="handleCreateTeamRole">
        <FontIcon icon="link-add" />{{ t("trade_common_create") }}
      </el-button>
    </div>
    <div v-show="tablePermission" class="team-module-area-body">
      <el-form
        ref="FormRef"
        label-position="top"
        label-width="auto"
        :model="form"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="`${t('trade_team_inputSearch')}：`">
              <el-input
                v-model="form.roleName"
                clearable
                :placeholder="t('trade_team_teamRoleName')"
                @change="handleSearchTable"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="`${t('trade_team_members')}：`">
              <LkTeamSelectV2
                v-model="form.memberId"
                filterable
                :options="memberList"
                clearable
                :placeholder="t('trade_common_selectText')"
                value-key="id"
                :props="{ label: 'username', value: 'id' }"
                :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
                @change="handleSearchTable"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item style="margin-top: 30px">
              <el-button @click="resetForm">
                {{ t("trade_common_reSet") }}
              </el-button>
              <el-button
                type="primary"
                color="#0070D2"
                @click="handleSearchTable"
              >
                {{ t("trade_common_query") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        :border="true"
        stripe
        max-height="calc(100vh - 280px)"
        :tooltip-options="{ showAfter: 500 }"
        :empty-text="loading ? '' : t('trade_common_emptyTip')"
      >
        <el-table-column
          :label="t('trade_team_teamRoleName')"
          width="255"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="scope">
            <ReText
              :class="[
                'tag-col',
                scope.row.manager && scope.row.owner
                  ? 'tag-owner'
                  : scope.row.manager
                    ? 'tag-manager'
                    : ''
              ]"
              :tippyProps="{ delay: 50000 }"
            >
              {{ scope.row.roleName }}
            </ReText>
            <span v-if="scope.row.roleMark">
              <el-tooltip
                :show-after="500"
                effect="dark"
                :content="scope.row.roleMark"
                placement="top"
              >
                <i
                  class="iconfont link-explain font12 color8c ml3"
                  style="cursor: pointer"
                />
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_team_members')"
          class-name="el-table-sccs-setting-cell2"
        >
          <template #default="scope">
            <ReText
              v-for="item in scope.row.memberUserList"
              :key="item.memberId"
              class="tag-col"
              :tippyProps="{ delay: 50000 }"
            >
              <span v-if="!item.activate" class="tag-register-tip">
                ({{ t("trade_common_unregistered") }})
              </span>
              <LkAvatar
                :size="18"
                fit="cover"
                :teamInfo="{
                  avatar: item.avatar,
                  username: item.username
                }"
              />
              <span class="tag-col-text">{{ item.username }}</span>
            </ReText>
          </template>
        </el-table-column>
        <el-table-column width="70" align="center">
          <template #default="{ row }">
            <span v-if="!row.owner || !row.manager">
              <LkTableOperate
                :tableOpeateLists="getTableOpeate(tableOpeateLists, row)"
                :messageBoxConfirmObject="messageBoxConfirmObject"
                :row="row"
                @handleOperate="handleOperate"
              />
            </span>
          </template>
        </el-table-column>
      </el-table>
      <LkPagination
        ref="LkPaginationRef"
        :total="total"
        @updatePagination="handleSearchTable"
      />
      <LkDialog
        ref="LkDialogRef"
        :title="
          dialogStatus === 'add'
            ? t('trade_team_createTeamRole')
            : t('trade_team_editTeamRole')
        "
        class="lk-maximum-dialog"
        @confirm="confrimLkDialog"
        @close="handleCloseDialog"
      >
        <template #default>
          <div class="create-team-dialog-form">
            <el-form
              ref="ruleFormRef"
              label-position="top"
              :model="ruleForm"
              :rules="rules"
              label-width="auto"
            >
              <el-form-item
                :label="t('trade_team_teamRoleName')"
                prop="roleName"
              >
                <el-input
                  v-model="ruleForm.roleName"
                  clearable
                  :placeholder="t('trade_team_teamRoleName')"
                  show-word-limit
                  maxlength="100"
                  :disabled="formDisable"
                />
              </el-form-item>
              <el-form-item :label="t('trade_team_members')">
                <LkTeamSelectV2
                  v-model="ruleForm.memberIdList"
                  filterable
                  :options="memberList"
                  clearable
                  :placeholder="t('trade_common_selectText')"
                  value-key="id"
                  :props="{ label: 'username', value: 'id' }"
                  multiple
                  :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
                />
              </el-form-item>
              <el-form-item class="role-permission-body">
                <TeamMemberPermissionCheckBox
                  v-for="(item, index) in checkboxList"
                  ref="TeamMemberPermissionCheckBoxRef"
                  :key="index"
                  :disabled="formDisable"
                  :checkboxData="item"
                  :bindData="ruleForm.permKeyList"
                />
              </el-form-item>
            </el-form>
          </div>
        </template>
      </LkDialog>
    </div>
    <LkPermissionEmpty v-show="!tablePermission" />
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { onMounted, reactive, ref } from "vue";
import {
  getTeamRoleList,
  getTeamRolePermissionTree,
  createTeamRole,
  updateTeamRole,
  deleteTeamRole
} from "@/api/team";
import { getMemberAll } from "@/api/member";
import LkPagination from "@/components/lkPagination/index";
import LkDialog from "@/components/lkDialog/index";
import LkAvatar from "@/components/lkAvatar";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import { useDisabled, type FormInstance, type FormRules } from "element-plus";
import TeamMemberPermissionCheckBox from "./TeamMemberPermissionCheckBox.vue";
import { message } from "@/utils/message";
import LkTableOperate from "@/components/lkTableOperate";
import LkPermissionEmpty from "@/components/lkPermissionEmpty/index";
import { cloneDeep } from "@pureadmin/utils";
import { ReText } from "@/components/ReText";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";

interface RoleListProp {
  roleName: string;
  memberIdList: string[];
  permKeyList: string[];
}

const { t } = useI18n();
const loading = ref<boolean>(true);
const LkPaginationRef = ref<any>(null);
const LkDialogRef = ref<any>(null);
const FormRef = ref<any>(null);
const ruleFormRef = ref<any>(null);
const TeamMemberPermissionCheckBoxRef = ref<any>(null);
const memberList = ref<any[]>([]);
const operateId = ref<string>("");
let tableData = ref<any[]>([]);
let total = ref<number>(0);
let checkboxList = ref<any[]>([]);
const formDisable = ref<boolean>(true);
const dialogStatus = ref<string>("add");
const tablePermission = ref<boolean>(true);
const form = reactive({
  roleName: "",
  memberId: ""
});
let ruleForm = reactive<RoleListProp>({
  roleName: "",
  memberIdList: [],
  permKeyList: []
});
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_team_deleteTeamRole",
  messageBoxTitle: "trade_team_deleteTeamRoles",
  messageBoxTipArray: ["roleName"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-edit",
    title: "trade_common_edit",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);
const rules = reactive<FormRules<RoleListProp>>({
  roleName: [
    {
      required: true,
      message: t("trade_team_inputTeamRoleName"),
      trigger: "blur"
    }
  ]
});

const getTeamMemberList = async (): Promise<void> => {
  const res = await getMemberAll();
  if (res.code === 0) {
    memberList.value = res.data;
  }
};

const getTableOpeate = (tableOpeateLists: TableOperateProp[], data: any) => {
  if (data.manager) {
    return cloneDeep(tableOpeateLists).splice(0, 1);
  } else {
    return cloneDeep(tableOpeateLists);
  }
};

const handleCreateTeamRole = () => {
  dialogStatus.value = "add";
  formDisable.value = false;
  LkDialogRef.value.open();
};

const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "edit") {
    let memberIdList: string[] = [];
    row.memberUserList.map(memberUser =>
      memberIdList.push(memberUser.memberId)
    );
    ruleForm.roleName = row.roleName;
    ruleForm.memberIdList = memberIdList;
    ruleForm.permKeyList = row.permKeyList;
    operateId.value = row.id;
    formDisable.value = row.manager;
    dialogStatus.value = "edit";
    LkDialogRef.value.open();
  } else {
    const res = await deleteTeamRole({ id: row.id });
    if (res.code === 0) {
      message(t("trade_common_deleteSuccess"), {
        customClass: "el",
        type: "success"
      });
      init();
    }
  }
};

const resetForm = () => {
  form.roleName = "";
  form.memberId = "";
  handleSearchTable();
};

const handleSearchTable = async (): Promise<void> => {
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getTeamRoleList({
    ...form,
    ...pageParams
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
    tablePermission.value = true;
  } else if (res.code === 403) {
    tablePermission.value = false;
  }
  loading.value = false;
};

const getTeamRolePermissionTreeFun = async (): Promise<void> => {
  const res = await getTeamRolePermissionTree();
  if (res.code === 0) {
    checkboxList.value = res.data;
  }
};

const confrimLkDialog = async (): Promise<void> => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      let permissionCheckId = [];
      const TeamPermissionCheckbox = TeamMemberPermissionCheckBoxRef.value;
      for (let i = 0, len = TeamPermissionCheckbox.length; i < len; i++) {
        permissionCheckId.push(...TeamPermissionCheckbox[i].checkedCities);
      }
      ruleForm.permKeyList = permissionCheckId;
      const res =
        dialogStatus.value === "add"
          ? await createTeamRole(ruleForm)
          : await updateTeamRole({ ...ruleForm, id: operateId.value });
      if (res.code === 0) {
        message(t("trade_common_dealSuccess"), {
          customClass: "el",
          type: "success"
        });
        ruleFormRef.value.resetFields();
        LkDialogRef.value.close();
        ruleForm.roleName = "";
        ruleForm.memberIdList = [];
        ruleForm.permKeyList = [];
        ruleFormRef.value.clearValidate();
        init();
      }
    }
  });
};

const handleCloseDialog = (): void => {
  ruleForm.roleName = "";
  ruleForm.memberIdList = [];
  ruleForm.permKeyList = [];
  ruleFormRef.value.clearValidate();
};

const init = () => {
  handleSearchTable();
  getTeamMemberList();
  getTeamRolePermissionTreeFun();
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

::v-deep(.tag-col) {
  padding: 3px 6px;
  margin-right: 4px;
  overflow: hidden;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  text-overflow: ellipsis;
  word-wrap: break-word;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  margin-bottom: 0 !important;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  text-overflow: ellipsis;
  word-wrap: break-word;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-permission-body-mask) {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1990;
    display: inline-block;
    width: 100%;
    height: 100%;
    background: transparent;
  }

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
