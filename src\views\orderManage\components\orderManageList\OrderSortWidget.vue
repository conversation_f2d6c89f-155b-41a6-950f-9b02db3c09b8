<template>
  <el-popover
    placement="top"
    width="363"
    trigger="click"
    :show-arrow="false"
    @hide="handleHidePopover"
  >
    <template #reference>
      <span>
        <el-tooltip
          :content="t('trade_common_sort')"
          :disabled="!!buttonTextVisible"
        >
          <span
            class="order-manage-btn"
            :class="[currentScreenWidth <= 1366 ? 'Screen_1366' : '']"
          >
            <i class="iconfont link-sort" />
            <span v-if="buttonTextVisible">{{ t("trade_common_sort") }}</span>
          </span>
        </el-tooltip>
      </span>
    </template>
    <div class="order-sort-widget-container">
      <div class="order-sort-widget-title">{{ t("trade_common_sortTip") }}</div>
      <draggable
        :list="tableFieldsList"
        :disabled="!enabled"
        item-key="groupName"
        class="order-field-draggable-container"
        ghost-class="ghost"
        chosen-class="chosen"
        animation="300"
        @start="dragging = true"
        @end="handleDraggingEnd"
      >
        <template #item="{ element, index }">
          <div
            class="order-field-draggable-col"
            :class="{ 'not-draggable': !enabled }"
          >
            <n-tag
              :key="element.id"
              class="item"
              :class="{ 'not-draggable': !enabled }"
            >
              <div class="vxe-table-draggable-container">
                <i class="iconfont link-drag" />
                <el-select-v2
                  v-model="element.sortBy"
                  class="vxe-table-fields-select-v2"
                  :options="orderFields"
                  :props="{ label: 'label', value: 'name' }"
                  :teleported="false"
                  @change="handleChangeTableFieldType(element)"
                >
                  <template #default="{ item }">
                    <ReText
                      type="info"
                      :tippyProps="{ delay: 50000 }"
                      style="color: #606266"
                    >
                      {{ t(item.label) }}
                    </ReText>
                  </template>
                </el-select-v2>
                <el-select-v2
                  v-model="element.descending"
                  class="vxe-table-sort-select-v2"
                  :options="sortOptions"
                  :teleported="false"
                >
                  <template #label="{ label }">{{ t(label) }}</template>
                  <template #default="{ item }">
                    <ReText type="info" :tippyProps="{ delay: 50000 }">
                      {{ t(item.label) }}
                    </ReText>
                  </template>
                </el-select-v2>
                <span
                  class="vxe-table-sort-icon"
                  @click="handleRemoveRow(index)"
                >
                  <i class="iconfont link-close" />
                </span>
              </div>
            </n-tag>
          </div>
        </template>
      </draggable>
      <div
        v-if="tableFieldsList.length < 3"
        class="order-sort-widget-bottom"
        @click="handleAddRow"
      >
        <i class="iconfont link-add" />
        {{ t("trade_common_addFields") }}
      </div>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import { inject, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";
import draggable from "vuedraggable";

interface sortOptionProp {
  label: string;
  value: boolean;
}

interface tableSortField {
  sortBy: string;
  descending: boolean;
  type: string;
}

const props = defineProps({
  orderFields: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  sortWidgetList: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  currentScreenWidth: {
    type: Number as PropType<any>,
    default: 0
  }
});

const buttonTextVisible = inject("buttonTextVisible");

const { t } = useI18n();
const dragging = ref<boolean>(false);
const enabled = ref<boolean>(true);
const tableFieldsList = ref<tableSortField[]>([]);

const sortOptions = ref<sortOptionProp[]>([
  {
    label: "trade_common_descending",
    value: true
  },
  {
    label: "trade_common_ascending",
    value: false
  }
]);

const emit = defineEmits<{
  (e: "handleTableSort", data): void;
}>();

const handleHidePopover = (): void => {
  emit("handleTableSort", tableFieldsList.value);
};

const handleDraggingEnd = (): void => {};

const handleAddRow = (): void => {
  tableFieldsList.value.push({
    sortBy: "",
    descending: false,
    type: ""
  });
};

const handleRemoveRow = (index: number): void => {
  tableFieldsList.value.splice(index, 1);
};

const handleChangeTableFieldType = (element: any) => {
  const orderField = props.orderFields.find(
    orderField => orderField.name === element.sortBy
  );
  element.type = orderField ? orderField.type : "";
};

watch(
  () => props.sortWidgetList,
  data => {
    tableFieldsList.value = props.sortWidgetList;
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped>
@use "../index.scss";
</style>
