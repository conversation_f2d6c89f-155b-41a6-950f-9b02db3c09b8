<template>
  <div ref="containerRef">
    <el-form
      ref="trendsFormWidgetFormRef"
      label-position="top"
      label-width="auto"
      :model="bindTrendsForm"
      scroll-to-error
    >
      <div
        v-if="createUser || (replayUsers && replayUsers.length > 0)"
        class="trends-collaspse-form-header"
      >
        <label v-if="createUser" class="trends-collaspse-form-header-title">
          {{ t("trade_common_createor") }}：
          <LkAvatarGroupNext
            :size="22"
            mode="reply"
            :avatarListGroup="[createUser]"
            :maxAvatar="4"
          />
        </label>
        <label
          v-if="replayUsers && replayUsers.length > 0"
          class="trends-collaspse-form-header-title"
        >
          {{ t("trade_common_milestone_replayor") }}：
          <LkAvatarGroupNext
            :size="22"
            mode="reply"
            :avatarListGroup="replayUsers"
            :maxAvatar="4"
          />
        </label>
      </div>
      <el-collapse v-model="dynamicFormActiveIds" class="trends-collaspse-form">
        <el-collapse-item
          v-for="collapseItem of formCollapseList"
          :key="collapseItem.id"
          :title="collapseItem.name"
          :name="collapseItem.id"
        >
          <template #title>
            <div class="collapse-form-item-title">{{ collapseItem.name }}</div>
            <slot name="collapseFormHeader" :collapseData="collapseItem" />
          </template>
          <div v-if="workOrderAllowOperation.includes(collapseItem.id)">
            <div v-if="collapseItem.widgetJsonList.length > 0">
              <div
                v-for="item in collapseItem.widgetJsonList"
                :key="item._fc_id"
              >
                <el-form-item
                  v-show="
                    !bindTrendsVisible.hasOwnProperty(item._fc_id) ||
                    (bindTrendsVisible.hasOwnProperty(item._fc_id) &&
                      bindTrendsVisible[item._fc_id])
                  "
                  :label-position="getLabelPosition(item.wrap)"
                  :label-width="item.wrap?.labelWidth"
                  :rules="widgetRule(item)"
                  :prop="item._fc_id"
                >
                  <template #label>
                    <div
                      v-if="!['DrCard', 'DrDivider'].includes(item.type)"
                      class="widget-form-item-label"
                    >
                      <el-tooltip
                        v-if="item.info"
                        effect="light"
                        :content="widgetInfo(item)"
                        placement="top"
                        popper-class="widget-popper-label-class"
                        :show-after="500"
                      >
                        <i
                          class="iconfont link-explain widget-form-item-label-icon"
                        />
                      </el-tooltip>
                      <div class="widget-form-item-title">
                        <span v-if="item.$required" class="widget-required">
                          *
                        </span>
                        {{ widgetLabelTitle(item) }}
                      </div>
                    </div>
                  </template>
                  <component
                    :is="item.type"
                    :widgetConfigure="item"
                    :trendsForm="bindTrendsForm"
                    :bindTrendFormId="formIdList"
                    :staffPerson="staffPersonObject"
                    :notRequriedList="notRequriedList"
                    :bindTrendsVisible="bindTrendsVisible"
                    :placeholder="widgetPlaceholder(item)"
                    :workOrderId="workOrderId"
                    :createState="createState"
                    :sccsId="sccsId"
                    :orderId="orderId"
                    :linkedRefrenceSourceFactoryData="
                      linkedRefrenceSourceFactoryObject
                    "
                  />
                </el-form-item>
              </div>
            </div>
            <div v-else>
              <el-empty
                style="background: #fff"
                :description="t('trade_common_emptyTip')"
                :image-size="314"
                :image="sccsGroupNoImage"
              />
            </div>
          </div>
          <div v-else class="order-readonly-collapse-item">
            <OrderDetailDescWidget
              :widgetForm="collapseItem.widgetJsonList"
              :widgetData="formWidgetData"
            />
          </div>
        </el-collapse-item>
        <slot name="otherCollapse" />
      </el-collapse>
    </el-form>
  </div>
</template>
<script lang="ts" setup>
import { computed, provide, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import { cloneDeep, debounce } from "lodash-es";
import sccsGroupNoImage from "@/assets/images/sccs/sccs-no-image.png";
import {
  calculateFormulasWidget,
  transformFormulasValue
} from "@/utils/formulasActuator/formulasActuator";
import { ExecuteFormVisibleExpression } from "@/utils/formVisibleExpression";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import {
  initFormModalData,
  obtainFormAllList,
  getWidgetRules
} from "@/utils/formDesignerUtils";
import { getSccsCoopTeamList, getSccsMemberList } from "@/api/sccs";
import { isEqual, storageLocal } from "@pureadmin/utils";
import { formatThousandNumber } from "@/utils/common";
import { useRoute } from "vue-router";

const props = defineProps({
  formList: {
    type: Array as PropType<any>,
    default: () => []
  },
  formWidgetData: {
    type: Array as PropType<any>,
    default: () => []
  },
  hiddenFormIdList: {
    type: Array as PropType<any>,
    default: () => []
  },
  trendsWidgetRequireFormIdList: {
    type: Array as PropType<any>,
    default: () => []
  },
  operationalFactorData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  defaultValueActuatorFlag: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  sccsId: {
    type: String as PropType<string>,
    default: ""
  },
  orderId: {
    type: String as PropType<string>,
    default: ""
  },
  createUser: {
    type: Object as PropType<any>,
    default: () => {}
  },
  replayUsers: {
    type: Array as PropType<any>,
    default: () => []
  },
  workOrderAllowOperation: {
    type: Array as PropType<any>,
    default: () => []
  },
  trendsFormFlag: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  workOrderId: {
    type: String as PropType<string>,
    default: ""
  },
  createState: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const { t } = useI18n();
const route = useRoute();
const trendsFormWidgetFormRef = ref<HTMLElement | any>(null);
const dynamicFormActiveIds = ref<any>([]);
const bindTrendsForm = ref<any>({}); // 表单绑定的值
const bindTrendsVisible = ref<any>({}); // 表单显隐绑定的对象值
const formWidgetObject = ref<any>({});
const trendsWidgetList = ref<any>([]);
const notRequriedList = ref<any[]>([]);
const linkedRefrenceSourceFactoryData = ref<any>([]);
const linkedRefrenceSourceFactoryObject = ref<any>({});
const linkedRefrenceSourceList = ref<any[]>([]);
const staffPersonObject = ref<any>({
  sccsMemberList: [],
  sccsCoopTeamList: []
});
const translationLang = storageLocal().getItem("translationLang");

const containerRef = ref<HTMLElement | any>(null);
provide("containerRef", containerRef);

const emit = defineEmits(["handleWidgetChange"]);

const formCollapseList = computed(() => {
  if (props.hiddenFormIdList.length === 0) {
    return props.formList;
  } else {
    return props.formList.filter(
      form => !props.hiddenFormIdList.includes(form.id)
    );
  }
});

const widgetRule = computed(() => {
  return item => {
    return notRequriedList.value.includes(item._fc_id) ||
      !(
        !bindTrendsVisible.value.hasOwnProperty(item._fc_id) ||
        (bindTrendsVisible.value.hasOwnProperty(item._fc_id) &&
          bindTrendsVisible.value[item._fc_id])
      )
      ? []
      : getWidgetRules(item.$required, item);
  };
});

const widgetLabelTitle = computed(() => {
  return item => {
    return translationLang === "en"
      ? item.props?.titleEn || item.title
      : item.title;
  };
});

const widgetInfo = computed(() => {
  return item => {
    return translationLang === "en"
      ? item.props?.infoEn || item.info
      : item.info;
  };
});

const widgetPlaceholder = computed(() => {
  return item => {
    return translationLang === "en"
      ? item.props?.placeholderEn || item.props?.placeholder
      : item.props?.placeholder;
  };
});

const formIdList = computed(() => {
  return props.formList.map(form => form.id);
});

const getLabelPosition = (wrap: any): any => {
  const wrapClass = wrap && wrap.class ? wrap.class.split("-")[2] : "top";
  return wrapClass;
};

const deduplicateByWidgetIdInPlace = arr => {
  const seen = new Set();
  let writeIndex = 0;

  for (let readIndex = 0; readIndex < arr.length; readIndex++) {
    const item = arr[readIndex];
    if (!seen.has(item.widgetId)) {
      seen.add(item.widgetId);
      arr[writeIndex++] = item;
    }
  }

  arr.length = writeIndex; // 截断数组
  return arr;
};

/**
 * 初始化表单
 * 1.判断当前表单是否有控件的值，有的话渲染控件的值,无则渲染默认值
 * 2.根据表单当前绑定的值执行一次公式控件的运算
 * 3.根据表单绑定的值在执行一次表单显隐
 * 4.根据表单的值和公式的值在执行表单默认值运算
 * @param formList
 */
const initAggregationForm = (formList: any, formData: any) => {
  // 1.判断当前表单是否有控件的值，有的话渲染控件的值
  const widgetJsonList = obtainFormAllList(
    formList.flatMap(form => form.widgetJsonList)
  );
  const bindFormData = initFormModalData(widgetJsonList, cloneDeep(formData));

  let formulasBindData = {};
  if (!props.trendsFormFlag) {
    // 2.聚合所有表单项的值，进行公式计算
    const formulasWidgetJsonList = obtainFormAllList(
      formList.flatMap(form => form.widgetJsonList)
    ).filter(widget => widget.type === "DrFormulas");

    // 2.1先把公式互相套用的结果选出来，并计算出公式套公式的结果是什么，在将这个结果和表单当前聚合项合并进行运算
    let needExecFormulasList = [];
    const inFormWidgetIdLists = obtainFormAllList(
      formList.flatMap(form => form.widgetJsonList)
    ).map(widget => widget._fc_id);

    let factorDataObject =
      cloneDeep(props.operationalFactorData?.entireDetail) || {};
    let formulasWidgetDataList = [];
    let formulasFormObject = {};

    for (let formulasWidget of formulasWidgetJsonList) {
      if (
        formulasWidget.props.formulaObject &&
        formulasWidget.props.formulaObject.formulas
      ) {
        const formulasParams =
          formulasWidget.props.formulaObject.formulasParams;
        if (formulasParams) {
          const factoryFormulasParams = Array.from(
            new Set(
              formulasParams.filter(factor => factor.indexOf("Formulas") > -1)
            )
          ).map((factoryId: any) => factoryId.split("@@")[1]);

          const setB = new Set(inFormWidgetIdLists);

          const hasCommonElements = factoryFormulasParams.some(item =>
            setB.has(item)
          );

          if (hasCommonElements) {
            needExecFormulasList.push(formulasWidget);
          } else {
            const formId = findKeyByValue(
              formWidgetObject.value,
              formulasWidget._fc_id
            );

            const formulasValue = invocationFormulaCalculateExecutor(
              formulasWidget.props.formulaObject,
              factorDataObject[formId]
            );

            formulasBindData[formulasWidget._fc_id] = formulasValue;

            let formulasFormObjectData = {};
            formulasFormObjectData[formulasWidget._fc_id] = formulasValue;

            if (formulasFormObject.hasOwnProperty(formId)) {
              formulasFormObject[formId] = Object.assign(
                formulasFormObject[formId],
                formulasFormObjectData
              );
            } else {
              formulasFormObject[formId] = formulasFormObjectData;
            }

            formulasWidgetDataList.push({
              label: formulasValue,
              obj: formulasValue,
              widgetType: "DrFormulas",
              widgetId: formulasWidget._fc_id,
              formId: formId,
              $rowIndex: -1
            });
          }
        } else {
          const formId = findKeyByValue(
            formWidgetObject.value,
            formulasWidget._fc_id
          );

          const formulasValue = invocationFormulaCalculateExecutor(
            formulasWidget.props.formulaObject,
            factorDataObject[formId]
          );

          formulasBindData[formulasWidget._fc_id] = formulasValue;

          let formulasFormObjectData = {};
          formulasFormObjectData[formulasWidget._fc_id] = formulasValue;

          if (formulasFormObject.hasOwnProperty(formId)) {
            formulasFormObject[formId] = Object.assign(
              formulasFormObject[formId],
              formulasFormObjectData
            );
          } else {
            formulasFormObject[formId] = formulasFormObjectData;
          }

          formulasWidgetDataList.push({
            label: formulasValue,
            obj: formulasValue,
            widgetType: "DrFormulas",
            widgetId: formulasWidget._fc_id,
            formId: formId,
            $rowIndex: -1
          });
        }
      }
    }

    for (let formulasWidget of needExecFormulasList) {
      if (
        formulasWidget.props.formulaObject &&
        formulasWidget.props.formulaObject.formulas
      ) {
        const formId = findKeyByValue(
          formWidgetObject.value,
          formulasWidget._fc_id
        );

        const formulasValue = invocationFormulaCalculateExecutor(
          formulasWidget.props.formulaObject,
          Object.assign(
            {},
            ...Object.values(
              Object.assign(factorDataObject, formulasFormObject)
            )
          )
        );

        formulasBindData[formulasWidget._fc_id] = formulasValue;

        formulasWidgetDataList.push({
          label: formulasValue,
          obj: formulasValue,
          widgetType: "DrFormulas",
          widgetId: formulasWidget._fc_id,
          formId: formId,
          $rowIndex: -1
        });
      }
    }
    trendsWidgetList.value.push(...formulasWidgetDataList);

    // 3.根据聚合后的值，进行显隐计算
    if (props.operationalFactorData?.formVisibleTemplateFormConfigure) {
      const visibleTemplateList =
        props.operationalFactorData.formVisibleTemplateFormConfigure.filter(
          visibleTemplate => formIdList.value.includes(visibleTemplate.formId)
        );

      let formVisibleTrendsObject = {};
      for (let visibleTemplateItem of visibleTemplateList) {
        const formVisibleBoolean = ExecuteFormVisibleExpression(
          visibleTemplateItem.conditions,
          props.operationalFactorData
        );
        for (let widgetId of visibleTemplateItem.widgetIdList) {
          formVisibleTrendsObject[widgetId] = formVisibleBoolean;
        }
      }
      bindTrendsVisible.value = formVisibleTrendsObject;
    }
  }

  // 4.执行控件默认值逻辑
  let defaultValueObject = {};
  if (props.defaultValueActuatorFlag) {
    let defaultWidgetDataList = [];
    if (props.defaultValueActuatorFlag) {
      const formAllWidgetJsonList = obtainFormAllList(
        formList.flatMap(form => form.widgetJsonList)
      );
      for (let widget of formAllWidgetJsonList) {
        if (widget && widget.props && widget.props.defaultValueConfig) {
          let defaultValue;
          const formId = findKeyByValue(formWidgetObject.value, widget._fc_id);
          if (widget.type === "DrDatePicker") {
            const contrastShowType = {
              datetimesecond: "YYYY-MM-DD HH:mm:ss",
              datetime: "YYYY-MM-DD HH:mm",
              date: "YYYY-MM-DD",
              month: "YYYY-MM",
              year: "YYYY"
            };

            defaultValue = invocationDefaultValueExecutor(
              widget.props.defaultValueConfig
            );

            if (defaultValue) {
              const formatDefaultValue = dayjs(new Date(defaultValue)).format(
                contrastShowType[widget.props.showType]
              );

              defaultValueObject[widget._fc_id] = formatDefaultValue;
            }
          } else {
            defaultValue = invocationDefaultValueExecutor(
              widget.props.defaultValueConfig,
              widget
            );

            defaultValueObject[widget._fc_id] = defaultValue;
          }

          if (defaultValue !== null && defaultValue !== undefined) {
            let widgetObjectData = {};

            if (["DrInput", "DrTextarea"].includes(widget.type)) {
              widgetObjectData = {
                label: defaultValue,
                obj: defaultValue,
                formId: formId,
                widgetId: widget._fc_id,
                widgetType: widget.type,
                $rowIndex: -1
              };
            } else if (
              ["DrInputNumber", "DrPercentage"].includes(widget.type)
            ) {
              const widgetLabelText = widget.props.useThousandSeparator
                ? formatThousandNumber(defaultValue)
                : defaultValue;

              widgetObjectData = {
                label: widgetLabelText,
                obj: defaultValue,
                formId: formId,
                widgetId: widget._fc_id,
                widgetType: widget.type,
                $rowIndex: -1
              };
            } else if (["DrDatePicker"].includes(widget.type)) {
              const contrastShowType = {
                datetimesecond: "YYYY-MM-DD HH:mm:ss",
                datetime: "YYYY-MM-DD HH:mm",
                date: "YYYY-MM-DD",
                month: "YYYY-MM",
                year: "YYYY"
              };
              const dateDefaultValue = defaultValue
                ? dayjs(new Date(defaultValue)).format(
                    contrastShowType[widget.props.showType]
                  )
                : "";
              widgetObjectData = {
                label: dateDefaultValue,
                obj: dateDefaultValue,
                formId: formId,
                widgetId: widget._fc_id,
                widgetType: widget.type,
                $rowIndex: -1
              };
            }
            defaultWidgetDataList.push(widgetObjectData);
          }
        }
      }

      const optionsWidget = obtainFormAllList(
        formList.flatMap(form => form.widgetJsonList)
      ).filter(
        widget =>
          ["DrCheckbox", "DrRadio"].includes(widget.type) &&
          widget.props.options.filter(option => option.checked).length > 0
      );
      for (let optionWidget of optionsWidget) {
        const optionList = optionWidget.props.options.filter(
          option => option.checked
        );
        if (optionList.length > 0) {
          defaultValueObject[optionWidget._fc_id] = optionList.map(
            option => option.value
          );
          const widgetLabelText = optionList
            .map(
              option =>
                `<span class="select-v2-text" style="background: ${option.color}">${option.label}</span>`
            )
            .join(",");
          const formId = findKeyByValue(
            formWidgetObject.value,
            optionWidget._fc_id
          );
          defaultWidgetDataList.push({
            label: widgetLabelText,
            obj: optionList.map(option => option.value),
            formId: formId,
            widgetId: optionWidget._fc_id,
            $rowIndex: -1
          });
        }
      }
    }
    trendsWidgetList.value.push(...defaultWidgetDataList);
  }

  if (props.defaultValueActuatorFlag) {
    Object.assign(
      bindTrendsForm.value,
      cloneDeep(bindFormData),
      defaultValueObject,
      formulasBindData
    );
  } else {
    Object.assign(
      bindTrendsForm.value,
      cloneDeep(bindFormData),
      formulasBindData
    );
  }

  let createFormulasBindData = {};
  if (!!props.trendsFormFlag) {
    // 2.聚合所有表单项的值，进行公式计算
    const formulasWidgetJsonList = obtainFormAllList(
      formList.flatMap(form => form.widgetJsonList)
    ).filter(widget => widget.type === "DrFormulas");

    // 2.1先把公式互相套用的结果选出来，并计算出公式套公式的结果是什么，在将这个结果和表单当前聚合项合并进行运算
    let needExecFormulasList = [];
    const inFormWidgetIdLists = obtainFormAllList(
      formList.flatMap(form => form.widgetJsonList)
    ).map(widget => widget._fc_id);

    let factorDataObject = cloneDeep(
      props.operationalFactorData.entireDetail || {}
    );
    let formulasWidgetDataList = [];
    let formulasFormObject = {};

    for (let formulasWidget of formulasWidgetJsonList) {
      if (
        formulasWidget.props.formulaObject &&
        formulasWidget.props.formulaObject.formulas &&
        formulasWidget.props.formulaObject.formulasParams
      ) {
        const formulasParams =
          formulasWidget.props.formulaObject.formulasParams;

        const factoryFormulasParams = Array.from(
          new Set(
            formulasParams.filter(factor => factor.indexOf("Formulas") > -1)
          )
        ).map((factoryId: any) => factoryId.split("@@")[1]);

        const setB = new Set(inFormWidgetIdLists);
        const hasCommonElements = factoryFormulasParams.some(item =>
          setB.has(item)
        );

        if (hasCommonElements) {
          needExecFormulasList.push(formulasWidget);
        } else {
          const formId = findKeyByValue(
            formWidgetObject.value,
            formulasWidget._fc_id
          );

          const formulasValue = invocationFormulaCalculateExecutor(
            formulasWidget.props.formulaObject
          );

          createFormulasBindData[formulasWidget._fc_id] = formulasValue;

          let formulasFormObjectData = {};
          formulasFormObjectData[formulasWidget._fc_id] = formulasValue;

          if (formulasFormObject.hasOwnProperty(formId)) {
            formulasFormObject[formId] = Object.assign(
              formulasFormObject[formId],
              formulasFormObjectData
            );
          } else {
            formulasFormObject[formId] = formulasFormObjectData;
          }

          formulasWidgetDataList.push({
            label: formulasValue,
            obj: formulasValue,
            widgetType: "DrFormulas",
            widgetId: formulasWidget._fc_id,
            formId: formId,
            $rowIndex: -1
          });
        }
      } else {
        const formId = findKeyByValue(
          formWidgetObject.value,
          formulasWidget._fc_id
        );
        let formulasValue =
          formulasWidget.props.formulaObject &&
          formulasWidget.props.formulaObject.formulas
            ? formulasWidget.props.formulaObject.formulas.replace(/["']/g, "")
            : "";

        let formulasFormObjectData = {};
        formulasFormObjectData[formulasWidget._fc_id] = formulasValue;

        if (formulasFormObject.hasOwnProperty(formId)) {
          formulasFormObject[formId] = Object.assign(
            formulasFormObject[formId],
            formulasFormObjectData
          );
        } else {
          formulasFormObject[formId] = formulasFormObjectData;
        }

        createFormulasBindData[formulasWidget._fc_id] = formulasValue;
        formulasWidgetDataList.push({
          label: formulasValue,
          obj: formulasValue,
          widgetType: "DrFormulas",
          widgetId: formulasWidget._fc_id,
          formId: formId,
          $rowIndex: -1
        });
      }
    }

    for (let formulasWidget of needExecFormulasList) {
      if (
        formulasWidget.props.formulaObject &&
        formulasWidget.props.formulaObject.formulas
      ) {
        const formId = findKeyByValue(
          formWidgetObject.value,
          formulasWidget._fc_id
        );

        const formulasValue = invocationFormulaCalculateExecutor(
          formulasWidget.props.formulaObject,
          Object.assign(
            {},
            ...Object.values(
              Object.assign(factorDataObject, formulasFormObject)
            )
          )
        );

        createFormulasBindData[formulasWidget._fc_id] = formulasValue;

        formulasWidgetDataList.push({
          label: formulasValue,
          obj: formulasValue,
          widgetType: "DrFormulas",
          widgetId: formulasWidget._fc_id,
          formId: formId,
          $rowIndex: -1
        });
      }
    }
    trendsWidgetList.value.push(...formulasWidgetDataList);

    // 3.根据聚合后的值，进行显隐计算
    if (props.operationalFactorData?.formVisibleTemplateFormConfigure) {
      const visibleTemplateList =
        props.operationalFactorData.formVisibleTemplateFormConfigure.filter(
          visibleTemplate => formIdList.value.includes(visibleTemplate.formId)
        );

      const cloneFactorData = cloneDeep(props.operationalFactorData);
      let formObject = {};
      for (let formId of formIdList.value) {
        formObject[formId] = bindTrendsForm.value;
        Object.assign(cloneFactorData.entireDetail, formObject);
      }

      let formVisibleTrendsObject = {};
      for (let visibleTemplateItem of visibleTemplateList) {
        const formVisibleBoolean = ExecuteFormVisibleExpression(
          visibleTemplateItem.conditions,
          cloneFactorData
        );
        for (let widgetId of visibleTemplateItem.widgetIdList) {
          formVisibleTrendsObject[widgetId] = formVisibleBoolean;
        }
      }
      bindTrendsVisible.value = formVisibleTrendsObject;
    }
  }

  Object.assign(bindTrendsForm.value, createFormulasBindData);
};

const deepFormulasFactor = (
  formulasNestingList: any,
  formulasWidget: any,
  needExecFormulasList: any
) => {
  if (formulasNestingList.length > 0) {
    for (let formulasNesting of formulasNestingList) {
      const factorItem = props.operationalFactorData?.widgetList.find(
        widget => widget._fc_id === (formulasNesting as string).split("@@")[1]
      );

      if (!factorItem.props.formulaObject) {
        continue;
      }
      const formulasNestingNextList = Array.from(
        new Set(
          factorItem.props.formulaObject.formulasParams.filter(
            formulasFactor => formulasFactor.indexOf("Formulas") > -12
          )
        )
      );
      deepFormulasFactor(
        formulasNestingNextList,
        formulasNesting,
        needExecFormulasList
      );
    }
  } else {
    needExecFormulasList.push(formulasWidget);
  }
};

const findKeyByValue = (obj, value) => {
  for (const key in obj) {
    if (obj[key].includes(value)) {
      return key;
    }
  }
  return null; // 未找到时返回 null
};

// 使用防抖来减少频繁的事件触发
const debouncedEmitWidgetChange = debounce(
  (widgetList: any[]) => {
    emit("handleWidgetChange", widgetList);
  },
  100,
  {
    leading: false,
    trailing: true
  }
);

const getFormIdByWidgetId = (widgetId: string) => {
  return findKeyByValue(formWidgetObject.value, widgetId);
};

const handleSaveLinkedReferenceSource = (
  linkedRefrenceSource: any,
  state?: string
) => {
  const sccsId = route.query.sccsId;
  const orderId = route.query.orderId;

  // linkedRefrenceSourceList 初始为空，更新的时候把新的值添加进去，如果是删除，对应的 widgetList 为空
  if (state === "delete") {
    const index = linkedRefrenceSourceList.value.findIndex(
      linkedSource => linkedSource.linkedId === linkedRefrenceSource.linkedId
    );
    if (index !== -1) {
      linkedRefrenceSourceList.value.splice(index, 1, {
        ...linkedRefrenceSourceList.value[index],
        sourceId: "",
        widgetList: []
      });
    } else {
      linkedRefrenceSourceList.value.push(
        Object.assign({}, linkedRefrenceSource, { widgetList: [] })
      );
    }
  } else if (state === "clear") {
    linkedRefrenceSourceList.value = linkedRefrenceSourceList.value.map(
      item => {
        return {
          ...item,
          widgetList: []
        };
      }
    );
  } else {
    const index = linkedRefrenceSourceList.value.findIndex(
      linkedSource => linkedSource.linkedId === linkedRefrenceSource.linkedId
    );

    if (index === -1) {
      linkedRefrenceSourceList.value.push(
        Object.assign(
          {
            sccsId: sccsId,
            orderId: orderId,
            workOrderId: props.workOrderId
          },
          linkedRefrenceSource
        )
      );
    } else {
      linkedRefrenceSourceList.value.splice(
        index,
        1,
        Object.assign(
          {
            sccsId: sccsId,
            orderId: orderId,
            workOrderId: props.workOrderId
          },
          linkedRefrenceSource
        )
      );
    }
  }

  // 关联引用公式计算因子
  const index = linkedRefrenceSourceFactoryData.value.findIndex(
    factory =>
      factory.factoryWidgetId ===
      linkedRefrenceSource.factoryObject.factoryWidgetId
  );
  if (index === -1) {
    linkedRefrenceSourceFactoryData.value.push(
      linkedRefrenceSource.factoryObject
    );
  } else {
    linkedRefrenceSourceFactoryData.value.splice(
      index,
      1,
      linkedRefrenceSource.factoryObject
    );
  }
  linkedRefrenceSourceFactoryObject.value = linkedRefrenceSource.factoryObject;
};

const handleWidgetFormsValue = (
  widgetId: string,
  widgetDataValue: any,
  value: any
) => {
  const formId = findKeyByValue(formWidgetObject.value, widgetId);
  const index = trendsWidgetList.value.findIndex(
    trendsWidget => trendsWidget.widgetId === widgetId
  );

  const widgetData = Object.assign(widgetDataValue, { formId: formId });
  index === -1
    ? trendsWidgetList.value.push(widgetData)
    : trendsWidgetList.value.splice(index, 1, widgetData);

  let obj = {};
  obj[widgetId] = value;
  Object.assign(bindTrendsForm.value, obj);

  if (props.operationalFactorData.formVisibleTemplateFormConfigure) {
    const visibleTemplateList =
      props.operationalFactorData.formVisibleTemplateFormConfigure.filter(
        visibleTemplate => formIdList.value.includes(visibleTemplate.formId)
      );

    const visibleTemplateWidgetIdList = visibleTemplateList.filter(
      visibleCondition => {
        const conditionWidgetList = visibleCondition.conditions.flatMap(
          conditionRow => {
            return conditionRow.map(condition => {
              return condition.firstData[2];
            });
          }
        );
        return conditionWidgetList.includes(widgetId);
      }
    );

    let visibleWidgetIdsJsonList = [];
    if (visibleTemplateWidgetIdList.length > 0) {
      const cloneFactorData = cloneDeep(props.operationalFactorData);
      let formObject = {};
      formObject[formId] = bindTrendsForm.value;
      Object.assign(cloneFactorData.entireDetail, formObject);
      let formVisibleTrendsObject = {};
      for (let visibleTemplateItem of visibleTemplateWidgetIdList) {
        const formVisibleBoolean = ExecuteFormVisibleExpression(
          visibleTemplateItem.conditions,
          cloneFactorData
        );
        visibleWidgetIdsJsonList.push(...visibleTemplateItem.widgetIdList);
        for (let widgetId of visibleTemplateItem.widgetIdList) {
          formVisibleTrendsObject[widgetId] = formVisibleBoolean;
        }
      }

      bindTrendsVisible.value = Object.assign(
        bindTrendsVisible.value,
        formVisibleTrendsObject
      );
    }

    if (visibleWidgetIdsJsonList.length > 0) {
      const visibleBindTrendsFormList = visibleWidgetIdsJsonList.map(
        widgetId => {
          const formId = getFormIdByWidgetId(widgetId);
          const widgetJsonList = obtainFormAllList(
            props.formList.flatMap(form => form.widgetJsonList)
          );
          const widgetItem = widgetJsonList.find(
            widget => widget._fc_id === widgetId
          );
          if (widgetItem.type !== "DrTableForm") {
            let widgetObj =
              (["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(
                widgetItem.type
              ) &&
                widgetItem.props.multiple) ||
              widgetItem.type === "DrCheckbox"
                ? []
                : "";
            return {
              label: "",
              obj: widgetObj,
              widgetType: widgetItem.type,
              widgetId: widgetId,
              formId: formId,
              $rowIndex: -1
            };
          } else {
            return {
              label: null,
              obj: null,
              childrenList: [],
              widgetType: widgetItem.type,
              widgetId: widgetId,
              formId: formId
            };
          }
        }
      );
      trendsWidgetList.value = mergeArraysByWidgetId(
        trendsWidgetList.value,
        visibleBindTrendsFormList
      );
      let newTrendsFormObject = {};
      for (let widgetId of visibleWidgetIdsJsonList) {
        newTrendsFormObject[widgetId] = "";
      }
      Object.assign(bindTrendsForm.value, newTrendsFormObject);
    }
  }

  // 使用防抖的emit，减少频繁触发
  debouncedEmitWidgetChange([...trendsWidgetList.value]);
};

const mergeArraysByWidgetId = (a, b) => {
  // 创建一个Map以便快速查找
  const aMap = new Map(a.map(item => [item.widgetId, item]));

  // 遍历b数组进行合并
  for (const bItem of b) {
    if (aMap.has(bItem.widgetId)) {
      // 存在则合并（覆盖）
      Object.assign(aMap.get(bItem.widgetId), bItem);
    } else {
      // 不存在则添加
      aMap.set(bItem.widgetId, bItem);
    }
  }

  // 将Map转换回数组
  return Array.from(aMap.values());
};

const handleGainWidgetFormData = (validate: boolean = true) => {
  const deDupTrendsWidgetList = deduplicateByWidgetIdInPlace(
    trendsWidgetList.value
  );

  return new Promise((resolve, reject) => {
    if (validate) {
      trendsFormWidgetFormRef.value.validate((valid, invalidFields) => {
        if (valid) {
          resolve(deDupTrendsWidgetList);
        } else {
          resolve(false);
        }
      });
    } else {
      resolve(deDupTrendsWidgetList);
    }
  });
};

const handleValidate = async () => {
  return new Promise((resolve, reject) => {
    trendsFormWidgetFormRef.value.validate((valid, invalidFields) => {
      if (valid) {
        resolve(true);
      } else {
        resolve(invalidFields);
      }
    });
  });
};

/**
 * 默认值执行器
 * @param defaultValueConfig 控件默认值配置
 */
const invocationDefaultValueExecutor = (
  defaultValueConfig: any,
  itselfWidget?: any
) => {
  if (defaultValueConfig.type === "none") {
    return "";
  } else if (defaultValueConfig.type === "custom") {
    if (itselfWidget && itselfWidget.type === "DrAddress") {
      return {
        areaCodeList: defaultValueConfig.areas,
        address: defaultValueConfig.content
      };
    } else {
      return defaultValueConfig.content;
    }
  } else if (defaultValueConfig.type === "currentDay") {
    const contrastShowType = {
      datetimesecond: "YYYY-MM-DD HH:mm:ss",
      datetime: "YYYY-MM-DD HH:mm",
      date: "YYYY-MM-DD",
      month: "YYYY-MM",
      year: "YYYY"
    };
    return dayjs(new Date()).format(
      contrastShowType[
        itselfWidget ? itselfWidget.props.showType : "YYYY-MM-DD"
      ]
    );
  } else if (defaultValueConfig.type === "relate") {
    const bindTrendFormData = cloneDeep(bindTrendsForm.value);
    // 关联其他控件数据
    if (bindTrendFormData.hasOwnProperty(defaultValueConfig.content)) {
      const widgetJsonList = props.formList.flatMap(
        form => form.widgetJsonList
      );
      const widgetItem = widgetJsonList.find(
        widget => widget._fc_id === defaultValueConfig.content
      );
      return bindTrendFormData[defaultValueConfig.content];
    } else {
      const factorWidgetDataValue =
        props.operationalFactorData?.entireFormObject;
      return factorWidgetDataValue[defaultValueConfig.content];
    }
  } else if (defaultValueConfig.type === "formula") {
    return invocationFormulaCalculateExecutor(defaultValueConfig.content);
  }
};

/**
 * 公式执行器，根据传递进来的公式配置进行公式运算，运输完毕后返回公式计算完毕后的值
 * @param formulasObject
 */
const invocationFormulaCalculateExecutor = (
  formulasObject: any,
  trendsFormData?: any
) => {
  const trendsFormCloneData = trendsFormData
    ? trendsFormData
    : cloneDeep(bindTrendsForm.value);

  let encapsulationTrendsFormData = props.operationalFactorData?.entireDetail
    ? cloneDeep(props.operationalFactorData?.entireDetail)
    : {};

  for (let formItem of props.formList) {
    let traverseWidgetData = {};
    let traverseFormWidgetData = {};
    const widgetJsonList: any[] = obtainFormAllList(formItem.widgetJsonList);

    // 1.数字类型控件，当值不存在的时候，需要转化为0进行运算
    const digitWidgetList = widgetJsonList.filter(widget =>
      ["DrPercentage"].includes(widget.type)
    );

    for (let digitWidget of digitWidgetList) {
      if (!!trendsFormCloneData[digitWidget._fc_id]) {
        traverseWidgetData[digitWidget._fc_id] =
          trendsFormCloneData[digitWidget._fc_id];
        if (digitWidget.type === "DrPercentage") {
          traverseWidgetData[digitWidget._fc_id] =
            traverseWidgetData[digitWidget._fc_id] / 100;
        }
      }
    }

    // 2.选项类控件，单选，多选需要拿控件的label值进行运算
    const optionsWidgetList = widgetJsonList.filter(widget =>
      ["DrCheckbox", "DrRadio"].includes(widget.type)
    );

    for (let optionWidget of optionsWidgetList) {
      if (
        trendsFormCloneData[optionWidget._fc_id] &&
        trendsFormCloneData[optionWidget._fc_id].length > 0
      ) {
        const trendsFormListData =
          trendsFormCloneData[optionWidget._fc_id] instanceof Array
            ? trendsFormCloneData[optionWidget._fc_id]
            : optionWidget.type === "DrRadio"
              ? trendsFormCloneData[optionWidget._fc_id]
              : [trendsFormCloneData[optionWidget._fc_id]];

        let trendsFormOption;
        if (optionWidget.type !== "DrRadio") {
          trendsFormOption = trendsFormListData.map(optionWidgetValue => {
            const optionItem = optionWidget.props.options.find(
              option =>
                option.value === optionWidgetValue ||
                option.label === optionWidgetValue
            );
            return optionItem ? optionItem.label : "";
          });
        } else {
          const optionItem = optionWidget.props.options.find(option =>
            trendsFormListData instanceof Array
              ? trendsFormListData.includes(option.value) ||
                trendsFormListData.includes(option.label)
              : option.value === trendsFormListData ||
                option.label === trendsFormListData
          );
          trendsFormOption = optionItem ? optionItem.label : "";
        }
        traverseWidgetData[optionWidget._fc_id] = trendsFormOption;
      }
    }

    // 3.日期控件默认值应该为1970-01-01 00:00:00计算，不然计算出来有误
    // const dateWidgetList = widgetJsonList.filter(widget =>
    //   ["DrDatePicker"].includes(widget.type)
    // );

    // for (let dateWidget of dateWidgetList) {
    //   if (!trendsFormCloneData[dateWidget._fc_id]) {
    //     traverseWidgetData[dateWidget._fc_id] = dayjs(new Date(0)).format(
    //       "YYYY-MM-DD"
    //     );
    //   } else {
    //     traverseWidgetData[dateWidget._fc_id] =
    //       trendsFormCloneData[dateWidget._fc_id];
    //   }
    // }

    // 4.子表控件
    const tableWidgetList = widgetJsonList.filter(
      widget => widget.type === "DrTableForm"
    );
    for (let tableWidget of tableWidgetList) {
      const childrenWidgetIds = tableWidget.children
        .slice(1)
        .map(childWidget => childWidget.children[0]?._fc_id);
      const childrenWidgetList = tableWidget.children
        .slice(1)
        .map(childWidget => childWidget.children[0]);

      let subTableObject = {};
      if (trendsFormCloneData[tableWidget._fc_id] instanceof Array) {
        for (let widgetId of childrenWidgetIds) {
          let widgetDataList = [];
          for (let childRow of trendsFormCloneData[tableWidget._fc_id]) {
            const widgetColData = childRow.find(
              childCol => childCol.widgetId === widgetId
            );
            const widgetItem = childrenWidgetList.find(
              widgetConfig => widgetConfig._fc_id === widgetId
            );
            if (widgetItem.type === "DrRadio") {
              const radioOptions = widgetItem.props.options;
              const radioItemData = radioOptions.find(
                radioOption =>
                  radioOption.value === widgetColData?.obj ||
                  radioOption.label === widgetColData?.obj
              );
              widgetDataList.push(radioItemData ? radioItemData.label : "");
            } else if (widgetItem.type === "DrCheckbox") {
              const checkboxOptions = widgetItem.props.options;
              if (
                widgetColData?.obj &&
                widgetColData?.obj instanceof Array &&
                widgetColData?.obj.length > 0
              ) {
                const checkboxData = widgetColData?.obj.map(checkboxData => {
                  const checkboxItemData = checkboxOptions.find(
                    checkboxOption =>
                      checkboxOption.value === checkboxData ||
                      checkboxOption.label === checkboxData
                  );
                  return checkboxItemData ? checkboxItemData.label : "";
                });

                widgetDataList.push(...checkboxData);
              } else {
                widgetDataList.push(null);
              }
            } else if (["DrPercentage"].includes(widgetItem.type)) {
              const widgetValueData = widgetColData?.obj;
              widgetDataList.push(widgetValueData / 100);
            }
            // else if (widgetItem.type === "DrDatePicker") {
            //   const widgetValueData = widgetColData?.obj
            //     ? widgetColData?.obj
            //     : dayjs(new Date(0)).format("YYYY-MM-DD");
            //   widgetDataList.push(widgetValueData);
            // }
            else {
              widgetDataList.push(widgetColData?.obj);
            }
          }
          subTableObject[widgetId] = widgetDataList;
        }
      } else {
        let trendsTableData = {};
        for (let widgetId of childrenWidgetIds) {
          const widgetItem = childrenWidgetList.find(
            widgetConfig => widgetConfig._fc_id === widgetId
          );
          if (
            trendsFormCloneData[tableWidget._fc_id] &&
            trendsFormCloneData[tableWidget._fc_id].hasOwnProperty(widgetId)
          ) {
            if (widgetItem.type === "DrRadio") {
              const radioOptions = widgetItem.props.options;
              const radioTrendsFormData =
                trendsFormCloneData[tableWidget._fc_id][widgetId] instanceof
                Array
                  ? trendsFormCloneData[tableWidget._fc_id][widgetId]
                  : [trendsFormCloneData[tableWidget._fc_id][widgetId]];
              const trendRadioData = radioTrendsFormData.map(option => {
                const radioItemData = radioOptions.find(
                  radioOption =>
                    radioOption.value === option || radioOption.label === option
                );
                return radioItemData ? radioItemData.label : null;
              });
              trendsTableData = Object.assign(trendsTableData, {
                [widgetId]: trendRadioData
              });
            } else if (widgetItem.type === "DrCheckbox") {
              const checkboxOptions = widgetItem.props.options;
              const checkboxData = trendsFormCloneData[tableWidget._fc_id][
                widgetId
              ].flatMap(data =>
                data && data instanceof Array && data.length !== 0 ? data : null
              );
              const trendCheckboxData = checkboxData.map(option => {
                const radioItemData = checkboxOptions.find(
                  radioOption =>
                    radioOption.value === option || radioOption.label === option
                );
                return radioItemData ? radioItemData.label : null;
              });

              trendsTableData = Object.assign(trendsTableData, {
                [widgetId]: trendCheckboxData
              });
            } else if (["DrPercentage"].includes(widgetItem.type)) {
              const widgetValueData =
                trendsFormCloneData[tableWidget._fc_id][widgetId];
              trendsTableData = Object.assign(trendsTableData, {
                [widgetId]: widgetValueData / 100
              });
            } else {
              trendsTableData = Object.assign(trendsTableData, {
                [widgetId]: trendsFormCloneData[tableWidget._fc_id][widgetId]
              });
            }
          }
        }
        subTableObject = trendsTableData;
      }
      traverseWidgetData[tableWidget._fc_id] = subTableObject;
    }

    // 5.其余控件类型的控件值
    const otherWidgetList = widgetJsonList.filter(
      widget =>
        ![
          // "DrInputNumber",
          "DrPercentage",
          "DrCheckbox",
          "DrRadio",
          "DrTableForm"
          // "DrDatePicker"
        ].includes(widget.type)
    );

    for (let otherWidget of otherWidgetList) {
      traverseWidgetData[otherWidget._fc_id] =
        trendsFormCloneData[otherWidget._fc_id];
    }
    traverseFormWidgetData[formItem.id] = traverseWidgetData;

    encapsulationTrendsFormData = Object.assign(
      encapsulationTrendsFormData,
      traverseFormWidgetData
    );
  }

  for (let linkedRefrenceData of linkedRefrenceSourceFactoryData.value) {
    Object.assign(
      encapsulationTrendsFormData[linkedRefrenceData.factoryFormData],
      linkedRefrenceData.factoryData
    );
  }

  const formulasResult = calculateFormulasWidget(
    formulasObject.formulas
      .replace(/(?<![><])=(?!=)/g, "==")
      .replace(/<>/g, "!==")
      .replace(/&/g, "+"),
    encapsulationTrendsFormData
  );
  return transformFormulasValue(formulasResult);
};

const initTrendsFormSccsWidgetList = async (formList: any) => {
  const widgetJsonList = obtainFormAllList(
    formList.flatMap(form => form.widgetJsonList)
  );
  const isWidgetType = (widget, types) => types.includes(widget.type);

  const isTableFormWithType = (widget, types) => {
    if (widget.type !== "DrTableForm" || !Array.isArray(widget.children))
      return false;
    return widget.children
      .slice(1)
      .some(
        child =>
          child.children &&
          child.children[0] &&
          types.includes(child.children[0].type)
      );
  };

  const findWidgetIndex = types =>
    widgetJsonList.findIndex(
      widget =>
        isWidgetType(widget, types) || isTableFormWithType(widget, types)
    );

  const memberTypes = [
    "DrSCCSMemberSelect",
    "DrSCCSMemberSingleSelect",
    "DrSCCSMemberMultipleSelect"
  ];
  const teamTypes = [
    "DrSCCSGroupMemberSelect",
    "DrSCCSGroupMemberSingleSelect",
    "DrSCCSGroupMemberMultipleSelect"
  ];

  const memberIndex = findWidgetIndex(memberTypes);
  const teamIndex = findWidgetIndex(teamTypes);

  if (memberIndex !== -1) {
    const { code, data } = await getSccsMemberList({ sccsId: props.sccsId });
    if (code === 0) {
      staffPersonObject.value.sccsMemberList = data;
    }
  }

  if (teamIndex !== -1) {
    const { code, data } = await getSccsCoopTeamList({ sccsId: props.sccsId });
    if (code === 0) {
      staffPersonObject.value.sccsCoopTeamList = data;
    }
  }
};

const handleUpdateFormData = (formData: any) => {
  const widgetJsonList = obtainFormAllList(
    props.formList.flatMap(form => form.widgetJsonList)
  );
  const bindFormData = initFormModalData(widgetJsonList, cloneDeep(formData));
  // 更新表单数据
  Object.assign(bindTrendsForm.value, bindFormData);

  // 更新变更数据
  const widgetMap = new Map(
    trendsWidgetList.value.map(widget => [widget.widgetId, widget])
  );

  formData.forEach(item => {
    const targetItem = widgetMap.get(item.widgetId);
    if (targetItem) {
      // 批量更新属性
      Object.assign(targetItem, {
        label: item.label,
        obj: item.obj,
        showObj: item.showObj
      });
      if (item.childrenList) {
        Object.assign(targetItem, {
          childrenList: item.childrenList
        });
      }
    } else {
      trendsWidgetList.value.push(item);
    }
  });
  debouncedEmitWidgetChange([...trendsWidgetList.value]);
};

watch(
  [() => props.formList, () => props.formWidgetData],
  (newVal, oldVal) => {
    const [newFormList, newFormWidgetData] = newVal;
    const [oldFormList, oldFormWidgetData] = oldVal || [[], []];

    if (newFormList.length > 0) {
      const shouldReinitialize =
        !oldFormList ||
        newFormList.length !== oldFormList.length ||
        newFormList.some(
          (form, index) =>
            !oldFormList[index] || form.id !== oldFormList[index].id
        );

      if (shouldReinitialize) {
        const dynamicFormActiveIdLists = newFormList.map(
          formItem => formItem.id
        );
        dynamicFormActiveIdLists.push("msCollapsePlan");
        dynamicFormActiveIds.value = dynamicFormActiveIdLists;
        let formObject = {};
        for (let formItem of newFormList) {
          formObject[formItem.id] = obtainFormAllList(
            formItem.widgetJsonList
          ).map(form => form._fc_id);
        }
        formWidgetObject.value = formObject;
        initAggregationForm(newFormList, newFormWidgetData);
        initTrendsFormSccsWidgetList(newFormList);
      } else if (!isEqual(newFormWidgetData, oldFormWidgetData)) {
        // 如果只是数据变化，只重新初始化表单数据，不重建整个结构
        initAggregationForm(newFormList, newFormWidgetData);
      }
    }
  },
  {
    immediate: true
  }
);

watch(
  () => props.trendsWidgetRequireFormIdList,
  () => {
    const requiredFormList = cloneDeep(formCollapseList.value).filter(form =>
      props.trendsWidgetRequireFormIdList.includes(form.id)
    );
    const notRequriedWidgetList = requiredFormList.flatMap(requiredForm =>
      obtainFormAllList(requiredForm.widgetJsonList).map(form => form._fc_id)
    );
    notRequriedList.value = notRequriedWidgetList;
  },
  {
    deep: true
  }
);

provide("ActuatorDefaultValue", props.defaultValueActuatorFlag);
provide(
  "uploadFileURL",
  `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_API_TRADE_PREFIX}/trade/file/upload`
);
provide("handleWidgetFormsValue", handleWidgetFormsValue);
provide("handleSaveLinkedReferenceSource", handleSaveLinkedReferenceSource);
provide("invocationDefaultValueExecutor", invocationDefaultValueExecutor);
provide("getFormIdByWidgetId", getFormIdByWidgetId);
provide(
  "invocationFormulaCalculateExecutor",
  invocationFormulaCalculateExecutor
);

defineExpose({
  linkedRefrenceSourceDataLists: linkedRefrenceSourceList.value,
  handleSaveLinkedReferenceSource,
  handleValidate,
  handleGainWidgetFormData,
  handleUpdateFormData
});
</script>
<style lang="scss" scoped>
.trends-collaspse-form-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 44px;
  line-height: 44px;
  border-bottom: 1px solid #e6eaf0;

  .trends-collaspse-form-header-title {
    display: flex;
    align-items: center;
    margin-right: 30px;
    font-size: 14px;
    font-weight: 400;
    color: #797979;
    white-space: nowrap;
  }
}

.trends-collaspse-form {
  border: 0 none;

  .el-collapse-item {
    margin-bottom: 19px;
    border: 1px solid #e6eaf0;
    border-radius: 4px;

    &:last-child {
      margin-bottom: 0;
    }

    ::v-deep(.el-collapse-item__header) {
      padding: 0 15px;
      background: #f6f9fb;

      .collapse-form-item-title {
        font-size: 14px;
        font-weight: bolder;
        color: #262626;
      }
    }

    ::v-deep(.el-collapse-item__content) {
      padding: 0 20px 10px;

      .el-form-item {
        padding-top: 4px !important;
        margin-bottom: 8px !important;

        .widget-form-item-label {
          display: inline-flex;
          align-items: center;

          .widget-form-item-title {
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            color: #262626;

            .widget-required {
              margin-left: 5px;
              color: red;
            }
          }

          .widget-form-item-label-icon {
            font-size: 12px;
            font-weight: 400;
            color: #262626;
            cursor: pointer;
          }
        }

        .el-form-item__content {
          width: 100%;

          .el-form-item__error {
            position: relative;
            margin-top: 2px;
            color: red;
          }

          .el-input-number {
            width: 100%;
          }

          .wangeditor {
            line-height: 1.5;
          }
        }
      }

      .el-form-item__label {
        margin-bottom: 0 !important;
      }
    }
  }
}

.order-readonly-collapse-item {
  padding-top: 20px;
}
</style>
