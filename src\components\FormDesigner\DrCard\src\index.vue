<template>
  <el-card
    v-show="drCardVisible"
    class="dr-card-body"
    :shadow="widgetConfigure?.props?.shadow"
    :header="widgetHeader"
  >
    <el-row :gutter="drCardGutter">
      <el-col
        v-for="widgetItem in widgetConfigure.children"
        :key="widgetItem._fc_id"
        :span="widgetItem.props.span"
        :offset="widgetItem.props.offset"
        :push="widgetItem.props.push"
        :pull="widgetItem.props.pull"
      >
        <div
          v-for="widgetChildItem in widgetItem.children"
          :key="widgetChildItem._fc_id"
          style="margin-bottom: 14px"
        >
          <el-form-item
            v-show="
              !bindTrendsVisible.hasOwnProperty(widgetChildItem._fc_id) ||
              (bindTrendsVisible.hasOwnProperty(widgetChildItem._fc_id) &&
                bindTrendsVisible[widgetChildItem._fc_id])
            "
            :label="widgetChildItem.title"
            :label-position="getLabelPosition(widgetChildItem.wrap)"
            :label-width="widgetChildItem.wrap?.labelWidth"
            :rules="widgetRule(widgetChildItem)"
            :prop="widgetChildItem._fc_id"
          >
            <template #label>
              <div
                v-if="widgetChildItem.type !== 'DrDivider'"
                class="widget-form-item-label"
              >
                <el-tooltip
                  v-if="widgetChildItem.info"
                  effect="light"
                  :content="widgetInfo(widgetChildItem)"
                  placement="top"
                  popper-class="widget-popper-label-class"
                  :show-after="500"
                >
                  <i
                    class="iconfont link-explain widget-form-item-label-icon"
                  />
                </el-tooltip>
                <div class="widget-form-item-title">
                  <span
                    v-if="widgetChildItem.$required"
                    class="widget-required"
                  >
                    *
                  </span>
                  {{ widgetLabelTitle(widgetChildItem) }}
                </div>
              </div>
            </template>
            <component
              :is="widgetChildItem.type"
              :widgetConfigure="widgetChildItem"
              :trendsForm="trendsForm"
              :bindTrendFormId="bindTrendFormId"
              :staffPerson="staffPerson"
              :placeholder="widgetPlaceholder(widgetChildItem)"
            />
            <template #error="{ error }">
              <span class="widget-form-error-body">{{ error }}</span>
            </template>
          </el-form-item>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { getWidgetRules } from "@/utils/formDesignerUtils.tsx";
import { cloneDeep, storageLocal } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  bindTrendFormId: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  staffPerson: {
    type: Object as PropType<any>,
    default: () => {}
  },
  notRequriedList: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  bindTrendsVisible: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const translationLang = storageLocal().getItem("translationLang");
const widgetRule = computed(() => {
  return item => {
    return props.notRequriedList.includes(item._fc_id) ||
      !(
        !props.bindTrendsVisible.hasOwnProperty(item._fc_id) ||
        (props.bindTrendsVisible.hasOwnProperty(item._fc_id) &&
          props.bindTrendsVisible[item._fc_id])
      )
      ? []
      : getWidgetRules(item.$required, item);
  };
});

const getLabelPosition = (wrap: any): any => {
  const wrapClass = wrap && wrap.class ? wrap.class.split("-")[2] : "top";
  return wrapClass;
};

const widgetLabelTitle = computed(() => {
  return item => {
    return translationLang === "en"
      ? item.props?.titleEn || item.title
      : item.title;
  };
});

const widgetInfo = computed(() => {
  return item => {
    return translationLang === "en"
      ? item.props?.infoEn || item.info
      : item.info;
  };
});

const widgetPlaceholder = computed(() => {
  return item => {
    return translationLang === "en"
      ? item.props?.placeholderEn || item.props?.placeholder
      : item.props?.placeholder;
  };
});

const widgetHeader = computed(() => {
  return translationLang === "en"
    ? props.widgetConfigure.props?.headerEn
    : props.widgetConfigure.props?.header;
});

const drCardGutter = computed(() => {
  return props.widgetConfigure.props.gutter
    ? props.widgetConfigure.props.gutter
    : "20";
});

const drCardVisible = computed(() => {
  const cardInWidgetIdList = props.widgetConfigure.children.map(
    widgetCol =>
      widgetCol.children &&
      widgetCol.children[0] &&
      widgetCol.children[0]._fc_id
  );

  const trendsVisible = cloneDeep(props.bindTrendsVisible);
  return !allItemsFalseInObject(cardInWidgetIdList, trendsVisible);
});

/**
 * 检查数组中所有项是否都存在于对象中且值为false
 * @param {Array} arr - 要检查的数组
 * @param {Object} obj - 要检查的对象
 * @returns {boolean} - 如果所有数组项都存在于对象中且值为false则返回true，否则返回false
 */
const allItemsFalseInObject = (arr, obj) => {
  // 快速失败：如果数组为空，直接返回true（空数组没有项需要检查）
  if (arr.length === 0) return true;

  // 使用for循环而不是数组方法以获得更好的性能
  for (let i = 0; i < arr.length; i++) {
    const key = arr[i];

    // 检查键是否存在且值为false
    if (!(key in obj) || obj[key] !== false) {
      return false;
    }
  }

  return true;
};
</script>
<style lang="scss" scoped>
.widget-form-error-body {
  width: 100%;
  overflow: hidden;
  font-size: 12px;
  line-height: 18px;
  color: #e62412;
  text-overflow: ellipsis;
  word-wrap: break-word;
}

.dr-card-body {
  display: block;
  width: 100%;
  border: 0 none;

  ::v-deep(.el-card__header) {
    padding: 0;
    padding-bottom: 20px;
    font-size: 14px;
    font-weight: bolder;
    line-height: 16px;
    color: #262626;
    border-bottom: solid 1px #e5e5e5;
  }

  ::v-deep(.el-card__body) {
    padding: 10px 0 0;
  }
}
</style>
