<template>
  <el-form
    ref="ruleFormRef"
    :model="userInfoForm"
    label-width="auto"
    label-position="top"
    :rules="rules"
    size="large"
  >
    <el-form-item :label="t('trade_login_email')" required prop="username">
      <el-input
        v-model="userInfoForm.username"
        clearable
        :placeholder="t('trade_login_mailText')"
        @change="handleWatchUserName(userInfoForm)"
      />
    </el-form-item>
    <el-form-item :label="t('trade_common_password')" required prop="password">
      <el-input
        v-model="userInfoForm.password"
        type="password"
        clearable
        :placeholder="t('trade_login_passwordPlaceholder')"
        :show-password="true"
      />
    </el-form-item>
    <el-form-item class="user-login-btn-container">
      <UserError :respMessage="respMessage" :loginForm="userInfoForm" />
      <el-button
        class="user-login-button"
        type="primary"
        @click="onSubmit(ruleFormRef)"
      >
        {{ t("trade_common_login") }}
      </el-button>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, watch, inject } from "vue";
import { useI18n } from "vue-i18n";
import type { FormInstance } from "element-plus";
import { loginApi } from "@/api/login";
import { LoginFormState } from "@/api/type";
import { LoginSuccessResponseDataProp } from "../utils/type.d";
import UAParser from "ua-parser-js";
import type { FormRules } from "element-plus";
import { isEmail, storageLocal } from "@pureadmin/utils";
import UserError from "./UserError.vue";

const props = defineProps({
  remember: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const { t } = useI18n();
let userInfoForm = reactive<LoginFormState>({
  username: "",
  password: "",
  userOs: "",
  userAgent: "",
  userDevice: ""
});
const ruleFormRef = ref<FormInstance>();
const respMessage = ref<string>("");
const rules = reactive<FormRules<any>>({
  username: [
    {
      required: true,
      message: t("trade_login_mailText"),
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        if (!isEmail(value)) {
          callback(new Error(t("trade_login_truemail")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: t("trade_login_passwordPlaceholder"),
      trigger: "blur"
    }
  ],
  //@ts-ignore
  mailCode: [
    {
      required: true,
      message: t("trade_common_inputCaptcha"),
      trigger: "blur"
    }
  ]
});

const emit = defineEmits<{
  (e: "handleLoginSuccess", data: LoginSuccessResponseDataProp): void;
}>();

const onSubmit = (formEl: FormInstance | undefined): void => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      const parser = new UAParser();
      userInfoForm.userDevice = parser.getDevice().model as string;
      userInfoForm.userOs = `${parser.getOS().name} ${parser.getOS().version}`;
      userInfoForm.userAgent = parser.getUA();
      const response = await loginApi(userInfoForm);
      if (response.code !== 0) {
        respMessage.value = response.msg;
      } else {
        emit("handleLoginSuccess", response.data);
      }
    }
  });
};

onMounted(() => {
  userInfoForm.username = sessionStorage.getItem("username");
});

watch(
  () => userInfoForm,
  () => {
    respMessage.value = "";
    if (props.remember) {
      const { username, password } = userInfoForm;
      const base64Str = window.btoa(
        JSON.stringify({ username: username, password: password })
      );
      storageLocal().setItem("tradeUserInfo", base64Str);
    } else {
      storageLocal().removeItem("tradeUserInfo");
    }
  },
  { deep: true }
);

const handleWatchUserName = inject(
  "handleWatchUserName",
  (userInfo: any) => {}
);
defineExpose({
  userInfoForm
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
