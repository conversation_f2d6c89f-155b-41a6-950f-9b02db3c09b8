<template>
  <div class="order-table-header-left">
    <div class="not-select-list-workOrderSharedIn">
      <span v-if="tableSelectedNumber === 0" class="header-tip-desc"
        ><i class="iconfont link-tips-warm" />{{ warmTip }}
      </span>
      <span v-else class="order-table-header-flex">
        <el-switch v-model="switched" size="small" />
        <span class="header-tip-desc default"
          >{{ t("trade_common_selected")
          }}<span class="header-tip-number">{{ tableSelectedNumber }}</span
          >{{ t("trade_article_record") }}</span
        >
        <span class="header-tip-clear-btn" @click="clearSelectNumber">{{
          t("trade_clear_selected_items")
        }}</span>
      </span>
      <span class="line" />
      <span>
        {{ label }}
        <span class="header-tip-orderMark">
          <ReText type="info" :tippyProps="{ delay: 50000 }">
            {{ combinnationName }}
          </ReText>
        </span>
      </span>
      <span
        class="header-tip-confirm-btn"
        :class="tableSelectedNumber === 0 ? 'disabled' : ''"
        @click="handleConfirmDialog"
      >
        <i class="iconfont link-shared-in" />
        {{ t("trade_order_MutualIdentification") }}
      </span>
      <span class="header-tip-cancel-btn" @click="cancelMutuallyUse"
        ><i class="iconfont link-delete" />{{
          t("trade_order_cancelMutuallyUse")
        }}</span
      >
    </div>
  </div>
  <LkDialog
    ref="DialogRef"
    class="create-success-dialog"
    :no-footer="true"
    height="300"
    width="480"
    @close="cancelMutuallyUse"
  >
    <template #default>
      <div class="create-work-order-container">
        <svg class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#link-work-order-success" />
        </svg>
        <div class="create-work-order-text">
          <span class="create-work-order-text-orderMark"
            >{{
              relationType === "workOrderSharedIn"
                ? `${route.query.orderMark} - ${route.query.msName} - ${route.query.workOrderName}`
                : `${route.query.orderMark} - ${route.query.msName}`
            }} </span
          >{{
            ["addMutuallyUseWorkOrder"].includes(relationType)
              ? t("trade_successfully_interoperable")
              : `${t("trade_successfully_interoperable_with")}：`
          }}
          <span class="create-work-order-text-orderMark"
            >{{ selectedOrderMarks }}
            <span v-if="relationType === 'addMutuallyUseWorkOrder'">
              <span class="create-work-order-text-number"
                >{{ `${t("trade_of")} ${tableSelectedNumber}` }}
              </span>
              {{ t("trade_work_order_number") }}
            </span>
          </span>
        </div>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="tsx" setup>
import { computed, markRaw, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import LkDialog from "@/components/lkDialog/index";
import { ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { ReText } from "@/components/ReText";
import { deleteThatMenu } from "@/utils/dynamicTag";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { createNewCloneWorkOrder, createNewCloneMilestone } from "@/api/order";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const switched = ref(false);

const props = defineProps({
  relationType: {
    type: String as PropType<string>,
    default: () => ""
  },
  tableSelectedNumber: {
    type: Number as PropType<number>,
    default: () => 0
  },
  tableSelectedList: {
    type: Array as PropType<Array<string>>,
    default: () => []
  }
});
const DialogRef = ref(null);
const emit = defineEmits(["clearSelectNumber"]);
const selectedOrderMarks = computed(() => {
  let str = "";
  props.tableSelectedList.forEach(element => {
    str !== ""
      ? (str = str + "、" + (element.orderMark || element.serialNumber))
      : (str += element.orderMark || element.serialNumber);
  });
  return str;
});
// 判断当前操作类型给出不同提示
const operationTypeMap = {
  addMutuallyUseWorkOrder: {
    title: t("trade_order_relation_confirm_title"),
    message: t("trade_order_relation_confirm_tip")
  },
  cloneMilesStoneFromOtherOrder: {
    title: t("trade_ms_by_ms_relation_confirm_title"),
    message: t("trade_ms_relation_confirm_tip")
  },
  milesStoneSharedIn: {
    title: t("trade_ms_relation_confirm_title_"),
    message: t("trade_ms_share_in_confirm_tip")
  }
};
const warmTip = computed(() => {
  switch (props.relationType) {
    case "addMutuallyUseWorkOrder":
      return `${t("trade_please_select_the_work_order_below")} (${t("trade_support_multiple_selection")})`;
    case "workOrderSharedIn":
      return `${t("trade_please_choose_to_use_interchangeably")} ${route.query.orderMark} - ${route.query.msName}${t("trade_of")}${t("trade_common_order")} (${t("trade_support_multiple_selection")})`;
    case "milesStoneSharedIn":
      return `${t("trade_please_choose_to_use_interchangeably")} ${route.query.orderMark} - ${route.query.msName}${t("trade_of")}${t("trade_common_order")} (${t("trade_support_multiple_selection")})`;
    case "cloneMilesStoneFromOtherOrder":
      return `${t("trade_please_select_the_order_below")}`;
    default:
      return "";
  }
});
const label = computed(() => {
  switch (props.relationType) {
    case "addMutuallyUseWorkOrder":
      return `${t("trade_order_MutualUse")}：`;
    case "workOrderSharedIn":
      return `${t("trade_original_work_order")}：`;
    case "milesStoneSharedIn":
      return `${t("trade_original_milestone")}：`;
    case "cloneMilesStoneFromOtherOrder":
      return `${t("trade_order_MutualUse")}：`;
    default:
      return "";
  }
});

const combinnationName = computed(() => {
  switch (props.relationType) {
    case "workOrderSharedIn":
      return `${route.query.orderMark} - ${route.query.workOrderName}`;
    default:
      return `${route.query.orderMark} - ${route.query.msName}`;
  }
});
const clearSelectNumber = () => {
  emit("clearSelectNumber", 0);
};

const cancelMutuallyUse = () => {
  deleteThatMenu({ path: route.path, meta: route.meta }, route, router);
  const parameter = {
    sccsId: route.query.sccsId,
    templateId: route.query.templateId,
    coopTeamMark: route.query.coopTeamMark,
    sccsName: route.query.sccsName
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: `/orderManage`,
    name: "orderManage",
    query: parameter,
    meta: {
      title: {
        zh: `${parameter.sccsName}`
      },
      dynamicLevel: 3
    }
  });
  const { sccsId, orderMark, sccsName, templateId, coopTeamMark, orderId } =
    route.query;
  const params = {
    sccsName: sccsName,
    sccsId: sccsId,
    templateId: templateId,
    orderId: orderId,
    orderMark: orderMark,
    coopTeamMark: coopTeamMark
  };
  // 路由跳转
  router.push({ name: "orderDetail", query: params });
};
const handleConfirmDialog = async () => {
  if (props.tableSelectedNumber === 0) {
    return;
  } else {
    if (
      ["addMutuallyUseWorkOrder", "workOrderSharedIn"].includes(
        props.relationType
      )
    ) {
      // 属于工单互用于
      let toOrderIdList = props.tableSelectedList.map(
        (item: any) => item.orderId
      );
      let fromWorkOrderIdList = [route.query.workOrderId];
      if (props.relationType === "addMutuallyUseWorkOrder") {
        toOrderIdList = [route.query.orderId];
        fromWorkOrderIdList = props.tableSelectedList.map(
          (item: any) => item.workOrderId
        );
      }
      // 属于工单互用提交
      const res = await createNewCloneWorkOrder({
        fromWorkOrderIdList,
        toOrderIdList
      });
      res.code === 0 && DialogRef.value.open();
    } else {
      // 属于里程碑互用提交
      ElMessageBox.confirm(
        operationTypeMap[props.relationType].message,
        operationTypeMap[props.relationType].title,
        {
          confirmButtonText: t("trade_make_sure_clone"),
          cancelButtonText: t("trade_not_clone"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      )
        .then(async () => {
          const tpe = props.relationType;
          let fromOrderId = route.query.orderId;
          let fromMsId = route.query.msId;
          let toOrderIdList = props.tableSelectedList.map(q => q.orderId);
          let todo = true;
          if (tpe === "cloneMilesStoneFromOtherOrder") {
            fromOrderId = props.tableSelectedList[0].orderId;
            toOrderIdList = [route.query.orderId];
            todo = false;
          }
          // 属于里程碑互用提交
          const res = await createNewCloneMilestone({
            fromOrderId: fromOrderId,
            fromMsId: fromMsId,
            toOrderIdList: toOrderIdList,
            todo: todo
          });
          res.code === 0 && DialogRef.value.open();
        })
        .catch(e => {
          return;
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.not-select-list-addMutuallyUseWorkOrder,
.not-select-list-workOrderSharedIn {
  display: flex;
  align-items: center;
  width: 100%;
}

.order-table-header-flex {
  display: inline-flex;
  align-items: center;
}

.header-tip-desc {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  color: #fa8d0a;

  .iconfont {
    margin-right: 3px;
  }

  &.default {
    margin: 0 18px 0 5px;
    font-size: 14px;
    font-weight: regular;
    line-height: 20px;
    color: #595959;
  }

  .header-tip-number {
    margin: 0 3px;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }
}

.line {
  width: 1px;
  height: 17px;
  margin: 0 16px;
  background-color: #dfdfdf;
}

.header-tip-orderMark {
  display: inline-flex;
  align-items: center;
  max-width: 220px;
  padding: 4px 6px;
  font-size: 14px;
  line-height: 14px;
  color: #262626;
  background: #ddefff;
  border-radius: 2px;
}

.header-tip-clear-btn {
  font-size: 14px;
  font-weight: regular;
  line-height: 20px;
  color: #2082ed;
  cursor: pointer;
}

.header-tip-confirm-btn {
  padding: 6px 12px;
  margin: 0 16px;
  font-size: 14px;
  line-height: 20px;
  color: #0070d2;
  cursor: pointer;
  background: #e1edff;
  border-radius: 4px;

  .iconfont {
    margin-right: 4px;
    font-size: 14px;
  }

  &.disabled {
    color: #87c9ff;
    cursor: not-allowed;
  }
}

.header-tip-cancel-btn {
  padding: 6px 12px;
  font-size: 14px;
  line-height: 20px;
  color: #cf1421;
  cursor: pointer;
  background: #ffeded;
  border-radius: 4px;

  .iconfont {
    margin-right: 4px;
    font-size: 14px;
  }
}

.create-success-dialog {
  .el-dialog__body {
    height: auto !important;
    padding-bottom: 0 !important;
    text-align: center;
    background: #fff;

    .svg-icon {
      width: 80px;
      margin: 0 auto;
    }

    .create-work-order-text {
      margin: 0 24px;
      font-size: 14px;
      line-height: 28px;
      color: #8c8c8c;
      text-align: center;

      .create-work-order-text-orderMark {
        font-size: 14px;
        line-height: 14px;
        color: #262626;
      }

      .create-work-order-span-text {
        margin: 0 5px;
        font-weight: 600;
        color: #262626;
      }
    }

    .create-work-order-tip {
      margin-top: 11px;
      font-size: 14px;
      line-height: 24px;
      color: #262626;
    }

    .create-work-order-btn-group {
      margin-top: 48px;

      .create-work-order-plain-btn {
        color: #0070d2;
        border-color: #ccecff;
      }
    }
  }
}
</style>
