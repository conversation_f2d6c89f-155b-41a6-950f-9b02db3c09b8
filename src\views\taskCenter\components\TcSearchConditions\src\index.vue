<template>
  <div class="custom-conditions-control-row">
    <!-- 第一个控件类型 -->
    <el-select-v2
      ref="CascaderRef"
      v-model="conditionsForm.queryField"
      class="custom-conditions-operator-control"
      filterable
      :options="QueryFieldOptions"
      clearable
      :teleported="teleported"
      @change="handleSelectQueryField"
    >
      <template #label="{ label }">
        {{ t(label) }}
      </template>
      <template #default="{ item }">
        {{ t(item.label) }}
      </template>
    </el-select-v2>
    <!-- 第二个控件：值比较规则控件 -->
    <el-select-v2
      v-model="conditionsForm.operator"
      class="custom-conditions-operator-control"
      filterable
      :options="MathOperatorSelectOptions"
      clearable
      :teleported="teleported"
      @change="handleSelectOperator"
    >
      <template #label="{ label }">
        {{ t(label) }}
      </template>
      <template #default="{ item }">
        {{ t(item.label) }}
      </template>
    </el-select-v2>
    <div
      v-if="
        firstOpertatorSelectControl &&
        !['AFTER', 'EQ', 'BEFORE', 'IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
          conditionsForm.operator
        )
      "
    >
      <el-select-v2
        v-model="conditionsForm.rangeDateType"
        class="custom-conditions-operator-control"
        filterable
        :options="getRangeTypeOptions()"
        clearable
        :teleported="teleported"
        @change="handleSelectDateRangeType"
      >
        <template #label="{ label }">
          {{ t(label) }}
        </template>
        <template #default="{ item }">
          {{ t(item.label) }}
        </template>
      </el-select-v2>
      <el-date-picker
        v-if="conditionsForm.rangeDateType === 'DATE_RANGE'"
        v-model="dateRange"
        range-separator="-"
        :start-placeholder="t('trade_common_beginDate')"
        :end-placeholder="t('trade_common_endDate')"
        style="width: 236px"
        :type="`${widgetDate.type}range`"
        :value-format="widgetDate.valueFormat"
        :format="widgetDate.format"
        :time-format="widgetDate.timeFormat"
        :teleported="teleported"
        @change="handleDatePickerValue"
      />
      <el-input
        v-if="['LAST', 'NEXT'].includes(conditionsForm.rangeDateType)"
        v-model="conditionsForm.value"
        clearable
        class="conditions-form-input"
        @input="handleEdit"
      >
        <template #append>{{ getTimeDateType() }}</template>
      </el-input>
    </div>
    <div
      v-if="
        (firstOpertatorSelectControl &&
          !['BETWEEN', 'IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
            conditionsForm.operator
          )) ||
        !firstOpertatorSelectControl
      "
    >
      <slot name="fieldTypeSlot" />
      <!-- 第四个区域类型控件 -->
      <div
        v-if="
          !['IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
            conditionsForm.operator
          )
        "
        class="custom-conditions-col"
      >
        <el-select-v2
          v-if="firstOpertatorSelectControl"
          v-model="conditionsForm.customerDateType"
          :teleported="teleported"
          class="custom-conditions-operator-control"
          filterable
          :options="getDatePickerOptionsByType(DatePickerOptions)"
          clearable
          @change="conditionsForm.value = ''"
        >
          <template #label="{ label }">
            {{ t(label) }}
          </template>
          <template #default="{ item }">
            {{ t(item.label) }}
          </template>
        </el-select-v2>
        <el-date-picker
          v-if="conditionsForm.customerDateType === 'ASSIGN'"
          v-model="conditionsForm.value"
          :teleported="teleported"
          :type="widgetDate.type"
          :value-format="widgetDate.valueFormat"
          :format="widgetDate.format"
          :time-format="widgetDate.timeFormat"
          :placeholder="t('trade_common_datePickerTip')"
        />
        <el-input
          v-if="['BEFORE', 'AFTER'].includes(conditionsForm.customerDateType)"
          v-model="conditionsForm.value"
          clearable
          class="conditions-form-input"
          @input="handleEdit"
        >
          <template #append>{{ getTimeDateType() }}</template>
        </el-input>
        <CustomConditionControl
          v-if="!firstOpertatorSelectControl"
          ref="customConditionControlRef"
          :widget="widgetForm"
          :teleported="teleported"
          :widgetValue="conditionsForm.value"
          :widgetOptions="widgetOptions"
          :widgetDateShowType="widgetDateShowType"
          :multiple="multiple"
          style="display: inline-flex"
          @handleWidgetValue="handleWidgetValue"
        />
      </div>
      <!-- 订单创建人，订单更新人 -->
      <el-cascader
        v-if="widgetMainFormPersonBool"
        ref="teamMemberCascaderRef"
        :model-value="teamMemberBindValue"
        :options="widgetTeamMemberList"
        :teleported="teleported"
        :props="cascaderProps"
        :multiple="multiple"
        clearable
        collapse-tags-tooltip
        popper-append-to-body
        collapse-tags
        @change="handleSelectMember"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, watchEffect, computed } from "vue";
import { useI18n } from "vue-i18n";
import { storageLocal, storageSession } from "@pureadmin/utils";
import CustomConditionControl from "./CustomConditionControl.vue";
import {
  MathOperatorOptions,
  DateRangeOptions,
  DatePickerOptions
} from "./enum";

const props = defineProps({
  condition: {
    type: Object as PropType<any>,
    default: () => {}
  },
  conditionList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  creatorData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  teleported: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

// todo 人员控件需要新增两级控件
const cascaderProps = computed(() => {
  return {
    multiple: ["IN", "NOT_IN"].includes(conditionsForm.value.operator),
    emitPath: false
    // checkStrictly: true
  };
});

const { t } = useI18n();
const teamMemberCascaderRef = ref<any | HTMLElement>(null);
const widgetDate = ref<any>({
  valueFormat: "YYYY-MM-DD",
  type: "date"
});
const teamMemberBindValue = ref<any>([]);
const conditionsForm = ref<any>({
  customerDateType: null,
  operator: null,
  queryField: null,
  rangeDateType: null,
  value: ""
});
const dateRange = ref<any[]>([]);
const QueryFieldOptions = ref<any>([
  // 发起人
  {
    label: "trade_common_creator",
    value: "CREATE_MEMBER_ID"
  },
  // 发起时间
  {
    label: "trade_common_creatorTime",
    value: "CREATE_TIME"
  },
  // 计划结束时间
  {
    label: "trade_sccs_planned_end_date",
    value: "PLANNED_END_TIME"
  },
  // 任务更新时间
  {
    label: "trade_Task_update_time",
    value: "TASK_UPDATE_TIME"
  },
  // 任务完成时间
  {
    label: "trade_Task_completion_time",
    value: "TASK_COMPLETE_TIME"
  }
]);
const MathOperatorSelectOptions = ref<any>([]);
const firstOpertatorSelectControl = ref<boolean>(false);
const CascaderRef = ref<HTMLElement | any>(null);
const customConditionControlRef = ref<HTMLElement | any>(null);
const planSettingSelect = ref<any[]>([]);
const widgetForm = ref<any>({});
const multiple = ref<boolean>(false);
const widgetOptions = ref<any>([]);
const widgetDateShowType = ref<string>("");
const widgetTeamMemberList = ref<any>([]);
const widgetMainFormPersonBool = computed(() => {
  return (
    ["CREATE_MEMBER_ID"].includes(conditionsForm.value.queryField) &&
    !["IS_NULL", "IS_NOT_NULL"].includes(conditionsForm.value.operator)
  );
});

const emit = defineEmits<{
  (e: "handleChange", data: any): void;
  (e: "handleChangeConditions", data: any): void;
  (
    e: "handleConditionFirstData",
    bool: boolean,
    data: any,
    formPubId: string
  ): void;
}>();

/**
 * 第一个控件选择值类型
 */
const handleSelectQueryField = async (): Promise<void> => {
  if (conditionsForm.value.queryField === "CREATE_MEMBER_ID") {
    MathOperatorSelectOptions.value = MathOperatorOptions["widget"];
    getSystemMembersList();
    firstOpertatorSelectControl.value = false;
  } else {
    MathOperatorSelectOptions.value = MathOperatorOptions["date"];
  }
  conditionsForm.value.operator = null;
  conditionsForm.value.rangeDateType = null;
  conditionsForm.value.customerDateType = null;
  conditionsForm.value.value = "";
};

/**
 * 第二个控件切换值
 */
const handleSelectOperator = () => {
  multiple.value =
    conditionsForm.value.queryField === "CREATE_MEMBER_ID" &&
    ["IN", "NOT_IN"].includes(conditionsForm.value.operator);
  conditionsForm.value.rangeDateType = null;
  conditionsForm.value.customerDateType = null;
  conditionsForm.value.value = "";
  customConditionControlRef.value?.clearData();
};

const handleSelectDateRangeType = () => {
  conditionsForm.value.value = "";
  customConditionControlRef.value?.clearData();
};

const handleWidgetValue = (bindValue: string, label: string) => {
  conditionsForm.value.value = bindValue;
  if (label) {
    conditionsForm.value.label = label;
  }
};

const getSystemMembersList = async () => {
  // 创建人和订单创建人
  const { teamMemberList, coopTeamList } = props.creatorData;
  const teamMemberLists = [];
  const teamList: any[] = storageSession().getItem("userMemberTeam");
  //@ts-ignore
  const { latestLoginTeamId } = storageLocal().getItem("user-info");
  const thatTeam = teamList.find(teamItem => teamItem.id === latestLoginTeamId);
  teamMemberList.length > 0 &&
    teamMemberLists.push({
      label: `${thatTeam.teamName}(本团队)`,
      value: thatTeam.id,
      children: teamMemberList.map(teamMember => {
        return {
          label: teamMember.username,
          value: teamMember.teamMemberId
        };
      })
    });
  coopTeamList.forEach(coopTeam => {
    teamMemberLists.push({
      label: coopTeam.teamName,
      value: coopTeam.id,
      children: coopTeam.teamMemberList.map(teamMember => {
        return {
          label: teamMember.username,
          value: teamMember.teamMemberId
        };
      })
    });
  });
  widgetTeamMemberList.value = teamMemberLists;
  return teamMemberLists;
};

const getRangeTypeOptions = () => {
  if (conditionsForm.value.dateType) {
    const dateTypeOptions = {
      year: ["THIS_YEAR", "LAST_YEAR", "NEXT_YEAR"],
      month: ["THIS_MONTH", "LAST_MONTH", "NEXT_MONTH"],
      day: [
        "THIS_YEAR",
        "LAST_YEAR",
        "NEXT_YEAR",
        "THIS_MONTH",
        "LAST_MONTH",
        "NEXT_MONTH",
        "THIS_WEEK",
        "LAST_WEEK",
        "NEXT_WEEK"
      ]
    };
    return DateRangeOptions.filter(
      date =>
        dateTypeOptions[conditionsForm.value.dateType].includes(date.value) ||
        ["DATE_RANGE", "LAST", "NEXT"].includes(date.value)
    );
  } else {
    return DateRangeOptions;
  }
};

const handleDatePickerValue = () => {
  conditionsForm.value.value = dateRange.value.join(",");
};

const handleEdit = e => {
  let value = e.replace(/^(0+)|[^\d]+/g, ""); // 以0开头或者输入非数字，会被替换成空
  value = value.replace(/(\d{15})\d*/, "$1"); // 最多保留15位整数
  conditionsForm.value.value = value;
};

const getDatePickerOptionsByType = options => {
  const dateTypeOptions = {
    year: ["THIS_YEAR", "LAST_YEAR", "NEXT_YEAR"],
    month: ["THIS_MONTH", "LAST_MONTH", "NEXT_MONTH"],
    day: ["TODAY", "YESTERDAY", "TOMORROW"]
  };
  return dateTypeOptions[conditionsForm.value.dateType]
    ? options.filter(
        option =>
          dateTypeOptions[conditionsForm.value.dateType].includes(
            option.value
          ) || ["ASSIGN", "BEFORE", "AFTER"].includes(option.value)
      )
    : options;
};

// 订单创建人，订单更新人更新值
const handleSelectMember = () => {
  const teamCodeList = teamMemberCascaderRef.value.getCheckedNodes(false);
  const commitTeamList = teamCodeList.filter(
    teamCode => teamCode.pathValues.length === 2
  );
  let bindValue = "";
  let bindText = "";
  commitTeamList.forEach(teamMember => {
    bindValue += `,${teamMember.value}`;
    bindText += `,${teamMember.text}`;
  });
  conditionsForm.value.value = bindValue.substr(1);
  conditionsForm.value.label = bindText.substr(1);
};

watch(
  props.condition,
  newVal => {
    if (newVal && Object.keys(newVal).length > 0) {
      conditionsForm.value = newVal;
      if (conditionsForm.value.queryField === "CREATE_MEMBER_ID") {
        MathOperatorSelectOptions.value = MathOperatorOptions["widget"];
        getSystemMembersList();
        teamMemberBindValue.value = ["IN", "NOT_IN"].includes(newVal.operator)
          ? newVal.value.split(",")
          : newVal.value;
        multiple.value = ["IN", "NOT_IN"].includes(
          conditionsForm.value.operator
        );
      } else {
        MathOperatorSelectOptions.value = MathOperatorOptions["date"];
        firstOpertatorSelectControl.value = true;
        if (newVal.rangeDateType === "DATE_RANGE") {
          dateRange.value = newVal.value.split(",");
        }
      }
    }
  },
  { deep: true, immediate: true }
);

watchEffect(() => {
  if (props.conditionList) {
    planSettingSelect.value = props.conditionList;
  }
});

const getTimeDateType = () => {
  return conditionsForm.value.dateType === "year"
    ? t("trade_common_year")
    : conditionsForm.value.dateType === "month"
      ? t("trade_common_month")
      : t("trade_common_days");
};

defineExpose({
  CascaderRef: CascaderRef.value,
  conditionsForm: conditionsForm.value,
  handleSelectQueryField
});
</script>
<style lang="scss" scoped>
.custom-conditions-control-row {
  display: flex;

  ::v-deep(.el-cascader) {
    width: 240px;
    margin-right: 10px;

    .el-cascader__tags {
      flex-wrap: nowrap;
    }
  }

  ::v-deep(.el-select) {
    margin-right: 10px;
  }

  ::v-deep(.el-date-editor) {
    margin-right: 10px;
  }

  ::v-deep(.el-textarea) {
    margin-right: 10px;
  }

  ::v-deep(.el-input) {
    margin-right: 10px;
  }

  ::v-deep(.el-cascader-node) {
    max-width: 300px;
  }

  .conditions-form-input {
    width: 100px;

    ::v-deep(.el-input-group__append) {
      padding: 0 10px;
    }
  }

  .custom-conditions-operator-control {
    width: 130px;

    ::v-deep(.el-select-dropdown) {
      width: 200px !important;
      max-width: 300px !important;

      .el-select-dropdown__list.el-vl__window {
        width: 200px !important;
        max-width: 300px !important;
      }
    }
  }

  .custom-conditions-person-operator-control {
    width: 180px;
  }

  .custom-conditions-operator-small-control {
    width: 80px;
  }

  .custom-conditions-col {
    display: inline-flex;
  }
}
</style>
