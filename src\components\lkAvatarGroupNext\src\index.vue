<template>
  <div
    v-if="avatarBool"
    class="avater-next-group-container"
    style="pointer-events: auto; cursor: pointer"
  >
    <el-popover placement="bottom" trigger="click" width="auto">
      <div class="avater-next-group-popover">
        <div
          v-for="avatarItem in avatarList"
          :key="avatarItem.id"
          :class="{
            'avater-next-col': true,
            'reply-avatar': avatarItem.edited
          }"
        >
          <span
            v-if="!avatarItem.activate"
            style="margin-right: 5px; color: #fa8d0a !important"
          >
            ({{ t("trade_common_unregistered") }})
          </span>
          <el-avatar
            :class="{
              colorOrange: !avatarItem.user,
              'reply-avatar-edited': mode === 'reply' && avatarItem.edited
            }"
            :size="32"
            :shape="avatarItem.user ? 'circle' : 'square'"
            :src="avatarItem.avatar"
            fit="cover"
          >
            {{ avatarItem.name }}
          </el-avatar>
          <svg
            v-if="avatarItem.coopUser"
            class="icon svg-icon"
            aria-hidden="true"
          >
            <use xlink:href="#link-coop" />
          </svg>
          <div v-if="!avatarItem.coopUser" class="avater-group-popover-text">
            <ReText
              v-if="avatarItem.user"
              type="info"
              class="avater-group-popover-user-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ avatarItem.username }}
              <span v-if="avatarItem.activate">({{ avatarItem.email }})</span>
            </ReText>
            <ReText
              v-else
              type="info"
              class="avater-group-popover-team-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ avatarItem.username }}({{ avatarItem.shortName }})
            </ReText>
          </div>
          <div v-else class="avater-group-popover-text">
            <ReText
              type="info"
              class="avater-group-popover-user-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ avatarItem.username }}({{ avatarItem.email }})
            </ReText>
            <ReText
              type="info"
              class="avater-group-popover-team-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ avatarItem.teamName }}({{ avatarItem.shortName }})
            </ReText>
          </div>
        </div>
      </div>
      <template #reference>
        <div>
          <div class="avatar-user-list">
            <div
              v-for="avatarItem in avatarList"
              :key="avatarItem.id"
              :class="{
                'avatar-item-col': true,
                'coop-avatar': avatarItem.coopUser
              }"
            >
              <el-avatar
                :size="size"
                :class="{
                  'reply-avatar': mode === 'reply' && avatarItem.edited,
                  'collect-avatar': mode === 'collect' && avatarItem.edited,
                  colorOrange: !avatarItem.user
                }"
                :shape="avatarItem.user ? 'circle' : 'square'"
                :src="avatarItem.avatar"
                fit="cover"
              >
                {{ avatarItem.name }}
              </el-avatar>
              <svg
                v-if="avatarItem.coopUser"
                class="icon svg-icon"
                aria-hidden="true"
              >
                <use xlink:href="#link-coop" />
              </svg>
            </div>
            <span
              v-if="avatarList && avatarList.length > 3"
              class="avatar-next-text"
            >
              +{{ remainingAvatarNumber }}
            </span>
          </div>
          <div v-if="mode" class="avatar-user-state-list">
            <div v-if="mode === 'reply'">
              <span v-if="operateTime" class="state-time-title">
                {{ dayjs(new Date(operateTime)).format("YYYY-MM-DD HH:mm") }}
              </span>
              <span v-else-if="operateTime !== undefined" class="state-title">
                {{ t("trade_order_notReply") }}
              </span>
            </div>
            <div v-if="mode === 'collect'">
              <span v-if="operateTime" class="state-time-title">
                {{ dayjs(new Date(operateTime)).format("YYYY-MM-DD HH:mm") }}
              </span>
              <span v-else-if="operateTime !== undefined" class="state-title">
                {{ t("trade_order_notCollection") }}
              </span>
            </div>
          </div>
        </div>
      </template>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";
import { ElPopover, ElAvatar } from "element-plus";
import dayjs from "dayjs";

const props = defineProps({
  avatarListGroup: {
    type: Array as PropType<any>,
    default: () => []
  },
  size: {
    type: Number as PropType<number>,
    default: 32
  },
  mode: {
    type: String as PropType<string>,
    default: "collect"
  },
  operateTime: {
    type: String as PropType<any>,
    default: undefined
  }
});

const { t } = useI18n();
const avatarList = ref<any[]>([]);
const avatarBool = ref<boolean>(false);

const remainingAvatarNumber = computed(() => {
  return avatarList.value.length - 3;
});

watch(
  () => props.avatarListGroup,
  () => {
    avatarList.value = props.avatarListGroup.map(avatarItem => {
      return {
        activate: avatarItem.user ? avatarItem.activate : true,
        id: avatarItem.user ? avatarItem.userId : avatarItem.teamId,
        name: avatarItem.user
          ? avatarItem.userName?.slice(0, 1)
          : avatarItem.teamName?.slice(0, 1),
        user: avatarItem.user,
        username: avatarItem.user ? avatarItem.userName : avatarItem.teamName,
        avatar: avatarItem.user ? avatarItem.userAvatar : avatarItem.teamAvatar,
        edited: avatarItem.edited,
        coopUser: avatarItem.coopTeamUser,
        teamName: avatarItem.teamName,
        shortName: avatarItem.shortName,
        email: avatarItem.email
      };
    });

    avatarBool.value = true;
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.avater-next-group-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 100%;
  cursor: pointer;

  ::v-deep(.avatar-item-col) {
    position: relative;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;

    &.coop-avatar {
      position: relative;

      .svg-icon {
        position: absolute;
        right: -5px;
        bottom: -3px;
        z-index: 333;
        width: 13px;
        height: 13px;
      }
    }

    .el-avatar {
      position: relative;
      z-index: 333;
      margin-right: -6px;
      border: 1px solid #fff;

      &.reply-avatar {
        border: 2px solid #ffb780 !important;
      }

      &.reply-avatar,
      &.collect-avatar {
        position: relative;

        &::after {
          position: absolute;
          width: 100%;
          height: 100%;
          content: "";
          background: #000;
          opacity: 0.45;
        }

        &::before {
          position: absolute;
          top: 50%;
          left: 50%;
          z-index: 1980;
          font-family: iconfont !important;
          color: #00cc7b;
          content: "\e7ad";
          transform: translate(-50%, -50%);
        }
      }
    }

    &:nth-child(2) {
      z-index: 334;
    }

    &:nth-child(3) {
      z-index: 335;
    }

    &:nth-child(4) {
      .el-avatar {
        position: relative;
        z-index: 336;
        background: rgb(38 38 38 / 64%) !important;
        border-color: #fff;

        &::after {
          position: absolute;
          top: 0;
          left: 0;
          display: inline-block;
          width: 100%;
          height: 100%;
          content: "";
          background: rgb(38 38 38 / 64%);
          opacity: 1;
        }
      }

      .svg-icon {
        display: none;
      }
    }

    &:nth-child(n + 5) {
      display: none;
    }
  }

  .avatar-next-text {
    position: absolute;
    top: 50%;
    // right: 2%;
    right: 0;
    z-index: 337;
    font-size: 12px;
    color: #fff;
    transform: translateY(-50%);
  }

  .el-tooltip__trigger {
    display: flex;
    place-items: center start;

    .avatar-user-list {
      position: relative;
      display: inline-flex;

      .reply-avatar {
        border: 2px solid #ffb780 !important;
      }
    }

    .avatar-user-state-list {
      margin-left: 10px;

      .state-title {
        font-size: 12px;
        color: #cf1421;
      }

      .state-time-title {
        font-size: 12px;
        color: #595959;
      }
    }
  }
}

.avater-next-group-popover {
  .avater-next-col {
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px;

    &.reply-avatar {
      position: relative;

      .reply-avatar-edited {
        border: 2px solid #ffb780 !important;
      }

      &::after {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 337;
        display: inline-block;
        width: 28px !important;
        height: 28px !important;
        content: "";
        background: rgb(0 0 0 / 30%) !important;
        border-radius: 50%;
      }

      &::before {
        position: absolute;
        z-index: 338;
        width: 32px;
        font-family: iconfont !important;
        font-size: 10px;
        color: #00cc7b;
        text-align: center;
        content: "\e7ad";
      }
    }

    .svg-icon {
      position: absolute;
      bottom: 8px;
      left: 30px;
      z-index: 339;
      width: 13px;
      height: 13px;
    }

    .avater-group-popover-text {
      display: inline-flex;
      flex: 1;
      flex-direction: column;
      margin-left: 8px;

      .avater-group-popover-user-text {
        flex: 1;
        width: 100%;
        overflow: hidden;
        font-size: 12px;
        color: #262626;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .avater-group-popover-team-text {
        width: 100%;
        font-size: 12px;
        color: #262626;
        text-align: left;
      }
    }
  }
}
</style>
