<template>
  <div />
</template>
<script setup lang="ts">
import { onMounted } from "vue";
import { useRoute } from "vue-router";
import { getUserActive } from "@/api/common";
import { useNav } from "@/layout/hooks/useNav";
import { storageLocal } from "@pureadmin/utils";
import { switchTeam } from "@/utils/auth";
import NProgress from "@/utils/progress";

const route = useRoute();
const { logout } = useNav();

onMounted(async () => {
  NProgress.start();
  const { teamId, email } = route.query;
  const { data } = await getUserActive({ email: email as string });
  if (data) {
    NProgress.done();
    //已注册
    const userInfo: any = storageLocal().getItem("user-info");
    if (userInfo) {
      //用户已登录
      if (userInfo.email === email) {
        // 邮箱与当前登录的用户邮箱相同；认为当前用户是登录状态；
        await switchTeam(teamId, true);
      } else {
        // 登录的是不同账号
        logout(`/login?email=${email}`);
      }
    } else {
      logout(`/login?email=${email}`);
    }
  } else {
    //未注册
    logout(`/register?email=${email}&nextStep=true`);
  }
});
</script>
