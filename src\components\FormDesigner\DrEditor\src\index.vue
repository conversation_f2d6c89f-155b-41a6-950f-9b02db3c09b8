<template>
  <div class="wangeditor" style="width: 100%">
    <Toolbar
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
      style="border-bottom: 1px solid #ccc"
    />
    <Editor
      v-model="editorValue"
      :defaultConfig="editorConfig"
      :mode="mode"
      :autofocus="false"
      style="height: 500px; overflow-y: hidden"
      @onCreated="handleCreated"
    />
  </div>
</template>
<script setup lang="ts">
import { onBeforeUnmount, inject, shallowRef, watch, ref } from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { uploadFile } from "@/api/common";
import { useRoute } from "vue-router";
import { createFormData, isEqual, storageLocal } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const route = useRoute();
const mode = "default";
const editorRef = shallowRef<HTMLElement | any>();
let editorValue = ref<string>("");
const translationLang = storageLocal().getItem("translationLang");
// const parentWidgetFormRef: any = unref(inject("widgetFormRef"));
const toolbarConfig: any = {
  excludeKeys: [
    "insertVideo", // 去掉插入视频功能
    "uploadVideo", // 去掉上传视频功能
    "group-video",
    "fullScreen", // 去掉全屏功能
    "emotion"
  ]
};
const widgetConfigureProps = props.widgetConfigure.props;
const editorConfig = {
  placeholder:
    translationLang === "en"
      ? widgetConfigureProps?.placeholderEn || widgetConfigureProps?.placeholder
      : widgetConfigureProps?.placeholder,
  readOnly: widgetConfigureProps?.readonly,
  autoFocus: false,
  MENU_CONF: {
    uploadImage: {
      async customUpload(file: File, insertFn: any) {
        const formDataParams = createFormData({
          sccsId: route.query.sccsId,
          ossFileType: "TRADE_SCCS",
          file: file
        });
        const { code, data } = await uploadFile(formDataParams);
        if (code === 0) {
          insertFn(data, file.name, "");
        }
      }
    }
  }
};

const handleCreated = editor => {
  editorRef.value = editor;
};

const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const emit = defineEmits(["handleUpdateWidgetData"]);

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    // 当 editorRef.value 初始化之后，需要使用 setHtml 方法设置值
    if (editorRef.value) {
      editorRef.value.setHtml(newVal ?? "");
    } else {
      editorValue.value = newVal ?? "";
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => editorValue.value,
  (newVal, oldVal) => {
    // 判断当前控件的值是否和表单的值一样，一样则认为没修改
    if (
      isEqual(
        editorValue.value,
        props.trendsForm[props.widgetConfigure._fc_id]
      ) ||
      (newVal === "<p><br></p>" && !oldVal)
    ) {
      return;
    }

    if (props.widgetRowIndex !== -1) {
      emit("handleUpdateWidgetData", "DrEditor", {
        obj: editorValue.value,
        label: editorValue.value,
        widgetId: props.widgetConfigure._fc_id,
        widgetType: props.widgetConfigure.type,
        $rowIndex: props.widgetRowIndex
      });
    } else {
      handleWidgetFormsValue(
        props.widgetConfigure._fc_id,
        {
          obj: editorValue.value,
          label: editorValue.value,
          widgetId: props.widgetConfigure._fc_id,
          widgetType: props.widgetConfigure.type,
          $rowIndex: props.widgetRowIndex
        },
        editorValue.value
      );
    }
  },
  {
    deep: true,
    immediate: true
  }
);

onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>
<style lang="scss">
@use "@wangeditor/editor/dist/css/style.css";

.wangeditor {
  border: 1px solid var(--el-input-border-color, var(--el-border-color));
  border-radius: 4px;
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color))
    inset;
}

.w-e-text-container {
  line-height: 1.5;
}

.w-e-text-container a {
  color: #0070d2 !important;
  text-decoration: underline;
}

.w-e-modal button {
  line-height: 20px;
}
</style>
