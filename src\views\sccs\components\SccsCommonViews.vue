<template>
  <div v-if="commonViews.length > 0" class="sccs-common-views-body">
    <div
      v-for="(view, index) in commonViews"
      :key="index"
      class="sccs-common-views-col"
      @click="handleViewClick(view)"
    >
      <div
        class="sccs-common-views-image"
        :class="[view.viewType === 'ORDER' ? 'order' : 'milestone']"
      >
        <i v-if="view.viewType === 'ORDER'" class="iconfont link-basic-info" />
        <i
          v-if="view.viewType === 'MILESTONE'"
          class="iconfont link-workorder"
        />
      </div>
      <div class="sccs-common-views-desc">
        <div class="sccs-common-views-title">
          <span class="order-name">
            <ReText
              type="info"
              class="sccs-common-views-flex-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ view.title }}
            </ReText>
          </span>
          <span class="order-num">
            <i class="iconfont link-display" />
            {{ formatNumberWithK(view.browseNum) }}
          </span>
        </div>
        <div v-if="view.msName" class="sccs-common-views-tip">
          <span class="sccs-common-views-flex-label">
            {{ `${t("trade_common_milestone")} :` }}
          </span>
          <ReText
            type="info"
            class="sccs-common-views-flex-text"
            :tippyProps="{ delay: 50000 }"
          >
            <span>{{ view.msName }}</span>
          </ReText>
        </div>
        <div class="sccs-common-views-team">
          <span class="sccs-common-views-flex">
            <span v-if="view.sccsName" class="sccs-common-views-flex-label">
              {{ `${t("trade_join_other_sccs")} :` }}
            </span>
            <svg
              v-if="view.sccsName && view.coopTeamMark"
              class="svg-icon svg-icon-coop"
              aria-hidden="true"
            >
              <use xlink:href="#link-coop" />
            </svg>
            <ReText
              type="info"
              class="sccs-common-views-flex-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{
                view.coopTeamMark
                  ? `${view.sccsName}(${view.coopTeamName})`
                  : view.sccsName
              }}
            </ReText>
          </span>
          <span @click.stop="changeCollect(view)">
            <i v-if="!view.favorite" class="iconfont link-star colorNormal" />
            <i v-else class="iconfont link-star-filled colorOrange" />
          </span>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="sccs-common-views-body">
    <el-empty
      style="margin: 0 auto"
      :image-size="342"
      :image="sccsGroupNoImage"
    >
      <template #description>
        <div
          class="sccs-common-views-empty-desc"
          v-html="sccsCommonViewsEmptyDesc"
        />
      </template>
    </el-empty>
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";
import { getUserRolePerm } from "@/utils/auth";
import { updateTaskFavorite } from "@/api/order";
import sccsGroupNoImage from "@/assets/images/sccs/sccs-no-image.png";
import { computed } from "vue";

interface orderViewProp {
  viewType: string;
  title: string;
  favorite: boolean;
  msName: string;
  sccsName: string;
  coopTeamName: string;
  coopTeamMark: boolean;
}
const props = defineProps({
  commonViews: {
    type: Array as PropType<orderViewProp[]>,
    default: () => []
  }
});

const { t } = useI18n();

const emit = defineEmits(["handleViewClick", "changeCollect"]);
const sccsCommonViewsEmptyDesc = computed(() => {
  return useI18n().t("trade_sccs_common_view_empty_desc", {
    params0: `<i class="iconfont link-star-filled colorOrange" />`
  });
});

const changeCollect = async view => {
  const { id, favorite } = view;
  const { code } = await updateTaskFavorite({
    favoriteType: "ORDER_VIEW", // 收藏类型
    sourceId: id, // 收藏对象id
    favorite: !favorite // 是否收藏
  });
  if (code === 0) {
    emit("changeCollect");
  }
};
const handleViewClick = async view => {
  const { sccsId, groupId, templateId, sccsName, coopTeamMark, viewType } =
    view;
  getUserRolePerm(sccsId, coopTeamMark);
  emit("handleViewClick", {
    sccsId,
    groupId,
    templateId,
    sccsName,
    coopTeamMark,
    viewId: view.id,
    viewType
  });
};

const formatNumberWithK = num => {
  if (num < 1000) {
    return num.toString();
  }
  return (num / 1000).toFixed(1) + "K";
};
</script>
<style lang="scss" scoped>
.sccs-common-views-body {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  margin-top: 0;

  .sccs-common-views-empty-desc {
    font-size: 16px;
    color: #797979;

    ::v-deep(.link-star-filled::before) {
      color: rgb(254 199 60);
    }
  }
}

.sccs-common-views-col {
  display: flex;
  width: 376px;
  height: 100px;
  padding: 15px;
  margin-right: 20px;
  margin-bottom: 20px;
  cursor: pointer;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 6px 0 rgb(1 6 62 / 10%);

  &:hover {
    box-shadow: 0 6px 18px 0 rgb(1 6 62 / 14%);
  }

  .sccs-common-views-image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    margin-right: 10px;
    border-radius: 3px;

    &.milestone {
      background: linear-gradient(180deg, #ffa23d, #ff7600);
    }

    &.order {
      background: #3c7fff;
    }

    .iconfont {
      font-size: 12px;
      color: #fff;
    }
  }

  .sccs-common-views-desc {
    flex: 1;

    .sccs-common-views-title {
      display: flex;
      justify-content: space-between;

      .order-name {
        max-width: 280px;
        font-size: 14px;
        color: #262626;
      }

      .order-num {
        display: flex;
        align-items: center;
        margin-left: 4px;
        font-size: 12px;
        color: #bfbfbf;
      }
    }

    .sccs-common-views-tip {
      display: flex;
      align-items: center;
      max-width: 294px;
      height: 20px;
      margin-top: 4px;
      font-size: 14px;
      color: #8c8c8c;

      .sccs-common-views-flex-label {
        margin-right: 3px;
        font-size: 12px;
        color: #8c8c8c;
        white-space: nowrap;
      }

      .sccs-common-views-flex-text {
        font-size: 12px;
        color: #595959;
      }
    }

    .sccs-common-views-team {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .colorNormal {
        color: #8c8c8c;
      }

      .colorOrange {
        color: rgb(254 199 60);
      }

      .sccs-common-views-flex {
        display: flex;
        align-items: center;
        max-width: 294px;

        .svg-icon-coop {
          flex-shrink: 0;
          margin-right: 3px;
        }

        .sccs-common-views-flex-label {
          margin-right: 3px;
          font-size: 12px;
          color: #8c8c8c;
          white-space: nowrap;
        }

        .sccs-common-views-flex-text {
          font-size: 12px;
          color: #595959;
        }
      }
    }
  }
}
</style>
