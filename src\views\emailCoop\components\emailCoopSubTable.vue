<!-- 已废弃，由于 vxe-grid 的性能有问题，使用 lkSubTable 代替， 参考 emailCoopSubTableV2 代码 -->
<template>
  <LkDialog
    ref="widgetDialogRef"
    :title="widgetDialogTitle"
    append-to-body
    width="50vw"
    confrimText="trade_email_confirmUseData"
    :destroy-on-close="true"
    @confirm="handleConfirmWidget"
  >
    <template #default>
      <vxe-grid
        ref="gridRef"
        v-bind="gridOptions"
        :span-method="mergeRowMethod"
      />
    </template>
    <template #dialogFooterLeft>
      <div class="dialog-footer-left-body">
        {{ t("trade_email_selected") }}
        <span class="dialog-footer-left-title">{{ gridCheckedNumber }}</span>
        {{ t("trade_common_strip") }}
      </div>
    </template>
  </LkDialog>
  <EmailCoopSubTableMessageBox
    ref="EmailCoopSubTableMessageBoxRef"
    @handleConfirm="handleSubTableConfirm"
  />
</template>
<script lang="tsx" setup>
import { computed, nextTick, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import type { VxeGridProps, VxeGridInstance } from "vxe-table";
import LkDialog from "@/components/lkDialog";
import EmailCoopSubTableMessageBox from "./emailCoopSubTableMessageBox.vue";
import {
  TransformSubmitDataStructure,
  handleRenderWidget
} from "@/utils/formDesignerUtils";
import { cloneDeep } from "@pureadmin/utils";
import dayjs from "dayjs";

const props = defineProps({
  workOrderId: {
    type: String as PropType<string>,
    default: ""
  },
  milestoneId: {
    type: String as PropType<string>,
    default: ""
  },
  worderName: {
    type: String as PropType<string>,
    default: ""
  },
  quoteVisible: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const emit = defineEmits(["handleQuoteData", "handleQuoteSubTableData"]);

const { t } = useI18n();
const route = useRoute();
const widgetDialogRef = ref<HTMLElement | any>(null);
const EmailCoopSubTableMessageBoxRef = ref<HTMLElement | any>(null);
const widgetField = ref<string>("");
const widgetDialogTitle = ref<string>("");
const gridRef = ref<VxeGridInstance<any>>();
const gridOptions = reactive<VxeGridProps<any>>({
  border: true,
  showOverflow: false,
  height: "500",
  scrollY: {
    enabled: true,
    gt: 0,
    mode: "wheel"
  },
  scrollX: {
    enabled: true,
    gt: 0
  },
  rowConfig: {
    keyField: "rowId"
  },
  resizable: true,
  columns: [],
  headerCellClassName({ column }) {
    return column.params && column.params.widget ? "FAFAE8" : "";
  },
  data: []
});
const mergeFields = ref<any>([
  "subject",
  "submitEmail",
  "createTime",
  "updateTime"
]);

const gridCheckedNumber = computed(() => {
  return gridRef.value ? gridRef.value.getCheckboxRecords(true).length : 0;
});

const open = (columns: any, tableData: any, field: string) => {
  widgetDialogRef.value.open();
  widgetField.value = field;
  let tableList = [];
  tableData.forEach(tableRow => {
    const widgetData = TransformSubmitDataStructure(
      tableRow.workOrderCoopEmailDataVO.widgetDataList,
      tableRow.workOrderCoopEmailDataVO.widgetDataSubMap,
      tableRow.workOrderCoopEmailDataVO.linkedReferenceMap
    );
    const widgetItem = widgetData.find(widget => widget.widgetId === field);

    if (widgetItem && widgetItem.childrenList) {
      widgetItem.childrenList.forEach(widgetChild => {
        let orderWidget = {};
        widgetChild.forEach(widgetChildItem => {
          orderWidget[widgetChildItem["widgetId"]] = [
            "IMAGE_UPLOAD",
            "FILE_UPLOAD"
          ].includes(widgetChildItem.widgetType)
            ? widgetChildItem["obj"]
            : widgetChildItem["label"];
        });
        tableList.push(
          Object.assign(cloneDeep(tableRow), orderWidget, {
            rowId: new Date().getTime() + Math.floor(Math.random() * 1000),
            originalData: widgetChild
          })
        );
      });
    }
  });

  let gridsColumns: any = [
    {
      type: "checkbox",
      width: 60,
      align: "center"
    },
    {
      field: "subject",
      title: t("trade_email_subject"),
      width: 140,
      align: "center",
      headerAlign: "left"
    },
    {
      field: "submitEmail",
      title: t("trade_login_email"),
      width: 140,
      align: "center",
      headerAlign: "left"
    }
  ];
  columns.forEach(column => {
    const { label, name, widgetType } = column;
    gridsColumns.push({
      field: name,
      title: label,
      align: "center",
      headerAlign: "left",
      width: 140,
      params: {
        widget: true
      },
      slots: {
        default({ row }) {
          return handleRenderWidget(widgetType, row, name, column);
        }
      }
    });
  });
  gridsColumns.push(
    {
      field: "createTime",
      title: t("trade_common_creatorTime"),
      width: 140,
      align: "center",
      headerAlign: "left",
      slots: {
        default({ row }) {
          return (
            <div>
              <div>
                {row.submitTime
                  ? dayjs(new Date(row.createTime)).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )
                  : ""}
              </div>
            </div>
          );
        }
      }
    },
    {
      field: "updateTime",
      title: t("trade_common_updateTime"),
      width: 140,
      align: "center",
      headerAlign: "left",
      slots: {
        default({ row }) {
          return (
            <div>
              <div>
                {row.submitTime
                  ? dayjs(new Date(row.updateTime)).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )
                  : ""}
              </div>
            </div>
          );
        }
      }
    }
  );
  gridOptions.columns = gridsColumns;
  nextTick(() => {
    gridRef.value.reloadColumn(gridsColumns);
    gridRef.value.reloadData(tableList);
  });
};

const mergeRowMethod = ({ row, _rowIndex, column, visibleData }) => {
  const fields = mergeFields.value;
  if (fields.length == 0) {
    return;
  }
  const cellValue = row[column.property];
  if (
    cellValue != undefined &&
    cellValue != null &&
    fields.includes(column.property)
  ) {
    const prevRow = visibleData[_rowIndex - 1];
    let nextRow = visibleData[_rowIndex + 1];
    //if (prevRow && prevRow[column.property] === cellValue) {
    if (prevRow && checkMergeFields(row, prevRow, column.property)) {
      return { rowspan: 0, colspan: 0 };
    } else {
      let countRowspan = 1;
      //while (nextRow && nextRow[column.property] === cellValue) {
      while (nextRow && checkMergeFields(row, nextRow, column.property)) {
        nextRow = visibleData[++countRowspan + _rowIndex];
      }
      if (countRowspan > 1) {
        return { rowspan: countRowspan, colspan: 1 };
      }
    }
  }
};

//循环判断前面的列，如果值不一样的话就不合并
const checkMergeFields = (row, nextRow, property) => {
  var ret = true;
  for (var i = 0; i < mergeFields.value.length; i++) {
    var field = mergeFields.value[i];
    if (nextRow[field] != row[field]) {
      ret = false;
      break;
    }
    if (field == property) {
      break;
    }
  }

  return ret;
};

const handleConfirmWidget = () => {
  EmailCoopSubTableMessageBoxRef.value.open(gridCheckedNumber);
};

const handleSubTableConfirm = () => {
  EmailCoopSubTableMessageBoxRef.value.close();
  widgetDialogRef.value.close();
  emit("handleQuoteSubTableData", {
    records: cloneDeep(gridRef.value.getCheckboxRecords(true)),
    widgetField: widgetField.value
  });
};

watch(
  () => gridCheckedNumber,
  () => {
    nextTick(() => {
      widgetDialogRef.value.handleSetConfirmBtnDisabled(
        gridCheckedNumber.value === 0
      );
    });
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
<style lang="scss">
.dialog-footer-left-body {
  display: inline-block;
  margin-right: 38px;
  font-size: 14px;
  line-height: 14px;
  color: #606266;
  text-align: left;

  .dialog-footer-left-title {
    padding: 0 3px;
    font-weight: bold;
    color: #262626;
  }
}
</style>
