<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_perCenter_updatePersonalInformation')"
    :formRef="ruleFormRef"
    @confirm="confirmFun"
  >
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="auto"
      label-position="top"
    >
      <el-form-item :label="t('trade_common_name')" prop="username">
        <el-input
          v-model="ruleForm.username"
          :placeholder="t('trade_team_enterAccount')"
          show-word-limit
          clearable
          maxlength="50"
        />
      </el-form-item>
      <el-form-item :label="t('trade_common_sex')" prop="sex">
        <el-select
          v-model="ruleForm.sex"
          clearable
          :placeholder="t('trade_common_sexTip')"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";
import type { FormInstance, FormRules } from "element-plus";
import { updateUserInfo } from "@/api/common";
import { getSystemUserInfo } from "@/utils/auth";
import { ElMessage } from "element-plus";

interface userInfoProp {
  username: string;
  sex?: number | null;
}

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const ruleFormRef = ref<FormInstance>();
const options = [
  {
    value: 1,
    label: t("trade_common_male")
  },
  {
    value: 2,
    label: t("trade_common_female")
  }
];
const ruleForm = reactive<userInfoProp>({
  username: "",
  sex: null
});
const rules = reactive<FormRules<userInfoProp>>({
  username: [
    {
      required: true,
      message: t("trade_team_enterAccount"),
      trigger: "blur"
    }
  ]
});

const props = defineProps({
  userInfo: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const emit = defineEmits<{ (e: "updateUserInfo"): void }>();

const open = (): void => {
  ruleForm.username = props.userInfo.username;
  ruleForm.sex = props.userInfo.sex;
  LkDialogRef.value.open();
};

const confirmFun = (): void => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      updateUserInfo(ruleForm).then(res => {
        if (res.code === 0) {
          const { accessToken, refreshToken, expires } = props.userInfo;
          getSystemUserInfo({ accessToken, refreshToken, expires }).then(
            res => {
              ElMessage({
                message: t("trade_common_dealSuccess"),
                type: "success"
              });
              emit("updateUserInfo");
              LkDialogRef.value.close();
            }
          );
        }
      });
    }
  });
};

defineExpose({
  open
});
</script>
