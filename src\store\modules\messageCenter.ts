import { cloneDeep } from "@pureadmin/utils";
import { defineStore } from "pinia";
import { debounce } from "lodash-es";
import {
  getMessageCenter,
  getMessageCenterNode,
  setMessageRead,
  setAllMessageRead
} from "@/api/message";
import { store } from "../utils";

// 消息数据类型定义
export interface MessageItem {
  id?: string;
  templateTitle: string;
  messageType: string;
  templateContent: string;
  toTeamAvatar: string;
  fromUserAvatar: string;
  fromUserName: string;
  toTeamName: string;
  fromTeamName: string;
  fromTeamAvatar: string;
  link: string;
  readStatus: boolean;
  unreadCount: number;
  createTime: string;
  title?: string;
  content?: string;
}

// 消息中心状态类型
export interface MessageCenterState {
  // 所有消息列表（按消息类型分组）
  messageList: MessageItem[];
  // 未读消息列表
  unreadMessageList: MessageItem[];
  // 详情页消息列表（按消息类型获取的具体消息）
  detailMessageList: MessageItem[];
  // 全局未读消息总数
  totalUnreadCount: number;
  // 消息通知设置开关
  notifyEnabled: boolean;
  // 浮层提示消息列表
  tooltipMessageList: MessageItem[];
  // 加载状态
  loading: boolean;
  // 详情页加载状态
  detailLoading: boolean;
  // 详情页分页信息
  detailPagination: {
    pageNo: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
  // 消息提示相关状态
  tooltipCurrentIndex: number;
}

export const useMessageCenterStore = defineStore("messageCenter", {
  state: (): MessageCenterState => ({
    messageList: [],
    unreadMessageList: [],
    detailMessageList: [],
    totalUnreadCount: 0,
    notifyEnabled: true,
    tooltipMessageList: [],
    loading: false,
    detailLoading: false,
    detailPagination: {
      pageNo: 1,
      pageSize: 20,
      total: 0,
      hasMore: true
    },
    tooltipCurrentIndex: 0
  }),

  getters: {
    // 计算各消息类型的未读数量
    unreadCountByType: (state): Map<string, number> => {
      const countMap = new Map<string, number>();
      state.messageList.forEach(item => {
        countMap.set(item.messageType, item.unreadCount || 0);
      });
      return countMap;
    },
    // 计算消息提示是否可见
    tooltipVisible: (state): boolean => {
      return state.tooltipMessageList.length > 0;
    }
  },

  actions: {
    // 获取消息中心数据（带防抖）
    fetchMessageCenterData: debounce(async function (this: any): Promise<void> {
      try {
        this.loading = true;

        const [allMessagesRes, unreadMessagesRes] = await Promise.all([
          getMessageCenter(),
          getMessageCenter(0) // 0 表示只获取未读消息
        ]);

        // 更新所有消息列表
        if (allMessagesRes.code === 0) {
          this.messageList = allMessagesRes.data || [];
          this.notifyEnabled =
            (allMessagesRes as any).extend?.notifyEnable ?? true;
        }

        // 更新未读消息列表和总数
        if (unreadMessagesRes.code === 0) {
          const unreadData = unreadMessagesRes.data || [];
          this.unreadMessageList = unreadData.filter(
            item => item.unreadCount > 0
          );

          // 计算总未读数量
          this.totalUnreadCount = unreadData.reduce(
            (acc, item) => acc + (item.unreadCount || 0),
            0
          );
        }

        // 触发全局未读数量更新事件
        // emitter.emit("updateUnReadCount");
      } catch (error) {
        console.error("获取消息中心数据失败:", error);
      } finally {
        this.loading = false;
      }
    }, 300),

    // 获取消息详情列表
    async fetchMessageDetail(
      messageType: string,
      readStatus?: number,
      isLoadMore: boolean = false
    ): Promise<void> {
      try {
        this.detailLoading = true;

        const params = {
          messageType,
          readStatus,
          pageNo: isLoadMore ? this.detailPagination.pageNo + 1 : 1,
          pageSize: this.detailPagination.pageSize
        };

        const response = await getMessageCenterNode(params);

        if (response.code === 0) {
          const newMessages = response.data?.list || [];

          if (isLoadMore) {
            this.detailMessageList.push(...newMessages);
            this.detailPagination.pageNo += 1;
          } else {
            this.detailMessageList = newMessages;
            this.detailPagination.pageNo = 1;
          }

          this.detailPagination.total = response.data?.total || 0;
          this.detailPagination.hasMore =
            newMessages.length === this.detailPagination.pageSize;
        }
      } catch (error) {
        console.error("获取消息详情失败:", error);
      } finally {
        this.detailLoading = false;
      }
    },

    // 标记单条消息为已读
    async markMessageAsRead(messageId: string): Promise<boolean> {
      try {
        const response = await setMessageRead({ id: messageId });

        if (response.code === 0) {
          // 更新详情列表中的消息状态
          const messageIndex = this.detailMessageList.findIndex(
            msg => msg.id === messageId
          );
          if (messageIndex !== -1) {
            this.detailMessageList[messageIndex].readStatus = true;
          }

          // 更新对应消息类型的未读数量
          const messageItem = this.detailMessageList[messageIndex];
          if (messageItem) {
            this.updateUnreadCountForType(messageItem.messageType, -1);
          }

          this.updateMessageCenterTip({
            type: "deleteById",
            content: messageId
          });

          return true;
        }
        return false;
      } catch (error) {
        console.error("标记消息已读失败:", error);
        return false;
      }
    },

    // 标记某类型所有消息为已读
    async markAllMessagesAsRead(messageType?: string): Promise<boolean> {
      try {
        const response = await setAllMessageRead({ messageType });

        if (response.code === 0) {
          if (messageType) {
            // 更新特定类型的消息为已读
            this.detailMessageList = this.detailMessageList.map(msg =>
              msg.messageType === messageType
                ? { ...msg, readStatus: true }
                : msg
            );

            // 重置该类型的未读数量
            this.updateUnreadCountForType(messageType, 0, true);

            // 触发消息中心提示更新
            this.updateMessageCenterTip({
              type: "deleteByMessageType",
              content: messageType
            });
          } else {
            // 标记所有消息为已读
            this.detailMessageList = this.detailMessageList.map(msg => ({
              ...msg,
              readStatus: true
            }));

            // 重置所有未读数量
            this.totalUnreadCount = 0;
            this.messageList = this.messageList.map(msg => ({
              ...msg,
              unreadCount: 0
            }));
            this.unreadMessageList = this.unreadMessageList.map(msg => ({
              ...msg,
              unreadCount: 0
            }));
            this.clearTooltipMessages();
          }

          return true;
        }
        return false;
      } catch (error) {
        console.error("标记所有消息已读失败:", error);
        return false;
      }
    },

    // 更新特定消息类型的未读数量
    updateUnreadCountForType(
      messageType: string,
      change: number,
      reset: boolean = false
    ): void {
      // 更新主列表和未读列表中的未读数量
      const updateUnreadCount = (list: any[], index: number) => {
        if (index !== -1) {
          list[index].unreadCount = reset
            ? 0
            : Math.max(0, (list[index].unreadCount || 0) + change);
        }
      };

      // 更新主列表
      const messageIndex = this.messageList.findIndex(
        msg => msg.messageType === messageType
      );
      updateUnreadCount(this.messageList, messageIndex);

      // 更新未读列表
      const unreadMessageIndex = this.unreadMessageList.findIndex(
        msg => msg.messageType === messageType
      );
      updateUnreadCount(this.unreadMessageList, unreadMessageIndex);

      // 重新计算总未读数量
      this.totalUnreadCount = this.messageList.reduce(
        (acc, item) => acc + (item.unreadCount || 0),
        0
      );
    },

    resetUnreadMessageList(): void {
      this.unreadMessageList = cloneDeep(this.messageList).filter(
        item => (item.unreadCount || 0) > 0
      );
    },

    // 添加新消息到提示列表
    addTooltipMessage(message: MessageItem): void {
      // 检查是否已存在相同ID的消息
      const existingIndex = this.tooltipMessageList.findIndex(
        msg => msg.id === message.id
      );

      if (existingIndex === -1) {
        this.tooltipMessageList.unshift(message);
      }
    },

    // 从提示列表中移除消息
    removeTooltipMessage(messageId: string): void {
      this.tooltipMessageList = this.tooltipMessageList.filter(
        msg => msg.id.toString() !== messageId.toString()
      );
    },

    // 清空提示消息列表
    clearTooltipMessages(): void {
      this.tooltipMessageList = [];
    },

    // 重置详情页分页信息
    resetDetailPagination(): void {
      this.detailPagination = {
        pageNo: 1,
        pageSize: 20,
        total: 0,
        hasMore: true
      };
      this.detailMessageList = [];
    },

    // 初始化消息中心数据
    initializeMessageCenter(): void {
      // 重置数据
      this.messageList = [];
      this.unreadMessageList = [];
      this.detailMessageList = [];
      this.totalUnreadCount = 0;
      this.tooltipMessageList = [];
      this.tooltipCurrentIndex = 0;
      this.detailLoading = false;
      // 获取初始数据
      this.fetchMessageCenterData();
    },

    // 更新消息提示
    updateMessageCenterTip(data: { type: string; content: any }): void {
      if (data.type === "deleteById") {
        this.removeTooltipMessage(data.content);
      } else if (data.type === "deleteByMessageType") {
        // 按消息类型删除
        const filteredMessages = this.tooltipMessageList.filter(
          item => item.messageType !== data.content
        );
        // 清空后重新添加过滤后的消息
        this.clearTooltipMessages();
        filteredMessages.forEach(msg => this.addTooltipMessage(msg));
      } else if (data.type === "add") {
        this.addTooltipMessage(data.content);
      }
      this.tooltipCurrentIndex = 0;
    },

    // 切换消息提示
    switchTooltipMessage(mode: "prev" | "next"): void {
      if (mode === "next") {
        ++this.tooltipCurrentIndex;
        if (this.tooltipCurrentIndex === this.tooltipMessageList.length) {
          this.tooltipCurrentIndex = 0;
        }
      } else {
        --this.tooltipCurrentIndex;
        if (this.tooltipCurrentIndex === -1) {
          this.tooltipCurrentIndex = this.tooltipMessageList.length - 1;
        }
      }
    },

    // 关闭消息提示
    closeTooltip(): void {
      this.clearTooltipMessages();
      this.tooltipCurrentIndex = 0;
    }
  }
});

export function useMessageCenterStoreHook() {
  return useMessageCenterStore(store);
}
