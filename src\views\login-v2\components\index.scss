// 登录组件共享样式

// 表单容器
.login-form-container {
  width: 100%;
}

// 输入框样式
.login-form-item {
  margin-bottom: 24px;

  :deep(.el-form-item) {
    margin-bottom: 24px;

    .el-form-item__label {
      padding: 0;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
      color: #1a1a1a;
    }

    .el-form-item__content {
      .el-input {
        height: 48px;

        .el-input__wrapper {
          padding: 0 16px;
          background: #f4f6fa;
          border: none;
          box-shadow: none;
          transition: all 0.3s ease;

          &:hover {
            background: #fff;
            border-color: var(--el-input-focus-border-color);
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
          }

          &.is-focus {
            background: #fff;
            border-color: var(--el-input-focus-border-color);
            box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
          }

          .el-input__inner {
            height: 46px;
            font-size: 14px;
            line-height: 46px;
            color: #1a1a1a;

            &::placeholder {
              color: #c0c4cc;
            }
          }
        }
      }

      // 验证码输入框
      .el-input-group {
        .el-input-group__append {
          background: #f5f7fa;
          border: 1px solid #e4e7ed;
          border-left: none;
          border-radius: 0 12px 12px 0;

          .el-button {
            height: 46px;
            font-size: 14px;
            font-weight: 500;
            color: #409eff;
            background: transparent;
            border: none;

            &:hover {
              background: rgb(64 158 255 / 10%);
            }

            &.is-disabled {
              color: #c0c4cc;
              background: transparent;
            }
          }
        }

        .el-input__wrapper {
          border-radius: 12px 0 0 12px;
        }
      }

      .el-input__count-inner {
        background-color: transparent;
      }

      // select
      .el-select {
        .el-select__wrapper {
          height: 48px;
          background: #f4f6fa;
          border: none;
          box-shadow: none;
          transition: all 0.3s ease;

          &:hover {
            background: #fff;
            border-color: #0070d2;
            box-shadow: 0 0 0 1px #0070d2 inset;
          }

          &.is-focus {
            background: #fff;
            border-color: #0070d2;
            box-shadow: 0 0 0 1px #0070d2 inset;
          }
        }
      }
    }

    // 错误状态
    &.is-error {
      .el-input .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
      }

      .el-select {
        .el-select__wrapper {
          box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
        }
      }
    }
  }
}

// 登录按钮
.login-button {
  width: 100%;
  height: 48px;
  margin-top: 8px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 12px;

  &.el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);

    &:hover {
      background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
    }

    &:active {
      background: linear-gradient(135deg, #337ecc 0%, #2d6cb3 100%);
    }

    &.is-loading {
      background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
    }
  }
}

// 记住我和忘记密码
.login-form-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 40px;

  .remember-me {
    .el-checkbox {
      .el-checkbox__label {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .forgot-password {
    .el-button {
      height: auto;
      padding: 0;
      font-size: 14px;
      color: #409eff;

      &:hover {
        color: #3a8ee6;
      }
    }
  }
}

// 验证码倒计时
.captcha-countdown {
  font-size: 14px;
  color: #c0c4cc;
}

// 响应式
@media screen and (width <= 768px) {
  .login-form-item {
    margin-bottom: 20px;

    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__content {
        .el-input {
          height: 44px;

          .el-input__wrapper .el-input__inner {
            height: 42px;
            line-height: 42px;
          }
        }

        .el-input-group .el-input-group__append .el-button {
          height: 42px;
        }
      }
    }
  }

  .login-button {
    height: 44px;
    font-size: 15px;
  }
}

@media screen and (width <= 480px) {
  .login-form-options {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

// 验证码表单项布局
.captcha-form-item {
  :deep(.el-form-item__content) {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .captcha-input-container {
    flex: 1;
  }

  .send-verify-code-btn,
  .count-down-body {
    flex-shrink: 0;
    min-width: 120px;
    height: 48px;
    font-family: "PingFang SC", "PingFang SC-Regular";
    font-size: 14px;
    font-weight: 500;
    color: #0070d2;
    background-color: #edf1fd;
    border: none;

    &:hover {
      background-color: #eff5ff;
    }

    &:active {
      background-color: #e5e9f2;
    }
  }

  .count-down-body {
    :deep(.el-statistic .el-statistic__content .el-statistic__number) {
      font-family: "PingFang SC", "PingFang SC-Semibold";
      font-size: 14px !important;
      font-weight: semibold;
      line-height: 56px;
      color: #87c9ff;
      text-align: left;
      background-color: #edf1fd;
    }

    :deep(.el-statistic .el-statistic__content .el-statistic__prefix span) {
      font-size: 14px !important;
      color: #87c9ff;
    }
  }
}

// 提示文本
.captcha-tip-text {
  display: block;
  margin-top: -16px;
  margin-bottom: 16px;
  font-size: 12px;
  line-height: 18px;
  color: #8c8c8c;
  transition:
    margin-top 0.3s ease-in-out,
    margin-bottom 0.3s ease-in-out;
}

// 响应式适配
@media screen and (width <= 768px) {
  .captcha-form-item {
    :deep(.el-form-item__content) {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;

      .send-verify-code-btn,
      .count-down-body {
        width: 100%;
        min-width: unset;
        height: 44px;
      }
    }
  }

  .captcha-tip-text {
    margin-top: -12px;
    margin-bottom: 12px;
  }
}

@media screen and (width <= 480px) {
  .captcha-form-item {
    :deep(.el-form-item__content) {
      gap: 6px;
    }
  }
}
