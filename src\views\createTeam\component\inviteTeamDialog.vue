<template>
  <div class="create-team-guide-dialog">
    <LkDialog
      ref="LkDialogRef"
      :title="t('trade_team_inviteCollaborateTeam') + '1111'"
      :noFooter="true"
      @close="handleClose"
    >
      <template #default>
        <el-form
          ref="formRef"
          style="height: 206px"
          :model="inviteMembersForm"
          label-position="top"
        >
          <el-form-item :label="t('trade_invite_team_tip')">
            <el-input
              v-model.trim="inviteMembersForm.teamCode"
              clearable
              maxlength="6"
              @input="handleInput"
              @blur="handleBlur"
              @clear="handleClear"
              @keydown.enter.prevent="handleEnter"
            />
          </el-form-item>
          <el-text
            v-if="inviteSearchTeam.inviteSearchTeamStatus === 1"
            type="info"
          >
            {{ t("trade_team_queryResults") }}：
            {{ t(inviteSearchTeam.inviteSearchTeamCode) }}
          </el-text>
          <el-text
            v-if="inviteSearchTeam.inviteSearchTeamStatus === 2"
            type="danger"
          >
            {{ t(inviteSearchTeam.inviteSearchTeamCode) }}
          </el-text>
          <el-text
            v-if="inviteSearchTeam.inviteSearchTeamStatus === 3"
            type="danger"
          >
            {{ t(inviteSearchTeam.inviteSearchTeamCode) }}
          </el-text>
        </el-form>
      </template>
      <template #footer>
        <el-button
          type="primary"
          color="#0070D2"
          :disabled="
            !(
              inviteMembersForm.teamCode &&
              inviteSearchTeam.inviteSearchTeamStatus === 1
            )
          "
          @click="confirmFun"
        >
          {{ t("trade_team_inviteTeam") }}
        </el-button>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import LkDialog from "@/components/lkDialog/index";
import { useI18n } from "vue-i18n";
import { message } from "@/utils/message";
import type { FormInstance } from "element-plus";
import { getTeamByCode, inviteTeam } from "@/api/team";
import _ from "lodash";

interface inviteTeamProp {
  inviteSearchTeamCode: string | null;
  inviteSearchTeamStatus: number;
  inviteTeamId: string | null;
}

const formRef = ref<FormInstance>();
const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const inviteSearchTeam = reactive<inviteTeamProp>({
  inviteSearchTeamCode: "",
  inviteSearchTeamStatus: 0,
  inviteTeamId: ""
});
const teamId = ref<string>("");
const inviteMembersForm = reactive<{
  teamCode: string;
}>({
  teamCode: ""
});

const emit = defineEmits<{
  (e: "inviteSuccess", id: string): void;
}>();

const open = (id: string): void => {
  teamId.value = id;
  inviteSearchTeam.inviteSearchTeamStatus = 0;
  LkDialogRef.value.open();
};

const handleClose = (): void => {
  inviteMembersForm.teamCode = "";
  formRef.value.clearValidate();
};

// 防抖方法
const debouncedSearch = _.debounce((value: string) => {
  handleSearchInviteTeam(value);
}, 1500);

// 输入框输入事件
const handleInput = (value: string): void => {
  if (value.length === 6) {
    // 输入满 6 位时立即触发搜索
    handleSearchInviteTeam(value);
  } else if (value.length < 6) {
    // 输入未满 6 位时延迟 1.5 秒触发搜索
    debouncedSearch(value);
  }
};

// 失去焦点事件
const handleBlur = (): void => {
  handleSearchInviteTeam(inviteMembersForm.teamCode);
};

// 清除按钮事件
const handleClear = (): void => {
  inviteMembersForm.teamCode = "";
  inviteSearchTeam.inviteSearchTeamCode = "trade_welcome_hintInputTeamCode";
  inviteSearchTeam.inviteSearchTeamStatus = 3;
};

// 回车键事件
const handleEnter = (): void => {
  handleSearchInviteTeam(inviteMembersForm.teamCode);
};

// 搜索逻辑
const handleSearchInviteTeam = async (value: string): Promise<void> => {
  if (!value) {
    inviteSearchTeam.inviteSearchTeamCode = "trade_welcome_hintInputTeamCode";
    inviteSearchTeam.inviteSearchTeamStatus = 3;
    return;
  }

  const res = await getTeamByCode({ teamCode: value });
  if (res.code === 0) {
    inviteSearchTeam.inviteSearchTeamCode = res.data.teamName;
    inviteSearchTeam.inviteTeamId = res.data.id;
    inviteSearchTeam.inviteSearchTeamStatus = 1;
  } else if (res.code === 1000) {
    inviteSearchTeam.inviteSearchTeamCode = res.node || res.msg;
    inviteSearchTeam.inviteSearchTeamStatus = 2;
  }
};

const confirmFun = async (): Promise<void> => {
  if (!inviteSearchTeam.inviteTeamId) return;
  const res = await inviteTeam({
    invitedTeamId: inviteSearchTeam.inviteTeamId
  });
  if (res.code === 0) {
    message(t("trade_team_invitationSuccessful"), {
      customClass: "el",
      type: "success"
    });
    LkDialogRef.value.close();
    inviteMembersForm.teamCode = "";
    inviteSearchTeam.inviteSearchTeamStatus = 0;
    formRef.value.clearValidate();
    emit("inviteSuccess", teamId.value);
  }
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.link-ashbin-body {
  margin-top: 8px;

  .iconfont {
    font-size: 12px;
    color: #808080;
  }
}
</style>
