<template>
  <div class="lk-avater-container">
    <el-avatar
      :src="avaterInfo.avaterURL"
      :size="44"
      v-bind="$attrs"
      fit="cover"
    >
      {{ avaterInfo.userName }}
    </el-avatar>
    <svg v-if="avaterInfo.coop" class="icon svg-icon" aria-hidden="true">
      <use xlink:href="#link-coop" />
    </svg>
    <div
      v-if="avaterInfo.status"
      class="lk-avatar-mask"
      :style="{
        width: `${$attrs.size}px`,
        height: `${$attrs.size}px`,
        'line-height': `${$attrs.size}px`
      }"
    >
      <i class="iconfont link-tick" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { ElAvatar } from "element-plus";
import { storageLocal } from "@pureadmin/utils";

interface userAvaterInfo {
  avaterURL: string | null;
  userName: string | null;
  status: string | null;
  coop: boolean | null;
}

interface TeamInfo {
  id?: string;
  avatar: string;
  username: string;
}

const props = defineProps({
  teamInfo: {
    type: Object as PropType<TeamInfo>
  }
});

const avaterInfo = ref<userAvaterInfo>({
  avaterURL: "",
  userName: "",
  status: "",
  coop: false
});

watchEffect(() => {
  if (props.teamInfo) {
    avaterInfo.value.avaterURL = props.teamInfo?.avatar;
    avaterInfo.value.userName = props.teamInfo?.username?.substring(0, 1);
    avaterInfo.value.status = props.teamInfo?.status;
    avaterInfo.value.coop = props.teamInfo?.coop;
  } else {
    //@ts-ignore
    const { avatar, username } = storageLocal().getItem("user-info");
    avaterInfo.value.avaterURL = avatar;
    avaterInfo.value.userName = username?.substring(0, 1);
    avaterInfo.value.status = "";
    avaterInfo.value.coop = false;
  }
});

const reloadAvatar = (): void => {
  if (props.teamInfo) {
    avaterInfo.value.avaterURL = props.teamInfo?.avatar;
    avaterInfo.value.userName = props.teamInfo?.username?.substring(0, 1);
    avaterInfo.value.status = props.teamInfo?.status;
  } else {
    //@ts-ignore
    const { avatar, username } = storageLocal().getItem("user-info");
    avaterInfo.value.userName = username?.substring(0, 1);
    avaterInfo.value.avaterURL = avatar;
    avaterInfo.value.status = "";
  }
};

defineExpose({
  reloadAvatar
});
</script>
<style lang="scss" scoped>
.lk-avater-container {
  position: relative;
  display: inline-block;

  .el-avatar {
    cursor: pointer;
    background: #2082ed;
  }

  .lk-avatar-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1001;
    text-align: center;
    background: rgb(0 0 0 / 45%);
    border-radius: 50%;

    .iconfont {
      font-size: 10px;
      color: #00cc7b;
    }
  }

  .svg-icon {
    position: absolute;
    right: 0;
    bottom: -3px;
    z-index: 1000;
    width: 13px;
    height: 13px;
  }
}
</style>
