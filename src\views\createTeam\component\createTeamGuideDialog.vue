<template>
  <div class="create-team-guide-dialog">
    <LkDialog
      ref="LkDialogRef"
      :title="t('trade_team_createTeamSuccess')"
      :no-footer="true"
      @confirm="confirm"
      @closed="handleOpenHome"
    >
      <template #default>
        <div class="create-team-dialog-title">
          {{ t("trade_team_createTeamSuccessTip") }}
        </div>
        <div class="create-team-dialog-card-container">
          <div
            :class="[
              'create-team-guide-card',
              cardSelectMode === 'person' ? 'active' : ''
            ]"
            @click="changeCardMode('person')"
          >
            <el-image :src="guidePerson" fit="cover" />
            <div class="create-team-guide-msg">
              {{ t("trade_joined_team") }}
            </div>
          </div>
          <div class="create-team-guide-card-msg">
            {{ t("trade_common_or") }}
          </div>
          <div
            :class="[
              'create-team-guide-card',
              cardSelectMode === 'team' ? 'active' : ''
            ]"
            @click="changeCardMode('team')"
          >
            <el-image :src="guideTeam" fit="cover" />
            <div class="create-team-guide-msg">
              {{ t("trade_team_inviteCollaborateTeam") }}
            </div>
          </div>
        </div>
        <div class="create-team-dialog-guide-tip">
          {{ t("trade_team_youCanAlsoChoose") }}
          <span class="overall-type-button" @click="handleOpenHome">
            {{ t("trade_team_startUse") }}
          </span>
        </div>
        <div class="create-team-dialog-guide-small-tip">
          {{ t("trade_team_createSuccessAfterTip") }}
        </div>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import LkDialog from "@/components/lkDialog/index";
import { useI18n } from "vue-i18n";
import { message } from "@/utils/message";
import guidePerson from "@/assets/images/team/guidePerson.png";
import guideTeam from "@/assets/images/team/guideTeam.png";
import { useRouter } from "vue-router";
import { switchTeam } from "@/utils/auth";

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const cardSelectMode = ref<string>("person");
const teamId = ref<string>("");

const open = (id: string): void => {
  teamId.value = id;
  LkDialogRef.value.open();
};
const router = useRouter();
const emit = defineEmits<{
  (e: "switchCardMode", mode: string): void;
  (e: "inviteSuccess"): void;
}>();

const confirm = (): void => {
  emit("inviteSuccess");
};

const changeCardMode = (mode: string): void => {
  cardSelectMode.value = mode;
  emit("switchCardMode", mode);
};

const handleOpenHome = () => {
  switchTeam(teamId.value, true);

  // router.push("/");
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.create-team-guide-dialog {
  ::v-deep(.el-dialog) {
    .el-dialog__header {
      padding-bottom: 10px;
    }

    .create-team-dialog-title {
      font-size: 14px;
      line-height: 16px;
      color: #262626;
      text-align: left;
    }

    .create-team-guide-card-msg {
      width: 80px;
      text-align: center;
    }

    .create-team-dialog-card-container {
      display: flex;
      align-items: center;
      margin: 60px 72px;

      .create-team-guide-card {
        box-sizing: content-box;
        align-items: center;
        width: 196px;
        height: 174px;
        padding: 10px;
        text-align: center;
        cursor: pointer;
        border: 1px solid #dfe0e3;
        border-radius: 4px;

        &.active {
          border-color: #0070d2;
        }

        .el-image {
          width: 168px;
          margin-top: 14px;
          margin-bottom: 17px;
        }

        .create-team-guide-msg {
          font-size: 16px;
          font-weight: bolder;
          line-height: 22px;
          color: #262626;
          white-space: nowrap;
        }
      }
    }

    .create-team-dialog-guide-tip {
      margin-bottom: 7px;
      font-size: 14px;
      line-height: 16px;
      color: #262626;
      text-align: center;

      .overall-type-button {
        font-size: 14px;
        line-height: 16px;
        color: #0070d2;
        text-align: left;
      }
    }

    .create-team-dialog-guide-small-tip {
      font-size: 12px;
      line-height: 16px;
      color: #8c8c8c;
      text-align: center;
    }
  }
}
</style>
