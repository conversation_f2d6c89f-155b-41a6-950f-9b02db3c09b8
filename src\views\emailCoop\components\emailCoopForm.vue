<template>
  <el-tabs v-model="emailActiveName" @tab-click="handleChangeTabName">
    <el-tab-pane
      :label="t('trade_order_emailCollaborate')"
      name="emailCoop"
      lazy
    >
      <EmailCoopTable
        ref="EmailCoopTableRef"
        :workOrderId="workOrderId"
        :milestoneId="milestoneId"
        :sccsId="sccsId"
        :worderName="worderName"
        :quoteVisible="quoteVisible"
        :btnVisible="btnVisible"
        @handleQuoteData="handleQuoteData"
        @handleQuoteSubTableData="handleQuoteSubTableData"
      />
    </el-tab-pane>
    <el-tab-pane :label="t('trade_common_history')" name="history" lazy>
      <EmailHistoryTable
        ref="EmailHistoryTableRef"
        :workOrderId="workOrderId"
        :worderName="worderName"
      />
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import { nextTick, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import EmailCoopTable from "./emailCoopTable.vue";
import EmailHistoryTable from "./emailHistoryTable.vue";
import { TabsPaneContext } from "element-plus";

const props = defineProps({
  sccsId: {
    type: String as PropType<string>,
    default: ""
  },
  workOrderId: {
    type: String as PropType<string>,
    default: ""
  },
  milestoneId: {
    type: String as PropType<string>,
    default: ""
  },
  worderName: {
    type: String as PropType<string>,
    default: ""
  },
  quoteVisible: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  btnVisible: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const emit = defineEmits(["handleQuoteData", "handleQuoteSubTableData"]);

const { t } = useI18n();
const emailActiveName = ref<string>("emailCoop");
const EmailCoopTableRef = ref<HTMLElement | any>(null);
const EmailHistoryTableRef = ref<HTMLElement | any>(null);

const handleQuoteData = (row: any) => {
  emit("handleQuoteData", row);
};

const handleQuoteSubTableData = (row: any) => {
  emit("handleQuoteSubTableData", row);
};

const handleChangeTabName = (pane: TabsPaneContext, ev: any) => {
  if (emailActiveName.value === "emailCoop") {
    EmailCoopTableRef.value.initGridConfigData();
  } else {
    nextTick(() => {
      EmailHistoryTableRef.value.handleSearchTable();
    });
  }
};

onMounted(() => {
  EmailCoopTableRef.value.initGridConfigData();
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.el-tabs {
  height: 100%;

  .el-tab-pane {
    height: 100%;
  }

  ::v-deep(.el-tabs__header) {
    .el-tabs__nav-scroll {
      padding: 0 5px;
    }
  }
}
</style>
