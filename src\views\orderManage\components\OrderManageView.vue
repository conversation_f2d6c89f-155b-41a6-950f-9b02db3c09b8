<template>
  <div class="order-manage-col-main">
    <el-scrollbar class="no-scrollbar">
      <div v-show="isShowAnalysisView" class="order-manage-col">
        <div class="order-manage-title">
          {{ t("trade_order_statistical_analysis") }}
        </div>
        <div class="order-manage-list-body">
          <div
            v-for="item in analysisList"
            :key="item.id"
            class="order-manage-draggable-col"
            :class="{
              active: currentAnalysisPanelId === item.id
            }"
            @click.stop="handleSwitchAnalysis(item)"
          >
            <n-tag class="item">
              <div class="order-manage-draggable-col-left">
                <i
                  class="iconfont link-drag vis-hidden-icon"
                  style="visibility: hidden"
                />
                <i class="iconfont link-table font14 table-manage-icon" />
                <ReText
                  type="info"
                  class="order-manage-draggable-col-title"
                  :tippyProps="{ delay: 50000 }"
                >
                  {{ t(item.panelName) }}
                </ReText>
              </div>
            </n-tag>
          </div>
        </div>
      </div>
      <div v-show="isShowOrderView" class="order-manage-col">
        <div class="order-manage-title">{{ t("trade_order_orderList") }}</div>
        <draggable
          :list="orderViewList.orderViewList"
          :disabled="false"
          item-key="groupName"
          group="freeGroup"
          class="order-manage-list-body"
          ghost-class="ghost"
          chosen-class="chosen"
          animation="300"
          @start="dragging = true"
          @move="handleMoved"
          @end="handleOrderViewDraggingEnd"
        >
          <template #item="{ element }">
            <div
              class="order-manage-draggable-col"
              :class="{
                'not-draggable': element.defaultView,
                active: selectViewId === element.id,
                'default-row': element.defaultView
              }"
              :data-draggable-name="element.name"
            >
              <n-tag
                :key="element.id"
                class="item"
                :class="{
                  'not-draggable': !element.defaultView
                }"
                @click.stop="handleSwitchView(element)"
              >
                <div class="order-manage-draggable-col-left">
                  <i class="iconfont link-drag vis-hidden-icon" />
                  <i class="iconfont link-table font14 table-manage-icon" />
                  <ReText
                    type="info"
                    class="order-manage-draggable-col-title"
                    :tippyProps="{ delay: 50000 }"
                  >
                    {{ t(element.title) }}
                  </ReText>
                </div>
                <div class="order-manage-draggable-col-right">
                  <el-dropdown placement="bottom">
                    <i class="iconfont link-more" />
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          v-if="!element.favorite"
                          @click.prevent="changeCollect(element)"
                        >
                          <i class="iconfont link-star" />
                          {{ t("trade_add_common") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="element.favorite"
                          @click.prevent="changeCollect(element)"
                        >
                          <i class="iconfont link-cancel-star" />
                          {{ t("trade_cancel_set_common") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="!element.defaultView"
                          @click="handleEditView(element)"
                        >
                          <i class="iconfont link-edit" />
                          {{ t("trade_order_rename") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="!element.defaultView"
                          class="colorRed"
                          @click.stop="
                            handleDeleteView(element.id, element.title)
                          "
                        >
                          <i class="iconfont link-ashbin colorRed" />
                          {{ t("trade_order_deleteViews") }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </n-tag>
            </div>
          </template>
        </draggable>
      </div>
      <draggable
        :list="orderViewList.msViewList"
        :disabled="false"
        item-key="msId"
        group="msViewGroup"
        class="ms-view-list-wrapper"
        ghost-class="ghost"
        chosen-class="chosen"
        animation="300"
        @start="dragging = true"
        @move="handleMsMoved"
        @end="handleMsViewDraggingEnd"
      >
        <template #item="{ element: msItem }">
          <div v-show="isShowMsView(msItem)" class="order-manage-col">
            <div class="order-manage-title">{{ msItem.msName }}</div>
            <draggable
              :list="msItem.workOrderViewList"
              item-key="groupName"
              :group="msItem.msId"
              class="order-manage-list-body"
              ghost-class="ghost"
              chosen-class="chosen"
              animation="300"
              :preventOnFilter="true"
              @start="dragging = true"
              @move="handleMoved"
              @end="handleOrderViewDraggingEnd"
            >
              <template #item="{ element }">
                <div
                  class="order-manage-draggable-col"
                  :class="{
                    'not-draggable': !enabled,
                    active: selectViewId === element.id,
                    'default-row': element.defaultView
                  }"
                  :data-draggable-name="element.name"
                >
                  <n-tag
                    :key="element.id"
                    class="item"
                    :class="{
                      'not-draggable': element.defaultView
                    }"
                    @click.stop="handleSwitchView(element)"
                  >
                    <div class="order-manage-draggable-col-left">
                      <i class="iconfont link-drag vis-hidden-icon" />
                      <i class="iconfont link-table font14 table-manage-icon" />
                      <ReText
                        type="info"
                        class="order-manage-draggable-col-title"
                        :tippyProps="{ delay: 50000 }"
                      >
                        {{ t(element.title) }}
                      </ReText>
                    </div>
                    <div class="order-manage-draggable-col-right">
                      <el-dropdown placement="bottom">
                        <i class="iconfont link-more" />
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item
                              v-if="!element.favorite"
                              @click.prevent="changeCollect(element)"
                            >
                              <i class="iconfont link-star" />
                              {{ t("trade_add_common") }}
                            </el-dropdown-item>
                            <el-dropdown-item
                              v-if="element.favorite"
                              @click.prevent="changeCollect(element)"
                            >
                              <i class="iconfont link-cancel-star" />
                              {{ t("trade_cancel_set_common") }}
                            </el-dropdown-item>
                            <el-dropdown-item
                              v-if="!element.defaultView"
                              @click="handleEditView(element)"
                            >
                              <i class="iconfont link-edit" />
                              {{ t("trade_order_rename") }}
                            </el-dropdown-item>
                            <el-dropdown-item
                              v-if="!element.defaultView"
                              class="colorRed"
                              @click.stop="
                                handleDeleteView(element.id, element.title)
                              "
                            >
                              <i class="iconfont link-ashbin colorRed" />
                              {{ t("trade_order_deleteViews") }}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </n-tag>
                </div>
              </template>
            </draggable>
          </div>
        </template>
      </draggable>
    </el-scrollbar>
  </div>
  <el-dropdown placement="top">
    <div class="order-manage-sccs-btn">
      <i class="iconfont link-setting" />
      {{ t("trade_sccs_moreDeal") }}
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="item in sccsRoleDropDownList"
          :key="item.dropdownText"
          @click="item.eventName"
        >
          <i class="iconfont font14" :class="item.iconfont" />
          {{ t(item.dropdownText) }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <SccsManageDrawer ref="SccsManageRef" />
  <SccsCoopManageDrawer ref="SccsCoopManageRef" />
  <LkDialog
    ref="orderViewRef"
    :title="t('trade_order_renameViews')"
    @confirm="handleRenameView"
  >
    <template #default>
      <div>{{ t("trade_common_addViewTip") }}</div>
      <el-input
        v-model="orderViewName"
        maxlength="20"
        cleariable
        show-word-limit
        placeholder="最多20字"
      />
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { markRaw, onMounted, ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import draggable from "vuedraggable";
import { ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { storageSession } from "@pureadmin/utils";
import { ReText } from "@/components/ReText";
import SccsManageDrawer from "@/views/sccsManage/index.vue";
import SccsCoopManageDrawer from "@/views/sccsCoopManage/index.vue";
import LkDialog from "@/components/lkDialog/index";
import { emitter } from "@/utils/mitt";
import {
  deleteOrderView,
  getOrderViewById,
  getUserOrderView,
  updateOrderView,
  updateOrderViewSort,
  updateTaskFavorite
} from "@/api/order";

const emit = defineEmits([
  "handleLoadedView",
  "handleClearTableSelectedRecords",
  "handleLoadedAnalysis"
]);

const { t } = useI18n();
const route = useRoute();
const orderViewList = ref<any>([]); //当前用户允许看到的全部视图列表
const selectedViewData = ref<any>([]); // 当前选中的视图

const selectViewId = ref<string>("");
const SccsManageRef = ref<any | HTMLElement>(null);
const SccsCoopManageRef = ref<any | HTMLElement>(null);
const orderViewRef = ref<any | HTMLElement>(null);
const orderViewName = ref<string>("");
const enabled = ref<boolean>(true);
const dragging = ref<boolean>(false);
const positionChanged = ref<boolean>(false); // 新增位置变化标志
const msPositionChanged = ref<boolean>(false); // 新增 msViewList 位置变化标志
const orderViewElement = ref<any>();
const sccsRoleDropDownList = ref<any[]>([]);
const analysisList = ref<any[]>([]); // 新增统计列表
const currentAnalysisPanelId = ref<string>(""); // 当前选中的统计面板id

const relationType = ref<any>("");
const currentMsViewId = ref<any>("");
const isShowAnalysisView = computed(() => {
  return analysisList.value.length > 0;
});
const isShowOrderView = computed(() => {
  if (relationType.value === "addMutuallyUseWorkOrder") {
    return false;
  } else {
    return true;
  }
});

const isShowMsView = computed(() => {
  return (msItem: any) => {
    if (relationType.value === "addMutuallyUseWorkOrder") {
      if (msItem.msId === currentMsViewId.value) {
        return true;
      } else {
        return false;
      }
    } else if (!relationType.value) {
      return true;
    } else {
      return false;
    }
  };
});

const handleOpenSccsSetting = (): void => {
  const { sccsId, coopTeamMark } = route.query;
  coopTeamMark == "2"
    ? SccsManageRef.value.open(sccsId)
    : SccsCoopManageRef.value.open(sccsId);
};

const handleMoved = () => {
  positionChanged.value = true; // 设置位置变化标志
};

const handleMsMoved = () => {
  msPositionChanged.value = true; // 设置 msViewList 位置变化标志
};

const handleOrderViewDraggingEnd = async () => {
  if (!positionChanged.value) return; // 如果没有发生位置变化，则不请求接口
  positionChanged.value = false; // 重置位置变化标志
  let orderViewIdList: string[] = [];
  let msViewOrderList = [];
  orderViewList.value.orderViewList.forEach(orderViewItem => {
    orderViewIdList.push(orderViewItem.id);
  });
  orderViewList.value.msViewList.forEach(msViewItem => {
    msViewOrderList.push({
      msId: msViewItem.msId,
      orderViewIdList: msViewItem.workOrderViewList.map(
        workOrderView => workOrderView.id
      )
    });
  });
  const { sccsId } = route.query;
  const { code } = await updateOrderViewSort({
    sccsId: sccsId,
    orderViewIdList: orderViewIdList,
    msViewIndexInfoList: msViewOrderList
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
  }
};

const handleMsViewDraggingEnd = async () => {
  if (!msPositionChanged.value) return; // 未发生位置变化则不处理
  msPositionChanged.value = false; // 重置标志

  // 获取 orderViewIdList
  const orderViewIdList = orderViewList.value.orderViewList.map(
    item => item.id
  );

  // 获取 msViewOrderList 和 msViewIdList
  const msViewOrderList = [];
  const msViewIdList: string[] = [];
  orderViewList.value.msViewList.forEach(msViewItem => {
    msViewIdList.push(msViewItem.msId);
    msViewOrderList.push({
      msId: msViewItem.msId,
      orderViewIdList: msViewItem.workOrderViewList.map(
        workOrderView => workOrderView.id
      )
    });
  });

  const { sccsId } = route.query;
  const { code } = await updateOrderViewSort({
    sccsId,
    orderViewIdList,
    msViewIndexInfoList: msViewOrderList,
    msViewIdList
  });

  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
  }
};

const handleObtainSccsDropDownList = () => {
  let sccsDropdownList = [];
  const userPermission = storageSession().getItem("userSccsPerm") as any;
  const coopTeamMark: string = storageSession().getItem("coopTeamMark");

  if (coopTeamMark === "mainTeam") {
    if (userPermission.includes("sccs_setting")) {
      sccsDropdownList.push({
        iconfont: "link-setting",
        dropdownText: "trade_sccs_sccsSetting",
        eventName: handleOpenSccsSetting
      });
    }
  } else {
    if (userPermission.includes("coop_sccs_setting")) {
      sccsDropdownList.push({
        iconfont: "link-setting",
        dropdownText: "trade_sccs_sccsSetting",
        eventName: handleOpenSccsSetting
      });
    }
  }
  if (userPermission.includes(["resource_manage"])) {
    sccsDropdownList.push({
      iconfont: "link-project-resource",
      dropdownText: "trade_common_AssociatedResourceLibrary",
      eventName: null
    });
  }
  sccsDropdownList.push({
    iconfont: "link-automation",
    dropdownText: "trade_common_automation",
    eventName: null
  });
  sccsRoleDropDownList.value = sccsDropdownList;
};

const handleEditView = async (element: any) => {
  orderViewRef.value.open();
  orderViewName.value = element.title;
  orderViewElement.value = element;
};

const handleDeleteView = (id: string, title: string) => {
  const assistantLangText = `${t("trade_order_deleteViewTips")}「${title}」？`;
  ElMessageBox.confirm(assistantLangText, t("trade_order_deleteViews"), {
    confirmButtonText: t("trade_common_confirm"),
    cancelButtonText: t("trade_common_cancel"),
    confirmButtonClass: "confrim-message-btn-class",
    cancelButtonClass: "cancel-message-btn-class",
    type: "error",
    icon: markRaw(WarningFilled),
    center: true
  }).then(async () => {
    const { code } = await deleteOrderView({ id: id });
    if (code === 0) {
      ElMessage({
        message: t("trade_common_updateSuccess"),
        type: "success"
      });
      handleGetUserOrderView();
      emitter.emit("handleReloadOrderPageData");
    }
  });
};

const handleRenameView = async () => {
  selectViewId.value = orderViewElement.value.id;
  const { data } = await getOrderViewById({
    sccsId: route.query.sccsId as string,
    orderListViewId: orderViewElement.value.id
  });
  const { code } = await updateOrderView({
    ...data,
    title: orderViewName.value
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    orderViewRef.value.close();
    handleGetUserOrderView();
    emitter.emit("handleReloadOrderPageData");
  }
};

const handleSwitchView = async (element: any) => {
  selectViewId.value = element.id;
  currentAnalysisPanelId.value = ""; // 清除统计面板的高亮
  emit("handleClearTableSelectedRecords");
  emit("handleLoadedView", orderViewList.value, element);
};

const handleSwitchAnalysis = (element: any) => {
  selectViewId.value = ""; // 清除订单视图的高亮
  currentAnalysisPanelId.value = element.id; // 设置统计面板的高亮
  emit("handleClearTableSelectedRecords");
  emit("handleLoadedAnalysis", element.panelUrl, element.id);
};

const changeCollect = async (element: any) => {
  const { id, favorite } = element;
  const { code } = await updateTaskFavorite({
    favoriteType: "ORDER_VIEW", // 收藏类型
    sourceId: id, // 收藏对象id
    favorite: !favorite // 是否收藏
  });
  if (code === 0) {
    ElMessage({
      message: !favorite
        ? t("trade_collect_success_tip")
        : t("trade_common_updateSuccess"),
      type: "success"
    });
    handleGetUserOrderView("noUploadTableData");
  }
};

const handleGetUserOrderView = async (flag?: string) => {
  const sccsId = route.query.sccsId as string;
  const { code, data } = await getUserOrderView({
    sccsId: sccsId
  });
  if (code === 0) {
    const orderViewLists = [];
    data.orderViewList.forEach(orderViewItem => {
      orderViewLists.push(orderViewItem);
    });
    data.msViewList.forEach(msViewItem => {
      msViewItem.workOrderViewList.forEach(workOrderView => {
        orderViewLists.push(workOrderView);
      });
    });
    // 赋值 analysisList
    analysisList.value = data.analysis || [];
    // 默认选中最新的视图
    let orderView = null;
    orderViewList.value = data;
    if (flag === "noUploadTableData") {
      return;
    }
    if (!route.query.type) {
      if (route.query.viewType && route.query.viewId) {
        orderView = orderViewLists.find(
          orderView => route.query.viewId === orderView.id
        );
      } else {
        orderView = orderViewLists.find(orderView => orderView.latest);
      }
    } else {
      relationType.value = route.query.type;
      // 1、需要增加判断是否是通过详情增加互用跳转
      if (route.query.type === "addMutuallyUseWorkOrder") {
        currentMsViewId.value = route.query.msId;
        // 添加互用工单
        orderView = orderViewLists.find(
          orderView =>
            orderView.msId === route.query.msId && orderView.defaultView
        );
      } else {
        orderView = orderViewLists.find(orderView => orderView.defaultView);
      }
    }
    // 2、如是则需要根据类型判断是选中订单列表视图还是里程碑视图
    // 3、如果不是则默认选中最新的视图
    selectedViewData.value = orderView;
    selectViewId.value = orderView.id;

    emit("handleLoadedView", data, selectedViewData.value);
  }
};

onMounted(() => {
  handleGetUserOrderView();
});

defineExpose({
  handleGetUserOrderView,
  handleObtainSccsDropDownList
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.ms-view-list-wrapper {
  .order-manage-title {
    cursor: grab;
  }

  .order-manage-col {
    margin-bottom: 16px;
  }

  .ghost {
    opacity: 0.5;
  }

  .chosen {
    cursor: grabbing !important;
  }
}
</style>
