<template>
  <div
    v-loading="loading"
    :class="['lk-avatar-upload-body', shape === 'circle' ? 'circle' : '']"
    @mouseenter="handleMouseOver(true)"
    @mouseleave="handleMouseOver(false)"
  >
    <el-upload
      class="avatar-uploader"
      :action="requireURL"
      :show-file-list="false"
      :data="requireParams"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      accept=".bmp,.gif,.jpeg,.jpg,.pjpeg,.png,.tiff,.webp"
    >
      <div class="avatar-uploader-main">
        <el-avatar
          :shape="shape"
          :size="58"
          fit="cover"
          :class="[
            ossFileType === 'TRADE_TEAM_AVATAR'
              ? 'colorOrange avatarFont16'
              : ''
          ]"
          :src="teamInfo.teamAvatar"
        >
          {{ teamInfo.teamName?.substring(0, 1) }}
        </el-avatar>
        <span class="avatar-picture-body">
          <i class="iconfont link-edit-picture" />
        </span>
      </div>
      <el-tooltip
        v-if="teamInfo.teamAvatar"
        effect="dark"
        :content="t('trade_reset_avatar')"
        placement="top"
        :show-after="500"
      >
        <div class="avatar-uploader-mask" @click.stop="handleResetAvatar">
          <i class="iconfont link-restore-default" />
        </div>
      </el-tooltip>
    </el-upload>
    <div
      v-if="avatarUploadHover"
      class="lk-avatar-upload-container"
      @click="handleResetAvatar"
    >
      <el-tooltip
        effect="dark"
        :content="t('trade_reset_avatar')"
        placement="top"
        :show-after="500"
      >
        <i class="iconfont link-restore-default" />
      </el-tooltip>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { type PropType, ref } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import type { UploadProps } from "element-plus";
import LkAvatar from "@/components/lkAvatar/index";
import { updateTeamAvatar, updateUserAvatar } from "@/api/team";

interface TeamInfo {
  id?: string;
  teamAvatar: string;
  teamName: string;
}

interface ossFileTypeProp {
  ossFileType: string;
}

const props = defineProps({
  teamInfo: {
    type: Object as PropType<TeamInfo>
  },
  ossFileType: {
    type: String as PropType<string>,
    default: () => "TRADE_TEAM_AVATAR"
  },
  shape: {
    type: String as PropType<any>,
    default: () => "square"
  }
});

const emit = defineEmits<{
  (e: "onSuccessUpdateAvatar", data: any): void;
}>();

const { t } = useI18n();
const loading = ref<boolean>(false);
const avatarUploadHover = ref<boolean>(false);
const requireURL = ref<string>(
  import.meta.env.VITE_BASE_URL +
    import.meta.env.VITE_API_TRADE_PREFIX +
    "/trade/file/upload"
);
const requireParams = ref<ossFileTypeProp>({
  ossFileType: props.ossFileType
});

const handleAvatarSuccess: UploadProps["onSuccess"] = uploadFile => {
  loading.value = false;
  updateTeamAvatarFn(uploadFile.data);
};

const beforeAvatarUpload: UploadProps["beforeUpload"] = rawFile => {
  loading.value = true;
  const validImageFormats = [
    "image/bmp",
    "image/gif",
    "image/jpeg",
    "image/pjpeg",
    "image/png",
    "image/tiff",
    "image/webp"
  ];
  if (!validImageFormats.includes(rawFile.type)) {
    ElMessage.error(t("trade_common_imageNotRequired"));
    return false;
  }
  if (rawFile.size / 1024 / 1024 > 20) {
    ElMessage.error("Avatar picture size can not exceed 20MB!");
    return false;
  }
  return true;
};

const updateTeamAvatarFn = async (teamAvatar: string): Promise<void> => {
  const res =
    props.ossFileType === "TRADE_TEAM_AVATAR"
      ? await updateTeamAvatar({
          id: props.teamInfo.id,
          teamAvatar: teamAvatar
        })
      : await updateUserAvatar({ avatar: teamAvatar });
  if (res.code === 0) {
    emit("onSuccessUpdateAvatar", res);
  }
};

const handleResetAvatar = () => {
  avatarUploadHover.value = false;
  updateTeamAvatarFn("");
};

const handleMouseOver = (bool: boolean) => {
  if (props.teamInfo.teamAvatar) {
    avatarUploadHover.value = bool;
  }
};
</script>
<style lang="scss" scoped>
.lk-avatar-upload-body {
  position: relative;

  &.circle {
    .lk-avatar-upload-container {
      border-radius: 50%;
    }
  }

  .avatar-uploader {
    position: relative;

    .avatar-uploader-main {
      .avatar-picture-body {
        position: absolute;
        right: 3px;
        bottom: -2px;
        z-index: 1991;
        display: inline-block;
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        background: #fa8d0a;
        border: 1px solid #fff;
        border-radius: 50%;

        .iconfont {
          font-size: 9px;
          color: #fff;
          vertical-align: top;
        }
      }

      .el-avatar {
        margin: 0 !important;
      }
    }

    .avatar-uploader-mask {
      position: absolute;
      z-index: 1890;
      display: none;
      align-content: center;
      width: 100%;
      height: 100%;
      text-align: center;
      background: #000;
      border-radius: 50%;
      opacity: 0.5;
      transition: all 2s ease;

      .iconfont {
        font-size: 14px;
        color: #fff;
      }
    }

    &:hover {
      .avatar-uploader-mask {
        display: block;
        transition: all 2s ease;
      }
    }

    ::v-deep(.lk-avater-container) {
      .el-avatar {
        margin: 0;
      }
    }
  }

  .lk-avatar-upload-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    background: #63636380;

    .iconfont {
      font-size: 10px;
      line-height: 58px;
      color: #fff;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
