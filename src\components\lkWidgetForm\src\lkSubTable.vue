<template>
  <ListTable
    ref="tableInstanceRef"
    class="lk-grid-table"
    :options="option"
    :style="{
      height: `${tableHeight}px`,
      'z-index': 1000,
      'max-height': '700px',
      ...style
    }"
  />
  <TableStatistics
    v-if="!!tableStatisticsFlag"
    ref="TableStatisticsRef"
    :column="tableStatisticsColumns"
    :statisticsResultList="[]"
    :presentView="presentView"
    :selectedRow="tableRecordsRef"
    :isOperate="false"
    :colStatisticsSearchCondition="{}"
    :isShowTooltip="false"
    :customScrollStyle="{
      hoverOn: true,
      barToSide: true
    }"
    @handleScrollChange="handleScrollChange"
  />
  <el-dialog
    v-model="openDialogVisible"
    class="lk-maximum-dialog"
    :class="{
      'lk-sub-table-dialog': true
    }"
    append-to-body
    align-center
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <LKSubTable
      ref="subTableRef"
      :widgetConfig="subTableConfig.widgetConfig"
      :widgetData="subTableConfig.widgetData"
      :presentView="props.presentView"
      :tableStatisticsFlag="true"
      tableFirstType=""
    />
  </el-dialog>
</template>
<script lang="ts" setup>
import {
  computed,
  h,
  markRaw,
  nextTick,
  onMounted,
  ref,
  watch,
  getCurrentInstance
} from "vue";
import { useI18n } from "vue-i18n";
import { ElRate } from "element-plus";
import { VTable } from "@visactor/vue-vtable";
import { storageLocal, storageSession } from "@pureadmin/utils";
import TableStatistics from "@/components/lkGridTable/components/TableStatistics/index.vue";
import TagColumn from "@/components/lkGridTable/components/TagColumn";
import AvatarGroup from "@/components/lkGridTable/components/AvatarGroup";
import FileColumn from "@/components/lkGridTable/components/FileColumns";
import ImageListColumn from "@/components/lkGridTable/components/ImageListColumn";
import LkHeaderColumn from "@/components/lkHeaderColumn/index";
import LKSubTable from "@/components/lkWidgetForm/src/lkSubTable.vue";

const props = defineProps({
  widgetConfig: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetDataRow: {
    type: Object as PropType<any>,
    default: () => {}
  },
  tableFirstType: {
    type: String as PropType<string>,
    default: "rowSeriesNumber"
  },
  tableStatisticsFlag: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  presentView: {
    type: Object as PropType<any>
  },
  modeRadioValue: {
    type: Number as PropType<number>,
    default: 0
  },
  style: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const getTitleByLang = colunm =>
  storageLocal().getItem("translationLang") === "en"
    ? colunm?.props?.titleEn || colunm?.title
    : colunm?.title;

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const tableInstanceRef = ref<HTMLElement | any>(null);
const columns = ref<any>([]);
const tableStatisticsColumns = ref<any[]>([]);
const tableHeight = ref<number>(0);
const openDialogVisible = ref<boolean>(false);
const subTableConfig = ref<any>({});
const option = markRaw({
  records: [],
  columns: [],
  defaultHeaderRowHeight: 36,
  defaultRowHeight: 50,
  defaultHeaderColWidth: [60, "auto"],
  autoFillWidth: true,
  columnWidthComputeMode: "only-body",
  hover: {
    highlightMode: "row",
    disableHover: true
  },
  select: {
    highlightMode: "row",
    disableSelect: true
  },
  customConfig: {
    createReactContainer: true
  },
  resize: {
    columnResizeMode: "header"
  },
  tooltip: {
    isShowOverflowTextTooltip: true,
    overflowTextTooltipDisappearDelay: 500
  },
  emptyTip: {
    text: t("trade_common_emptyTip"),
    textStyle: {
      fontSize: 12,
      color: "#797979",
      fontWeight: "bold"
    },
    icon: {
      width: 0,
      height: 0
    }
  },
  theme: {
    headerStyle: {
      color: "#797979",
      fontSize: "13",
      fontWeight: "bolder",
      textAlign: "center",
      bgColor: "#F7F7F7",
      borderColor: "#e8e8e8"
    },
    bodyStyle: {
      color: "#262626",
      fontSize: "13",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    scrollStyle: {
      visible: props.tableStatisticsFlag ? "none" : "always",
      hoverOn: true,
      width: 10,
      barToSide: true,
      scrollRailColor: "#fbfbfb",
      scrollSliderColor: "#d3d3d3"
    },
    buttonStyle: {
      buttonColor: "transparent",
      buttonBorderColor: "transparent",
      buttonHoverColor: "transparent",
      buttonHoverBorderColor: "transparent"
    }
  }
});

// 子表数据,提供给统计组件使用,统计组件只有设置了selectedRow,才会进行本地统计
const tableRecordsRef = ref<any[]>([]);

const tableInstance: any = computed(() => {
  return tableInstanceRef.value.vTableInstance;
});

/**
 * 统计表格排序执行逻辑，同步数据表格滚动条
 * @param e
 */
const handleScrollChange = e => {
  tableInstance.value.setScrollLeft(e.scrollLeft);
};

/**
 * 视图发生变化需要同步统计列宽度
 */
const handleAssembleViewData = () => {
  const tableColumns = tableInstance.value.getAllColumnHeaderCells()[0];
  tableStatisticsColumns.value = columns.value.map((column, index) => {
    const childColumn = tableColumns.find(
      childColumn => childColumn.field === column.field
    );
    if (childColumn) {
      return {
        name: column.field,
        label: getTitleByLang(column),
        show: true,
        width: tableInstance.value.getColWidth(childColumn.col),
        type: "ORDER_WIDGET",
        widgetType: column.type
      };
    } else {
      return {
        name: column.field,
        label: getTitleByLang(column),
        show: true,
        width: tableInstance.value.getColWidth(index + 1),
        type: "ORDER_WIDGET",
        widgetType: column.type
      };
    }
  });
};

const handleReloadVTableData = (records: any) => {
  let tableRecords = [];
  for (let tableRow of records) {
    let tableRecordObject = {};
    for (let column of columns.value) {
      if (column.cellType === "radio") {
        continue;
      }

      const tableColData =
        tableRow instanceof Array
          ? tableRow.find(tableCol => tableCol.widgetId === column._fc_id)
          : tableRow[column._fc_id];

      let tableValue = "";
      if (tableColData) {
        if (["DrRadio", "DrCheckbox"].includes(column.type)) {
          tableValue = tableColData.obj;
        } else if (
          [
            "DrSCCSMemberSelect",
            "DrSCCSGroupMemberSelect",
            "DrSCCSMemberSingleSelect",
            "DrSCCSGroupMemberSingleSelect",
            "DrSCCSMemberMultipleSelect",
            "DrSCCSGroupMemberMultipleSelect"
          ].includes(column.type)
        ) {
          tableValue = tableColData.obj
            ? typeof tableColData.obj === "string"
              ? tableColData.obj.split(",")
              : tableColData.obj
            : [];
        } else if (
          ["DrImagesUpload", "DrFilesUpload", "DrSignature"].includes(
            column.type
          )
        ) {
          tableValue = JSON.stringify(tableColData.obj);
        } else if (column.type === "DrExchangeRates") {
          const {
            exchangeType,
            rate,
            fromMoney,
            fromMoneyType,
            targetMoney,
            targetMoneyType
          } = tableColData.obj;
          const exchangeTypeText =
            exchangeType === "fixed"
              ? t("trade_common_fixedExchangeRate")
              : t("trade_common_realTimeExchangeRate");
          tableValue = fromMoney
            ? `${exchangeTypeText}  ${rate}  ${fromMoney}${fromMoneyType}~${targetMoney}${targetMoneyType}`
            : "";
        } else if (column.type === "DrTableForm") {
          tableValue = tableColData;
        } else {
          tableValue = tableColData.label ? tableColData.label : "";
        }
      }

      tableRecordObject[column._fc_id] = tableValue;
    }
    tableRecords.push(tableRecordObject);
  }
  tableRecordsRef.value = tableRecords;
  tableHeight.value =
    tableRecords.length !== 0 ? tableRecords.length * 50 + 42 : 96;

  tableInstance.value.setRecords(tableRecords);
};

const renderCustomLayout = (childColumn: any) => {
  let customLayoutTemplate = {};

  if (["DrRadio", "DrCheckbox"].includes(childColumn.type)) {
    customLayoutTemplate = {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);
        let tagList = [];
        if (value) {
          if (value.indexOf(",") > -1) {
            for (let tagItem of value.split(",")) {
              const optionItem = childColumn.props.options.find(
                childOption => childOption.value === tagItem
              );
              if (optionItem) {
                tagList.push({
                  content: optionItem.label,
                  bgValue: optionItem.color
                });
              }
            }
          } else {
            const optionItem = childColumn.props.options.find(
              childOption => childOption.value === value
            );
            if (optionItem) {
              tagList.push({
                content: optionItem.label,
                bgValue: optionItem.color
              });
            }
          }
        }
        let container;
        if (tagList instanceof Array && tagList.length > 0) {
          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "center",
            alignItems: "center",
            //@ts-ignore
            vue: {
              element: h(TagColumn, {
                tagList: tagList,
                width: width,
                style: {
                  pointerEvents: "auto"
                }
              }),
              container: table.bodyDomContainer
            }
          });
        }
        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (
    [
      "DrSCCSMemberSelect",
      "DrSCCSGroupMemberSelect",
      "DrSCCSMemberSingleSelect",
      "DrSCCSGroupMemberSingleSelect",
      "DrSCCSMemberMultipleSelect",
      "DrSCCSGroupMemberMultipleSelect"
    ].includes(childColumn.type)
  ) {
    customLayoutTemplate = {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);

        let container;
        const widgetData: any = storageSession().getItem("widgetData");

        const dataList = [
          "DrSCCSMemberSelect",
          "DrSCCSMemberSingleSelect",
          "DrSCCSMemberMultipleSelect"
        ].includes(childColumn.type)
          ? widgetData?.sccsMemberList
          : widgetData?.sccsCoopTeamList;

        if (value) {
          let userInfoList;
          let dataValueList;
          if (
            [
              "DrSCCSMemberSelect",
              "DrSCCSMemberSingleSelect",
              "DrSCCSMemberMultipleSelect"
            ].includes(childColumn.type)
          ) {
            dataValueList = dataList.filter(dataItem =>
              value.split(",").includes(dataItem.userId)
            );

            userInfoList = dataValueList.map(teamMember => {
              return {
                activate: teamMember.activate,
                user: true,
                id: teamMember.userId,
                name: teamMember.username,
                username: teamMember.username,
                avatar: teamMember.avatar,
                edited: false,
                coopUser: false,
                teamName: teamMember.teamName,
                shortName: teamMember.teamShortName,
                email: teamMember.account
              };
            });
          } else {
            dataValueList = dataList.filter(dataItem =>
              value.split(",").includes(dataItem.teamId)
            );

            userInfoList = dataValueList.map(team => {
              return {
                activate: true,
                user: false,
                teamId: team.teamId,
                name: team.teamName,
                avatar: team.teamAvatar,
                edited: false,
                coopTeamUser: true,
                shortName: team.teamShortName,
                email: team.teamShortName
              };
            });
          }

          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            //@ts-ignore
            vue: {
              element: h(AvatarGroup, {
                avatarListGroup: userInfoList,
                size: 26
              }),
              container: table.bodyDomContainer
            }
          });
        }

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (childColumn.type === "DrFilesUpload") {
    customLayoutTemplate = {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);

        let container;
        if (value) {
          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            //@ts-ignore
            vue: {
              element: h(FileColumn, {
                filePreviewList: JSON.parse(value),
                fileList: JSON.parse(value)
              }),
              container: table.bodyDomContainer
            }
          });
        }

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (["DrImagesUpload", "DrSignature"].includes(childColumn.type)) {
    customLayoutTemplate = {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);
        let container;

        if (!!value && value !== '""') {
          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            //@ts-ignore
            vue: {
              element: h(ImageListColumn, {
                imageList:
                  JSON.parse(value) instanceof Array
                    ? JSON.parse(value)
                    : [{ url: JSON.parse(value) }],
                size: { width: width, height: height }
              }),
              container: table.bodyDomContainer
            }
          });
        }

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  } else if (childColumn.type === "DrRate") {
    customLayoutTemplate = {
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);
        let container;
        if (value) {
          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            //@ts-ignore
            vue: {
              element: h(ElRate, {
                modelValue: JSON.parse(value),
                max: JSON.parse(value)
              }),
              container: table.bodyDomContainer
            }
          });
        }

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    };
  }
  return customLayoutTemplate;
};

const getRadioCheckedRow = () => {
  const index = tableInstance.value.getRadioState("isCheck");
  return props.widgetData[index];
};

const getCheckboxCheckedRow = () => {
  const checkboxList = tableInstance.value.getCheckboxState("isCheckbox");
  const result = [];
  for (let i = 0; i < checkboxList.length; i++) {
    if (checkboxList[i] === true) {
      result.push(i);
    }
  }
  return result.map(u => props.widgetData[u]);
};

watch(
  [() => props.widgetConfig, () => props.modeRadioValue],
  () => {
    let columnList: any[] = [];
    let rowSeriesNumberObject = {};
    if (props.tableFirstType === "rowRadio") {
      columnList.push({
        field: "isCheck",
        title: "",
        width: 50,
        cellType: "radio",
        checked: args => {
          if (args.row === props.modeRadioValue + 1) return true;
        }
      });
    } else if (props.tableFirstType === "rowCheckbox") {
      columnList.push({
        field: "isCheckbox",
        title: "",
        width: 60,
        headerType: "checkbox",
        cellType: "checkbox",
        frozen: "left" // 固定第一列
      });
    } else {
      rowSeriesNumberObject = {
        rowSeriesNumber: {
          title: t("trade_common_SerialNumber"),
          width: 60,
          style: { padding: [24, 12] }
        }
      };
    }

    const tableColumnList = props.widgetConfig.children.slice(1).map(column => {
      const childColumn = column.children[0];
      let customLayoutTemplate = {};
      if (
        [
          "DrRadio",
          "DrCheckbox",
          "DrSCCSMemberSelect",
          "DrSCCSGroupMemberSelect",
          "DrSCCSMemberSingleSelect",
          "DrSCCSGroupMemberSingleSelect",
          "DrSCCSMemberMultipleSelect",
          "DrSCCSGroupMemberMultipleSelect",
          "DrImagesUpload",
          "DrFilesUpload",
          "DrAddress",
          "DrEditor",
          "DrRate",
          "DrSignature"
        ].includes(childColumn.type)
      ) {
        customLayoutTemplate = renderCustomLayout(childColumn);
      }

      let columnConfig = {};
      if (["DrEditor", "DrTableForm"].includes(column.children[0].type)) {
        columnConfig = {
          cellType(args) {
            return args.value ? "button" : "text";
          },
          text: "查看",
          style: {
            color: "#2082ED"
          }
        };
      }

      return Object.assign(
        {
          $required: childColumn.$required,
          display: childColumn.display,
          field: childColumn._fc_id,
          props: childColumn.props,
          width: 160,
          title: getTitleByLang(childColumn),
          type: childColumn.type,
          _fc_id: childColumn._fc_id,
          fieldFormat: (record: any) => {
            if (
              childColumn.type === "DrPercentage" &&
              record[childColumn._fc_id]
            ) {
              return `${record[childColumn._fc_id]}%`;
            } else {
              return `${record[childColumn._fc_id]}`;
            }
          },
          headerCustomLayout: args => {
            const { table, row, col, rect } = args;
            const { height, width } = rect ?? table.getCellRect(col, row);

            let container;
            container = new VTable.CustomLayout.Group({
              height,
              width,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              //@ts-ignore
              vue: {
                element: h(LkHeaderColumn, {
                  column: column.children[0],
                  width: width
                }),
                container: table.headerDomContainer
              }
            });

            return {
              rootContainer: container,
              renderDefault: false
            };
          }
        },
        customLayoutTemplate,
        { ...columnConfig }
      );
    });

    columns.value = columnList.concat(tableColumnList);

    nextTick(() => {
      tableInstance.value.updateOption(
        Object.assign(option, rowSeriesNumberObject, {
          columns: columns.value
        })
      );
      nextTick(() => {
        handleAssembleViewData();
      });
    });
  },
  {
    immediate: true
  }
);

watch(
  () => props.widgetData,
  () => {
    const tableData = props.widgetData || [];
    nextTick(() => {
      handleReloadVTableData(tableData);
    });
  },
  {
    deep: true,
    immediate: true
  }
);

onMounted(() => {
  // 挂载子表点击事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.BUTTON_CLICK, async e => {
    const tableRow = tableInstance.value.getCellOriginRecord(e.col, e.row);
    const fieldName = tableInstance.value.getCellHeaderPaths(e.col, e.row)
      .colHeaderPaths[0].field;

    const widget = columns.value.find(column => column._fc_id === fieldName);

    if (widget.type === "DrTableForm") {
      const widgetConfig = props.widgetConfig.children
        .slice(1)[0]
        .children.find(column => column._fc_id === fieldName);
      subTableConfig.value = {
        widgetConfig: widgetConfig,
        widgetData: tableRow[fieldName]
      };
      openDialogVisible.value = true;
    } else {
      const valueHtml = tableRow[fieldName];
      //@ts-ignore
      await proxy.$dialogPreview.openPreivew({
        editorValueHtml: valueHtml
      });
    }
  });

  // 列宽调整事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.RESIZE_COLUMN, e => {
    handleAssembleViewData();
  });
});

defineExpose({
  tableInstance,
  getRadioCheckedRow,
  getCheckboxCheckedRow
});
</script>
<style lang="scss">
.lk-grid-table {
  .vtable {
    overflow: inherit !important;
  }
}
</style>
