<template>
  <div id="sidebar-bottom-link-news" class="sidebar-bottom-container">
    <div class="sidebar-bottom-item">
      <div class="sidebar-bottom-col" @click="handleOpenMessageCenter">
        <el-badge
          :value="messageCenterStore.totalUnreadCount"
          :max="99"
          :show-zero="false"
          :offset="[0, 3]"
          :badge-style="{
            padding: '0 4px',
            // width: '18px',
            'font-size': '10px'
          }"
        >
          <FontIcon icon="link-news" />
        </el-badge>
      </div>
    </div>
    <div class="sidebar-bottom-item">
      <div class="sidebar-bottom-col" @click="handleOpenHelp">
        <FontIcon icon="link-help-center" />
      </div>
    </div>
    <div class="sidebar-bottom-item">
      <div class="sidebar-bottom-col">
        <el-popover
          ref="popoverRef"
          placement="right"
          :width="274"
          :height="235"
          trigger="click"
          :show-arrow="false"
          popper-class="popper-user-name"
        >
          <template #reference>
            <LkNoPopoverAvatar
              ref="LKAvaterNormalRef"
              :size="32"
              class="avatarfont14"
            />
          </template>
          <div class="user-sidebar-body">
            <div class="user-sidebar-top">
              <div class="user-sidebar-avater">
                <LkNoPopoverAvatar
                  ref="LKAvaterRef"
                  :size="58"
                  class="avatarfont16"
                />
              </div>
              <div class="user-sidebar-bottom">
                <div class="user-sidebar-username">{{ userInfo.username }}</div>
                <div class="user-sidebar-useremail">
                  {{ userInfo.email }}
                </div>
              </div>
            </div>
            <div class="user-sidebar-container-bottom">
              <div class="user-popper-container">
                <div class="user-popper-row" @click="openUserInfoDrawer">
                  <FontIcon icon="link-per-center" />
                  <span>{{ t("trade_common_personalCentre") }}</span>
                </div>
                <el-popover
                  ref="popoverLanguageRef"
                  placement="right"
                  width="auto"
                  :height="120"
                  trigger="hover"
                  :show-arrow="false"
                  :offset="3"
                  popper-class="popper-user-child"
                >
                  <template #reference>
                    <div class="user-popper-row">
                      <FontIcon icon="link-system-language" />
                      <span>{{ t("trade_common_systemLanguage") }}</span>
                      <FontIcon icon="link-arrow-right" />
                    </div>
                  </template>
                  <ul class="user-language-body">
                    <li
                      :class="[
                        'user-language',
                        translationLang === 'system' ? 'is-active' : ''
                      ]"
                      @click="handleTranslationLang('system')"
                    >
                      <span>{{ t("trade_common_followSystem") }}</span>
                      <FontIcon icon="link-tick" />
                    </li>
                    <li
                      :class="[
                        'user-language',
                        translationLang === 'zh' ? 'is-active' : ''
                      ]"
                      @click="handleTranslationLang('zh')"
                    >
                      <span>简体中文</span>
                      <FontIcon icon="link-tick" />
                    </li>
                    <li
                      :class="[
                        'user-language',
                        translationLang === 'en' ? 'is-active' : ''
                      ]"
                      @click="handleTranslationLang('en')"
                    >
                      <span>English</span>
                      <FontIcon icon="link-tick" />
                    </li>
                  </ul>
                </el-popover>
                <div class="user-popper-row" @click="handleLogout()">
                  <FontIcon icon="link-exit" />
                  <span>{{ t("trade_common_logout") }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
    <UserInfoDialog ref="userInfoDialogRef" />
    <MessageCenter ref="MessageCenterRef" />
  </div>
</template>
<script setup lang="ts">
import { markRaw, onMounted, ref, watch, computed } from "vue";
import LkNoPopoverAvatar from "@/components/lkNoPopoverAvatar/index";
import { useNav } from "@/layout/hooks/useNav";
import UserInfoDialog from "@/views/userInfo/index.vue";
import MessageCenter from "@/views/messageCenter/index.vue";
import { storageLocal } from "@pureadmin/utils";
import { useI18n } from "vue-i18n";
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import { ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { emitter } from "@/utils/mitt";
import { updateUserLanguage } from "@/api/common";
import { useMessageCenterStoreHook } from "@/store/modules/messageCenter";

defineProps({
  collapse: Boolean
});

const { t } = useI18n();
let userInfo = ref<any>({});
const userInfoDialogRef = ref<any>(null);
const MessageCenterRef = ref<any>(null);
const popoverRef = ref<any>(null);
const popoverLanguageRef = ref<any>(null);
const translationLang = ref<string>("");
const LKAvaterRef = ref<HTMLElement | any>(null);
const LKAvaterNormalRef = ref<HTMLElement | any>(null);

// 使用消息中心 store 获取未读数量
const messageCenterStore = useMessageCenterStoreHook();
const { initializeMessageCenter } = messageCenterStore;

const openUserInfoDrawer = () => {
  popoverRef.value.hide();
  userInfoDialogRef.value.open();
};

const { logout } = useNav();
const { translationCh, translationEn } = useTranslationLang();

const handleLogout = () => {
  ElMessageBox.confirm(t("trade_login_exitSystem"), t("trade_logou_tip"), {
    confirmButtonText: t("trade_common_confirm"),
    cancelButtonText: t("trade_common_cancel"),
    confirmButtonClass: "confrim-message-btn-warn-class",
    customClass: "order_confirm_message_box",
    type: "warning",
    icon: markRaw(WarningFilled),
    center: true
  })
    .then(() => {
      logout();
    })
    .catch(() => {});
};

const handleOpenHelp = (): void => {
  window.open("https://help.linkincrease.com/zh/");
};

const handleTranslationLang = (str: string) => {
  translationLang.value = str;
  if (str === "system") {
    navigator.language.indexOf("zh") > -1 ? translationCh() : translationEn();
  } else {
    str.indexOf("zh") > -1 ? translationCh() : translationEn();
  }
  storageLocal().setItem("translationLang", str);

  const languageStr =
    str === "system"
      ? navigator.language.indexOf("zh") > -1
        ? "zh"
        : "en"
      : str;
  updateUserLanguage({ lan: languageStr });

  popoverLanguageRef.value.hide();
  popoverRef.value.hide();

  window.location.reload();
};

const handleOpenMessageCenter = (): void => {
  MessageCenterRef.value.toggle();
};

onMounted(() => {
  userInfo.value = storageLocal().getItem("user-info");
  translationLang.value = storageLocal().getItem("translationLang");

  // 初始化消息中心数据
  initializeMessageCenter();

  if (translationLang.value === "system") {
    navigator.language.indexOf("zh") > -1 ? translationCh() : translationEn();
  } else {
    translationLang.value.indexOf("zh") > -1
      ? translationCh()
      : translationEn();
  }
  emitter.on("updateUserInfoAvatar", userInfo => {
    LKAvaterRef.value.reloadAvatar();
    LKAvaterNormalRef.value.reloadAvatar();
  });
  emitter.on("updateUserInfo", () => {
    userInfo.value = storageLocal().getItem("user-info");
    LKAvaterRef.value.reloadAvatar();
    LKAvaterNormalRef.value.reloadAvatar();
  });
});
</script>
<style lang="scss" scoped>
.sidebar-bottom-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 146px;
  margin-bottom: 12px;

  .sidebar-bottom-item {
    flex: 1;
    width: 100%;

    .sidebar-bottom-col {
      align-items: center;
      width: 32px;
      height: 32px;
      margin: 0 auto;
      text-align: center;
      cursor: pointer;

      .iconfont {
        font-size: 18px;
        color: #8c8c8c;

        &:hover {
          color: #0070d2;
        }
      }
    }
  }
}

.popper-user-name {
  .user-sidebar-body {
    border-radius: 8px;

    .user-sidebar-top {
      display: flex;
      align-items: center;
      width: 100%;
      height: 103px;
      padding: 21px 26px 24px 14px;
      background: linear-gradient(125deg, #007be6 16%, #a693ff 84%);
      border-radius: 8px 8px 0 0;

      .user-sidebar-avater {
        width: 67px;

        ::v-deep(.lk-avater-container) {
          .el-avatar {
            border: 1px solid #fff;
          }
        }
      }

      .user-sidebar-bottom {
        flex: 1;
        align-items: center;
        max-width: 190px;
        color: #fff;

        .user-sidebar-username {
          overflow: hidden;
          font-size: 20px;
          font-weight: bolder;
          line-height: 28px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .user-sidebar-useremail {
          overflow: hidden;
          font-size: 14px;
          line-height: 20px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .user-sidebar-container-bottom {
      .user-popper-container {
        .user-popper-row {
          display: flex;
          align-items: center;
          height: 40px;
          padding: 0 21px 0 24px;
          line-height: 40px;
          cursor: pointer;

          .iconfont {
            margin-right: 6px;
            font-size: 13px;
            color: #b8b7b7;
          }

          .popper-right-icon {
            text-align: right;
          }

          span {
            flex: 1;
            align-items: center;
            font-size: 14px;
            line-height: 20px;
            color: #262626;
            text-indent: 3px;
          }

          &:hover {
            background: #f2f2f2;
          }

          &:last-child {
            .iconfont {
              color: #e62412;
            }

            span {
              color: #e62412;
            }
          }
        }
      }
    }
  }
}

.popper-user-child {
  .user-language-body {
    .user-language {
      display: flex;
      height: 40px;
      padding: 0 13px 0 15px;
      font-size: 14px;
      line-height: 40px;
      color: #262626;
      cursor: pointer;

      span {
        flex: 1;
      }

      .iconfont {
        display: none;
      }

      &.is-active {
        color: #2082ed;

        .iconfont {
          display: block;
          font-size: 14px;
          color: #2082ed;
          text-align: right;
        }
      }

      &:hover {
        background: #f2f2f2;
      }
    }
  }
}
</style>
<style>
.popper-user-name {
  padding: 0 !important;
  border-radius: 8px !important;
}

.popper-user-child {
  padding: 0 !important;
  border-radius: 8px !important;
}
</style>
