<template>
  <div class="user-login-container">
    <div class="user-login-title">{{ t("trade_common_resetPassword") }}</div>
    <div class="user-login-tip">
      <span class="middle-button" @click="jumpLogin">
        {{ t("trade_login_backtoLogin") }}
      </span>
    </div>
    <div class="user-login-form-item">
      <CheckMailLogin
        v-show="!nextStep"
        ref="ResetPasswordNextRef"
        :mailCodeType="mailCodeType"
      >
        <template #default>
          <el-button
            class="user-login-button"
            type="primary"
            @click="handleSendVerifyCode"
          >
            {{ t("trade_login_verifyEmail") }}
          </el-button>
        </template>
      </CheckMailLogin>
      <ResetPasswordNext
        v-show="nextStep"
        :email="userEmail"
        @handleLoginSuccess="handleLoginSuccess"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, inject } from "vue";
import { useI18n } from "vue-i18n";
import CheckMailLogin from "./CheckMailLogin.vue";
import { checkMailCode } from "@/api/login";
import { VerifyCodeEmailProp } from "@/api/type";
import ResetPasswordNext from "./ResetPasswordNext.vue";
import { useRouter } from "vue-router";

const { t } = useI18n();
const router = useRouter();
const mailCodeType = ref<string>("TRADE_FORGET_PASSWORD");
const ResetPasswordNextRef = ref<any>(null);
const nextStep = ref<boolean>(false);
const userEmail = ref<string>("");

const emit = defineEmits<{
  (e: "handleLoginSuccess", data: any): void;
}>();

const handleSendVerifyCode = (): Promise<void> => {
  const EmailRef = ResetPasswordNextRef.value;
  const formEl: any = EmailRef.ruleFormRef;
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      userEmail.value = EmailRef.userInfoForm.username;
      const checkMailCodeForm: VerifyCodeEmailProp = {
        email: EmailRef.userInfoForm.username,
        mailCode: EmailRef.userInfoForm.mailCode,
        //@ts-ignore
        mailCodeType: mailCodeType.value
      };
      const response = await checkMailCode(checkMailCodeForm);
      if (response.code !== 0) {
        EmailRef.setResponseMessage(t(`${response.msg}`));
      } else {
        nextStep.value = true;
      }
    }
  });
};

const handleLoginSuccess = (data: any) => {
  emit("handleLoginSuccess", data);
};

const handleWatchUserName = inject(
  "handleWatchUserName",
  (userInfo: any) => {}
);

const jumpLogin = (): void => {
  handleWatchUserName(ResetPasswordNextRef.value.userInfoForm);
  router.push("/login");
};
</script>
<style lang="scss" scoped>
@use "./index.scss";

.user-login-form-item {
  margin-bottom: 266px;
}
</style>
