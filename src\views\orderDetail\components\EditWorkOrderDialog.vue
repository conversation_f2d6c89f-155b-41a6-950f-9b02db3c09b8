<template>
  <el-drawer
    v-model="drawerVisible"
    class="edit-work-order-dialog"
    size="83%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    :with-header="false"
    :before-close="handleClosedAskBefore"
  >
    <div class="work-order-header">
      <div class="work-order-header-left">
        {{ t("trade_order_updatePersonnel") }}_{{ workDataForm.name }}
      </div>
      <div class="work-order-header-right">
        <el-tooltip :content="t('trade_common_orderMessage')">
          <span
            class="dialog-header-info-box"
            :class="[mainFormVisible ? 'active' : '']"
            @click="handleOpenMainFormDialog"
          >
            <span class="dialog-header-icon-box">
              <i class="iconfont link-basic-info" />
            </span>
          </span>
        </el-tooltip>
        <span
          class="create-work-order-tabs-right"
          @click.stop="() => handleClosedAskBefore(handleCloseDialog)"
        >
          <i class="iconfont link-close" />
        </span>
      </div>
    </div>
    <div class="create-work-order-container">
      <div class="create-work-order-left">
        <el-scrollbar class="no-scrollbar">
          <div class="create-work-order-form-container">
            <div class="create-work-order-title">
              {{ t("trade_work_order") }}
            </div>
            <div class="dialog-content-form-item">
              <el-input
                v-model="workDataForm.name"
                clearable
                maxlength="50"
                show-word-limit
                @change="isChangeFlag = true"
              />
            </div>
          </div>
          <div class="create-work-order-form-personnel-body">
            <div class="create-work-order-form-header">
              <div
                class="create-work-order-form-left"
                @click="handleAddWorkOrderPeronnel"
              >
                <i class="iconfont link-sharing" />
                {{ t("trade_order_Batchsettingofprocessors") }}
              </div>
              <div
                v-if="getWorkOrderFormAssigned > 1"
                class="create-work-order-form-right"
              >
                <span class="work-order-tooltip-text">
                  {{
                    t("trade_common_cooperateWith", {
                      params0: getWorkOrderFormAssigned
                    })
                  }}
                </span>
                <el-tooltip
                  effect="dark"
                  :content="t('trade_order_workOrderReplyAssignedTip')"
                  placement="top"
                  :show-after="500"
                >
                  <i class="iconfont link-explain" />
                </el-tooltip>
              </div>
            </div>
            <div
              v-for="workOrderItem in workDataForm.workOrderProcessorVOList"
              :key="workOrderItem"
              class="create-work-order-form-personnel"
            >
              <div class="create-work-order-form-top">
                <div class="create-work-order-form-top-left">
                  {{ workOrderItem.formName }}
                </div>
                <div class="create-work-order-form-top-right">
                  <el-tooltip
                    v-if="workOrderItem.formProperty === 'DISPLAY'"
                    :content="t('trade_createWorkOrder_show')"
                    placement="top"
                    :show-after="500"
                  >
                    <div
                      class="create-work-order-form-item"
                      @click="handleChangeSwitch(workOrderItem, 'HIDDEN')"
                    >
                      <i class="iconfont link-display" />
                    </div>
                  </el-tooltip>
                  <el-tooltip
                    v-if="workOrderItem.formProperty === 'HIDDEN'"
                    :content="t('trade_createWorkOrder_hide')"
                    placement="top"
                    :show-after="500"
                  >
                    <div
                      class="create-work-order-form-item"
                      @click="handleChangeSwitch(workOrderItem, 'DISPLAY')"
                    >
                      <i class="iconfont link-hide" />
                    </div>
                  </el-tooltip>
                </div>
              </div>
              <div class="create-work-order-form-bottom">
                <div
                  v-if="workOrderItem.formProperty === 'DISPLAY'"
                  class="work-order-personnel-control-operation"
                >
                  <OrderPersonnelTag
                    :type="personnelType"
                    :workOrderUserInfo="workOrderItem"
                    :personnelList="workOrderItem.collectUserVOList"
                    @handleChangePersonnel="handleChangePersonnel"
                  />
                </div>
                <span v-else class="work-order-peronnel-container-text">
                  {{ t("trade_order_BatchsettingofprocessorsTip") }}
                </span>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="create-work-body-right">
        <el-scrollbar class="horizontal-scrollbar-only">
          <div class="dialog-content-flex-right-header">
            {{ t("trade_order_createWorkOrderTip") }}
          </div>
          <LkTrendsAggregationForm
            ref="TrendsAggregationFormRef"
            :formList="widgetFormDataList"
            :workOrderId="workOrderData.workOrderId"
            :formWidgetData="workDataForm.widgetItemDataList"
            :operationalFactorData="operationalFactorData"
            :defaultValueActuatorFlag="false"
            :sccsId="route.query.sccsId"
            :hiddenFormIdList="hiddenFormIdList"
            :workOrderAllowOperation="workOrderAllowOperation"
            :trendsWidgetRequireFormIdList="trendsWidgetRequireFormIdList"
          >
            <template #collapseFormHeader="{ collapseData }">
              <div style="display: inline-flex; margin-left: 20px" @click.stop>
                <LkAvatarGroupNext
                  :size="22"
                  mode="reply"
                  :avatarListGroup="
                    getWorkOrderFormOperateMember(workDataForm, collapseData.id)
                  "
                  :maxAvatar="4"
                />
              </div>
            </template>
          </LkTrendsAggregationForm>
        </el-scrollbar>
      </div>
    </div>
    <OrderMainFormHeader
      ref="OrderMainFormRef"
      :mainFormData="mainFormWidgetData"
    />
    <template #footer>
      <div class="drawer-footer">
        <el-button
          plain
          @click="() => handleClosedAskBefore(handleCloseDialog)"
        >
          {{ t("trade_common_cancel") }}
        </el-button>
        <el-button
          type="primary"
          :disabled="btnOperationState"
          @click="handleConfirm"
        >
          {{ t("trade_common_sure") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
  <OrderChooseProcessorDialog
    ref="WorkOrderChooseProcessorRef"
    @handleChooseConfirm="handleAllChooseConfirm"
  />
</template>
<script lang="ts" setup>
import { computed, getCurrentInstance, nextTick, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils.tsx";
import { cloneDeep, isEqual, storageLocal } from "@pureadmin/utils";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";
import OrderChooseProcessorDialog from "@/views/orderDetail/components/OrderChooseProcessor/OrderChooseProcessorDialog.vue";
import OrderMainFormHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderMainFormHeader.vue";
import OrderPersonnelTag from "@/views/orderDetail/components/OrderChooseProcessor/OrderPersonnelTag.vue";
import {
  getWorkOrderStructure,
  updateWorkOrderOperator,
  getWorkOrderProcessorList
} from "@/api/order";
import { emitter } from "@/utils/mitt";

const { t } = useI18n();
const route = useRoute();
const { proxy } = getCurrentInstance();
const TrendsAggregationFormRef = ref<HTMLElement | any>(null);
const WorkOrderChooseProcessorRef = ref<HTMLElement | any>(null);
const OrderMainFormRef = ref<HTMLElement | any>(null);
const drawerVisible = ref<boolean>(false);
const personnelType = ref<string>("UPDATE_PROCESSOR");
const mainFormVisible = ref<boolean>(false);
const isChangeFlag = ref<boolean>(false);
const btnOperationState = ref<boolean>(false);
const orderSccsId = ref<string>("");
const mainFormWidgetData = ref<any>({});
const workDataForm = ref<any>({});
const workDataFormOriginalData = ref<any>({});
const WorkOrderFormTemplateList = ref<any>([]);
const workOrderData = ref<any>({});
const widgetFormDataList = ref<any>([]);
const trendsWidgetRequireFormIdList = ref<string[]>([]);
const hiddenFormIdList = ref<string[]>([]);
const operationalFactorData = ref<any>({});

const workOrderAllowOperation = computed(() => {
  // if (!workDataForm.value.dataList) return [];

  // const displayFormList =
  //   workDataForm.value.dataList.workOrderProcessorVOList.filter(
  //     form => form.formProperty === "DISPLAY"
  //   );

  // const workOrderIds = displayFormList.map(displayForm => {
  //   if (displayForm.collectUserVOList.length === 0) {
  //     return displayForm.formId;
  //   } else {
  //     const displayFormCollectorIds = displayForm.collectUserVOList.flatMap(
  //       collect => Object.values(collect)
  //     );
  //     //@ts-ignore
  //     const { latestLoginTeamMemberId } = storageLocal().getItem("user-info");
  //     if (displayFormCollectorIds.includes(latestLoginTeamMemberId)) {
  //       return displayForm.formId;
  //     } else {
  //       return "";
  //     }
  //   }
  // });
  return widgetFormDataList.value.map(form => form.id);
});

const getWorkOrderFormOperateMember = computed(() => {
  return (item: any, formId: string) => {
    if (
      !item ||
      !item.workOrderProcessorVOList ||
      !item.workOrderProcessorVOList.length
    )
      return [];

    const workOrderFormItem = item?.workOrderProcessorVOList?.find(
      workOrderForm => workOrderForm.formId === formId
    );
    const workOrderProcessorListData: any = storageLocal().getItem(
      "workOrderProcessorList"
    );

    let userList: any[] = [];
    if (
      workOrderProcessorListData &&
      workOrderFormItem.collectUserVOList.length > 0
    ) {
      const { sccsMemberList, sccsCoopTeamList } = workOrderProcessorListData;
      let personnelList = [];

      let sccsMember = [];
      for (let coopTeam of sccsCoopTeamList) {
        sccsMember = sccsMember.concat(coopTeam.teamMemberList);
      }

      sccsMember = sccsMember.concat(sccsMemberList);

      for (let personnel of workOrderFormItem.collectUserVOList) {
        if (Object.keys(personnel)[0] === "assignedTeamMemberId") {
          const userInfo = sccsMember.find(
            member => member.teamMemberId === personnel["assignedTeamMemberId"]
          );
          personnelList.push(
            Object.assign(userInfo, {
              user: true,
              coop: userInfo.showHand
            })
          );
        } else if (Object.keys(personnel)[0] === "assignedTeamId") {
          const teamInfo = sccsCoopTeamList.find(
            team => team.id === personnel["assignedTeamId"]
          );
          personnelList.push(Object.assign(teamInfo, { user: false }));
        }
      }
      userList = personnelList.map(member => {
        return {
          activate: member.activate,
          user: member.user,
          userAvatar: member.avatar,
          coopTeamUser: member.coop,
          email: member.account,
          teamName: member.teamName,
          shortName: member.teamShortName,
          userName: member.username,
          teamAvatar: member.teamAvatar
        };
      });
    } else {
      userList = [];
    }
    return userList;
  };
});

const getWorkOrderFormAssigned = computed(() => {
  if (!workDataForm.value.hasOwnProperty("workOrderProcessorVOList")) return 0;
  const collectVoList = workDataForm.value.workOrderProcessorVOList
    .filter(workOrder => workOrder.formProperty !== "HIDDEN")
    .flatMap(workOrderProcessor => workOrderProcessor.collectUserVOList);
  return Array.from(
    new Set(
      collectVoList.map(
        collect => collect.assignedTeamMemberId || collect.assignedTeamId
      )
    )
  ).length;
});

const handleConfirm = async () => {
  const { workOrderId, msId, orderId } = workOrderData.value;
  const { name, workOrderProcessorVOList } = workDataForm.value;
  if (!handleValidateWorkOrder(workDataForm.value)) {
    return false;
  }

  const widgetItemDataList =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(true);
  const linkedReferenceSourceList =
    TrendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  if (!widgetItemDataList) return;

  const { code } = await updateWorkOrderOperator({
    sccsId: orderSccsId.value,
    orderId: orderId,
    msId: msId,
    workOrderId: workOrderId,
    name: name,
    workOrderProcessorVOList: workOrderProcessorVOList,
    workOrderReplyAssignedUserVOList: [],
    widgetItemDataList: widgetItemDataList,
    linkedReferenceSaveList: linkedReferenceSourceList
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_dealSuccess"),
      type: "success"
    });
    drawerVisible.value = false;
    emit("handleCreateWorkOrderSuccess");
  }
};

const handleAllChooseConfirm = (data): void => {
  workDataForm.value.workOrderProcessorVOList.map(
    dataItem => (dataItem.collectUserVOList = data)
  );
};

const handleChangeSwitch = (workOrderEdit: any, state: string) => {
  workOrderEdit.formProperty = state;
  const formPropertyList = workDataForm.value.workOrderProcessorVOList.map(
    edit => edit.formProperty
  );
  if (formPropertyList.every(element => element === "HIDDEN")) {
    ElMessage.error(t("trade_common_addWorkOrderTip"));
    workOrderEdit.formProperty = "DISPLAY";
    return;
  }
  hiddenFormIdList.value = workDataForm.value.workOrderProcessorVOList
    .filter(dataList => dataList.formProperty === "HIDDEN")
    .map(dataList => dataList.formId);
};

const handleClosedAskBefore = async done => {
  const widgetItemDataList =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(true);

  const changedWidgetItemDataList = widgetItemDataList?.filter(item => {
    const originalItem = workDataFormOriginalData.value.widgetItemDataList.find(
      original => original.widgetId === item.widgetId
    );
    return !originalItem || !isEqual(item.obj, originalItem.obj);
  });

  if (isChangeFlag.value || changedWidgetItemDataList.length > 0) {
    //@ts-ignore
    const result = await proxy.$submitMessageBox.confirm(
      t("trade_common_dataUpdateTip"),
      {
        confirmButtonText: t("trade_order_submitBtn"),
        cancelButtonText: t("trade_common_cancel"),
        nextButtonText: t("trade_order_unSubmit")
      }
    );
    if (result === "confirm") {
      isChangeFlag.value = false;
      mainFormVisible.value = false;
      handleConfirm();
      done();
    } else if (result === "nextConfirm") {
      isChangeFlag.value = false;
      handleCloseDialog();
      done();
    }
  } else {
    mainFormVisible.value = false;
    done();
  }
};

/**
 * 选择工单表单采集人
 * @param workOrderItemData
 * @param personnelList
 */
const handleChangePersonnel = (workOrderItemData: any, personnelList: any) => {
  workOrderItemData.collectUserVOList = personnelList;
  isChangeFlag.value = true;
  handleGetFormRequireWidget();
};

const handleGetFormRequireWidget = () => {
  trendsWidgetRequireFormIdList.value =
    workDataForm.value.workOrderProcessorVOList
      .filter(form => form.collectUserVOList.length > 0)
      .map(form => form.formId);
  hiddenFormIdList.value = workDataForm.value.workOrderProcessorVOList
    .filter(dataList => dataList.formProperty === "HIDDEN")
    .map(dataList => dataList.formId);
};

const handleAddWorkOrderPeronnel = (): void => {
  WorkOrderChooseProcessorRef.value.open([], "UPDATE_PROCESSOR", {
    sccsId: orderSccsId.value
  });
};

const handleValidateWorkOrder = dataList => {
  if (!dataList.name) {
    ElMessage.error(t("trade_workorder_nameIsNull"));
    return false;
  }
  const workOrderProcessorVOList = dataList.workOrderProcessorVOList;
  const hasWorkOrderProcessVoList = workOrderProcessorVOList.filter(
    workOrderProcessVo => {
      return (
        workOrderProcessVo.collectUserVOList.length !== 0 &&
        workOrderProcessVo.formProperty === "DISPLAY"
      );
    }
  );
  if (hasWorkOrderProcessVoList.length === 0) {
    ElMessage.error(t("trade_order_workOrderChooseProcessorTip"));
    return false;
  }
  return true;
};

const handleCloseDialog = (): void => {
  isChangeFlag.value = false;
  mainFormVisible.value = false;
  workDataForm.value = {};
  drawerVisible.value = false;
};

const emit = defineEmits<{
  (e: "handleCreateWorkOrderSuccess"): void;
}>();

const handleOpenMainFormDialog = (): void => {
  OrderMainFormRef.value.handleOpenMainFormVisible();
  mainFormVisible.value = !mainFormVisible.value;
};

const open = async (
  sccsId: string,
  workOrderFormListData: any,
  mainFormData: any
): Promise<void> => {
  orderSccsId.value = sccsId;
  const {
    msId,
    workOrderId,
    orderId,
    workOrderName,
    editUserList,
    widgetList,
    subWidgetMap,
    linkedReferenceMap
  } = workOrderFormListData;
  workOrderData.value = workOrderFormListData;

  Promise.all([
    getWorkOrderStructure({
      sccsId: sccsId,
      milestoneId: msId,
      orderId: orderId,
      workOrderId: workOrderId
    }),
    getWorkOrderProcessorList({
      sccsId: sccsId,
      type: "UPDATE_PROCESSOR"
    })
  ]).then(resp => {
    const { workOrderFormList } = resp[0].data;
    mainFormWidgetData.value = mainFormData;

    WorkOrderFormTemplateList.value = workOrderFormList;
    const workOrderFormData = workOrderFormList.map(workOrderForm => {
      return {
        formId: workOrderForm.id,
        formName: workOrderForm.name,
        formProperty: workOrderFormListData.hiddenFormIdList.includes(
          workOrderForm.id
        )
          ? "HIDDEN"
          : "DISPLAY",
        collectUserVOList: []
      };
    });
    widgetFormDataList.value = [];
    widgetFormDataList.value.push(...workOrderFormList);

    for (let editUser of editUserList) {
      for (const assignedFormId of editUser.assignedFormIdList) {
        const workOrderItemData = workOrderFormData.find(
          workOrderForm => workOrderForm.formId === assignedFormId
        );

        if (!workOrderItemData) continue;

        const { collectUserVOList } = workOrderItemData;

        if (editUser.user) {
          const hasTeamMember = collectUserVOList.some(
            item => item.assignedTeamMemberId === editUser.teamMemberId
          );

          if (!hasTeamMember) {
            collectUserVOList.push({
              assignedTeamMemberId: editUser.teamMemberId
            });
          }
        } else {
          const hasTeam = collectUserVOList.some(
            item => item.assignedTeamId === editUser.teamId
          );

          if (!hasTeam) {
            collectUserVOList.push({
              assignedTeamId: editUser.teamId
            });
          }
        }
      }
    }
    workDataForm.value = {
      name: workOrderName,
      workOrderProcessorVOList: workOrderFormData,
      widgetItemDataList: TransformSubmitDataStructure(
        widgetList,
        subWidgetMap,
        linkedReferenceMap
      )
    };
    hiddenFormIdList.value = workDataForm.value.workOrderProcessorVOList
      .filter(dataList => dataList.formProperty === "HIDDEN")
      .map(dataList => dataList.formId);

    storageLocal().setItem("workOrderProcessorList", {
      sccsMemberList: resp[1].data.teamMemberList,
      sccsCoopTeamList: resp[1].data.coopTeamList
    });

    operationalFactorData.value = getEntireFormData(
      workOrderData.value.msId,
      workOrderData.value.workOrderId
    );

    workDataFormOriginalData.value = cloneDeep(workDataForm.value);
    drawerVisible.value = true;
    nextTick(() => {
      handleGetFormRequireWidget();
    });
  });
};

onMounted(() => {
  emitter.on("widgetUploadLoading", (isUploading: boolean) => {
    btnOperationState.value = isUploading;
  });
});

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "./style/index.scss";
</style>
<style lang="scss">
.edit-work-order-dialog {
  &.el-drawer {
    .el-drawer__body {
      height: 100%;
      padding: 0;
      overflow: hidden;
    }

    .el-drawer__footer {
      height: 54px;
      padding: 0 !important;
    }
  }

  .create-work-order-container {
    position: relative;
    display: flex;
    height: calc(100% - 54px);
    padding: 10px;
    margin-top: 54px;
    background: #f6f6f6;

    .create-work-order-left {
      width: 358px;
      min-width: 358px;
      padding: 20px 14px;
      background: #fff;
      border-radius: 4px;

      .create-work-order-form-container {
        margin-bottom: 16px;

        .create-work-order-title {
          margin-bottom: 10px;
          font-size: 14px;
          font-weight: bolder;
          line-height: 16px;
          text-align: left;

          &::before {
            margin-right: 6px;
            color: #e61b1b;
            content: "*";
          }
        }
      }

      .create-work-order-form-personnel-body {
        padding: 8px;
        margin-bottom: 16px;
        background: #f4f5fa;
        border-radius: 4px;

        .create-work-order-form-personnel {
          padding: 5px 7px;
          margin-bottom: 8px;
          background: linear-gradient(#f7faff, #fff);
          border: 1px solid #e9eaed;
          border-radius: 4px;

          .create-work-order-form-top {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .create-work-order-form-top-left {
              font-size: 13px;
              font-weight: bolder;
              line-height: 14px;
              color: #262626;
              text-align: left;

              &.create-work-order-form-title-require {
                &::before {
                  margin-right: 5px;
                  color: #e61b1b;
                  content: "*";
                }
              }

              .create-work-order-form-tip {
                font-size: 12px;
                color: #797979;
              }
            }

            .create-work-order-form-top-right {
              .create-work-order-form-item {
                width: 30px;
                height: 24px;
                text-align: center;
                cursor: pointer;
                border-radius: 2px;

                &:hover {
                  background: #e5e5e5;
                }
              }

              .work-order-tooltip-body {
                display: flex;
                align-items: center;

                .work-order-tooltip-text {
                  margin-right: 5px;
                  font-size: 12px;
                  color: #f6974f;
                  text-align: center;
                }

                .iconfont {
                  font-size: 13px;
                  color: #808080;
                  cursor: pointer;
                }
              }
            }
          }

          .create-work-order-form-bottom {
            .work-order-peronnel-container-text {
              display: inline-block;
              margin-top: 10px;
              font-size: 14px;
              line-height: 24px;
              color: #bfbfbf;
              text-align: left;
            }
          }

          &:last-child {
            margin-bottom: 0;
          }
        }

        .create-work-order-form-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 10px;

          .create-work-order-form-left {
            display: flex;
            font-size: 12px;
            font-weight: bolder;
            color: #0070d2;
            text-align: left;
            cursor: pointer;

            .iconfont {
              margin-right: 2px;
              font-size: 13px;
            }
          }

          .create-work-order-form-right {
            .work-order-tooltip-text {
              margin-right: 5px;
              font-size: 12px;
              color: #f6974f;
              text-align: center;
            }

            .iconfont {
              font-size: 13px;
              color: #808080;
              cursor: pointer;
            }
          }
        }
      }
    }

    .create-work-body-right {
      flex: 1;
      margin-left: 17px;

      .dialog-content-flex-right-header {
        margin: 15px 0 11px;
        font-size: 12px;
        font-weight: bolder;
        line-height: 17px;
        color: #262626;
        text-align: left;
      }
    }

    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 3000;
      width: 100%;
      height: 100%;
      background: #fff0;
    }

    .main-form-container {
      top: 0 !important;
    }
  }

  .work-order-header {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 54px;
    padding: 0 20px;
    background: #fff;
    border-bottom: 1px solid #e6eaf0;

    .work-order-header-left {
      font-size: 18px;
      font-weight: bolder;
      color: #202020;
    }

    .work-order-header-right {
      display: inline-flex;
      align-items: center;

      .dialog-header-info-box {
        position: relative;
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        color: #606266;
        text-align: center;
        cursor: pointer;
        border-radius: 4px;

        // &::after {
        //   position: absolute;
        //   top: 50%;
        //   right: -10px;
        //   display: inline-block;
        //   width: 1px;
        //   height: 16px;
        //   content: "";
        //   background: #e5e5e5;
        //   transform: translateY(-50%);
        // }

        &:hover {
          background: #e5e5e5;
        }

        &.active {
          color: #2082ed;
          background: #e1edff;
        }
      }

      .create-work-order-tabs-right {
        position: relative;
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-left: 20px;
        line-height: 20px;
        color: #606266;
        text-align: center;
        border-radius: 4px;

        &::before {
          position: absolute;
          top: 0;
          left: -10px;
          display: inline-block;
          width: 1px;
          height: 20px;
          content: "";
          background: #e5e5e5;
        }

        .iconfont {
          font-size: 12px;
        }

        &:hover {
          background: #e5e5e5;

          .iconfont {
            color: #808080;
          }
        }
      }
    }
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 54px;
    padding: 0 20px;
    background: #fff;
    border-top: 1px solid #e6eaf0;
  }
}

.work-order-tabs {
  width: auto !important;
  height: 100%;

  .el-tabs__header {
    height: auto !important;
    padding-top: 6px;
    padding-left: 4px;
    margin-bottom: 0 !important;
    background: #fff;

    .el-tabs__item {
      height: 54px;
      padding: 0 11px !important;
      line-height: 54px;

      &.is-active {
        .work-order-tabs-label {
          color: #0070d2;
        }
      }

      .work-order-tabs-label {
        margin-right: 7px;
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        text-align: left;
      }

      .work-order-tabs-icon {
        position: relative;
        display: inline-block;
        width: 22px;
        height: 22px;
        margin-right: 5px;
        text-align: center;
        border-radius: 2px;

        &:last-child {
          margin-right: 0;
        }

        &:hover {
          background: #e5e5e5;
        }

        .iconfont {
          position: absolute;
          top: 50%;
          left: 50%;
          font-size: 12px;
          color: #808080;
          vertical-align: middle;
          transform: translate(-50%, -50%);
        }
      }
    }

    .el-tabs__nav-wrap {
      flex: none;
      width: auto;
    }

    .el-tabs__new-tab {
      flex: 1;
      justify-content: flex-start;
      border: 0 none;

      .work-order-tabs-add-icon {
        display: flex;
        align-items: center;
        width: 100%;

        .work-order-tabs-flex-left {
          flex: 1;
          text-align: left;
        }

        .work-order-tabs-flex-right {
          display: flex;
          align-items: center;
          width: 213px;

          .work-order-tabs-flex-right-operation {
            display: flex;
            flex: 1;
            align-items: center;
            text-align: left;

            .dialog-header-box {
              flex: 1;

              .iconfont {
                font-size: 14px;
                vertical-align: middle;
              }

              .dialog-header-box-text {
                margin-left: 5px;
                font-size: 14px;
                color: #595959;
              }
            }

            .dialog-header-info-box {
              width: 32px;
              height: 32px;
            }
          }

          .work-order-tabs-flex-right-close {
            width: 55px;
            color: #808080;
            border-left: 1px solid #e5e5e5;
          }
        }
      }

      &:hover {
        color: #595959;
      }
    }
  }

  ::v-deep(.el-tabs__content) {
    height: 100%;

    .el-tab-pane {
      height: 100%;
    }
  }

  .work-order-tabs-container {
    position: absolute;
    top: 54px;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    height: 480px;
    padding: 33px 43px;
    background: #fff;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 8px 13px 0 rgb(0 0 0 / 10%);
  }
}

.create-success-dialog {
  .el-dialog__body {
    height: auto !important;
    padding-bottom: 0 !important;
    text-align: center;
    background: #fff;

    .svg-icon {
      width: 80px;
      margin: 0 auto;
    }

    .create-work-order-text {
      font-size: 14px;
      line-height: 14px;
      color: #8c8c8c;

      .create-work-order-span-text {
        margin: 0 5px;
        font-weight: 600;
        color: #262626;
      }
    }

    .create-work-order-tip {
      margin-top: 11px;
      font-size: 14px;
      line-height: 24px;
      color: #262626;
    }

    .create-work-order-btn-group {
      margin-top: 48px;

      .create-work-order-plain-btn {
        color: #0070d2;
        border-color: #ccecff;
      }
    }
  }
}
</style>
