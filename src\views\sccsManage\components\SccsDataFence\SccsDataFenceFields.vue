<template>
  <div class="sccs-data-fence-template-fields">
    <div class="sccs-data-fence-template-top">
      <SccsDataFenceLabel
        ref="SccsDataFenceLabelRef"
        :labelFields="labelFields"
        :dataFenceWidgetHiddenIds="dataFenceWidgetHiddenIds"
        @handleChangeFieldsChecked="handleChangeFieldsChecked"
      />
    </div>
    <div class="sccs-data-fence-template-bottom">
      <el-row :gutter="20" class="sccs-data-fence-container">
        <el-col
          v-for="widgetField in widgetFields"
          :key="widgetField._fc_id"
          :span="24"
          class="sccs-data-fence-col"
        >
          <div
            v-if="widgetField.type === 'DrTableForm'"
            class="sccs-data-fence-subtable-body"
          >
            <div class="sccs-data-fence-subtable-header">
              <div class="sccs-data-fence-text">{{ widgetField.title }}</div>
              <el-checkbox
                :model-value="widgetHiddenIds.includes(widgetField._fc_id)"
                :label="t('trade_common_CannotBeViewed')"
                :value="widgetField._fc_id"
                @change="bool => handleCheckedWidget(bool, widgetField._fc_id)"
              />
            </div>

            <el-table
              :data="tableData"
              border
              header-row-class-name="table-background-Row"
            >
              <el-table-column
                v-for="column in widgetField.children.slice(1)"
                :key="column.children[0]._fc_id"
                header-align="center"
                :prop="column.children[0]._fc_id"
                :label="column.children[0].title"
                width="160"
              />
            </el-table>
          </div>
          <div
            v-else-if="widgetField.type === 'DrCard'"
            class="sccs-data-fence-card-body"
          >
            <SccsDataFenceCard
              ref="SccsDataFenceCardRef"
              :widget="widgetField"
              :dataFenceWidgetHiddenIds="dataFenceWidgetHiddenIds"
              @handleChangeCardCheckbox="handleChangeCardCheckbox"
            />
          </div>
          <div v-else class="sccs-data-fence-body">
            <div class="sccs-data-fence-text">{{ widgetField.title }}</div>
            <el-checkbox
              :model-value="widgetHiddenIds.includes(widgetField._fc_id)"
              :label="t('trade_common_CannotBeViewed')"
              :value="widgetField._fc_id"
              @change="bool => handleCheckedWidget(bool, widgetField._fc_id)"
            />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import SccsDataFenceCard from "@/views/sccsManage/components/SccsDataFence/SccsDataFenceCard.vue";
import SccsDataFenceLabel from "@/views/sccsManage/components/SccsDataFence/SccsDataFenceLabel.vue";
import { getOrderTemplateDetails } from "@/api/order";
import { getSccsDataFenceLabelList } from "@/api/sccs";

const { t } = useI18n();
const route = useRoute();
const SccsDataFenceLabelRef = ref<HTMLElement | any>(null);
const SccsDataFenceCardRef = ref<HTMLElement | any>(null);
const widgetFields = ref<any>([]);
const labelFields = ref<any>([]);
const widgetHiddenIds = ref<string[]>([]);
const tableData = ref<any>([]);
const otherWidget = ref<any>([]);
const labelFieldList = ref<any[]>([]);
const cardWidgetMap = reactive(new Map());

const props = defineProps({
  dataFenceWidgetHiddenIds: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const emit = defineEmits(["handleWidgetHiddenIds"]);

const handleChangeFieldsChecked = (labelFields: any[]) => {
  labelFieldList.value = labelFields;
};

const handleChangeCardCheckbox = (widget: any, widgetList: any[]) => {
  cardWidgetMap.set(widget, widgetList);
};

const handleCheckedWidget = (bool: any, widgetId: string) => {
  if (bool) {
    widgetHiddenIds.value.push(widgetId);
  } else {
    const index = widgetHiddenIds.value.findIndex(
      widget => widget === widgetId
    );
    widgetHiddenIds.value.splice(index, 1);
  }
};

const findCommonElements = (arrA, arrB) => {
  const setB = new Set(arrB);
  return arrA.filter(element => setB.has(element));
};

onMounted(() => {
  const { templateId, sccsId } = route.query;
  Promise.all([
    getOrderTemplateDetails({
      templateId: templateId
    }),
    getSccsDataFenceLabelList({
      sccsId: sccsId
    })
  ]).then(resp => {
    widgetFields.value = resp[0].data.mainForm.widgetJsonList.filter(
      widget => !["DrDivider", "DrPlaceholder"].includes(widget.type)
    );
    labelFields.value = resp[1].data;
    otherWidget.value = resp[0].data.mainForm.widgetJsonList.filter(
      widget => !["DrDivider", "DrPlaceholder", "DrCard"].includes(widget.type)
    );
  });
});

watch(
  [
    () => widgetHiddenIds.value,
    () => labelFieldList.value,
    () => cardWidgetMap
  ],
  () => {
    let cardChildList = [];
    for (let value of cardWidgetMap.values()) {
      cardChildList.push(...value);
    }
    const widgetEntireHiddenIds = [
      ...widgetHiddenIds.value,
      ...labelFieldList.value,
      ...cardChildList
    ];
    emit("handleWidgetHiddenIds", widgetEntireHiddenIds);
  },
  {
    deep: true
  }
);

watch(
  [() => props.dataFenceWidgetHiddenIds, () => otherWidget.value],
  () => {
    const otherWidgetIds = otherWidget.value.map(widget => widget._fc_id);
    widgetHiddenIds.value = findCommonElements(
      otherWidgetIds,
      props.dataFenceWidgetHiddenIds
    );
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.sccs-data-fence-template-fields {
  .sccs-data-fence-template-top {
    box-sizing: content-box;
    border: 1px solid #e5e5e5;
    border-radius: 4px;

    ::v-deep(.sccs-data-fence-template-top-header) {
      .sccs-data-fence-template-top-title {
        height: 48px;
        margin: 0 16px;
        font-size: 16px;
        font-weight: bolder;
        line-height: 48px;
        color: #262626;
        text-align: left;
        border-bottom: 1px solid #eee;

        .sccs-data-fence-template-top-tip {
          margin-right: 25px;
        }
      }
    }

    ::v-deep(.sccs-data-fence-container) {
      width: 100%;
      padding: 16px 16px 0;
      margin: 0 !important;
      overflow: hidden;

      .sccs-data-fence-col {
        margin-top: 10px;

        .sccs-data-fence-text {
          margin-bottom: 13px;
          font-size: 14px;
          line-height: 16px;
          color: #262626;
          text-align: left;
        }

        .el-checkbox {
          margin-bottom: 13px;
        }
      }
    }
  }

  .sccs-data-fence-template-bottom {
    margin-top: 16px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;

    .sccs-data-fence-container {
      width: 100%;
      padding: 16px 16px 0;
      margin: 0 !important;
      overflow: hidden;

      .sccs-data-fence-subtable-body {
        margin-bottom: 10px;

        .sccs-data-fence-subtable-header {
          display: flex;
          align-items: center;
          height: 48px;

          .sccs-data-fence-text {
            height: 48px;
            margin-right: 20px;
            font-size: 14px;
            font-weight: bolder;
            line-height: 48px;
            color: #262626;
            text-align: left;
          }
        }
      }

      .sccs-data-fence-col {
        margin-top: 10px;

        .sccs-data-fence-body {
          .sccs-data-fence-text {
            margin-bottom: 13px;
            font-size: 14px;
            line-height: 16px;
            color: #262626;
            text-align: left;
          }

          .el-checkbox {
            margin-bottom: 13px;
          }
        }
      }
    }
  }
}
</style>
