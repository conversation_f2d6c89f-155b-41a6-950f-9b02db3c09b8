<template>
  <div class="linkIncrease-login-container">
    <div class="linkIncrease-login-left">
      <div class="linkIncrease-login-left-container">
        <el-carousel :interval="5000" autoplay arrow="never">
          <el-carousel-item
            v-for="(carouseItem, carouseIndex) in carouselList"
            :key="carouseIndex"
            class="carousel-item-container"
          >
            <div class="carousel-title">
              {{ t(`${carouseItem.carouseTitle}`) }}
            </div>
            <p class="carousel-message-list">
              {{ t(`${carouseItem.carouseMessage[0]}`) }}
            </p>
            <el-image :src="carouseItem.carouseImage" fit="contain" />
          </el-carousel-item>
        </el-carousel>
        <div class="linkIncrease-logo-body">
          <el-image
            class="linkIncrease-logo"
            :src="companyLogo"
            fit="contain"
          />
        </div>
      </div>
    </div>
    <div class="linkIncrease-login-right">
      <LKSwitchLanuage />
      <div class="linkIncrease-login-form-item">
        <slot name="default" />
      </div>
      <div class="linkIncrease-login-filings-body">
        <div class="linkIncrease-login-filings">
          <div v-show="langValue !== 'en'" class="filings-body">
            闽ICP备17004372号
          </div>
          <div class="filings-msg">
            &#169; Xiamen Daren Supply Chain Management Co., Ltd.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import imageFirst from "@/assets/login/loginShufflingFirst.png";
import imageSecond from "@/assets/login/loginShufflingSecond.png";
import imageThird from "@/assets/login/loginShufflingThird.png";
import imageLogo from "@/assets/login/logo.png";
import LKSwitchLanuage from "@/components/lKSwitchLanguage";
import { emitter } from "@/utils/mitt";

const { t } = useI18n();
const companyLogo: any = imageLogo;
const langValue = ref<string>("system");
const carouselList: any = [
  {
    carouseTitle: "trade_login_sloganThree",
    carouseMessage: ["trade_login_sloganDescribeThree"],
    carouseImage: imageFirst
  },
  {
    carouseTitle: "trade_login_sloganTwo",
    carouseMessage: ["trade_login_sloganDescribeTwo"],
    carouseImage: imageSecond
  },
  {
    carouseTitle: "trade_login_sloganOne",
    carouseMessage: ["trade_login_sloganDescribeOne"],
    carouseImage: imageThird
  }
];

onMounted(() => {
  emitter.on("changeLanguage", lang => {
    langValue.value = lang;
  });
});
</script>
<style lang="scss" scoped>
@media screen and (width <= 1366px) {
  .linkIncrease-login-container {
    overflow: hidden;

    ::v-deep(.linkIncrease-login-left-container) {
      width: 80% !important;

      .el-carousel__container {
        height: 400px !important;
      }

      .el-carousel__indicators--horizontal {
        bottom: calc((100vh - 600px) / 2) !important;
      }
    }

    .carousel-title {
      font-size: 26px !important;
    }

    .carousel-message-list {
      margin-bottom: 40px !important;
      font-size: 16px !important;
    }

    .linkIncrease-logo-body {
      width: 100% !important;
      margin-top: 40px !important;
    }
  }

  .linkIncrease-login-right {
    padding: 0 !important;

    .linkIncrease-login-language {
      margin-top: 20px !important;
    }

    .linkIncrease-login-form-item {
      width: calc(100% - 93px) !important;
      margin: 0 auto !important;

      .user-login-container {
        padding: 0 !important;
        margin: 0 auto !important;
      }
    }

    .filings-body,
    .filings-msg {
      padding: 0 !important;
      white-space: nowrap !important;
    }
  }
}

@media screen and (width >= 1440px) and (width <= 1920px) {
  .linkIncrease-login-left-container {
    transform: scale(0.8) !important;

    ::v-deep(.el-carousel) {
      .el-carousel__indicators--horizontal {
        left: 50% !important;
      }
    }
  }
}

.linkIncrease-login-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100vh;

  .linkIncrease-login-left {
    position: relative;
    flex-direction: column;
    align-items: center;
    width: 50%;
    height: 100%;
    background: url("../../assets/login/login-background.png") no-repeat center
      center;
    background-size: 100% 100%;

    .linkIncrease-login-left-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 600px;
      height: 100%;
      margin: 0 auto;

      ::v-deep(.el-carousel) {
        position: relative;
        width: 100%;

        .el-carousel__container {
          width: 100%;
          height: 571px;

          .carousel-item-container {
            width: 100%;
            height: 571px;

            .carousel-title {
              max-width: 850px;
              font-size: 36px;
              font-weight: bolder;
              line-height: 50px;
              color: transparent;
              background: linear-gradient(146deg, #4851e5 33%, #935ad8 87%);
              background-clip: text;
              // text-indent: 20px;
            }

            .carousel-message-list {
              max-width: 800px;
              margin-bottom: 90px;
              font-size: 22px;
              line-height: 36px;
              color: #262626;
              // text-indent: 20px;
            }

            .carousel-message-list:nth-child(4) {
              margin-bottom: 90px;
            }
          }
        }

        .el-carousel__indicators--horizontal {
          position: fixed;
          bottom: calc((100vh - 800px) / 2);
          left: 25%;
          z-index: 3333;
          transform: translateX(-50%);

          .el-carousel__indicator--horizontal {
            display: inline-block;
            width: 8px;
            height: 8px;
            padding: 0;
            margin-right: 16px;
            background: #cadafa;
            border-radius: 50%;

            &.is-active {
              width: 15px;
              height: 8px;
              background: #8cb0fa;
              border-radius: 4px;
            }

            .el-carousel__button {
              width: 0;
            }
          }
        }
      }
    }

    .linkIncrease-logo-body {
      width: 100%;
      margin-top: 70px;
      text-align: center;

      .linkIncrease-logo {
        width: 207px;
      }
    }
  }

  .linkIncrease-login-right {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 50%;
    height: 100%;
    padding: 40px 60px 20px 0;

    .linkIncrease-login-form-item {
      display: flex;
      align-items: center;
      width: 600px;
      height: calc(100% - 93px);
      margin: 28px auto;
    }

    .linkIncrease-login-filings-body {
      width: 100%;

      .linkIncrease-login-filings {
        display: flex;
        justify-content: space-around;
        width: 100%;
        margin: 0 auto;

        .filings-body {
          padding-right: 60px;
          font-size: 14px;
          color: #808080;
          text-align: right;
        }

        .filings-msg {
          font-size: 14px;
          color: #808080;
        }
      }
    }
  }
}
</style>
