import { createApp, h } from "vue";
import lkFilePreview from "./src/index.vue";

const FilePreviewProp = {
  open(options) {
    return new Promise(() => {
      // 创建挂载容器
      const mountNode = document.createElement("div");
      document.body.appendChild(mountNode);
      // 清除组件实例
      let filePreviewInstance = null;

      // 关闭处理
      const close = () => {
        filePreviewInstance.unmount();
        document.body.removeChild(mountNode);
      };

      // 动态创建组件
      filePreviewInstance = createApp({
        render() {
          return h(lkFilePreview, {
            visible: true,
            ...options,
            // 监听确认/取消事件
            "onUpdate:visible": val => {
              if (!val) close();
            }
          });
        }
      });
      filePreviewInstance.mount(mountNode);
    });
  },
  preview(options) {
    return this.open({
      ...options
    });
  }
};

export default {
  install(app) {
    app.config.globalProperties.$filePreview = FilePreviewProp;
  }
};
