<template>
  <div class="team-module-area-body">
    <el-form
      ref="FormRef"
      label-position="top"
      label-width="auto"
      :model="form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_inputSearch')}：`">
            <el-input
              v-model="form.roleName"
              clearable
              :placeholder="t('trade_sccs_businessRoleNameTip')"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_members')}：`">
            <LkTeamSelectV2
              v-model="form.teamMemberId"
              filterable
              :options="sccsList"
              clearable
              :placeholder="t('trade_common_selectText')"
              :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
              :props="{ label: 'username', value: 'teamMemberId' }"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item style="margin-top: 30px">
            <el-button @click="resetForm">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchTable"
            >
              {{ t("trade_common_query") }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="tableData"
      :border="true"
      stripe
      :tooltip-options="{ showAfter: 500 }"
      :empty-text="
        loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
      "
    >
      <el-table-column
        :label="t('trade_sccs_businessRoleName')"
        width="255"
        prop="name"
        show-overflow-tooltip
      />
      <el-table-column
        :label="t('trade_team_members')"
        prop="teamMemberInfoList"
        class-name="el-table-sccs-setting-cell"
      >
        <template #default="scope">
          <ReText
            v-for="item in scope.row.teamMemberInfoList"
            :key="item.teamMemberId"
            class="tag-col"
            :tippyProps="{ delay: 50000 }"
          >
            <span v-if="!item.activate" class="tag-register-tip">
              ({{ t("trade_common_unregistered") }})
            </span>
            <LkAvatar
              :size="18"
              fit="cover"
              :teamInfo="{
                avatar: item.avatar,
                username: item.username,
                email: item.email || item.account
              }"
            />
            {{ item.username }}
          </ReText>
        </template>
      </el-table-column>
      <el-table-column width="70" align="center">
        <template #default="{ row }">
          <LkTableOperate
            :tableOpeateLists="tableOpeateLists"
            :messageBoxConfirmObject="messageBoxConfirmObject"
            :row="row"
            @handleOperate="handleOperate"
          />
        </template>
      </el-table-column>
    </el-table>
    <LkPagination
      ref="LkPaginationRef"
      :total="total"
      @updatePagination="handleSearchTable"
    />
    <SccsBusinessRoleEditDialog
      ref="SccsBusinessRoleEditDialogRef"
      :basicInfo="basicInfo"
      @updateSccsSuccess="handleSearchTable"
    />
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { nextTick, reactive, ref, watchEffect } from "vue";
import SccsBusinessRoleEditDialog from "./SccsBusinessRole/SccsBusinessRoleEditDialog.vue";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import LkAvatar from "@/components/lkAvatar/index";
import LkPagination from "@/components/lkPagination/index";
import LkTableOperate from "@/components/lkTableOperate/index";
import { ReText } from "@/components/ReText";
import { getSccsRole, getSccsMemberList, deleteSccsRole } from "@/api/sccs";
import { message } from "@/utils/message";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";

const { t } = useI18n();
const LkPaginationRef = ref<any>(null);
const FormRef = ref<any>(null);
const SccsBusinessRoleEditDialogRef = ref<any>(null);
let tableData = ref<any[]>([]);
let sccsList = ref<any[]>([]);
let total = ref<number>(0);
let loading = ref<boolean>(false);
const form = reactive({
  roleName: "",
  teamMemberId: ""
});
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_team_deleteSccsRole",
  messageBoxTitle: "trade_sccs_delSccsRole",
  messageBoxTipArray: ["name"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-edit",
    title: "trade_common_edit",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);

const resetForm = () => {
  form.roleName = "";
  form.teamMemberId = "";
  handleSearchTable();
};

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleSearchTable = async (): Promise<void> => {
  loading.value = true;
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getSccsRole({
    sccsId: props.basicInfo.id,
    ...pageParams,
    ...form
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleEdit = (id: string): void => {
  SccsBusinessRoleEditDialogRef.value.open(id);
};

const handleDeleteTable = async (id): Promise<void> => {
  const res = await deleteSccsRole({ id: id, sccsId: props.basicInfo.id });
  if (res.code === 0) {
    message(t("trade_common_deleteSuccess"), {
      customClass: "el",
      type: "success"
    });
    handleSearchTable();
  }
};

// const handleCreateTeamRole = (): void => {};
const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "edit") {
    handleEdit(row.id);
  } else {
    handleDeleteTable(row.id);
  }
};

const init = (): void => {
  loading.value = true;
  Promise.all([getSccsMemberList({ sccsId: props.basicInfo.id })])
    .then(res => {
      sccsList.value = res[0].data;
      handleSearchTable();
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 300);
    });
};

const addSccsBusinessRole = (): void => {
  SccsBusinessRoleEditDialogRef.value.open();
};

watchEffect(() => {
  if (props.basicInfo && props.basicInfo.id) {
    init();
  }
});

defineExpose({
  addSccsBusinessRole
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.team-module-area-body {
  max-height: calc(100vh - 160px);
  margin-top: 0 !important;
  overflow: hidden auto;

  .team-module-area-btn {
    position: absolute;
    top: 0;
    right: 0;
  }

  ::v-deep(.tag-col) {
    padding: 3px 6px;
    margin-right: 4px;
    font-size: 12px;
    line-height: 20px;
    color: #595959;
    background: #f0f0f0;
    border: 0 none;
    border-radius: 4px;
  }
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
