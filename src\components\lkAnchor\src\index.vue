<template>
  <Transition
    leave-active-class="animate__animated animate__fadeOut animate__faster"
    enter-active-class="animate__animated animate__fadeIn animate__faster"
  >
    <!-- eslint-disable-next-line vue/require-toggle-inside-transition -->
    <div
      v-if="!isMultiple"
      v-bind="$attrs"
      class="anchor-icon"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <i class="iconfont link-form-list" />
    </div>
    <slot v-else name="icon" />
  </Transition>
  <Transition
    leave-active-class="animate__animated animate__fadeOut animate__faster"
    enter-active-class="animate__animated animate__fadeIn animate__faster"
  >
    <div
      v-if="anchorVisible"
      v-bind="$attrs"
      class="anchor-list-container"
      @mouseenter.stop="handleMouseEnterChild"
      @mouseleave="handleMouseLeaveChild"
    >
      <div class="anchor-list-header">{{ t("trade_common_anchorTip") }}</div>
      <div class="anchor-list">
        <el-scrollbar ref="scrollbarRef" max-height="400px">
          <div
            v-for="item in anchorList"
            :key="item.ref"
            class="anchor-li"
            :class="{
              active: item.name === anchorActive,
              'achor-li-line': item.state
            }"
            @click="handleClickAnchor(item)"
          >
            <ReText
              type="info"
              :tippyProps="{ delay: 50000 }"
              class="anchor-li-text"
            >
              {{ t(item.title) }}
            </ReText>
            <span
              v-if="item.stateText"
              :class="[
                'anchor-li-state',
                item.state ? 'anchor-li-state-editable' : ''
              ]"
            >
              {{ t(item.stateText) }}
            </span>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </Transition>
</template>
<script lang="ts" setup>
import { onUnmounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";

const { t } = useI18n();
const anchorActive = ref<string>("");
const anchorVisible = ref<boolean>(false);
const delayedTimerRef = ref<any>();

const props = defineProps({
  anchorList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  isMultiple: {
    type: Boolean,
    default: false
  },
  container: {
    type: Object as PropType<any>,
    default: () => {}
  }
});
const emit = defineEmits(["mouseLeave"]);

const handleClickAnchor = (anchor: any) => {
  anchorActive.value = anchor.name;
  const element = document.querySelector(`.${anchor.ref}`);
  if (element) {
    if (props.container) {
      const containerElement = props.container;
      const hasVerticalScroll =
        containerElement.scrollHeight > containerElement.clientHeight;

      // 只有当容器有滚动条时才执行滚动
      if (hasVerticalScroll) {
        element.scrollIntoView({ block: "start", behavior: "smooth" });
      }
    } else {
      element.scrollIntoView({ block: "start", behavior: "smooth" });
    }
  }
};

const handleMouseEnter = () => {
  if (delayedTimerRef.value) {
    clearTimeout(delayedTimerRef.value);
  }
  anchorVisible.value = true;
};

const handleMouseLeave = () => {
  delayedTimerRef.value = setTimeout(() => {
    anchorVisible.value = false;
  }, 0);
  emit("mouseLeave", false);
};

const handleMouseEnterChild = () => {
  if (delayedTimerRef.value) {
    clearTimeout(delayedTimerRef.value);
  }
};
const handleMouseLeaveChild = () => {
  delayedTimerRef.value = setTimeout(() => {
    anchorVisible.value = false;
  }, 0);
  emit("mouseLeave", false);
};

onUnmounted(() => {
  clearTimeout(delayedTimerRef.value);
});

watch(
  () => props.anchorList,
  () => {
    if (props.anchorList && props.anchorList.length > 0) {
      anchorActive.value = props.anchorList[0].name;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  anchorVisible,
  handleMouseEnter,
  handleMouseLeave
});
</script>
<style lang="scss" scoped>
.anchor-icon {
  position: absolute;
  top: 200px;
  right: 0;
  z-index: 2003;
  width: 48px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  cursor: pointer;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 17px 0 rgb(0 0 0 / 24%);
}

.anchor-list-container {
  position: absolute;
  top: 200px;
  right: 0;
  z-index: 2003;
  width: 400px !important;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 21px 0 rgb(0 0 0 / 13%);

  .anchor-list-header {
    margin: 15px;
    font-size: 14px;
    line-height: 20px;
    color: #bfbfbf;
  }

  .anchor-list {
    margin-bottom: 10px;

    .anchor-li {
      display: flex;
      align-items: center;
      height: 42px;
      padding: 0 16px;
      line-height: 42px;
      cursor: pointer;

      &:hover {
        background: #f2f2f2;
      }

      &.active {
        background: #ebf4ff;

        &::before {
          background: #262626;
        }
      }

      &::before {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 12px;
        vertical-align: middle;
        content: "";
        background: #d8d8d8;
        border-radius: 50%;
      }

      &.achor-li-line {
        width: 400px;
      }

      .anchor-li-text {
        flex: 1;
        font-size: 14px;
        color: #262626;
      }

      .anchor-li-state {
        display: inline-block;
        height: 20px;
        padding: 0 6px;
        margin-left: 8px;
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        color: #808080;
        background: #e2e2e2;
        border-radius: 12px;

        &.anchor-li-state-editable {
          color: #2082ed;
          background: #d8eff9;
        }
      }
    }
  }
}
</style>
