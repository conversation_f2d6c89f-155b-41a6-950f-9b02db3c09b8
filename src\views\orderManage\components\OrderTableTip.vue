<template>
  <div class="vxe-tag-container">
    <div class="vxe-tag-col">
      <span class="vxe-tag-col-title primary">R</span>
      <span class="vxe-tag-col-desc">
        {{ t("trade_common_repeat_orders") }}
      </span>
    </div>
    <div class="vxe-tag-col">
      <span class="vxe-tag-col-title warn">S</span>
      <span class="vxe-tag-col-desc">
        {{ t("trade_order_MutualIdentification") }}
      </span>
    </div>
    <div class="vxe-tag-col">
      <span class="vxe-tag-col-title">
        <i class="iconfont link-info" />
      </span>
      <span class="vxe-tag-col-desc">
        {{ t("trade_common_moreMsg") }}
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
