<template>
  <div class="linkIncrease-login-language">
    <FontIcon icon="link-system-language" />
    <el-select
      v-model="langValue"
      placeholder="Select"
      placement="bottom-end"
      @change="changeSystemLanguage"
    >
      <el-option
        v-for="item in langOptions"
        :key="item.value"
        :label="t(item.label)"
        :value="item.value"
      />
    </el-select>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import { storageLocal } from "@pureadmin/utils";
import { useI18n } from "vue-i18n";
import { emitter } from "@/utils/mitt";

interface langProp {
  value?: string;
  label?: string;
}

const { t } = useI18n();
const { translationCh, translationEn } = useTranslationLang();
const langOptions = ref<langProp[]>([]);
const langValue = ref<string>("system");

const changeSystemLanguage = (val): void => {
  storageLocal().setItem("translationLang", val);
  langValue.value = val;
  if (val === "system") {
    navigator.language.indexOf("zh") > -1 ? translationCh() : translationEn();
  } else {
    val.indexOf("zh") > -1 ? translationCh() : translationEn();
  }
  emitter.emit("changeLanguage", val);
};

onMounted(() => {
  langOptions.value = [
    {
      value: "system",
      label: "trade_common_followSystem"
    },
    {
      value: "zh",
      label: "简体中文"
    },
    {
      value: "en",
      label: "English"
    }
  ];
  const translationLang = storageLocal().getItem("translationLang");
  //@ts-ignore
  langValue.value = translationLang ? translationLang : "system";
  changeSystemLanguage(langValue.value);
});
</script>
<style lang="scss" scoped>
.linkIncrease-login-language {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .iconfont {
    font-size: 14px;
    color: #262626;
    vertical-align: text-top;
  }

  ::v-deep(.el-select) {
    width: 93px !important;

    .el-select__wrapper {
      padding-left: 5px;
      background: transparent;
      border: 0 none;
      box-shadow: 0 0 0 0 !important;

      .el-select__selected-item {
        color: #262626;
      }

      .el-select__suffix {
        i {
          color: #262626;
        }
      }
    }
  }
}
</style>
