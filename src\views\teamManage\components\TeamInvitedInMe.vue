<template>
  <div class="team-module-area-body">
    <el-form
      ref="FormRef"
      label-position="top"
      label-width="auto"
      :model="form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_inputSearch')}：`">
            <el-input
              v-model="form.keyword"
              clearable
              :placeholder="t('trade_common_searchTeamInfo')"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="`${t('trade_join_other_sccs')}：`">
            <el-select-v2
              v-model="form.sccsId"
              filterable
              :options="sccsList"
              clearable
              :placeholder="t('trade_common_selectText')"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="margin-top: 30px">
            <el-button @click="resetForm">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchTable"
              >{{ t("trade_common_query") }}</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="tableData"
      :border="true"
      stripe
      :tooltip-options="{ showAfter: 500 }"
      max-height="calc(100vh - 280px)"
    >
      <el-table-column
        :label="t('trade_team_teamName')"
        width="255"
        prop="teamName"
        show-overflow-tooltip
      />
      <el-table-column
        :label="t('trade_team_teamShortName')"
        prop="teamShortName"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column :label="t('trade_join_other_sccs')" prop="sccsList">
        <template #default="{ row }">
          <div class="tag-row">
            <div
              v-for="(item, index) in row.sccsList"
              :key="index"
              class="tag-row-item"
            >
              <el-tooltip
                :content="item.sccsName"
                :show-after="500"
                placement="top"
              >
                <el-tag
                  :class="[
                    'tag-col',
                    item.manager && item.owner
                      ? 'tag-owner'
                      : item.manager
                        ? 'tag-manager'
                        : ''
                  ]"
                  >{{ item.sccsName }}</el-tag
                >
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <LkPagination
      ref="LkPaginationRef"
      :total="total"
      @updatePagination="handleSearchTable"
    />
    <InviteTeamDialog
      ref="inviteTeamDialogRef"
      @inviteSuccess="handleSearchTable"
    />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import LkPagination from "@/components/lkPagination/index";
import { message } from "@/utils/message";
import InviteTeamDialog from "@/views/createTeam/component/inviteTeamDialog.vue";
import { teamCooperationCache } from "@/utils/cooperatioTeam";
import { inviteTeamRecord, deleteInviteTeam, getSccsList } from "@/api/team";

const { t } = useI18n();
const LkPaginationRef = ref<any>(null);
const FormRef = ref<any>(null);
let tableData = ref<any[]>([]);
let sccsList = ref<any[]>([]);
let total = ref<number>(0);
const inviteTeamDialogRef = ref<any>(null);
const form = reactive({
  sccsId: "",
  keyword: "",
  status: ""
});

const resetForm = () => {
  form.sccsId = "";
  form.keyword = "";
  handleSearchTable();
};

// const joinStatusEnum = [
//   {
//     label: "等待对方处理",
//     value: "TO_ACCEPT"
//   },
//   {
//     label: "已接受",
//     value: "ACCEPT"
//   },
//   {
//     label: "已拒绝",
//     value: "REJECT"
//   }
// ];

const handleSearchTable = async (): Promise<void> => {
  const pageParams = LkPaginationRef.value.pageParams;
  //@ts-ignore
  const { id: teamId } = await teamCooperationCache.currentlyUsedTeam();
  const res = await inviteTeamRecord({
    invitedTeamId: teamId,
    pageSize: pageParams.pageSize,
    pageNo: pageParams.pageNo,
    status: "",
    ...form
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
};

const handleDeleteTable = async (id): Promise<void> => {
  const res = await deleteInviteTeam({ id: id });
  if (res.code === 0) {
    message(t("trade_common_deleteSuccess"), {
      customClass: "el",
      type: "success"
    });
    handleSearchTable();
  }
};

const handleCreateTeamRole = (): void => {
  inviteTeamDialogRef.value.open();
};

const init = (): void => {
  Promise.all([getSccsList({ sccsReqEnum: "INVITED_ME" })]).then(res => {
    sccsList.value = res[0].data;
    handleSearchTable();
  });
};

onMounted(() => {
  init();
});

defineExpose({
  handleCreateTeamRole
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.team-module-area-body {
  margin-top: 0 !important;

  .team-module-area-btn {
    position: absolute;
    top: 0;
    right: 0;
  }
}

.tag-row {
  display: flex;
  flex-flow: row wrap;
  width: 100%;
  white-space: pre-wrap;

  .tag-row-item {
    width: fit-content;
    max-width: 100%;
  }
}

.tag-col {
  max-width: 100%;
  padding: 3px 6px;
  margin-right: 4px;
  overflow: hidden;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;

  ::v-deep(.el-tag__content) {
    max-width: 100%;
    overflow: hidden;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
