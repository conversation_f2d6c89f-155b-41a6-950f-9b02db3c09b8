<template>
  <el-form
    ref="ruleFormRef"
    :model="userInfoForm"
    label-width="auto"
    label-position="top"
    :rules="rules"
    size="large"
  >
    <el-form-item :label="t('trade_login_email')" required prop="username">
      <el-input
        v-model="userInfoForm.username"
        :placeholder="t('trade_login_mailText')"
        clearable
        @change="handleWatchUserName(userInfoForm)"
      />
    </el-form-item>
    <el-form-item
      class="user-login-code-container"
      :label="t('trade_common_captcha')"
      required
      prop="mailCode"
    >
      <div class="user-login-code-mailCode-container">
        <el-input
          v-model="userInfoForm.mailCode"
          :placeholder="t('trade_common_inputCaptcha')"
          maxlength="6"
          clearable
        />
      </div>
      <el-button
        v-show="!beginCountDown"
        type="primary"
        class="send-verify-code-btn"
        @click="sendVerifyCode"
      >
        {{ t("trade_common_getCaptcha") }}
      </el-button>
      <el-button
        v-show="beginCountDown"
        class="count-down-body"
        type="primary"
        disabled
        color="#edf1fd"
      >
        <el-countdown
          :value="timeCode"
          format="ss"
          suffix="秒后重新获取"
          @finish="countDownFinish"
        />
      </el-button>
    </el-form-item>
    <el-text class="user-login-code-mailCode-text">
      {{ t("trade_login_emailCodeTip") }}
    </el-text>
    <el-form-item class="user-login-btn-container">
      <UserError :respMessage="respMessage" :loginForm="userInfoForm" />
      <el-button
        v-if="!props.mailCodeType || props.mailCodeType === 'TRADE_USER_LOGIN'"
        class="user-login-button"
        type="primary"
        @click="onSubmit(ruleFormRef)"
      >
        {{ t("trade_common_login") }}
      </el-button>
      <slot name="default" />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, watch, inject, onMounted } from "vue";
import { MailLoginProp, VerifyCodeProp } from "@/api/type";
import { useI18n } from "vue-i18n";
import type { FormInstance } from "element-plus";
import { getSendVerifyCode, mailLogin } from "@/api/login";
import { LoginSuccessResponseDataProp } from "../utils/type.d";
import type { FormRules } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { isEmail } from "@pureadmin/utils";
import UserError from "./UserError.vue";

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const userInfoForm = reactive<MailLoginProp>({
  username: route.query.username ? (route.query.username as any) : "",
  mailCode: "",
  //@ts-ignore
  userOs: "",
  userAgent: "",
  userDevice: ""
});

const validateLength = (rule: any, value: any, callback: any) => {
  if (value.length === 6) {
    callback();
  } else {
    callback(new Error(t("trade_users_mail_code_is_not_correct")));
  }
};

const rules = reactive<FormRules<any>>({
  username: [
    {
      required: true,
      message: `${t("trade_login_mailText")}`,
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        if (!isEmail(value)) {
          callback(new Error(t("trade_login_truemail")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: t("trade_login_passwordPlaceholder"),
      trigger: "blur"
    }
  ],
  //@ts-ignore
  mailCode: [
    {
      required: true,
      message: t("trade_common_inputCaptcha"),
      trigger: "blur"
    },
    { validator: validateLength, trigger: "blur" }
  ]
});

const ruleFormRef = ref<FormInstance>();
const timeCode = ref<number>(Date.now() + 1000 * 60);
const beginCountDown = ref<boolean>(false);
const respMessage = ref<string>("");

const props = defineProps({
  mailCodeType: {
    type: String as PropType<string | null>,
    default: "TRADE_USER_LOGIN"
  }
});

const emit = defineEmits<{
  (e: "handleLoginSuccess", data: LoginSuccessResponseDataProp): void;
}>();

const onSubmit = (formEl: FormInstance | undefined): void => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      const response = await mailLogin(userInfoForm);
      if (response.code !== 0) {
        respMessage.value = response.msg;
      } else {
        emit("handleLoginSuccess", response.data);
      }
    }
  });
};

const countDownFinish = (): void => {
  beginCountDown.value = false;
};

const sendVerifyCode = (): void => {
  ruleFormRef.value.validateField("username", async valid => {
    if (valid) {
      const sendVerify: VerifyCodeProp = {
        email: userInfoForm.username,
        mailCodeType: props.mailCodeType
      };
      const response = await getSendVerifyCode(sendVerify);
      if (response.code !== 0) {
        respMessage.value = response.msg;
      } else {
        beginCountDown.value = true;
        timeCode.value = Date.now() + 1000 * 60;
      }
    }
  });
};

watch(
  () => userInfoForm,
  () => {
    respMessage.value = "";
  },
  { deep: true }
);

const setResponseMessage = (msg: string): void => {
  respMessage.value = msg;
};

onMounted(() => {
  userInfoForm.username = sessionStorage.getItem("username");
  userInfoForm.username = route.query.email as string;
});

const handleWatchUserName = inject(
  "handleWatchUserName",
  (userInfo: any) => {}
);

defineExpose({
  ruleFormRef,
  userInfoForm,
  setResponseMessage
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
