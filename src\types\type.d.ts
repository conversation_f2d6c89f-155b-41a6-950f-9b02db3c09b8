export type PageProp = {
  pageNo: number;
  pageSize: number;
  sortBy?: string | null;
  descending?: boolean | null;
  createTime?: string | null;
};

export interface TableOperateProp {
  icon: string;
  title: string;
  slotName: string;
  iconClassName?: string;
  fieldConditions?: any[];
  conditionFunction?: any;
  permission?: string[];
}

export interface MessageBoxConfrimProp {
  messageBoxTip: string;
  messageBoxTitle: string;
  messageBoxTipArray: string[] | any;
}

export interface TableOperateResultProp {
  slot: string;
  row: any;
}
