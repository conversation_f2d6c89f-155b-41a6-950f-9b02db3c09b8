import dayjs from "dayjs";
import WidgetExchangeRate from "@/components/lkWidgetForm/src/lkExchangeRate.vue";
import OrderFilePreview from "@/views/orderManage/components/OrderFilePreview.vue";
import LkAvaterGroup from "@/components/lkAvatarGroup";
import LkAvatar from "@/components/lkAvatar";
import { transformI18n } from "@/plugins/i18n";
import { cloneDeep, storageSession } from "@pureadmin/utils";
import { getSccsFieldVisibleConditions } from "@/api/common";
import { formatNumberValueOf, formatThousandNumber } from "./common";
import { emitter } from "./mitt";

/**
 * 1.接口数据转化成表单需要的值
 * @param widgetList 控件值
 * @param subWidgetMap 子表控件的值
 * @param linkedReferenceMap 关联引用控件的值
 * @returns
 */
export const TransformSubmitDataStructure = (
  widgetList: any,
  subWidgetMap: any,
  linkedReferenceMap?: any
): any => {
  let widgetFormData = [];
  if (!widgetList) return;
  for (let i = 0, len = widgetList.length; i < len; i++) {
    const widgetData = widgetList[i];
    if (!widgetData.parentWidgetId) {
      if (!["TABLE_FORM", "DR_RELATE_CARD"].includes(widgetData.widgetType)) {
        const index = widgetFormData.findIndex(
          widgetItem => widgetItem.widgetId === widgetData.widgetId
        );
        if (index === -1) {
          widgetFormData.push({
            label: widgetData.label,
            obj: getWidgetBindValue(widgetData),
            showObj: widgetData.showObj,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId
          });
        } else {
          widgetFormData.splice(index, 1, {
            label: widgetData.label,
            obj: getWidgetBindValue(widgetData),
            showObj: widgetData.showObj,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId
          });
        }
      } else if (widgetData.widgetType === "DR_RELATE_CARD") {
        if (
          linkedReferenceMap &&
          linkedReferenceMap.hasOwnProperty(widgetData.widgetId)
        ) {
          const linkedReferenceMapData = linkedReferenceMap[
            widgetData.widgetId
          ].map(linkReference => {
            if (linkReference.widgetType === "TABLE_FORM") {
              const subTableAssemblyData = Object.values(
                subWidgetMap[linkReference.widgetId].reduce((res, item) => {
                  res[item.rowIndex]
                    ? res[item.rowIndex].push(item)
                    : (res[item.rowIndex] = [item]);
                  return res;
                }, {})
              );
              subTableAssemblyData.forEach((subTableRowData: any) => {
                subTableRowData.forEach(subTableChildRowData =>
                  getWidgetBindValue(subTableChildRowData)
                );
              });
              return Object.assign(linkReference, {
                childrenList: subTableAssemblyData
              });
            } else {
              return linkReference;
            }
          });

          const index = widgetFormData.findIndex(
            widgetItem => widgetItem.widgetId === widgetData.widgetId
          );
          if (index === -1) {
            widgetFormData.push({
              label: null,
              obj: linkedReferenceMapData,
              formId: widgetData.formId,
              widgetId: widgetData.widgetId
            });
          } else {
            widgetFormData.splice(index, 1, {
              label: null,
              obj: linkedReferenceMapData,
              formId: widgetData.formId,
              widgetId: widgetData.widgetId
            });
          }
        }
      } else {
        if (subWidgetMap[widgetData.widgetId]) {
          const subTableAssemblyData = Object.values(
            subWidgetMap[widgetData.widgetId].reduce((res, item) => {
              res[item.rowIndex]
                ? res[item.rowIndex].push(item)
                : (res[item.rowIndex] = [item]);
              return res;
            }, {})
          );

          subTableAssemblyData.forEach((subTableRowData: any) => {
            subTableRowData.forEach(subTableChildRowData =>
              getWidgetBindValue(subTableChildRowData)
            );
          });
          widgetFormData.push({
            label: null,
            obj: null,
            formId: widgetData.formId,
            widgetId: widgetData.widgetId,
            childrenList: subTableAssemblyData
          });
        }
      }
    }
  }
  return widgetFormData;
};

/**
 * 根据表单类型转化控件值
 * @param widget
 * @returns
 */
export const getWidgetBindValue = (widget: any) => {
  if (widget.widgetType === "DATE_PICKER") {
    return matchDateFormatStr(widget.label, widget.obj);
  } else {
    if (typeof widget.obj === "string") {
      return widget.obj.replace(/\\/g, "&&amp;");
    }
    return widget.obj;
  }
};

/**
 * 日期时间控件：前端传递的是时间戳，但是后端返回的是字符串，前端在传递进入控件之前，需要进行类型转化
 * @param valData
 * @param timeStamp
 * @returns
 */
export const matchDateFormatStr = (valData, timeStamp) => {
  if (!valData) return "";
  const dateFormatAll = valData.split(" ").length > 1;
  if (dateFormatAll) {
    return dayjs(new Date(timeStamp)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    if (valData.split(" ")[0].split("-").length === 1) {
      return dayjs(new Date(timeStamp)).format("YYYY");
    } else {
      return dayjs(new Date(timeStamp)).format("YYYY-MM-DD");
    }
  }
};

/**
 * 获取表单全部控件
 * @param widgetJsonList 控件集合
 * @returns
 */
export const obtainFormAllList = (widgetJsonList: any) => {
  let widgetList = [];
  for (let widget of widgetJsonList) {
    if (widget.type === "DrCard") {
      for (let widgetChild of widget.children) {
        if (widgetChild.children) {
          for (let widgetChilds of widgetChild.children) {
            widgetList.push(widgetChilds);
          }
        }
      }
    } else {
      widgetList.push(widget);
    }
  }
  return widgetList;
};

/**
 * 初始化表单数据
 * @param formWidgets 表单控件集合
 * @param widgetDefaultData  表单控件绑定的数据集合
 * @returns
 */
export const initFormModalData = (formWidgets: any[], formDefaultData: any) => {
  let widgetObject = {};
  let widgetJsonList = obtainFormAllList(formWidgets);
  for (let widget of widgetJsonList) {
    if (widget.type === "DrDivider") {
      continue;
    } else if (widget.type === "DrDrFormulas") {
      // todo 调用计算逻辑直接进行运算
    } else if (widget.type === "DrTableForm") {
      const widgetItem = formDefaultData.find(
        childData => childData.widgetId === widget._fc_id
      );
      const tableColumns = widget.children
        .slice(1)
        .map(tableWidget => tableWidget.children[0]);

      let tableWidgetData = widgetItem?.childrenList;
      if (
        widgetItem?.childrenList instanceof Array &&
        widgetItem?.childrenList.length > 0
      ) {
        tableWidgetData = widgetItem?.childrenList.map(trWidgetData => {
          return trWidgetData.map(tdWidgetData => {
            if (
              tdWidgetData.widgetType === "INPUT_NUMBER" ||
              tdWidgetData.widgetType === "PERCENT"
            ) {
              const tdColumn = tableColumns.find(
                column => column._fc_id === tdWidgetData.widgetId
              );
              const widgetObjectValue = tdColumn.props.useThousandSeparator
                ? formatThousandNumber(tdWidgetData.obj)
                : formatNumberValueOf(tdWidgetData.obj);
              return Object.assign(tdWidgetData, {
                label: widgetObjectValue
              });
            } else {
              return tdWidgetData;
            }
          });
        });
      }

      widgetObject[widget._fc_id] = tableWidgetData;
    } else {
      widgetObject[widget._fc_id] = obtainWidgetRealValue(
        widget,
        formDefaultData
      );
    }
  }
  return widgetObject;
};

export const obtainWidgetRealValue = (widget: any, FormDefaultData: any) => {
  // 1.整个表单控件没有值的情况
  if (FormDefaultData.length === 0) {
    if (widget.type === "DrCheckbox") {
      return [];
    } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
      return null;
    } else if (widget.type === "DrAddress") {
      return { areaCodeList: [], address: "" };
    } else if (widget.type === "DrExchangeRates") {
      return {};
    } else {
      return "";
    }
  } else {
    const widgetItem = FormDefaultData.find(
      widgetItem => widgetItem.widgetId === widget._fc_id
    );
    if (!widgetItem) {
      if (widget.type === "DrCheckbox") {
        return [];
      } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
        return null;
      } else if (widget.type === "DrAddress") {
        return { areaCodeList: [], address: "" };
      } else if (widget.type === "DrExchangeRates") {
        return {};
      } else {
        return "";
      }
    } else {
      if (widget.type === "DrRate") {
        return widget.props?.starCount > widgetItem.obj
          ? widgetItem.obj
          : widget.props?.starCount;
      } else if (widget.type === "DrInputNumber") {
        return widgetItem.obj;
      } else if (widget.type === "DrCheckbox") {
        return widgetItem.obj && widgetItem.obj instanceof Array
          ? widgetItem.obj
          : [];
      } else if (widget.type === "DrRadio") {
        return widgetItem.obj ? widgetItem.obj : "";
      } else if (widget.type === "DrAddress") {
        return widgetItem.obj
          ? widgetItem.obj
          : { areaCodeList: [], address: "" };
      } else if (widget.type === "DrExchangeRates") {
        return widgetItem.obj ? widgetItem.obj : {};
      } else if (widget.type === "DrFormulas") {
        return widgetItem.obj;
        // return widgetItem.obj ? widgetItem.obj : {};
      } else if (
        ["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(widget.type)
      ) {
        return widgetItem.obj ? widgetItem.obj : {};
      } else {
        return widgetItem.obj;
      }
    }
  }
};

export const handleGetWidgetCompletedData = (
  widgetData,
  formWidgets,
  widgetObject
) => {
  const formWidgetList = formWidgets.filter(
    formWidget => formWidget.type === "DrRate"
  );
  formWidgetList.forEach(formWidget => {
    const widgetDataChild = widgetData.find(
      widgetDataItem => widgetDataItem.widgetId === formWidget["_fc_id"]
    );
    if (widgetDataChild) {
      widgetDataChild.label = widgetObject[formWidget["_fc_id"]];
      widgetDataChild.obj = widgetObject[formWidget["_fc_id"]];
    }
  });
  return widgetData;
};

/**
 * 评分-表格渲染模版
 * @param rateRow
 * @returns
 */
const rateSlot = rateRow => {
  return rateRow !== undefined && rateRow !== null ? (
    <el-rate v-model={rateRow} disabled size="small" max={Number(rateRow)} />
  ) : (
    ""
  );
};

/**
 * 富文本-表格渲染模版
 * @param rateRow
 * @returns
 */
export const richTextSlot = richRow => {
  return richRow ? (
    <div class="vxe-table-cell-text" v-html={richRow}></div>
  ) : (
    ""
  );
};

/**
 * 汇率-表格渲染模版
 * @param rateRow
 * @returns
 */
const exchangeRateSlot = rateRow => {
  return rateRow ? <WidgetExchangeRate widgetData={rateRow} /> : "";
};

/**
 * 文本-表格渲染模版
 * @param rateRow
 * @returns
 */
export const textSlot = (textRow, mode) => {
  return textRow && mode ? (
    mode === "PERCENT" ? (
      <div class="vxe-table-cell-text">{textRow}%</div>
    ) : (
      <div class="vxe-table-cell-text">{textRow}</div>
    )
  ) : (
    ""
  );
};

/**
 * 文件-表格渲染模版
 * @param rateRow
 * @returns
 */
export const fileSlot = fileRow => {
  return fileRow instanceof Array
    ? fileRow.map(fileItem => <OrderFilePreview filePreview={fileItem} />)
    : "";
};

/**
 * 图片-表格渲染模版
 * @param rateRow
 * @returns
 */
const imageSlot = imageRow => {
  return imageRow instanceof Array
    ? imageRow.map(item => (
        <el-image
          class="vxe-table-image-upload"
          src={item.url}
          zoom-rate={1.2}
          MAX-scale={7}
          MIN-scale={0.2}
          preview-src-list={[item.url]}
          initial-index={0}
          fit="contain"
        />
      ))
    : "";
};

/**
 * 签名-表格渲染模版
 * @param rateRow
 * @returns
 */
const signatureSlot = signRow => {
  return signRow ? (
    <el-image
      class="vxe-table-image-upload"
      src={signRow}
      zoom-rate={1.2}
      MAX-scale={7}
      MIN-scale={0.2}
      preview-src-list={[signRow]}
      initial-index={0}
      fit="contain"
    />
  ) : (
    ""
  );
};

/**
 * 成员组-表格渲染模版
 * @param rateRow
 * @returns
 */
export const memberSlot = data => {
  return data?.length > 0 ? (
    <div>
      <LkAvaterGroup size={24} avatarList={data} maxAvatar="4" />
    </div>
  ) : (
    ""
  );
};

/**
 * 成员组-表格渲染模版
 * @param rateRow
 * @returns
 */
export const memberTimeSlot = (data, timeStamp, boolean, state) => {
  return (
    <div>
      <LkAvaterGroup size={24} avatarList={data} maxAvatar="4" />
      <div class="vxe-table-column-date-tip">
        {boolean ? (
          dayjs(new Date(timeStamp)).format("YYYY-MM-DD HH:mm:ss")
        ) : state === "reply" ? (
          <span class="milestone-reply-form-span-text milestone-reply-form-span-red-text">
            未批复
          </span>
        ) : (
          <span class="milestone-reply-form-span-text milestone-reply-form-span-red-text">
            未采集
          </span>
        )}
      </div>
    </div>
  );
};

/**
 * 成员-表格渲染模版
 * @param rateRow
 * @returns
 */
export const manageSlot = (avatar, username, email) => {
  return username ? (
    <div class="vxe-table-column-date-main">
      <el-popover
        placement="bottom"
        v-slots={avatarPopover(avatar, username, email)}
      ></el-popover>
    </div>
  ) : (
    ""
  );
};

/**
 * 单选，多选-表格渲染模版
 * @param rateRow
 * @returns
 */
const checkBoxSlot = (data, mode) => {
  let html = "";
  if (data) {
    data.split(",").forEach(widget => {
      html += widget;
    });
  }
  if (html) {
    return mode === "CHECKBOX" ? (
      <div class="vxe-table-column-checkbox-main" v-html={html} />
    ) : (
      <div class="vxe-table-column-radio-main" v-html={html} />
    );
  }
  // return data ? <ColumnJSXTemplate jsxTemplate={html} /> : "";
};

const avatarPopover = (avatar: string, username: string, email?: string) => {
  return {
    reference: () => {
      return (
        <LkAvatar
          size={24}
          teamInfo={{
            avatar: avatar,
            username: username,
            email: email
          }}
        />
      );
    },
    default: () => {
      return (
        <div class="vxe-table-column-avatar-main">
          <LkAvatar
            size={34}
            teamInfo={{
              avatar: avatar,
              username: username,
              email: email
            }}
          />
          <span class="vxe-table-column-avatar-tip">{username}</span>
        </div>
      );
    }
  };
};

/**
 * 创建/编辑用户模版-表格渲染模版
 * @param rateRow
 * @returns
 */
export const userSlot = (avatar, username, updateTime, email) => {
  return updateTime ? (
    <div class="vxe-table-column-date-main">
      <el-popover
        placement="bottom"
        v-slots={avatarPopover(avatar, username, email)}
      ></el-popover>
      <div class="vxe-table-column-date-tip">
        {dayjs(new Date(updateTime)).format("YYYY-MM-DD HH:mm:ss")}
      </div>
    </div>
  ) : (
    ""
  );
};

export const handleRenderWidget = (widgetType, row, name, config) => {
  let memberList = [];
  let coopList = [];
  let exchangeRate = {};

  if (row.widgetList || row.originalData) {
    const widgetShowObj =
      row.widgetList?.find(widget => widget.widgetId === name) ||
      row.originalData?.find(widget => widget.widgetId === name);

    if (
      widgetShowObj &&
      widgetShowObj.showObj &&
      widgetShowObj.showObj instanceof Array
    ) {
      widgetShowObj.showObj.forEach(member => {
        memberList.push({
          user: true,
          coop: false,
          edited: false,
          userAvatar: member.avatar,
          userName: member.name,
          email: member.email
        });
      });
      widgetShowObj.showObj.forEach(member => {
        coopList.push({
          user: false,
          coop: false,
          edited: false,
          teamAvatar: member.avatar,
          teamName: member.name,
          shortName: member.shortName
        });
      });
    }
    if (widgetType === "EXCHANGE_RATE") {
      exchangeRate = widgetShowObj?.obj;
    }
  }

  let result;
  switch (widgetType) {
    case "RATE":
      result = rateSlot(row[name]);
      break;
    case "RICH_TEXT":
      result = richTextSlot(row[name]);
      break;
    case "EXCHANGE_RATE":
      result = exchangeRateSlot(exchangeRate);
      break;
    case "PERCENT":
      result = textSlot(row[name], "PERCENT");
      break;
    case "FILE_UPLOAD":
      result = fileSlot(row[name]);
      break;
    case "IMAGE_UPLOAD":
      result = imageSlot(row[name]);
      break;
    case "SIGNATURE":
      result = signatureSlot(row[name]);
      break;
    case "MEMBER":
      result = memberSlot(memberList);
      break;
    case "MULTIPLE_MEMBER":
      result = memberSlot(memberList);
      break;
    case "COOP_TEAM":
      result = memberSlot(coopList);
      break;
    case "MULTIPLE_COOP_TEAM":
      result = memberSlot(coopList);
    case "CHECKBOX":
      result = checkBoxSlot(row[name], "CHECKBOX");
      break;
    case "RADIO":
      result = checkBoxSlot(row[name], "RADIO");
      break;
    case "TABLE_FORM":
      result = (
        <el-button
          type="text"
          onClick={() => {
            emitter.emit("viewSubTableInEmailCoop", {
              fieldName: config.name,
              childColumns: config.childrenList,
              rowId: row.id
            });
          }}
        >
          查看
        </el-button>
      );
      break;
    default:
      result = textSlot(row[name], "TEXT");
  }
  if (result) {
    return result;
  } else {
    return textSlot(row[name], "TEXT");
  }
};

/**
 * 表单校验方法
 * @param required
 * @param widgetConfig
 * @returns
 */
export const getWidgetRules = (
  required: string | boolean,
  widgetConfig: any
) => {
  // 输入框，数字框，多行文本，百分比，单选校验逻辑
  if (
    [
      "DrInput",
      "DrInputNumber",
      "DrRate",
      "DrTextarea",
      "DrRadio",
      "DrLocation",
      "DrDatePicker",
      "DrPercentage",
      "DrSignature"
    ].includes(widgetConfig.type)
  ) {
    if (required) {
      return {
        validator: (rule, value, callback) => {
          if (required) {
            if (!!value) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : transformI18n("trade_common_CannotEmpty")
              );
            }
          } else {
            callback();
          }
        },
        trigger: ["blur", "change"]
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        },
        trigger: ["blur", "change"]
      };
    }
  } else if (
    ["DrImagesUpload", "DrFilesUpload", "DrCheckbox"].includes(
      widgetConfig.type
    )
  ) {
    // 复选框, 图片上传，附件上传校验逻辑
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (required) {
            if (value.length > 0) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : transformI18n("trade_common_CannotEmpty")
              );
            }
          } else {
            callback();
          }
        }
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (widgetConfig.type === "DrAddress") {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (
            !value ||
            !value.areaCodeList ||
            (value.areaCodeList instanceof Array &&
              value.areaCodeList.length === 0) ||
            (widgetConfig.props.formatType === 1 && !value.address)
          ) {
            callback(
              typeof required !== "boolean"
                ? required
                : transformI18n("trade_common_CannotEmpty")
            );
          } else {
            callback();
          }
        },
        trigger: "change"
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (widgetConfig.type === "DrEditor") {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (value !== "<p><br></p>") {
            callback();
          } else {
            callback(
              typeof required !== "boolean"
                ? required
                : transformI18n("trade_common_CannotEmpty")
            );
          }
        },
        trigger: ["blur", "change"]
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (
    ["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(
      widgetConfig.type
    )
  ) {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (widgetConfig.props.multiple) {
            if (value instanceof Array && value.length > 0) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : transformI18n("trade_common_CannotEmpty")
              );
            }
          } else {
            if (!!value) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : transformI18n("trade_common_CannotEmpty")
              );
            }
          }
        },
        trigger: "change"
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (widgetConfig.type === "DrExchangeRates") {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          // if (widgetConfig.props.exchangeType === "fixed") {
          //   if (
          //     value &&
          //     value.rate &&
          //     value.fromMoney
          //   ) {
          //     callback();
          //   } else {
          //     callback(
          //       typeof required !== "boolean"
          //         ? required
          //         : transformI18n("trade_common_CannotEmpty")
          //     );
          //   }
          // } else {
          //   if (value && value.fromMoney) {
          //     callback();
          //   } else {
          //     callback(
          //       typeof required !== "boolean"
          //         ? required
          //         : transformI18n("trade_common_CannotEmpty")
          //     );
          //   }
          // }
          callback();
        },
        trigger: "change"
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (widgetConfig.type === "DrTableForm") {
    // 优化后的表格校验逻辑
    const getRequiredFields = () => {
      return (
        widgetConfig.children?.flatMap(col =>
          Array.isArray(col.children)
            ? col.children
                .filter(child => child.$required)
                .map(child => ({
                  widgetId: child._fc_id,
                  label: child.label
                }))
            : []
        ) || []
      );
    };

    const isCellEmpty = cell => {
      if (cell === undefined || cell === null || cell === "") {
        return true;
      }
      if (Array.isArray(cell) && cell.length === 0) {
        return true;
      }
      if (
        typeof cell === "object" &&
        Object.keys(cell).length === 0 &&
        Object.values(cell).every(
          item => item === undefined || item === null || item === ""
        )
      ) {
        return true;
      }
      return false;
    };

    const checkRowRequired = (row, requiredFields) => {
      if (!Array.isArray(row)) return true;
      return requiredFields.some(req => {
        const cell = row.find(cell => cell.widgetId === req.widgetId);
        const result = !cell || isCellEmpty(cell.obj);
        return result;
      });
    };

    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (!Array.isArray(value) || value.length === 0) {
            callback(transformI18n("trade_common_CannotEmpty"));
            return;
          }
          const requiredFields = getRequiredFields();
          const hasEmpty = value.some(row =>
            checkRowRequired(row, requiredFields)
          );
          if (hasEmpty) {
            callback(transformI18n("trade_common_CannotEmpty"));
          } else {
            callback();
          }
        },
        trigger: ["blur", "change"]
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          if (Array.isArray(value) && value.length > 0) {
            const requiredFields = getRequiredFields();
            const hasEmpty = value.some(row =>
              checkRowRequired(row, requiredFields)
            );
            if (hasEmpty) {
              callback(transformI18n("trade_common_CannotEmpty"));
              return;
            }
          }
          callback();
        },
        trigger: ["blur", "change"]
      };
    }
  }
};

/**
 * 表单校验方法
 * @param required
 * @param widgetConfig
 * @returns
 */
export const getWidgetTableRowRules = (
  required: string | boolean,
  widgetConfig: any,
  widgetValue: any
) => {
  // 输入框，数字框，多行文本，百分比，单选校验逻辑
  if (
    [
      "DrInput",
      "DrInputNumber",
      "DrRate",
      "DrTextarea",
      "DrRadio",
      "DrLocation",
      "DrDatePicker",
      "DrPercentage",
      "DrSignature"
    ].includes(widgetConfig.type)
  ) {
    if (required) {
      return {
        validator: (rule, value, callback) => {
          if (required) {
            if (!!widgetValue) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : widgetConfig.$required ||
                      transformI18n("trade_common_CannotEmpty")
              );
            }
          } else {
            callback();
          }
        },
        trigger: ["blur", "change"]
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        },
        trigger: ["blur", "change"]
      };
    }
  } else if (
    ["DrImagesUpload", "DrFilesUpload", "DrCheckbox"].includes(
      widgetConfig.type
    )
  ) {
    // 复选框, 图片上传，附件上传校验逻辑
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (required) {
            if (widgetValue.length > 0) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : transformI18n("trade_common_CannotEmpty")
              );
            }
          } else {
            callback();
          }
        }
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (widgetConfig.type === "DrAddress") {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (
            !widgetValue ||
            !widgetValue.areaCodeList ||
            (widgetValue.areaCodeList instanceof Array &&
              widgetValue.areaCodeList.length === 0) ||
            (widgetConfig.props.formatType === 1 && !widgetValue.address)
          ) {
            callback(
              typeof required !== "boolean"
                ? required
                : transformI18n("trade_common_CannotEmpty")
            );
          } else {
            callback();
          }
        },
        trigger: "change"
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (widgetConfig.type === "DrEditor") {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (widgetValue !== "<p><br></p>") {
            callback();
          } else {
            callback(
              typeof required !== "boolean"
                ? required
                : transformI18n("trade_common_CannotEmpty")
            );
          }
        },
        trigger: ["blur", "change"]
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (
    ["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(
      widgetConfig.type
    )
  ) {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          if (widgetConfig.props.multiple) {
            if (widgetValue instanceof Array && widgetValue.length > 0) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : transformI18n("trade_common_CannotEmpty")
              );
            }
          } else {
            if (!!widgetValue) {
              callback();
            } else {
              callback(
                typeof required !== "boolean"
                  ? required
                  : transformI18n("trade_common_CannotEmpty")
              );
            }
          }
        },
        trigger: "change"
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  } else if (widgetConfig.type === "DrExchangeRates") {
    if (widgetConfig.$required) {
      return {
        validator: (rule, value, callback) => {
          // if (widgetConfig.props.exchangeType === "fixed") {
          //   if (
          //     value &&
          //     value.rate &&
          //     value.fromMoney
          //   ) {
          //     callback();
          //   } else {
          //     callback(
          //       typeof required !== "boolean"
          //         ? required
          //         : transformI18n("trade_common_CannotEmpty")
          //     );
          //   }
          // } else {
          //   if (value && value.fromMoney) {
          //     callback();
          //   } else {
          //     callback(
          //       typeof required !== "boolean"
          //         ? required
          //         : transformI18n("trade_common_CannotEmpty")
          //     );
          //   }
          // }
          callback();
        },
        trigger: "change"
      };
    } else {
      return {
        validator: (rule, value, callback) => {
          callback();
        }
      };
    }
  }
};

function getUrlParams(): any {
  const reg = /(\w+)=([^&]+)/g;
  const params = {};
  let match;

  while ((match = reg.exec(window.location.href)) !== null) {
    params[match[1]] = match[2];
  }

  return params;
}

export const handleGetDynamicDefaultValue = (
  formWidgets: any[],
  widgetDefaultData: any
) => {
  let widgetObject = {};
  for (let i = 0, len = formWidgets.length; i < len; i++) {
    const widget = formWidgets[i];
    if (widget.type === "DrDivider") {
      continue;
    } else if (widget.type === "DrCard") {
      widget.children.forEach(widgetChild => {
        if (widgetChild.children) {
          widgetChild.children.forEach(widgetChildItem => {
            widgetObject[widgetChildItem._fc_id] =
              accordingToWidgetTypeGetDefaultValue(
                widgetChildItem,
                widgetDefaultData
              );
          });
        }
      });
    } else if (widget.type === "DrTableForm") {
      const widgetItem = widgetDefaultData.find(
        widgetChildData => widgetChildData.widgetId === widget._fc_id
      );
      widgetObject[widget._fc_id] = widgetItem?.childrenList;
    } else {
      widgetObject[widget._fc_id] = accordingToWidgetTypeGetDefaultValue(
        widget,
        widgetDefaultData
      );
    }
  }
  return widgetObject;
};

export const accordingToWidgetTypeGetDefaultValue = (
  widget: any,
  defaultData: any
) => {
  if (defaultData) {
    const widgetItem = defaultData.find(
      widgetItem => widgetItem.widgetId === widget._fc_id
    );
    if (widgetItem) {
      if (widget.type === "DrRate") {
        return widget.props?.starCount > widgetItem.obj
          ? widgetItem.obj
          : widget.props?.starCount;
      } else if (widget.type === "DrInputNumber") {
        if (!widget.props.min && !widget.props.max) {
          return widgetItem.obj;
        }
        if (widget.props.min && widget.props.min > widgetItem.obj) {
          return widget.props.min;
        }
        if (widget.props.max && widget.props.max < widgetItem.obj) {
          return widget.props.max;
        }
      } else if (widget.type === "DrCheckbox") {
        return widgetItem.obj && widgetItem.obj instanceof Array
          ? widgetItem.obj
          : [];
      } else if (widget.type === "DrRadio") {
        return widgetItem.obj ? widgetItem.obj : "";
      } else if (widget.type === "DrAddress") {
        return widgetItem.label ? widgetItem.label : "";
      } else if (widget.type === "DrExchangeRates") {
        return widgetItem.obj ? widgetItem.obj : {};
      } else if (
        ["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(widget.type)
      ) {
        return widgetItem.obj ? widgetItem.obj : {};
      } else {
        return widgetItem.obj;
      }
    } else {
      if (widget.type === "DrCheckbox") {
        return [];
      } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
        return null;
      } else if (widget.type === "DrAddress") {
        return "";
      } else {
        return "";
      }
    }
  } else {
    if (widget.type === "DrCheckbox") {
      return [];
    } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
      return null;
    } else if (widget.type === "DrExchangeRates") {
      return {};
    } else {
      return "";
    }
  }
};

export const handleObtainDynamicDefaultValue = (
  formWidgets: any[],
  widgetDefaultData: any
) => {
  let widgetObject = {};
  for (let i = 0, len = formWidgets.length; i < len; i++) {
    const widget = formWidgets[i];
    if (widget.type === "DrDivider") {
      continue;
    } else if (widget.type === "DrCard") {
      widget.children.forEach(widgetChild => {
        if (widgetChild.children) {
          widgetChild.children.forEach(widgetChildItem => {
            // if (
            //   ["DrInputNumber", "DrPercentage"].includes(widgetChildItem.type)
            // ) {
            //   widgetObject[widgetChildItem._fc_id] = getFormulasWidgetValues(
            //     widgetChildItem,
            //     widgetDefaultData
            //   )
            //     ? getFormulasWidgetValues(widgetChildItem, widgetDefaultData)
            //     : 0;
            // } else {
            widgetObject[widgetChildItem._fc_id] = getFormulasWidgetValues(
              widgetChildItem,
              widgetDefaultData
            );
            // }
          });
        }
      });
    } else if (widget.type === "DrTableForm") {
      if (widgetDefaultData) {
        const widgetItem = widgetDefaultData.find(
          widgetChildData => widgetChildData.widgetId === widget._fc_id
        );

        const childrenWidgetIds = widget.children
          .slice(1)
          .map(childWidget => childWidget.children[0]?._fc_id);

        let subTableObject = {};
        if (widgetItem?.childrenList) {
          for (let widgetId of childrenWidgetIds) {
            let widgetDataList = [];
            for (let childRow of widgetItem?.childrenList) {
              const widgetColData = childRow.find(
                childCol => childCol.widgetId === widgetId
              );
              widgetDataList.push(widgetColData?.obj);
            }
            subTableObject[widgetId] = widgetDataList;
          }
        }

        widgetObject[widget._fc_id] = subTableObject;
      }
    } else if (widget.type === "DrRelateCard") {
      if (widgetDefaultData) {
        const widgetItem = widgetDefaultData.find(
          widgetChildData => widgetChildData.widgetId === widget._fc_id
        );
        if (widgetItem && widgetItem.obj) {
          widgetObject[widget._fc_id] = handleObtainDynamicDefaultValue(
            widget.props.relatedValue.rules,
            widgetItem.obj
          );
        }
      }
    } else {
      // if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
      //   widgetObject[widget._fc_id] = getFormulasWidgetValues(
      //     widget,
      //     widgetDefaultData
      //   )
      //     ? getFormulasWidgetValues(widget, widgetDefaultData)
      //     : 0;
      // } else {
      widgetObject[widget._fc_id] = getFormulasWidgetValues(
        widget,
        widgetDefaultData
      );
      // }
    }
  }

  return widgetObject;
};

export const getFormulasWidgetValues = (widget: any, defaultData: any) => {
  if (defaultData && defaultData.length !== 0) {
    const widgetItem = defaultData.find(
      widgetItem => widgetItem.widgetId === widget._fc_id
    );
    if (widgetItem) {
      if (widget.type === "DrRate") {
        return widget.props?.starCount > widgetItem.obj
          ? widgetItem.obj
          : widget.props?.starCount;
      } else if (widget.type === "DrInputNumber") {
        if (!widget.props.min && !widget.props.max) {
          // return widgetItem.obj ? widgetItem.obj : 0;
          return widgetItem.obj;
        }
        if (widget.props.min && widget.props.min > widgetItem.obj) {
          return widget.props.min;
        }
        if (widget.props.max && widget.props.max < widgetItem.obj) {
          return widget.props.max;
        }
      } else if (widget.type === "DrCheckbox") {
        let checkboxHtml = "";
        if (!widgetItem.obj) return checkboxHtml;
        for (let widgetItemId of widgetItem.obj) {
          const widgetOptionItem = widget.props.options.find(
            option => option.value === widgetItemId
          );
          if (widgetOptionItem) {
            checkboxHtml += `,${widgetOptionItem.label}`;
          }
        }
        return checkboxHtml.substr(1);
      } else if (widget.type === "DrRadio") {
        const widgetOptionItem = widget.props.options.find(
          option => option.value === widgetItem.obj
        );
        return widgetOptionItem ? widgetOptionItem.label : "";
      } else if (widget.type === "DrAddress") {
        return widgetItem.label ? widgetItem.label : "";
      } else if (widget.type === "DrExchangeRates") {
        return widgetItem.obj ? widgetItem.obj : {};
      } else if (widget.type === "DrDatePicker") {
        return widgetItem.obj ? widgetItem.obj : "";
      } else if (
        ["DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"].includes(widget.type)
      ) {
        return widgetItem.obj ? widgetItem.obj : {};
      } else {
        return widgetItem.obj;
      }
    } else {
      if (widget.type === "DrCheckbox") {
        return [];
      } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
        return null;
      } else if (widget.type === "DrAddress") {
        return "";
      } else {
        return "";
      }
    }
  } else {
    if (widget.type === "DrCheckbox") {
      return [];
    } else if (["DrInputNumber", "DrPercentage"].includes(widget.type)) {
      return null;
    } else if (widget.type === "DrExchangeRates") {
      return {};
    } else {
      return "";
    }
  }
};

export const renderWidget = (
  widgetJsonList: any,
  widgetId: string,
  milestoneItemData: any
) => {
  let workOrderWidgetReplyObject = {};
  let workOrderReplyArrayObject = {};
  const workOrderWidgetIdList = widgetJsonList.map(
    workOrderReplyWidget => workOrderReplyWidget._fc_id
  );
  let workOrderWidgetList = {};
  for (let widgetName of workOrderWidgetIdList) {
    workOrderWidgetList[widgetName] = [];
  }

  if (milestoneItemData && milestoneItemData.workOrderGroupList) {
    for (let workOrderItem of milestoneItemData.workOrderGroupList) {
      const milestoneData = handleObtainDynamicDefaultValue(
        widgetJsonList,
        TransformSubmitDataStructure(
          workOrderItem.widgetList,
          workOrderItem.subWidgetMap,
          workOrderItem.linkedReferenceMap
        )
      );

      for (let milestoneName in milestoneData) {
        let workOrderList: any = workOrderWidgetList[milestoneName];
        if (!(workOrderWidgetList[milestoneName] instanceof Array)) {
          workOrderList = [];
        }

        workOrderList.push(milestoneData[milestoneName]);
      }

      workOrderReplyArrayObject[workOrderItem.workOrderId] = milestoneData;
    }
  }

  workOrderWidgetReplyObject[widgetId] = {
    flatMapData: workOrderReplyArrayObject,
    nestMapData: workOrderWidgetList
  };
  return workOrderWidgetReplyObject;
};

export const handleAccordingToFormIdObtainData = (
  data: any,
  templateData: any
): any => {
  return new Promise(async resolve => {
    // 1.主表单
    const mainFormData = handleObtainDynamicDefaultValue(
      templateData.mainForm.widgetJsonList,
      TransformSubmitDataStructure(
        data.widgetList,
        data.subWidgetMap,
        data.linkedReferenceMap
      )
    );

    let milestoneReplyObject = {};
    let workOrderReplyObject = {};
    let milestoneWidgetReplyObject = {};
    let workOrderWidgetReplyObject = {};
    let workOrderList = {};

    for (let milestone of templateData.milestoneList) {
      const milestoneItemData = data.milestoneGroupList.find(
        milestoneData => milestoneData.msId === milestone.msId
      );

      // 2.里程碑批复的数据
      if (
        milestoneItemData &&
        milestone &&
        milestone.replyForm &&
        milestone.replyForm.widgetJsonList.length > 0
      ) {
        milestoneReplyObject[milestone.msId] = handleObtainDynamicDefaultValue(
          milestone.replyForm.widgetJsonList,
          TransformSubmitDataStructure(
            milestoneItemData.replyWidgetList,
            milestoneItemData.subWidgetMap,
            milestoneItemData.linkedReferenceMap
          )
        );
        milestoneWidgetReplyObject[milestone.replyForm.id] =
          handleObtainDynamicDefaultValue(
            milestone.replyForm.widgetJsonList,
            TransformSubmitDataStructure(
              milestoneItemData.replyWidgetList,
              milestoneItemData.subWidgetMap,
              milestoneItemData.linkedReferenceMap
            )
          );
      }

      // 3.工单类型的数据
      let workOrderObject: any = {};
      if (milestoneItemData && milestoneItemData.workOrderGroupList) {
        for (let workOrderItem of milestoneItemData.workOrderGroupList) {
          if (milestone.workOrderReplyForm?.widgetJsonList) {
            let milestoneFormJsonList = [];
            let workOrderFormJsonList = [];
            milestone.formList.forEach(milestoneForm => {
              milestoneFormJsonList.push(...milestoneForm.widgetJsonList);
            });
            if (milestone.workOrderReplyForm?.widgetJsonList) {
              workOrderFormJsonList.push(
                ...milestone.workOrderReplyForm?.widgetJsonList
              );
            }

            workOrderObject[workOrderItem.workOrderId] = {
              widgetList: handleObtainDynamicDefaultValue(
                milestoneFormJsonList,
                TransformSubmitDataStructure(
                  workOrderItem.widgetList,
                  workOrderItem.subWidgetMap,
                  workOrderItem.linkedReferenceMap
                )
              ),
              workOrderReplyWidgetList: handleObtainDynamicDefaultValue(
                workOrderFormJsonList,
                TransformSubmitDataStructure(
                  workOrderItem.widgetList,
                  workOrderItem.subWidgetMap,
                  workOrderItem.linkedReferenceMap
                )
              )
            };
          }
        }
      }
      workOrderReplyObject[milestone.msId] = workOrderObject;
    }

    for (let milestone of templateData.milestoneList) {
      const milestoneItemData = data.milestoneGroupList.find(
        milestoneData => milestoneData.msId === milestone.msId
      );

      // 2.里程碑批复的数据
      if (
        milestoneItemData &&
        milestone &&
        milestone.replyForm &&
        milestone.replyForm.widgetJsonList.length > 0
      ) {
        milestoneWidgetReplyObject[milestone.replyForm.id] =
          handleObtainDynamicDefaultValue(
            milestone.replyForm.widgetJsonList,
            TransformSubmitDataStructure(
              milestoneItemData.replyWidgetList,
              milestoneItemData.subWidgetMap,
              milestoneItemData.linkedReferenceMap
            )
          );
      }

      // 3.工单类型的数据
      if (milestone.workOrderReplyForm) {
        workOrderWidgetReplyObject = renderWidget(
          milestone.workOrderReplyForm.widgetJsonList,
          milestone.workOrderReplyForm.id,
          milestoneItemData
        );
      }

      for (let workOrder of milestone.formList) {
        workOrderList[workOrder.id] = renderWidget(
          workOrder.widgetJsonList,
          workOrder.id,
          milestoneItemData
        );
      }
    }

    let mainFormObject = {};
    mainFormObject[templateData.mainForm.id] = mainFormData;
    // 表单显隐数据设置
    let formVisibleSettingObject = {};
    let mainFormVisibleObject = {};
    mainFormVisibleObject[templateData.mainForm.id] = mainFormData;
    formVisibleSettingObject["mainForm"] = mainFormVisibleObject;

    for (let milestone of templateData.milestoneList) {
      let msReplyObject = {};
      if (milestone.replyForm) {
        msReplyObject[milestone.replyForm.id] =
          milestoneReplyObject[milestone.msId];
      }

      formVisibleSettingObject[milestone.msId] = Object.assign(msReplyObject, {
        workOrderData: workOrderReplyObject[milestone.msId]
      });
    }

    resolve({
      formVisibleObject: formVisibleSettingObject,
      orderDetailData: data,
      formulaObject: Object.assign(
        mainFormObject,
        workOrderWidgetReplyObject,
        workOrderList,
        milestoneWidgetReplyObject
      )
    });
  });
};

export const getFormVisibleWidgetData = (
  formType: string,
  milestoneId: string,
  workOrderId: string,
  widgetIds: string[]
) => {
  return new Promise(async resolve => {
    const widgetData: any = storageSession().getItem("widgetData");
    const formVisibleWidgetData: any =
      widgetData.templateData.formVisibleObject;

    const sccsId: string = getUrlParams().sccsId as string;
    const res = await getSccsFieldVisibleConditions({
      sccsId: sccsId
    });

    const formVisibleExpressionList = res.data.filter(formData =>
      widgetIds.includes(formData.formId)
    );
    if (formType === "FORM") {
      // 主表单字段、同一里程碑所有表单（不含工单批复表单）
      let milestoneObject = {};
      for (let widgetData in formVisibleWidgetData["mainForm"]) {
        Object.assign(
          milestoneObject,
          formVisibleWidgetData["mainForm"][widgetData]
        );
      }
      resolve({
        widgetData: Object.assign(
          milestoneObject,
          formVisibleWidgetData[milestoneId].workOrderData[workOrderId]
            ?.widgetList
        ),
        formExpression: formVisibleExpressionList
      });
    } else if (formType === "workOrderReply") {
      // 主表单字段、同一里程碑所有表单、本工单批复表单
      let milestoneObject = {};
      for (let widgetData in formVisibleWidgetData["mainForm"]) {
        Object.assign(
          milestoneObject,
          formVisibleWidgetData["mainForm"][widgetData]
        );
      }
      resolve({
        widgetData: Object.assign(
          milestoneObject,
          formVisibleWidgetData[milestoneId].workOrderData[workOrderId]
            .widgetList,
          formVisibleWidgetData[milestoneId].workOrderData[workOrderId]
            .workOrderReplyWidgetList
        ),
        formExpression: formVisibleExpressionList
      });
    } else if (formType === "msReply") {
      // 主表单数据，本里程碑批复字段
      let milestoneObject = {};
      for (let widgetData in formVisibleWidgetData["mainForm"]) {
        Object.assign(
          milestoneObject,
          formVisibleWidgetData["mainForm"][widgetData]
        );
      }
      resolve({
        widgetData: Object.assign(
          milestoneObject,
          formVisibleWidgetData[milestoneId][workOrderId]
        ),
        formExpression: formVisibleExpressionList
      });
    } else if (formType === "mainForm") {
      if (formVisibleWidgetData) {
        // 主表单数据，里程碑批复字段
        let milestoneObject = {};
        for (let formId in formVisibleWidgetData) {
          if (formId !== "mainForm") {
            for (let milestoneData in formVisibleWidgetData[formId]) {
              if (milestoneData !== "workOrderData") {
                Object.assign(
                  milestoneObject,
                  formVisibleWidgetData[formId][milestoneData]
                );
              }
            }
          }
        }
        resolve({
          widgetData: Object.assign(
            formVisibleWidgetData["mainForm"][workOrderId],
            milestoneObject
          ),
          formExpression: formVisibleExpressionList
        });
      } else {
        resolve({
          widgetData: {},
          formExpression: []
        });
      }
    }
  });
};

/**
 * 在创建订单，编辑订单，第一次工单批复，第一次里程碑批复时需要获取表单默认值
 * @param widgetData
 * @param defaultValueConfig
 * @returns
 */
export const getFormDefaultValue = (
  widgetData: any,
  defaultValueConfig: any
) => {
  if (defaultValueConfig.type === "custom") {
    return cloneDeep(defaultValueConfig.content);
  } else if (defaultValueConfig.type === "relate") {
    return cloneDeep(widgetData[defaultValueConfig.content]);
  } else if (defaultValueConfig.type === "formula") {
    return {};
  }
};

const getFormListWidgetJsonList = (widgetJsonList: any) => {
  let widgetList = [];
  for (let widgetJson of widgetJsonList) {
    if (widgetJson.type === "DrCard") {
      const cardChilds = widgetJson.children
        .filter(col => col.children)
        .map(col => col.children[0]);
      widgetList.push(...getFormListWidgetJsonList(cardChilds));
    } else {
      widgetList.push(widgetJson);
    }
  }
  return widgetList;
};

const getWorkOrderGroupData = (workOrderData: any) => {
  if (workOrderData.length === 0) {
    return {};
  }
  let workOrderKeys = Object.keys(workOrderData[0]);
  let workOrderObject = {};
  for (let workOrderKey of workOrderKeys) {
    workOrderObject[workOrderKey] = workOrderData.map(
      workOrder => workOrder[workOrderKey]
    );
  }
  return workOrderObject;
};

/**
 * 获取当前订单的整个表单数据源
 * @param milestoneId 里程碑id 允许为空
 * @param workOrderId 工单id 允许为空
 * @returns
 */
export const getEntireFormData = (
  milestoneId?: string,
  workOrderId?: string
) => {
  const widgetDataCache = storageSession().getItem("widgetData") || {};
  if (Object.keys(widgetDataCache).length === 0) {
    return {};
  } else {
    const {
      entrieTemplateForm,
      templateData,
      formVisibleTemplateFormConfigure
    } = widgetDataCache as any;
    const detailData = templateData.orderDetailData;
    let orderDetailObject = {};
    let checkboxList = [];
    let widgetList = [];
    const mainFormData = TransformSubmitDataStructure(
      detailData.widgetList,
      detailData.subWidgetMap,
      detailData.linkedReferenceMap
    );
    const mainFormObject = handleObtainDynamicDefaultValue(
      entrieTemplateForm.mainForm.widgetJsonList,
      mainFormData
    );

    orderDetailObject[entrieTemplateForm.mainForm.id] = mainFormObject;
    checkboxList = checkboxList.concat(
      getFormListWidgetJsonList(
        entrieTemplateForm.mainForm.widgetJsonList
      ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
    );
    widgetList = widgetList.concat(
      getFormListWidgetJsonList(entrieTemplateForm.mainForm.widgetJsonList)
    );

    if (detailData.milestoneGroupList) {
      for (let msFormDetail of detailData.milestoneGroupList) {
        const { msId, subWidgetMap, replyWidgetList, linkedReferenceMap } =
          msFormDetail;
        const msTemplateConfig = entrieTemplateForm.milestoneList.find(
          msConfig => msConfig.msId === msId
        );

        if (msTemplateConfig.replyForm) {
          const msReplyObject = handleObtainDynamicDefaultValue(
            msTemplateConfig.replyForm.widgetJsonList,
            TransformSubmitDataStructure(
              replyWidgetList,
              subWidgetMap,
              linkedReferenceMap
            )
          );

          orderDetailObject[msTemplateConfig.replyForm.id] = msReplyObject;
          checkboxList = checkboxList.concat(
            getFormListWidgetJsonList(
              msTemplateConfig.replyForm.widgetJsonList
            ).filter(widget => ["DrCheckbox", "DrRadio"].includes(widget.type))
          );
          widgetList = widgetList.concat(
            getFormListWidgetJsonList(msTemplateConfig.replyForm.widgetJsonList)
          );
        }

        let msFormWidgetList = [];
        for (let msForm of msTemplateConfig.formList) {
          msFormWidgetList = msFormWidgetList.concat(msForm.widgetJsonList);
        }

        if (msId === milestoneId) {
          const currentWorkOrderForm = msFormDetail.workOrderGroupList.find(
            workOrderData => workOrderData.workOrderId === workOrderId
          );

          for (let msFormItem of msTemplateConfig.formList) {
            const msFormWidgetList = getFormListWidgetJsonList(
              msFormItem.widgetJsonList
            );

            const value = currentWorkOrderForm
              ? handleObtainDynamicDefaultValue(
                  msFormWidgetList,
                  TransformSubmitDataStructure(
                    currentWorkOrderForm.widgetList,
                    currentWorkOrderForm.subWidgetMap,
                    currentWorkOrderForm.linkedReferenceMap
                  )
                )
              : getWorkOrderGroupData(
                  msFormDetail.workOrderGroupList.map(workOrder => {
                    return handleObtainDynamicDefaultValue(
                      msFormWidgetList,
                      TransformSubmitDataStructure(
                        workOrder.widgetList,
                        workOrder.subWidgetMap,
                        workOrder.linkedReferenceMap
                      )
                    );
                  })
                );

            orderDetailObject[msFormItem.id] = value;
            checkboxList = checkboxList.concat(
              getFormListWidgetJsonList(msFormItem.widgetJsonList).filter(
                widget => ["DrCheckbox", "DrRadio"].includes(widget.type)
              )
            );
            widgetList = widgetList.concat(
              getFormListWidgetJsonList(msFormItem.widgetJsonList)
            );
          }

          if (msTemplateConfig.workOrderReplyForm) {
            const msReplyWidgetList = getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            );

            const value = currentWorkOrderForm
              ? handleObtainDynamicDefaultValue(
                  msReplyWidgetList,
                  TransformSubmitDataStructure(
                    currentWorkOrderForm.widgetList,
                    currentWorkOrderForm.subWidgetMap,
                    currentWorkOrderForm.linkedReferenceMap
                  )
                )
              : handleObtainDynamicDefaultValue(
                  msReplyWidgetList,
                  TransformSubmitDataStructure([], [])
                );

            orderDetailObject[msTemplateConfig.workOrderReplyForm.id] = value;
            checkboxList = checkboxList.concat(
              getFormListWidgetJsonList(
                msTemplateConfig.workOrderReplyForm.widgetJsonList
              ).filter(widget =>
                ["DrCheckbox", "DrRadio"].includes(widget.type)
              )
            );
            widgetList = widgetList.concat(
              getFormListWidgetJsonList(
                msTemplateConfig.workOrderReplyForm.widgetJsonList
              )
            );
          }
        } else {
          for (let msFormItem of msTemplateConfig.formList) {
            const msFormWidgetList = getFormListWidgetJsonList(
              msFormItem.widgetJsonList
            );
            let msFormObject = {};
            for (let msFormWidgetItem of msFormWidgetList) {
              msFormObject[msFormWidgetItem._fc_id] = [];
              for (let workOrderDetail of msFormDetail.workOrderGroupList) {
                const value = handleObtainDynamicDefaultValue(
                  [msFormWidgetItem],
                  TransformSubmitDataStructure(
                    workOrderDetail.widgetList,
                    workOrderDetail.subWidgetMap,
                    workOrderDetail.linkedReferenceMap
                  )
                );
                msFormObject[msFormWidgetItem._fc_id].push(
                  ...Object.values(value)
                );
              }
            }
            orderDetailObject[msFormItem.id] = msFormObject;
            checkboxList = checkboxList.concat(
              getFormListWidgetJsonList(msFormItem.widgetJsonList).filter(
                widget => ["DrCheckbox", "DrRadio"].includes(widget.type)
              )
            );
            widgetList = widgetList.concat(
              getFormListWidgetJsonList(msFormItem.widgetJsonList)
            );
          }

          if (msTemplateConfig.workOrderReplyForm) {
            const workOrderReplyWidgetList = getFormListWidgetJsonList(
              msTemplateConfig.workOrderReplyForm.widgetJsonList
            );
            let msFormObject = {};
            for (let msFormWidgetItem of workOrderReplyWidgetList) {
              msFormObject[msFormWidgetItem._fc_id] = [];
              for (let workOrderDetail of msFormDetail.workOrderGroupList) {
                const value = handleObtainDynamicDefaultValue(
                  [msFormWidgetItem],
                  TransformSubmitDataStructure(
                    workOrderDetail.widgetList,
                    workOrderDetail.subWidgetMap,
                    workOrderDetail.linkedReferenceMap
                  )
                );
                msFormObject[msFormWidgetItem._fc_id].push(
                  ...Object.values(value)
                );
              }
            }
            orderDetailObject[msTemplateConfig.workOrderReplyForm.id] =
              msFormObject;
            checkboxList = checkboxList.concat(
              getFormListWidgetJsonList(
                msTemplateConfig.workOrderReplyForm.widgetJsonList
              ).filter(widget =>
                ["DrCheckbox", "DrRadio"].includes(widget.type)
              )
            );
            widgetList = widgetList.concat(
              getFormListWidgetJsonList(
                msTemplateConfig.workOrderReplyForm.widgetJsonList
              )
            );
          }
        }
      }
    }

    return {
      checkboxList: checkboxList,
      entireFormObject: Object.assign({}, ...Object.values(orderDetailObject)),
      entireDetail: orderDetailObject,
      widgetList: widgetList,
      formVisibleTemplateFormConfigure: formVisibleTemplateFormConfigure
    };
  }
};
