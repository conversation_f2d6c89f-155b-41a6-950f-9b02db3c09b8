<template>
  <div class="user-agreement" v-bind="$attrs">
    <!-- 注册页面的协议文本 -->
    <div v-if="isRegister" class="register-agreement">
      <div class="agreement-row">
        <span class="agreement-text">
          {{ t("trade_login_registerAndAgree") }}
          <el-button type="primary" link @click="handleOpenUserService">
            Linkincrease{{ t("trade_login_userServiceAgreement") }}
          </el-button>
        </span>
      </div>
      <div class="agreement-row">
        <span class="agreement-text">
          {{ t("trade_login_read") }}
          <el-button type="primary" link @click="handleOpenPrivacy">
            Linkincrease{{ t("trade_login_privacyPolicy") }}
          </el-button>
        </span>
      </div>
    </div>
    <!-- 登录页面的协议文本 -->
    <div v-else class="login-agreement">
      <span class="agreement-text">
        {{ t("trade_login_loginAgree") }}
        <el-button type="primary" link @click="handleOpenUserService">
          Linkincrease{{ t("trade_login_userServiceAgreement") }}
        </el-button>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// Props
const props = defineProps({
  isRegister: {
    type: Boolean,
    default: false
  }
});

// 打开用户服务协议
const handleOpenUserService = () => {
  window.open("https://www.linkincrease.com/nd.jsp?id=30&id=30#device=pc");
};

// 打开隐私政策
const handleOpenPrivacy = () => {
  window.open("https://www.linkincrease.com/nd.jsp?id=30&id=30#device=pc");
};
</script>

<style lang="scss" scoped>
// 响应式适配
@media screen and (width <= 768px) {
  .user-agreement {
    margin-top: 20px;

    .agreement-text {
      font-size: 12px;

      .el-button {
        font-size: 12px;
      }
    }

    .register-agreement {
      .agreement-row {
        margin-bottom: 8px;
      }
    }
  }
}

.user-agreement {
  margin-top: 50px;

  .agreement-text {
    font-size: 14px;
    line-height: 1.5;
    color: #595959;

    .el-button {
      height: auto;
      padding: 0;
      font-size: 14px;
      vertical-align: baseline;

      &:hover {
        color: #3a8ee6;
      }
    }
  }

  // 注册页面的协议样式
  .register-agreement {
    text-align: center;

    .agreement-row {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 登录页面的协议样式
  .login-agreement {
    text-align: left;
  }
} // 用户协议
</style>
