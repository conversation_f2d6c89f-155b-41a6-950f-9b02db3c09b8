<template>
  <div class="order-milestone-card-popover">
    <div class="order-milestone-card-title">
      {{ milestoneData.name }}
    </div>
    <div class="order-milestone-card-tag-body">
      <span
        class="order-milestone-card-status-tag"
        :class="milestoneState[milestoneData.status].className"
      >
        <i :class="['iconfont', milestoneState[milestoneData.status].icon]" />
        <span class="order-milestone-card-status-tag-text">
          {{ t(milestoneState[milestoneData.status].text) }}
        </span>
      </span>
      <span
        v-for="label in labelList"
        :key="label.labelId"
        class="order-milestone-card-tag"
        :style="{ background: label.style }"
      >
        {{ label.labelValue }}
      </span>
    </div>
    <!-- 里程碑负责人 -->
    <div
      v-if="milestoneData.managerUserName"
      class="order-milestone-card-milestone-approval-body"
    >
      <div class="order-milestone-card-milestone-approval-icon">
        <i class="iconfont link-modify-processor" />
      </div>
      <div class="order-milestone-card-milestone-approval-title">
        <span class="order-milestone-card-milestone-approval-title-span">
          {{ t("trade_common_directorPerson") }}
        </span>

        <LkAvatar
          :teamInfo="{
            avatar: milestoneData.managerUserAvatar,
            name: milestoneData.managerUserName
          }"
          :size="18"
        />
      </div>
    </div>
    <!-- 里程碑结束开始时间 -->
    <div
      v-if="milestoneData.plannedStartDate && milestoneData.plannedEndDate"
      class="order-milestone-card-milestone-approval-body"
    >
      <div class="order-milestone-card-milestone-approval-icon">
        <i class="iconfont link-date" />
      </div>
      <span class="order-milestone-card-milestone-approval-title">
        <span class="order-milestone-card-milestone-approval-title-span">
          {{ t("trade_common_plan") }}
        </span>
        <span class="order-milestone-card-milestone-approval-title-text">
          {{
            getOrderPlanDateFn(
              milestoneData.plannedStartDate,
              milestoneData.plannedEndDate
            )
          }}
        </span>
        <span class="order-milestone-card-milestone-approval-date">
          {{ milestoneData.plannedDay }}{{ t("trade_common_day") }}
        </span>
      </span>
    </div>
    <!-- 里程碑实际开始时间 -->
    <div
      v-if="milestoneData.actualStartDate && milestoneData.actualEndDate"
      class="order-milestone-card-milestone-approval-body"
    >
      <div class="order-milestone-card-milestone-approval-icon">
        <i class="iconfont link-date" />
      </div>
      <span class="order-milestone-card-milestone-approval-title">
        <span class="order-milestone-card-milestone-approval-title-span">
          {{ t("trade_common_actual") }}
        </span>
        <span class="order-milestone-card-milestone-approval-title-text">
          {{
            getOrderPlanDateFn(
              milestoneData.actualStartDate,
              milestoneData.actualEndDate
            )
          }}
        </span>
        <span
          v-if="milestoneData.actualDay"
          class="order-milestone-card-milestone-approval-date"
        >
          {{ milestoneData.actualDay }}{{ t("trade_common_day") }}
        </span>
      </span>
    </div>
    <!-- 里程碑批复人 -->
    <div
      v-if="milestoneData.msReplyUser.length > 0"
      class="order-milestone-card-milestone-approval-body"
    >
      <div class="order-milestone-card-milestone-approval-icon">
        <i class="iconfont link-reply" />
      </div>
      <span class="order-milestone-card-milestone-approval-title">
        <span class="order-milestone-card-milestone-approval-title-span">
          {{ t("trade_template_replyBtnName") }}
        </span>
        <LkAvaterGroup
          class="orange-avatar-group"
          :size="18"
          :avatarList="milestoneData.msReplyUser"
          :maxAvatar="4"
        />
        <span class="order-milestone-card-milestone-approval-date">
          {{ computedDateFn(milestoneData.msReplyUser[0]?.latestEditeDate) }}
        </span>
      </span>
    </div>
    <div
      v-if="milestoneData.workOrderUserInfoList.length > 0"
      class="order-milestone-card-milestone-approval-body"
    >
      <div class="order-milestone-card-milestone-approval-icon">
        <i class="iconfont link-workorder font16" />
      </div>
      <div class="order-milestone-card-milestone-approval-title">
        <span class="order-milestone-card-milestone-approval-title-span">
          {{ t("trade_work_order") }}
          ({{ milestoneData.workOrderUserInfoList.length }})
        </span>
      </div>
    </div>
    <div class="order-milestone-card-milestone-approval-scrollbar">
      <el-scrollbar max-height="400px">
        <div class="order-milestone-card-details">
          <div class="order-milestone-card-details-list">
            <div
              v-for="item in milestoneData.workOrderUserInfoList"
              :key="item"
              class="order-milestone-card-details-li"
            >
              <div class="order-milestone-card-details-title">
                {{ item.workOrderName }}
              </div>
              <div
                v-if="item.replyUserList.length > 0"
                class="order-milestone-card-milestone-approval-body"
              >
                <span class="order-milestone-card-milestone-approval-flex">
                  <div class="order-milestone-card-milestone-approval-icon">
                    <i class="iconfont link-reply" />
                  </div>
                  <span class="order-milestone-card-milestone-approval-title">
                    {{ t("trade_home_approval") }}
                  </span>
                  <LkAvaterGroup
                    class="orange-avatar-group"
                    :size="18"
                    :avatarList="item.replyUserList"
                    :maxAvatar="4"
                  />
                </span>
                <span class="order-milestone-card-milestone-approval-date">
                  {{ computedDateFn(item.replyTime) }}
                </span>
              </div>
              <div class="order-milestone-card-milestone-approval-body">
                <span class="order-milestone-card-milestone-approval-flex">
                  <div class="order-milestone-card-milestone-approval-icon">
                    <i class="iconfont link-collect" />
                  </div>
                  <span class="order-milestone-card-milestone-approval-title">
                    {{ t("trade_common_gather") }}
                  </span>
                  <LkAvaterGroup
                    :avatarList="item.editUserList"
                    :size="18"
                    :maxAvatar="4"
                  />
                </span>
                <span class="order-milestone-card-milestone-approval-date">
                  {{ computedDateFn(item.editTime) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import LkAvatar from "@/components/lkAvatar/index";
import LkAvaterGroup from "@/components/lkAvatarGroup/index";
import dayjs from "dayjs";

const { t } = useI18n();
const milestoneState = {
  NOT_START: {
    className: "NOT_START",
    icon: "link-not-started",
    text: "trade_order_stateNotStart"
  },
  ON_GOING: {
    className: "ON_GOING",
    icon: "link-in-progress",
    text: "trade_home_inProgress"
  },
  COMPLETE: {
    className: "COMPLETE",
    icon: "link-completed",
    text: "trade_order_stateCompleted"
  },
  CANCEL: {
    className: "CANCEL",
    icon: "link-cancelled",
    text: "trade_order_stateCancel"
  }
};

const props = defineProps({
  milestoneData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  labelList: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const getOrderPlanDateFn = (startDate, endDate): string => {
  if (startDate && endDate) {
    return `${dayjs(new Date(startDate)).format("YYYY/MM/DD")}~${dayjs(new Date(endDate)).format("YYYY/MM/DD")}`;
  } else {
    return "";
  }
};

const computedDateFn = (date): string => {
  if (date) {
    return dayjs(new Date(date)).format("YYYY/MM/DD HH:mm:ss");
  } else {
    return "";
  }
};
</script>
<style lang="scss" scoped>
.order-milestone-card-popover {
  .order-milestone-card-title {
    font-size: 14px;
    font-weight: bolder;
    line-height: 20px;
    color: #262626;
    text-align: left;
  }

  .order-milestone-card-tag-body {
    margin: 10px 0 15px;

    .order-milestone-card-status-tag {
      box-sizing: border-box;
      display: inline-flex;
      align-items: center;
      height: 18px;
      padding: 0 6px;
      margin-right: 5px;
      margin-bottom: 6px;
      line-height: 18px;
      border-radius: 12px;

      .order-milestone-card-status-tag-text {
        margin-left: 3px;
        font-size: 12px;
      }

      .iconfont {
        font-size: 13px;
      }

      &.ON_GOING {
        color: #0070d2;
        background: #ddefff;
      }

      &.NOT_START {
        color: #3fc6fe;
        background: #ddf2ff;
      }

      &.COMPLETE {
        color: #32a645;
        background: #d9f4f0;
      }

      &.CANCEL {
        color: #262626;
        background: #e5e5e5;
      }
    }

    .order-milestone-card-tag {
      display: inline-flex;
      align-items: center;
      height: 18px;
      padding: 2px 6px;
      margin-right: 4px;
      margin-bottom: 6px;
      font-size: 12px;
      line-height: 14px;
      color: #fff;
      background: #2bd673;
      border-radius: 12px;

      &:first-child {
        background: #2bd673;
      }

      &:nth-child(2) {
        background: #ff5b9d;
      }

      &:nth-child(3) {
        background: #ff8e56;
      }

      .iconfont {
        font-size: 12px;
      }

      &.progressed {
        color: #0070d2;
        background: #ddefff;

        .iconfont {
          font-size: 14px;
        }
      }
    }
  }

  .order-milestone-card-milestone-approval-body {
    display: flex;
    align-items: center;
    margin-bottom: 7px;
    color: #8c8c8c;

    .order-milestone-card-milestone-approval-icon {
      .iconfont {
        margin-right: 6px;
        font-size: 12px;
      }
    }

    .order-milestone-card-milestone-approval-title {
      .order-milestone-card-milestone-approval-title-span {
        font-size: 12px;
        color: #8c8c8c;

        &::after {
          display: inline-block;
          font-size: 12px;
          color: #8c8c8c;
          content: "：";
        }
      }

      .order-milestone-card-milestone-approval-title-text {
        margin-right: 13px;
        color: #262626;
      }

      .order-milestone-card-milestone-approval-date {
        margin-left: 10px;
        font-size: 12px;
        color: #262626;
      }
    }
  }

  .order-milestone-card-milestone-approval-scrollbar {
    .order-milestone-card-details {
      .order-milestone-card-details-list {
        width: 100%;

        .order-milestone-card-details-li {
          width: 100%;
          padding: 8px;
          margin-bottom: 8px;
          background: #f5f5f5;
          border-radius: 3px;

          .order-milestone-card-details-title {
            height: 17px;
            margin-bottom: 3px;
            overflow: hidden;
            font-size: 12px;
            font-weight: bold;
            line-height: 17px;
            color: #595959;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .order-milestone-card-milestone-approval-body {
            display: flex;
            align-items: center;
            margin-bottom: 7px;

            &:last-child {
              margin-bottom: 0;
            }

            .order-milestone-card-milestone-approval-flex {
              display: inline-flex;
              flex: 1;
              align-items: center;

              .order-milestone-card-milestone-approval-icon {
                width: 20px;
                text-align: center;

                .iconfont {
                  margin-right: 5px;
                  font-size: 12px;
                }
              }

              .order-milestone-card-milestone-approval-title {
                align-items: center;
                font-size: 12px;
                color: #8c8c8c;

                &::after {
                  display: inline-block;
                  font-size: 12px;
                  color: #8c8c8c;
                  content: "：";
                }
              }
            }

            .order-milestone-card-milestone-approval-date {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }
}
</style>
