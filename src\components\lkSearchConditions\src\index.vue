<template>
  <div class="custom-conditions-control-row">
    <!-- 第一个控件类型 -->
    <el-cascader
      ref="CascaderRef"
      v-model="conditionsForm.firstData"
      :options="planSettingSelectData"
      :show-all-levels="false"
      :collapse-tags="true"
      :collapse-tags-tooltip="true"
      :teleported="teleported"
      popper-class="conditionFormFirstCascader"
      style="width: 280px"
      :props="{
        disabled: 'deleted'
      }"
      @change="handleSelectCascader"
    >
      <template #default="{ data }">
        <ReText type="info" :tippyProps="{ delay: 50000 }">
          {{ data.label }}
        </ReText>
      </template>
    </el-cascader>
    <!-- 第二个控件：值比较规则控件 -->
    <el-select-v2
      v-model="conditionsForm.operator"
      class="custom-conditions-operator-control"
      filterable
      :options="MathOperatorSelectOptions"
      :teleported="teleported"
      @change="handleSelectOperator"
    >
      <template #label="{ label }">
        {{ t(label) }}
      </template>
      <template #default="{ item }">
        {{ t(item.label) }}
      </template>
    </el-select-v2>
    <div
      v-if="
        firstOpertatorSelectControl &&
        !['AFTER', 'EQ', 'BEFORE', 'IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
          conditionsForm.operator
        )
      "
    >
      <el-select-v2
        v-model="conditionsForm.rangeDateType"
        class="custom-conditions-operator-control"
        filterable
        :options="getRangeTypeOptions()"
        clearable
        :teleported="teleported"
        @change="handleSelectDateRangeType"
      >
        <template #label="{ label }">
          {{ t(label) }}
        </template>
        <template #default="{ item }">
          {{ t(item.label) }}
        </template>
      </el-select-v2>
      <el-date-picker
        v-if="conditionsForm.rangeDateType === 'DATE_RANGE'"
        v-model="dateRange"
        range-separator="-"
        :start-placeholder="t('trade_common_beginDate')"
        :end-placeholder="t('trade_common_endDate')"
        style="width: 236px"
        :type="`${widgetDate.type}range`"
        :value-format="widgetDate.valueFormat"
        :format="widgetDate.format"
        :time-format="widgetDate.timeFormat"
        :teleported="teleported"
        @change="handleDatePickerValue"
      />
      <el-input
        v-if="['LAST', 'NEXT'].includes(conditionsForm.rangeDateType)"
        v-model="conditionsForm.value"
        clearable
        class="conditions-form-input"
        @input="handleEdit"
      >
        <template #append>{{ getTimeDateType() }}</template>
      </el-input>
    </div>
    <div
      v-if="
        (firstOpertatorSelectControl &&
          !['BETWEEN', 'IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
            conditionsForm.operator
          )) ||
        !firstOpertatorSelectControl
      "
      style="display: inline-flex"
    >
      <slot name="fieldTypeSlot" />
      <!-- 第四个区域类型控件 -->
      <div
        v-if="
          conditionsForm.fieldTypeEnum === 'CUSTOM' &&
          !['IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
            conditionsForm.operator
          )
        "
        class="custom-conditions-col"
      >
        <el-select-v2
          v-if="firstOpertatorSelectControl"
          v-model="conditionsForm.customerDateType"
          :teleported="teleported"
          class="custom-conditions-operator-control"
          filterable
          :options="getDatePickerOptionsByType(DatePickerOptions)"
          @change="conditionsForm.value = ''"
        >
          <template #label="{ label }">
            {{ t(label) }}
          </template>
          <template #default="{ item }">
            {{ t(item.label) }}
          </template>
        </el-select-v2>
        <el-date-picker
          v-if="conditionsForm.customerDateType === 'ASSIGN'"
          v-model="conditionsForm.value"
          :teleported="teleported"
          :type="widgetDate.type"
          :value-format="widgetDate.valueFormat"
          :format="widgetDate.format"
          :time-format="widgetDate.timeFormat"
          :placeholder="t('trade_common_datePickerTip')"
        />
        <el-input
          v-if="['BEFORE', 'AFTER'].includes(conditionsForm.customerDateType)"
          v-model="conditionsForm.value"
          clearable
          class="conditions-form-input"
          @input="handleEdit"
        >
          <template #append>{{ getTimeDateType() }}</template>
        </el-input>
        <CustomConditionControl
          v-if="
            !firstOpertatorSelectControl &&
            !widgetMainFormPersonBool &&
            !labelSelectVisible
          "
          ref="customConditionControlRef"
          :widget="widgetForm"
          :teleported="teleported"
          :widgetValue="conditionsForm.value"
          :widgetOptions="widgetOptions"
          :widgetDateShowType="widgetDateShowType"
          :multiple="multiple"
          style="display: inline-flex"
          @handleWidgetValue="handleWidgetValue"
        />
      </div>

      <!-- 标签设置 -->
      <el-select-v2
        v-if="
          labelSelectVisible &&
          conditionsForm.fieldTypeEnum === 'CUSTOM' &&
          !['IS_NULL', 'IS_NOT_NULL'].includes(conditionsForm.operator)
        "
        v-model="conditionsForm.value"
        :teleported="teleported"
        class="custom-conditions-operator-control"
        filterable
        :options="labelMapOptions"
        clearable
      >
        <template #label="{ label }">
          {{ t(label) }}
        </template>
        <template #default="{ item }">
          {{ t(item.label) }}
        </template>
      </el-select-v2>

      <!-- 订单创建人，订单更新人 -->
      <el-cascader
        v-if="
          widgetMainFormPersonBool && conditionsForm.fieldTypeEnum === 'CUSTOM'
        "
        ref="teamMemberCascaderRef"
        :model-value="teamMemberBindValue"
        :options="widgetTeamMemberList"
        :teleported="teleported"
        :props="cascaderProps"
        :multiple="multiple"
        clearable
        collapse-tags
        style="width: 250px"
        @change="handleSelectMember"
      />
    </div>
    <slot name="workOrderSlot" />
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, nextTick, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { dict } from "@/utils/dict";
import { cloneDeep, storageLocal, storageSession } from "@pureadmin/utils";
import CustomConditionControl from "./CustomConditionControl.vue";
import {
  MathOperatorOptions,
  DateRangeOptions,
  DatePickerOptions
} from "./enum";
import { getWidgetItem } from "@/api/sccs";
import { getWorkOrderProcessorList } from "@/api/order";
import { ReText } from "@/components/ReText";

const props = defineProps({
  condition: {
    type: Object as PropType<any>,
    default: () => {}
  },
  conditionList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  labelConditionMap: {
    type: Object as PropType<any>,
    default: () => {}
  },
  teleported: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

// todo 人员控件需要新增两级控件
const cascaderProps = computed(() => {
  return {
    multiple: ["IN", "NOT_IN"].includes(conditionsForm.value.operator),
    emitPath: false
    // checkStrictly: true
  };
});

const { t } = useI18n();
const route = useRoute();
const teamMemberCascaderRef = ref<any | HTMLElement>(null);
const widgetDate = ref<any>({
  valueFormat: "YYYY-MM-DD",
  type: "date"
});
const teamMemberBindValue = ref<any>([]);
const conditionsForm = ref<any>({
  firstData: [],
  firstDataObject: {},
  twoData: [],
  twoDataObject: {},
  operator: "",
  fieldTypeEnum: "CUSTOM",
  workOrderRange: null,
  rangeDateType: null,
  customerDateType: null,
  value: "",
  dateType: ""
});
const dateRange = ref<any[]>([]);
const MathOperatorSelectOptions = ref<any>([]);
const firstOpertatorSelectControl = ref<boolean>(false);
const CascaderRef = ref<HTMLElement | any>(null);
const customConditionControlRef = ref<HTMLElement | any>(null);
const workOrderRangeVisible = ref<boolean>(false);
const planSettingSelect = ref<any[]>([]);
const widgetTeamMemberList = ref<any>([]);
const widgetForm = ref<any>({});
const multiple = ref<boolean>(false);
const widgetOptions = ref<any>([]);
const labelMapOptions = ref<any>([]);
const labelSelectVisible = ref<boolean>(false);
const widgetDateShowType = ref<string>("");
const personWidgetName = [
  "createMemberId",
  "updateMemberId",
  "replyMemberId",
  "collector",
  "managerMemberId",
  "milestoneReplyCreator",
  "replyMember"
];
const widgetMainFormPersonBool = computed(() => {
  return (
    personWidgetName.includes(conditionsForm.value.firstDataObject.value) &&
    !["IS_NULL", "IS_NOT_NULL"].includes(conditionsForm.value.operator)
  );
});

const emit = defineEmits<{
  (e: "handleChange", data: any): void;
  (e: "handleChangeConditions", data: any): void;
  (
    e: "handleConditionFirstData",
    bool: boolean,
    data: any,
    formPubId: string
  ): void;
}>();

/**
 * 第一个控件选择值类型
 */
const handleSelectCascader = async (): Promise<void> => {
  const cascaderRefs = CascaderRef.value.getCheckedNodes()[0];
  workOrderRangeVisible.value =
    cascaderRefs.pathNodes[1].data.type === "formSysField";
  const { field, fieldType, formId, value } = cascaderRefs.data;
  conditionsForm.value.firstDataObject =
    CascaderRef.value.getCheckedNodes()[0].data;
  labelSelectVisible.value = cascaderRefs.pathNodes[1].data.type === "labels";
  if (cascaderRefs.pathNodes[1].data.type === "labels") {
    labelMapOptions.value = props.labelConditionMap.get(value);
    MathOperatorSelectOptions.value = MathOperatorOptions["widget"];
    firstOpertatorSelectControl.value = false;
    conditionsForm.value.operator = "";
    conditionsForm.value.twoData = [];
    conditionsForm.value.fieldTypeEnum = "CUSTOM";
    conditionsForm.value.workOrderRange = null;
    conditionsForm.value.rangeDateType = null;
    conditionsForm.value.customerDateType = null;
    conditionsForm.value.value = "";
    return;
  }
  if (["DrInputNumber", "DrRate", "DrPercentage"].includes(fieldType)) {
    MathOperatorSelectOptions.value = MathOperatorOptions["number"];
    firstOpertatorSelectControl.value = false;
  } else if (["DrDatePicker"].includes(fieldType)) {
    MathOperatorSelectOptions.value = MathOperatorOptions["date"];
    firstOpertatorSelectControl.value = true;
  } else if (["DrImagesUpload", "DrFilesUpload"].includes(fieldType)) {
    MathOperatorSelectOptions.value = MathOperatorOptions["file"];
    firstOpertatorSelectControl.value = false;
  } else {
    MathOperatorSelectOptions.value = MathOperatorOptions["widget"];
    firstOpertatorSelectControl.value = false;
  }

  if (
    ["DrRadio", "DrCheckbox", "DrDatePicker"].includes(fieldType) &&
    workOrderRangeVisible.value &&
    cascaderRefs.data.type === "form"
  ) {
    const { data } = await getWidgetItem({
      widgetId: field,
      formPubId: formId
    });

    const dateMap = {
      year: {
        showType: "YYYY"
      },
      month: {
        showType: "YYYY-MM"
      },
      date: {
        showType: "YYYY-MM-DD"
      },
      datetime: {
        showType: "YYYY-MM-DD"
      },
      datetimesecond: {
        showType: "YYYY-MM-DD"
      }
    };

    widgetOptions.value = data.props.options;
    widgetDate.value.type =
      data.props.showType === "datetimesecond" ||
      data.props.showType === "datetime"
        ? "date"
        : data.props.showType;
    widgetDate.value.valueFormat = dateMap[data.props.showType];
    widgetDate.value.format = dateMap[data.props.showType];
    widgetDate.value.timeFormat =
      data.props.showType === "datetime" ? "HH:mm" : "HH:mm:ss";

    conditionsForm.value.dateType =
      data.props.showType === "year"
        ? "year"
        : data.props.showType === "month"
          ? "month"
          : "day";
  } else if (fieldType === "DrRate") {
    const { data } = await getWidgetItem({
      widgetId: field,
      formPubId: formId
    });
    widgetOptions.value = data.props;
  } else if (field === "orderState") {
    widgetOptions.value = dict.getDictByCode("order_state");
  } else if (field === "status") {
    widgetOptions.value = dict.getDictByCode("milestone_status");
  } else if (personWidgetName.includes(field)) {
    widgetTeamMemberList.value = await getSystemMembersList();
  } else if (
    cascaderRefs.pathNodes[1].data.type === "main" &&
    ["DrRadio", "DrCheckbox", "DrDatePicker"].includes(fieldType)
  ) {
    widgetDate.value.type = "date";
    widgetDate.value.valueFormat = "YYYY-MM-DD";
    widgetDate.value.format = "YYYY-MM-DD";
    conditionsForm.value.dateType = "day";
  }

  if (
    [
      "DrRadio",
      "DrSCCSMemberSingleSelect",
      "DrSCCSMemberMultipleSelect",
      "DrSCCSGroupMemberSingleSelect",
      "DrSCCSGroupMemberMultipleSelect"
    ].includes(fieldType)
  ) {
    multiple.value = ["IN", "NOT_IN"].includes(conditionsForm.value.operator);
  } else if (fieldType === "DrCheckbox") {
    multiple.value = true;
  }

  widgetForm.value = Object.assign(cloneDeep(cascaderRefs.data), {
    type: cascaderRefs.data.fieldType
  });
  conditionsForm.value.operator = "";
  conditionsForm.value.twoData = [];
  conditionsForm.value.fieldTypeEnum = "CUSTOM";
  conditionsForm.value.workOrderRange = null;
  conditionsForm.value.rangeDateType = null;
  conditionsForm.value.customerDateType = null;
  conditionsForm.value.value = "";
  customConditionControlRef.value?.clearData();

  emit(
    "handleConditionFirstData",
    workOrderRangeVisible.value,
    CascaderRef.value.getCheckedNodes()[0],
    cascaderRefs.pathNodes[1].data.value
  );
};

/**
 * 第二个控件切换值
 */
const handleSelectOperator = () => {
  const cascaderRefs = CascaderRef.value.getCheckedNodes()[0];

  if (!cascaderRefs || !cascaderRefs.data) {
    return;
  }

  const { fieldType } = cascaderRefs.data;

  if (
    [
      "DrRadio",
      "DrSCCSMemberSingleSelect",
      "DrSCCSMemberMultipleSelect",
      "DrSCCSGroupMemberSingleSelect",
      "DrSCCSGroupMemberMultipleSelect"
    ].includes(fieldType)
  ) {
    multiple.value = ["IN", "NOT_IN"].includes(conditionsForm.value.operator);
  } else if (fieldType === "DrCheckbox") {
    multiple.value = true;
  }

  conditionsForm.value.twoData = [];
  conditionsForm.value.fieldTypeEnum = "CUSTOM";
  conditionsForm.value.workOrderRange = null;
  conditionsForm.value.rangeDateType = null;
  conditionsForm.value.customerDateType = null;
  conditionsForm.value.value = "";
  customConditionControlRef.value?.clearData();
};

const handleSelectDateRangeType = () => {
  conditionsForm.value.value = "";
  customConditionControlRef.value?.clearData();
};

const handleWidgetValue = (bindValue: string, label: string) => {
  conditionsForm.value.value = bindValue;
  if (label) {
    conditionsForm.value.label = label;
  }
};

const getSystemMembersList = () => {
  return new Promise(async resolve => {
    // 创建人和订单创建人
    const sccsId: string = route.query.sccsId as string;
    const { data } = await getWorkOrderProcessorList({
      sccsId: sccsId,
      type: "ASSIGN_PROCESSOR"
    });
    const { teamMemberList, coopTeamList } = data;
    const teamMemberLists = [];
    const teamList: any[] = storageSession().getItem("userMemberTeam");
    //@ts-ignore
    const { latestLoginTeamId } = storageLocal().getItem("user-info");
    const thatTeam = teamList.find(
      teamItem => teamItem.id === latestLoginTeamId
    );
    teamMemberLists.push({
      label: `${thatTeam.teamName}(本团队)`,
      value: thatTeam.id,
      children: teamMemberList.map(teamMember => {
        return {
          label: teamMember.username,
          value: teamMember.teamMemberId
        };
      })
    });
    coopTeamList.forEach(coopTeam => {
      teamMemberLists.push({
        label: coopTeam.teamName,
        value: coopTeam.id,
        children: coopTeam.teamMemberList.map(teamMember => {
          return {
            label: teamMember.username,
            value: teamMember.teamMemberId
          };
        })
      });
    });
    resolve(teamMemberLists);
    // return teamMemberLists;
  });
};

const getRangeTypeOptions = () => {
  if (conditionsForm.value.dateType) {
    const dateTypeOptions = {
      year: ["THIS_YEAR", "LAST_YEAR", "NEXT_YEAR"],
      month: ["THIS_MONTH", "LAST_MONTH", "NEXT_MONTH"],
      day: [
        "THIS_YEAR",
        "LAST_YEAR",
        "NEXT_YEAR",
        "THIS_MONTH",
        "LAST_MONTH",
        "NEXT_MONTH",
        "THIS_WEEK",
        "LAST_WEEK",
        "NEXT_WEEK"
      ]
    };
    return DateRangeOptions.filter(
      date =>
        dateTypeOptions[conditionsForm.value.dateType].includes(date.value) ||
        ["DATE_RANGE", "LAST", "NEXT"].includes(date.value)
    );
  } else {
    return DateRangeOptions;
  }
};

const handleDatePickerValue = () => {
  conditionsForm.value.value = dateRange.value.join(",");
};

const handleEdit = e => {
  let value = e.replace(/^(0+)|[^\d]+/g, ""); // 以0开头或者输入非数字，会被替换成空
  value = value.replace(/(\d{15})\d*/, "$1"); // 最多保留15位整数
  conditionsForm.value.value = value;
};

const getDatePickerOptionsByType = options => {
  const dateTypeOptions = {
    year: ["THIS_YEAR", "LAST_YEAR", "NEXT_YEAR"],
    month: ["THIS_MONTH", "LAST_MONTH", "NEXT_MONTH"],
    day: ["TODAY", "YESTERDAY", "TOMORROW"]
  };
  return dateTypeOptions[conditionsForm.value.dateType]
    ? options.filter(
        option =>
          dateTypeOptions[conditionsForm.value.dateType].includes(
            option.value
          ) || ["ASSIGN", "BEFORE", "AFTER"].includes(option.value)
      )
    : options;
};

// 订单创建人，订单更新人更新值
const handleSelectMember = () => {
  const teamCodeList = teamMemberCascaderRef.value.getCheckedNodes(false);
  const commitTeamList = teamCodeList.filter(
    teamCode => teamCode.pathValues.length === 2
  );
  let bindValue = "";
  let bindText = "";
  commitTeamList.forEach(teamMember => {
    bindValue += `,${teamMember.value}`;
    bindText += `,${teamMember.text}`;
  });
  conditionsForm.value.value = bindValue.substr(1);
  conditionsForm.value.label = bindText.substr(1);
};

const handleUpdateFieldTypeEnum = (fieldTypeEnum: any) => {
  conditionsForm.value.fieldTypeEnum = fieldTypeEnum;
};

const handleSetTableRowConditions = () => {
  emit("handleChangeConditions", conditionsForm.value);
};

const getTimeDateType = () => {
  return conditionsForm.value.dateType === "year"
    ? t("trade_common_year")
    : conditionsForm.value.dateType === "month"
      ? t("trade_common_month")
      : t("trade_common_days");
};

watch(
  () => conditionsForm,
  async () => {
    handleSetTableRowConditions();
  },
  {
    deep: true
  }
);

watch(
  props.condition,
  newVal => {
    if (newVal && newVal.firstDataObject && Object.keys(newVal).length > 0) {
      conditionsForm.value = newVal;
      nextTick(async () => {
        const { fieldType, field } = newVal.firstDataObject;
        workOrderRangeVisible.value =
          CascaderRef.value.getCheckedNodes()[0]?.pathNodes[1]?.data?.type ===
          "formSysField";

        emit(
          "handleConditionFirstData",
          workOrderRangeVisible.value,
          CascaderRef.value.getCheckedNodes()[0],
          CascaderRef.value.getCheckedNodes()[0]?.pathNodes[1]?.data?.value
        );

        if (["DrInputNumber", "DrRate", "DrPercentage"].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions["number"];
          firstOpertatorSelectControl.value = false;
        } else if (["DrDatePicker"].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions["date"];
          firstOpertatorSelectControl.value = true;
          if (newVal.rangeDateType === "DATE_RANGE") {
            dateRange.value = newVal.value.split(",");
          }
          if (newVal.dateType === "year") {
            widgetDate.value.valueFormat = "YYYY";
            widgetDate.value.format = "YYYY";
            conditionsForm.value.dateType = "year";
          }
        } else if (
          ["DrRadio", "DrCheckbox", "DrDatePicker", "DrRate"].includes(
            fieldType
          ) &&
          !["orderState", "status"].includes(field)
        ) {
          MathOperatorSelectOptions.value = MathOperatorOptions["widget"];
          firstOpertatorSelectControl.value = false;
          const cascaderRefs = CascaderRef.value.getCheckedNodes()[0];
          const { field, formId } = cascaderRefs.data;
          if (formId) {
            const { data } = await getWidgetItem({
              widgetId: field,
              formPubId: formId
            });
            widgetOptions.value = data.props.options;
          }
        } else if (["DrImagesUpload", "DrFilesUpload"].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions["file"];
          firstOpertatorSelectControl.value = false;
        } else {
          MathOperatorSelectOptions.value = MathOperatorOptions["widget"];
          firstOpertatorSelectControl.value = false;
        }
        if (field === "orderState") {
          widgetOptions.value = dict.getDictByCode("order_state");
        }
        if (field === "status") {
          widgetOptions.value = dict.getDictByCode("milestone_status");
        }
        if (personWidgetName.includes(field)) {
          teamMemberBindValue.value = ["IN", "NOT_IN"].includes(newVal.operator)
            ? newVal.value.split(",")
            : newVal.value;
        }
        widgetForm.value = Object.assign(cloneDeep(newVal.firstDataObject), {
          type: newVal.firstDataObject.fieldType
        });
        if (
          [
            "DrRadio",
            "DrSCCSMemberSingleSelect",
            "DrSCCSMemberMultipleSelect",
            "DrSCCSGroupMemberSingleSelect",
            "DrSCCSGroupMemberMultipleSelect"
          ].includes(newVal.firstDataObject.fieldType)
        ) {
          multiple.value = ["IN", "NOT_IN"].includes(
            conditionsForm.value.operator
          );
        } else if (newVal.firstDataObject.fieldType === "DrCheckbox") {
          multiple.value = true;
        }
      });
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => props.conditionList,
  () => {
    if (props.conditionList) {
      planSettingSelect.value = props.conditionList;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const planSettingSelectData = computed(() => {
  return cloneDeep(planSettingSelect.value);
  // const childDataList = cloneDeep(planSettingSelect.value).map(
  //   planSelectData => {
  //     return Object.assign(planSelectData, {
  //       children: planSelectData.children.map(planNextData => {
  //         return Object.assign(planNextData, {
  //           children: planNextData.children.filter(
  //             plannedData => !plannedData.deleted
  //           )
  //         });
  //       })
  //     });
  //   }
  // );
  // return childDataList;
});

onMounted(() => {
  nextTick(async () => {
    widgetTeamMemberList.value = await getSystemMembersList();
  });
});

defineExpose({
  CascaderRef: CascaderRef.value,
  conditionsForm: conditionsForm.value,
  handleSelectCascader,
  handleUpdateFieldTypeEnum
});
</script>
<style lang="scss" scoped>
.custom-conditions-control-row {
  display: flex;

  ::v-deep(.el-cascader) {
    width: 140px;
    margin-right: 10px;

    .el-cascader__tags {
      flex-wrap: nowrap;
      max-width: 200px;
    }
  }

  ::v-deep(.el-select) {
    margin-right: 10px;
  }

  ::v-deep(.el-date-editor) {
    margin-right: 10px;
  }

  ::v-deep(.el-textarea) {
    margin-right: 10px;
  }

  ::v-deep(.el-input) {
    margin-right: 10px;
  }

  ::v-deep(.el-cascader-node) {
    max-width: 400px;
  }

  .conditions-form-input {
    width: 100px;

    ::v-deep(.el-input-group__append) {
      padding: 0 10px;
    }
  }

  .custom-conditions-operator-control {
    width: 130px;
  }

  .custom-conditions-person-operator-control {
    width: 180px;
  }

  .custom-conditions-operator-small-control {
    width: 80px;
  }

  .custom-conditions-col {
    display: inline-flex;
  }
}
</style>
<style lang="scss">
.conditionFormFirstCascader {
  height: 410px;

  .el-cascader-panel {
    height: 100%;

    .el-cascader-node__label {
      height: 100%;

      .el-text {
        color: #606266 !important;
      }
    }

    .el-cascader-menu {
      .el-cascader-menu__wrap {
        height: 100%;
      }
    }
  }
}
</style>
