import { http } from "@/utils/http";
import type { Result } from "./type";

/**
 * 获得贸易端我的团队列表
 * @returns
 */
export const getMyTeamList = () => {
  return http.request<Result>("get", "/trade/team/list-by-user");
};

/**
 * 修改用户信息
 * @returns
 */
export const updateUserInfo = (data: {
  avatar?: string | null;
  username: string;
  sex?: number | null;
}) => {
  return http.request<Result>("post", "/trade/user/update-user", { data });
};

/**
 * 修改用户信息
 * @returns
 */
export const updateUserPassword = (data: {
  oldPassword: string;
  newPassword: string;
}) => {
  return http.request<Result>("post", "/trade/user/update-password", { data });
};

/**
 * 根据类型获取国际化翻译数据存在本地
 * @param data
 */
export const getSystemLang = () => {
  return http.request<Result>(
    "get",
    `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_API_ADMIN_PREFIX}/infra/internalization/list-by-type?type=trade`
  );
};

/**
 * 获取字典
 * @param data
 */
export const getSystemDictData = () => {
  return http.request<Result>(
    "get",
    `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_API_ADMIN_PREFIX}/system/dict-data/simple-list`
  );
};

/**
 * 取当前团队成员所有的团队权限列表
 * @param params
 * @returns
 */
export const getTeamMemberPermission = () => {
  return http.request<Result>(
    "get",
    "/trade/team-member/get-cur-perm-key-list"
  );
};

/**
 * 获取首页基础信息
 * @param params
 * @returns
 */
export const getTeamHome = (params: { teamId: string }) => {
  return http.request<Result>("get", "/trade/home/<USER>", {
    params
  });
};

/**
 * 获取团队详情
 * @param params
 * @returns
 */
export const getTeamDetail = (params: { id: string }) => {
  return http.request<Result>("get", "/trade/team/detail", {
    params
  });
};

/**
 * 刷新令牌
 * @returns
 */
export const getAuthRefreshToken = (params: { refreshToken: string }) => {
  return http.request<Result>("post", "/trade/auth/refresh-token", {
    params
  });
};

/**
 * 分片上传文件
 * @returns
 */
export const uploadFile = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/file/upload",
    {
      data
    },
    {
      headers: { "Content-Type": "multipart/form-data" }
    }
  );
};

/**
 * 获取汇率【需要用实际金额*汇率，单位元】
 * @returns
 */
export const exchangeRates = (params: { fromCoin: string; toCoin: string }) => {
  return http.request<Result>(
    "get",
    `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_API_ADMIN_PREFIX}/infra/common/exchangeRates`,
    {
      params
    }
  );
};

/**
 * 修改用户签名
 * @returns
 */
export const updateUserSign = (data: any) => {
  return http.request<Result>("post", "/trade/user/update-sign", {
    data
  });
};

/**
 * 修改用户语言
 * @returns
 */
export const updateUserLanguage = (data: any) => {
  return http.request<Result>("post", "/trade/user/update-language", {
    data
  });
};

/**
 * 检查用户邮箱是否激活
 * @returns
 */
export const getUserActive = (params: { email: string }) => {
  return http.request<Result>("get", "/trade/user/active", {
    params
  });
};

/**
 * 取当前用户在当前sccs的角色权限(和订单挂钩的权限)
 * @returns
 */
export const getSccsRolePermMap = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/sccs-role/get-cur-user-sccs-role-order-perm-map",
    {
      data
    }
  );
};

/**
 * 取当前用户在当前sccs的角色权限(没有和订单挂钩的权限)
 * @returns
 */
export const getSccsRolePermSet = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-role/get-cur-user-sccs-role-perm-set",
    {
      params
    }
  );
};

/**
 * 获取当前用户在当前协作sccs的角色权限(没有和订单挂钩的权限)
 * @returns
 */
export const getCoopSccsRolePermSet = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-coop-role/user-coop-sccs-role-perm-set",
    {
      params
    }
  );
};

/**
 * 获取子表关联引用数据
 * @returns
 */
export const getSubTableFormRefList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/work-order/get-table-form-work-order-ref-list",
    {
      params
    }
  );
};

/**
 * 获取字段显隐配置
 * @returns
 */
export const getSccsFieldVisibleConditions = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/sccs/get-field-visibility-conditions",
    {
      params
    }
  );
};

/**
 * 获取字段显隐配置
 * @returns
 */
export const linkedDataReference = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/widget/linked-reference-data-list",
    {
      params
    }
  );
};

/**
 * 保存关联引用来源数据
 * @returns
 */
export const saveLinkedReferenceSource = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/widget/save-linked-reference-source",
    {
      data
    }
  );
};
