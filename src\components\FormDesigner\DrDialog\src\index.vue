<template>
  <div class="widget-table-container" :style="{ width: `${width}px` }">
    <i class="iconfont link-add-to" @click="handleAdd" />
    <div
      v-if="widgetConfigure.type === 'DrImagesUpload'"
      class="widget-image-list-container"
    >
      <el-image
        v-for="imageItem in imageList"
        :key="imageItem.uid"
        :src="imageItem.url"
        :preview-src-list="imageList.map(image => image.url)"
        :preview-teleported="true"
        fit="cover"
      />
    </div>
    <div
      v-if="widgetConfigure.type === 'DrFilesUpload'"
      class="widget-file-list-container"
    >
      <OrderFilePreview
        v-for="item in fileList"
        :key="item"
        :filePreview="item"
        :fileList="fileList"
        style="pointer-events: auto; cursor: pointer"
      />
    </div>
    <ReText
      v-if="widgetConfigure.type === 'DrAddress'"
      class="widget-address-list-container"
      type="info"
      :tippyProps="{ delay: 50000 }"
    >
      {{ addressText }}
    </ReText>
    <div
      v-if="editorHtml && editorHtml !== '<p><br></p>'"
      class="widget-editor-list-container"
    >
      <el-button
        type="primary"
        plain
        size="small"
        @click="editorVisible = true"
      >
        查看
      </el-button>
    </div>
    <ReText
      v-if="
        widgetConfigure.type === 'DrExchangeRates' &&
        exchangeRatesValue.hasOwnProperty('fromMoney') &&
        exchangeRatesValue.fromMoney !== ''
      "
      class="widget-exchange-rates-list-container"
      type="info"
      :tippyProps="{ delay: 50000 }"
    >
      {{
        exchangeRatesValue.exchangeType === "fixed" ? "固定汇率" : "实时汇率"
      }}
      &nbsp;
      {{ exchangeRatesValue.rate }}
      &emsp;
      {{ exchangeRatesValue.fromMoney
      }}{{ exchangeRatesValue.fromMoneyType }}~{{
        exchangeRatesValue.targetMoney || " - "
      }}{{ exchangeRatesValue.targetMoneyType }}
    </ReText>
  </div>
  <el-dialog
    v-model="dialogVisible"
    :title="widgetConfigure.title"
    class="arco-select-vtable"
    width="700"
    append-to-body
    align-center
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    style="pointer-events: auto; cursor: pointer"
  >
    <DrImagesUpload
      v-if="widgetConfigure.type === 'DrImagesUpload'"
      ref="DrImagesUploadRef"
      :widgetConfigure="widgetConfigure"
      :trendsForm="widgetTrendsForm"
      :widgetRowIndex="widgetRowIndex"
      style="pointer-events: auto; cursor: pointer"
      @handleUpdateWidgetData="handleUpdateTableWidgetData"
    />
    <DrFilesUpload
      v-if="widgetConfigure.type === 'DrFilesUpload'"
      ref="DrFileUploadRef"
      :widgetConfigure="widgetConfigure"
      :trendsForm="widgetTrendsForm"
      :widgetRowIndex="widgetRowIndex"
      style="pointer-events: auto; cursor: pointer"
      @handleUpdateWidgetData="handleUpdateTableWidgetData"
    />
    <DrEditor
      v-if="widgetConfigure.type === 'DrEditor'"
      :widgetConfigure="widgetConfigure"
      :trendsForm="widgetTrendsForm"
      :widgetRowIndex="widgetRowIndex"
      style="pointer-events: auto; cursor: pointer"
      @handleUpdateWidgetData="handleUpdateTableWidgetData"
    />
    <DrAddress
      v-if="widgetConfigure.type === 'DrAddress'"
      :widgetConfigure="widgetConfigure"
      :trendsForm="widgetTrendsForm"
      :widgetRowIndex="widgetRowIndex"
      style="pointer-events: auto; cursor: pointer"
      @handleUpdateWidgetData="handleUpdateTableWidgetData"
    />
    <DrExchangeRates
      v-if="widgetConfigure.type === 'DrExchangeRates'"
      :widgetConfigure="widgetConfigure"
      :trendsForm="widgetTrendsForm"
      :widgetRowIndex="widgetRowIndex"
      style="pointer-events: auto; cursor: pointer"
      @handleUpdateWidgetData="handleUpdateTableWidgetData"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button
          type="primary"
          :disabled="dialogBtnDisabled"
          @click="handleSaveTableData"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="editorVisible"
    class="lk-maximum-dialog"
    append-to-body
    align-center
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <Editor
      v-model="editorHtml"
      :autofocus="false"
      :defaultConfig="{ readOnly: true }"
    />
  </el-dialog>
</template>
<script lang="tsx" setup>
import { computed, onMounted, provide, ref, watch } from "vue";
import { ElDialog, ElButton, ElImage } from "element-plus";
import { ReText } from "@/components/ReText";
import { Editor } from "@wangeditor/editor-for-vue";
import OrderFilePreview from "@/views/orderManage/components/OrderFilePreview.vue";
import DrEditor from "@/components/FormDesigner/DrEditor/index";
import DrImagesUpload from "@/components/FormDesigner/DrImagesUpload/index";
import DrFilesUpload from "@/components/FormDesigner/DrFilesUpload/index";
import DrAddress from "@/components/FormDesigner/DrAddress/index";
import DrExchangeRates from "@/components/FormDesigner/DrExchangeRates/index";
import { emitter } from "@/utils/mitt";
import { isEqual } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  width: {
    type: Number as PropType<number>,
    default: 0
  }
});

const DrImagesUploadRef = ref<HTMLElement | null>(null);
const DrFileUploadRef = ref<HTMLElement | null>(null);
const dialogBtnDisabled = ref<boolean>(false);
const dialogVisible = ref<boolean>(false);
const imageList = ref<any[]>([]);
const fileList = ref<any[]>([]);
const addressText = ref<string>("");
const editorVisible = ref<boolean>(false);
const editorHtml = ref<string>("");
const exchangeRatesValue = ref<any>({});
const dialogWidgetData = ref<any>({});
const cacheWidgetData = ref<any>({});

const widgetTrendsForm = computed(() => {
  const entries = Object.entries(props.trendsForm).map(([key, value]) => {
    return [
      key,
      typeof value === "string" &&
      (value.indexOf("[") > -1 || value.indexOf("{") > -1)
        ? JSON.parse(value) instanceof Array
          ? JSON.parse(value)
          : JSON.parse(value).hasOwnProperty("label")
            ? JSON.parse(value).obj
            : JSON.parse(value)
        : value
    ];
  });
  return Object.fromEntries(entries);
});

const emit = defineEmits(["handleOpenSubTableDialog"]);

const handleAdd = () => {
  dialogVisible.value = true;
};

const handleCloseDialog = () => {
  const widgetType = cacheWidgetData.value.widgetType;
  if (widgetType === "DrImagesUpload") {
    (DrImagesUploadRef.value as any).handleClearFiles();
  } else if (widgetType === "DrFilesUpload") {
    (DrFileUploadRef.value as any).handleClearFiles();
  }

  cacheWidgetData.value = {};
  dialogVisible.value = false;
};

const handleSaveTableData = () => {
  const widgetType = cacheWidgetData.value.widgetType;
  const widgetData = cacheWidgetData.value.widgetData;

  if (widgetType === "DrImagesUpload") {
    imageList.value = widgetData.obj;
  } else if (widgetType === "DrFilesUpload") {
    fileList.value = widgetData.obj;
  } else if (widgetType === "DrAddress") {
    addressText.value = widgetData.label;
  } else if (widgetType === "DrEditor") {
    editorHtml.value = widgetData.obj;
  } else if (widgetType === "DrExchangeRates") {
    exchangeRatesValue.value = widgetData.obj;
  }
  dialogWidgetData.value = widgetData;
  emit(
    "handleOpenSubTableDialog",
    dialogWidgetData.value,
    props.widgetRowIndex
  );
  dialogVisible.value = false;
};

const handleUpdateTableWidgetData = (widgetType: string, widgetData: any) => {
  cacheWidgetData.value = {
    widgetType: widgetType,
    widgetData: widgetData
  };
};

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    if (
      !!newVal &&
      !(typeof newVal === "object" && newVal.hasOwnProperty("areaCodeList"))
    ) {
      let widgetValue;
      try {
        // 尝试解析 JSON 字符串
        widgetValue = JSON.parse(newVal);
      } catch (error) {
        // 如果解析失败，使用原值
        widgetValue = newVal;
      }

      const widgetType = props.widgetConfigure.type;
      if (widgetType === "DrImagesUpload") {
        imageList.value = widgetValue;
      } else if (widgetType === "DrFilesUpload") {
        fileList.value = widgetValue;
      } else if (widgetType === "DrAddress") {
        addressText.value =
          typeof widgetValue.label === "string" ? widgetValue.label : "";
      } else if (widgetType === "DrEditor") {
        editorHtml.value = widgetValue;
      } else if (widgetType === "DrExchangeRates") {
        exchangeRatesValue.value = widgetValue;
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

onMounted(() => {
  emitter.on("widgetUploadLoading", (bool: boolean) => {
    dialogBtnDisabled.value = bool;
  });
});

provide(
  "uploadFileURL",
  `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_API_TRADE_PREFIX}/trade/file/upload`
);
</script>
<style lang="scss" scoped>
.widget-table-container {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 0 10px;
  pointer-events: auto;
  cursor: pointer;

  .iconfont {
    margin-right: 10px;
  }

  .widget-image-list-container {
    display: inline-flex;

    .el-image {
      width: 40px;
      height: 40px;
      margin-left: 10px;
      border: 1px solid #ccc;
    }
  }

  .widget-file-list-container {
    display: inline-flex;
  }

  .widget-address-list-container {
    font-size: 12px;
    color: #262626;
  }

  .widget-exchange-rates-list-container {
    font-size: 12px;
    color: #262626;
  }
}
</style>
