import type { PageProp } from "./type.d";

export interface SavePlanListDetailCalculationDateObjectProp {
  value: string;
  label: string;
  id: string;
  type: string;
  field: string;
  fieldType: string;
  children: SavePlanListDetailCalculationDateObjectProp[];
}

export interface SavePlanListDetailProp {
  milestoneId: string;
  timeType: string;
  relativeDateType: string;
  relativeDays: number;
  plannedDurationDays: number;
  calculationDateObject: SavePlanListDetailCalculationDateObjectProp;
}

export interface SavePlanListProp {
  sccsId: string;
  detailList: SavePlanListDetailProp[];
}

export interface SccsUpdateProp {
  id: string;
  sccsCode: string;
  sccsName: string;
  templateId: string;
  groupId: string;
}

export interface SccsFenceProp {
  id?: string | null;
  sccsId: string | null;
  dataFenceName: string;
  coopTeamIdList: string[];
  userIdList: string[];
  widgetList: string[];
}

export interface SccsOrderRoleProp {
  permissionKey?: string | null;
  orderScope?: string | null;
}

export interface SccsMileStoneRoleProp {
  permissionKeyList?: string[] | null;
  msId?: string | null;
}

export interface SccsEditRoleProp {
  id?: string | null;
  teamMemberIdList?: string[] | null;
  sccsId?: string | null;
  name?: string | null;
  manager?: boolean | null;
  teamId?: string | null;
  sccsToolRolePermItemList?: string[] | null;
  orderRolePermItemList?: SccsOrderRoleProp[] | null;
  milestonePermItem?: SccsOrderRoleProp[] | null;
  milestoneRolePermItemList?: SccsMileStoneRoleProp[] | null;
}

export type SccsCoopTeamRoleProp = PageProp & {
  sccsId: string;
  roleName?: string | null;
  teamId?: string | null;
};

export type SccsFenceListProp = PageProp & {
  sccsId: string;
  dataFenceName?: string | null;
  coopTeamId?: string | null;
  userId?: string | null;
};

export type SccsRoleProp = PageProp & {
  sccsId: string;
  roleName?: string | null;
  teamMemberId?: string | null;
};

export interface SccsCoopEditProp {
  id?: string | null;
  teamIdList?: string[] | null;
  sccsId?: string | null;
  name?: string | null;
  sccsToolRolePermItemList?: string[] | null;
  orderRolePermItemList?: SccsOrderRoleProp[] | null;
  milestonePermItem?: SccsOrderRoleProp[] | null;
  milestoneRolePermItemList?: SccsMileStoneRoleProp[] | null;
  teamMemberIdList?: string[] | null;
}
