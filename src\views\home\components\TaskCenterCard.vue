<template>
  <!-- 团队待指派任务 -->
  <div v-if="myTaskInfo.allTeamTaskCount > 0" class="linkIncrease-work-module">
    <svg class="svg-icon svg-icon-coop" aria-hidden="true">
      <use xlink:href="#link-team-tasks" />
    </svg>
    <span class="team-task-text">{{
      t("trade_team_tasks_to_be_assigned")
    }}</span>
    <span class="team-task-number" @click="goToTaskCenter('MY_TEAM_TASK')">{{
      myTaskInfo.teamNoAssignedCount
    }}</span>
    <span class="team-task-link" @click="goToTaskCenter('MY_TEAM_TASK')"
      >{{ t("trade_go_check_it_out") }}<i class="iconfont link-arrow-right"
    /></span>
  </div>
  <!-- 我的任务/邮件协作 -->
  <div class="home-task-center-card">
    <el-row :gutter="14">
      <el-col :span="12">
        <div class="home-task-center-card-left">
          <div class="card-left-top">
            <div class="card-left-top-title">
              <svg class="svg-icon svg-icon-coop" aria-hidden="true">
                <use xlink:href="#link-my-tasks" />
              </svg>
              {{ t("trade_my_task") }}
            </div>
            <div
              class="card-left-top-text"
              @click="goToTaskCenter('MY_USER_TASK')"
            >
              <span class="card-left-top-text-info"
                >{{ t("trade_view_more_my_tasks")
                }}<i class="iconfont link-arrow-right" />
              </span>
            </div>
          </div>
          <div class="card-left-bottom">
            <div class="my-todo">
              <div class="my-todo-title">{{ t("trade_home_myToDo") }}</div>
              <div
                class="my-todo-number"
                @click="goToTaskCenter('MY_USER_TASK')"
              >
                {{ myTaskInfo.userNoEditedCount }}
              </div>
            </div>
            <div
              class="deadline-today margin-20"
              :class="[screenWidth < 1920 ? 'margin-2' : '']"
            >
              <div class="deadline-today-title color-red">
                {{ t("trade_deadline_for_today") }}
              </div>
              <div
                class="deadline-today-number color-red"
                @click="
                  goToTaskCenter(
                    'MY_USER_TASK',
                    myTaskInfo.planBeforeTodayEndConditionList
                  )
                "
              >
                {{ myTaskInfo.planBeforeTodayEndCount }}
              </div>
            </div>
            <div class="card-line" />
            <div class="my-concerned">
              <div class="my-concerned-title">
                <span class="my-concerned-text">{{
                  t("trade_what_I_am_concerned_about")
                }}</span>
                <span
                  class="my-concerned-number"
                  @click="goToTaskCenter('MY_FAVORITE_TASK')"
                  >{{ myTaskInfo.userFavoriteCount }}</span
                >
              </div>
              <div
                class="my-concerned-desc"
                style="flex-direction: column; align-items: flex-start"
              >
                <el-dropdown trigger="click" @command="handleCommand">
                  <span class="el-dropdown-link">
                    {{ t(dateValue.label) }}
                    <i class="iconfont link-triangle-bottom" />
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="item in dateOptions"
                        :key="item.id"
                        :command="item"
                        >{{ t(item.label) }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <div class="flex">
                  <span class="my-concerned-desc-item">
                    <span
                      class="my-concerned-desc-item-number"
                      style="margin-left: 0"
                      @click="
                        goToTaskCenter(
                          'MY_FAVORITE_TASK',
                          myTaskInfo.taskChangeConditionList
                        )
                      "
                    >
                      {{ myTaskInfo.taskChangeCount }}
                    </span>
                    {{ `${t("trade_the_task_has_been_updated")};` }}
                  </span>
                  <span class="my-concerned-desc-item">
                    <span
                      class="my-concerned-desc-item-number"
                      @click="
                        goToTaskCenter(
                          'MY_FAVORITE_TASK',
                          myTaskInfo.taskCompletedConditionList
                        )
                      "
                    >
                      {{ myTaskInfo.taskCompletedCount }}
                    </span>
                    {{
                      `${t("trade_the_task_has_been_changed_and_completed")};`
                    }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="home-task-center-card-left">
          <div class="card-left-top">
            <div class="card-left-top-title">
              <svg class="svg-icon svg-icon-coop" aria-hidden="true">
                <use xlink:href="#link-email-tasks" />
              </svg>
              {{ t("trade_order_emailCollaborate") }}
            </div>
            <div
              class="card-left-top-text"
              @click="goToTaskCenter('MY_FAVORITE_EMAIL_COOP')"
            >
              <span class="card-left-top-text-info"
                >{{ t("trade_view_more_email_collaborations")
                }}<i class="iconfont link-arrow-right" />
              </span>
            </div>
          </div>
          <div class="card-left-bottom">
            <div class="my-todo">
              <div class="my-todo-title">{{ t("trade_home_myToDo") }}</div>
              <div
                class="my-todo-number"
                @click="goToTaskCenter('MY_FAVORITE_EMAIL_COOP')"
              >
                {{ coopEmailnfo.toDoNumber }}
              </div>
            </div>
            <div
              class="deadline-today margin-20"
              :class="[screenWidth < 1920 ? 'margin-2' : '']"
            >
              <div class="deadline-today-title color-red">
                {{ t("trade_deadline_for_today") }}
              </div>
              <div
                class="deadline-today-number color-red"
                @click="
                  goToTaskCenter(
                    'MY_FAVORITE_EMAIL_COOP',
                    coopEmailnfo.toDayDeadlineNumberConditionList
                  )
                "
              >
                {{ coopEmailnfo.toDayDeadlineNumber }}
              </div>
            </div>
            <div class="card-line" />
            <div class="my-concerned">
              <div class="my-concerned-title" style="margin-bottom: 14px">
                <span class="my-concerned-text">{{
                  t("trade_I_initiated_it")
                }}</span>
              </div>
              <div class="my-concerned-desc">
                <span class="my-concerned-desc-item flexClum"
                  >{{ t("trade_home_inProgress")
                  }}<span
                    class="my-concerned-desc-item-number margin-left-4"
                    @click="goToTaskCenter('MY_FAVORITE_EMAIL_TASK')"
                    >{{ coopEmailnfo.progressNumber }}</span
                  ></span
                >
                <div class="card-line" style="height: 21px; margin: 0 14px" />
                <span class="my-concerned-desc-item flexClum"
                  >{{ t("trade_submitted")
                  }}<span
                    class="my-concerned-desc-item-number margin-left-4"
                    @click="
                      goToTaskCenter(
                        'MY_FAVORITE_EMAIL_TASK',
                        coopEmailnfo.submittedNumberConditionList
                      )
                    "
                    >{{ coopEmailnfo.submittedNumber }}</span
                  ></span
                >
                <span
                  class="my-concerned-desc-item flexClum"
                  style="margin-left: 16px"
                  >{{ t("trade_order_notSubmit")
                  }}<span
                    class="my-concerned-desc-item-number margin-left-4"
                    @click="
                      goToTaskCenter(
                        'MY_FAVORITE_EMAIL_TASK',
                        coopEmailnfo.notSubmittedNumberConditionList
                      )
                    "
                    >{{ coopEmailnfo.notSubmittedNumber }}</span
                  ></span
                >
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

import {
  getUserFavoriteTaskCount,
  getCoopEmailCount,
  getTeamPendingTaskCount,
  getUserPendingTaskCount,
  getUserAllFavoriteTaskCount,
  getUserTodayStopCount
} from "@/api/taskCenter";

interface taskParams {
  taskQueryDayType: string;
}

const router = useRouter();

const { t } = useI18n();
const dateValue = ref({
  label: "trade_past_3_days",
  value: "THREE_DAYS"
});
const dateOptions = [
  {
    id: 1,
    label: "trade_past_3_days",
    value: "THREE_DAYS"
  },
  {
    id: 2,
    label: "trade_past_7_days",
    value: "SEVEN_DAYS"
  },
  {
    id: 3,
    label: "trade_past_month",
    value: "ONE_MONTH"
  }
];
const myTaskInfo = ref({
  userNoEditedCount: 0, // 我的待办数量
  userFavoriteCount: 0, // 我关注的数量
  planBeforeTodayEndCount: 0, // 今日截止数量
  planBeforeTodayEndConditionList: [], // 今日截止筛选条件列表
  teamNoAssignedCount: 0, // 团队待指派任务数量
  allTeamTaskCount: 0, // 所有团队任务数量
  taskChangeCount: 0,
  taskCompletedCount: 0,
  taskCompletedConditionList: [], // 我关注的任务完成筛选条件列表
  taskChangeConditionList: [] // 我关注的任务更新筛选条件列表
});

const coopEmailnfo = ref({
  toDoNumber: 0, // 我的待办数量
  toDayDeadlineNumber: 0, // 今日截止数量
  toDayDeadlineNumberConditionList: [], // 今日截止筛选条件列表
  progressNumber: 0, // 我发起的 进行中
  notSubmittedNumber: 0, // 我发起的 未提交
  notSubmittedNumberConditionList: [], // 我发起的 未提交筛选条件列表
  submittedNumber: 0, // 我发起的 已提交
  submittedNumberConditionList: [] // 我发起的 已提交筛选条件列表
});

const screenWidth = ref(window.innerWidth);
const handleResize = () => {
  screenWidth.value = window.innerWidth;
};

const getTaskCount = async () => {
  let params: taskParams = {
    taskQueryDayType: dateValue.value.value
  };
  const { data } = await getUserFavoriteTaskCount(params as taskParams);
  const {
    taskChangeCount,
    taskCompletedCount,
    taskChangeConditionList,
    taskCompletedConditionList
  } = data;
  myTaskInfo.value.taskChangeCount = taskChangeCount;
  myTaskInfo.value.taskCompletedCount = taskCompletedCount;
  myTaskInfo.value.taskChangeConditionList = taskChangeConditionList;
  myTaskInfo.value.taskCompletedConditionList = taskCompletedConditionList;
};

const handleCommand = (option: any) => {
  dateValue.value.label = option.label;
  dateValue.value.value = option.value;
};

const goToTaskCenter = (type: string, conditions?: any) => {
  let params: any = {
    taskType: type,
    conditions:
      conditions && conditions.length > 0 ? JSON.stringify(conditions) : ""
  };
  router.push({
    path: "/taskCenter",
    query: params
  });
};
const initCount = () => {
  let params: taskParams = {
    taskQueryDayType: dateValue.value.value
  };
  Promise.all([
    getCoopEmailCount(),
    getUserFavoriteTaskCount(params as taskParams),
    getTeamPendingTaskCount(), // 团队待指派任务数量
    getUserPendingTaskCount(), // 我的待办数量
    getUserAllFavoriteTaskCount(), // 我关注的数量
    getUserTodayStopCount() // 今日截止数量
  ])
    .then(data => {
      coopEmailnfo.value = data[0].data;
      const {
        taskChangeCount,
        taskCompletedCount,
        taskChangeConditionList,
        taskCompletedConditionList
      } = data[1].data;
      myTaskInfo.value.taskChangeCount = taskChangeCount;
      myTaskInfo.value.taskCompletedCount = taskCompletedCount;
      myTaskInfo.value.taskChangeConditionList = taskChangeConditionList;
      myTaskInfo.value.taskCompletedConditionList = taskCompletedConditionList;

      myTaskInfo.value.teamNoAssignedCount = data[2].data.count;
      myTaskInfo.value.allTeamTaskCount = data[2].data.allTeamTaskCount;
      myTaskInfo.value.userNoEditedCount = data[3].data.count;
      myTaskInfo.value.userFavoriteCount = data[4].data.count;
      myTaskInfo.value.planBeforeTodayEndCount = data[5].data.count;
      myTaskInfo.value.planBeforeTodayEndConditionList =
        data[5].data.planBeforeTodayEndConditionList;
    })
    .catch(err => {
      console.log(err);
    });
};
watch(
  () => dateValue.value,
  () => {
    getTaskCount();
  },
  {
    deep: true
  }
);
defineExpose({
  myTaskInfo
});
onMounted(() => {
  window.addEventListener("resize", handleResize);
  initCount();
});
onBeforeUnmount(() => {
  // 在组件卸载前移除 resize 事件监听器，避免内存泄漏
  window.removeEventListener("resize", handleResize);
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
// 我的任务
.home-task-center-card {
  width: 100%;
  padding: 14px 16px;
  margin-bottom: 16px;
  background-image: url("@/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 8px;

  .home-task-center-card-left {
    width: 100%;

    .card-left-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .card-left-top-title {
        padding: 11px 50px 11px 16px;
        font-size: 16px;
        font-weight: bolder;
        color: #262626;
        background-image: url("/src/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: cover;

        .svg-icon {
          width: 30px;
          height: 30px;
          margin-right: 6px;
          vertical-align: middle;
        }
      }

      .card-left-top-text {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #358ff0;
        cursor: pointer;

        .link-arrow-right {
          margin-left: 8px;
          font-size: 10px;
          vertical-align: middle;
        }
      }
    }

    .card-left-bottom {
      display: flex;
      align-items: center;
      width: 100%;
      height: 126px;
      padding: 10px 16px;
      background: #f3f8ff;
      border-radius: 0 12px 12px;
      opacity: 0.9;

      .my-todo,
      .deadline-today {
        display: flex;
        flex-direction: column;
        align-items: center;

        &.margin-20 {
          margin: 0 20px;
        }

        &.margin-2 {
          margin: 0 20px;
        }

        .my-todo-title,
        .deadline-today-title {
          margin-bottom: 24px;
          font-size: 14px;
          font-weight: normal;
          color: #595959;
          text-align: center;
          white-space: nowrap;

          &.color-red {
            color: #ff4635;
          }
        }

        .my-todo-number,
        .deadline-today-number {
          font-size: 24px;
          font-weight: bolder;
          color: #262626;
          cursor: pointer;

          &.color-red {
            color: #e62412;
          }
        }
      }

      .card-line {
        width: 1px;
        height: 69px;
        border: 1px solid #fff;
      }

      .my-concerned {
        flex: 1;
        margin-left: 20px;

        .my-concerned-title {
          display: flex;
          width: auto;

          .my-concerned-text {
            display: inline-block;
            margin-right: 14px;
            font-size: 14px;
            color: #595959;
          }

          .my-concerned-number {
            display: inline-block;
            margin-top: -3px;
            font-size: 24px;
            font-weight: bolder;
            line-height: normal;
            color: #262626;
            cursor: pointer;
          }
        }

        .my-concerned-desc {
          display: flex;
          align-items: center;

          .my-concerned-desc-item {
            font-size: 13px;
            font-weight: bold;
            color: #595959;
            white-space: nowrap;

            &.flexClum {
              display: flex;
              flex-direction: column;
            }

            .my-concerned-desc-item-number {
              margin: 0 4px 0 12px;
              font-size: 16px;
              font-weight: bold;
              color: #0070d2;
              cursor: pointer;

              &.margin-left-4 {
                margin: 0;
                margin-left: 4px;
              }
            }
          }

          .el-dropdown-link {
            padding: 5px;
            margin: 5px 0 2px;
            font-size: 14px;
            color: #262626;
            background: #c7dbff;
            border-radius: 4px;

            .link-triangle-bottom {
              font-size: 10px;
              color: #595959;
            }
          }
        }
      }
    }
  }
}
</style>
