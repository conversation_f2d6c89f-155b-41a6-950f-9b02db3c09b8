import type { Directive, DirectiveBinding } from "vue";
import { storageLocal, storageSession } from "@pureadmin/utils";
import { getSystemUserInfo } from "@/utils/auth";

export const perms: Directive = {
  async mounted(
    el: HTMLElement,
    binding: DirectiveBinding<string | Array<string>>
  ) {
    const { value } = binding;
    if (value) {
      let userPermissions: any[] = storageSession().getItem("userPermission");
      if (!userPermissions) {
        const { accessToken, refreshToken, expires, username } =
          storageLocal().getItem("user-info") as any;
        await getSystemUserInfo({ accessToken, refreshToken, expires });
        userPermissions = storageSession().getItem("userPermission");
        sessionStorage.setItem("username", username);
      }
      if (!userPermissions.includes(value)) {
        el.parentNode?.removeChild(el);
      }
    } else {
      throw new Error(
        "[Directive: perms]: need perms! Like v-perms=\"['btn.add','btn.edit']\""
      );
    }
  }
};
