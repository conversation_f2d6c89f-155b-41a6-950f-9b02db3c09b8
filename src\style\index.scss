@use "./transition";
@use "./element-plus";
@use "./sidebar";
@use "./dark";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;

  /* 常用border-color 需要时可取用 */
  --pure-border-color: rgb(5 5 5 / 6%);

  /* switch关闭状态下的color 需要时可取用 */
  --pure-switch-off-color: #a6a6a6;
}

.vxe-table--fixed-left-wrapper,
.vxe-table--fixed-right-wrapper {
  z-index: 2002 !important;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

.el-loading-mask {
  z-index: 3099 !important;
}

.el-tooltip__trigger:focus {
  outline: none; // unset 这个也行
}

.vxe-header--column {
  .vxe-cell-title-prefix-icon {
    cursor: pointer !important;
  }

  .vxe-cell-title-suffix-icon {
    cursor: pointer !important;
  }
}

.vxe-table--tooltip-wrapper {
  z-index: 3001 !important;
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

.widget-popper-label-class {
  max-width: 300px !important;
}

.font12 {
  font-size: 12px !important;
}

.font13 {
  font-size: 13px !important;
}

.font14 {
  font-size: 14px !important;
}

.font16 {
  font-size: 16px !important;
}

.font18 {
  font-size: 18px !important;
}

.font20 {
  font-size: 20px !important;
}

.font22 {
  font-size: 22px !important;
}

.font24 {
  font-size: 24px !important;
}

.verticalBottom {
  vertical-align: bottom;
}

.icon-custom-coop {
  margin-right: 6px;
  font-size: 12px;
  color: rgb(246 151 79);
  vertical-align: middle;
}

.color8c {
  color: #8c8c8c !important;
}

.width140 {
  width: 140px !important;
}

.width180 {
  width: 180px !important;
}

.width260 {
  width: 260px !important;
}

.mr4 {
  margin-right: 4px !important;
}

.mr10 {
  margin-right: 10px !important;
}

.mt10 {
  margin-top: 10px !important;
}

.colorRed {
  color: #cf1421 !important;
}

.show-btn {
  display: block;
}

.show-hide {
  display: none !important;
}

.no-footer-dialog {
  .el-dialog__footer {
    padding: 0 !important;
  }
}

.tippy-box {
  font-size: 12px !important;

  .tippy-content {
    padding: 5px 11px !important;
    word-break: break-all;
    white-space: normal;

    span {
      color: #fff !important;
      background: #333 !important;
    }

    // box-sizing: border-box;
    // height: 32px !important;
    // line-height: 32px !important;
  }
}

.el-table {
  .activate-row {
    .el-table__cell {
      &:first-child {
        padding: 0 !important;

        .cell {
          width: 100%;
          padding: 0 !important;

          .el-table-register-text {
            padding: 8px !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-wrap: break-word;
          }
        }
      }
    }
  }
}

.el-message-box--center {
  justify-content: left !important;
  padding: 20px !important;

  .el-message-box__title {
    justify-content: left !important;
    font-size: 16px;
    font-weight: bolder;
    line-height: 24px;
    color: #262626;

    .el-message-box__status {
      font-size: 19px !important;
      color: #fa8d0a !important;
    }

    .el-message-box-icon--error {
      font-size: 19px !important;
      color: #e62412 !important;
    }
  }

  .el-message-box__headerbtn {
    top: 7px !important;
    right: 7px !important;
  }

  .el-message-box__container {
    justify-content: left !important;

    .el-message-box__message {
      p {
        word-break: break-all;
        white-space: normal;
      }
    }
  }

  .el-message-box__btns {
    justify-content: right !important;
  }

  .confrim-message-btn-class {
    background: #e62412;
    border-color: #e62412 !important;

    &:hover {
      background: #f87872;
      border-color: #f87872 !important;
    }
  }

  .confrim-message-btn-warn-class {
    background: #0070d2;
    border-color: #0070d2 !important;

    &:hover {
      background: rgb(77 155 224);
      border-color: rgb(77 155 224) !important;
    }
  }

  .el-message-box__message {
    margin-bottom: 30px !important;
    font-size: 14px;
    line-height: 20px;
    color: #595959;
  }
}

.horizontal-scrollbar-only .is-horizontal {
  display: none !important;
}

.order_confirm_message_box.el-message-box {
  --el-messagebox-width: 480px !important;

  width: 480px !important;

  .el-message-box__message {
    padding-left: 26px;
    font-size: 14px;
    line-height: 20px;
    color: #595959;
  }

  .el-message-box__header {
    .el-message-box__title {
      span {
        font-weight: bolder;
      }
    }
  }
}

.ml3 {
  margin-left: 3px;
}

.w210 {
  .el-tabs__item {
    width: 210px !important;
  }
}

.color80 {
  color: #808080;
}

.colorfa8d0a {
  color: #fa8d0a;
}

.el-avatar {
  margin-right: 3px;
  font-size: 10px !important;
  vertical-align: middle !important;
  background: #0070d2 !important;
}

.color0070d2 {
  color: #0070d2 !important;

  .iconfont {
    margin-right: 5px;
  }
}

.alignRigth {
  .el-form-item__content {
    justify-content: right;
  }
}

.el-avatar.colorOrange {
  font-size: 10px !important;
  background: #ec7840 !important;
  border-radius: 4px !important;
}

.el-dropdown-menu__item {
  padding: 2px 0;
}

.el-dropdown-menu__item:not(.is-disabled):hover {
  color: #303133 !important;
  background: #f2f2f2 !important;
}

.create-email-collaborate-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 54px;
    padding: 0 16px !important;
    line-height: 54px;

    .el-dialog__title {
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      width: calc(100% - 30px);

      .work-order-header-small-button {
        display: inline-block;
        width: 28px;
        height: 30px;
        margin-right: 8px;
        line-height: 30px;
        text-align: center;
        border-radius: 4px;

        &.work-order-header-default-button {
          width: 42px;

          &:hover {
            background: transparent;
          }

          .iconfont {
            font-size: 14px;
          }
        }

        &:hover {
          background: #e5e5e5;
        }

        &.active {
          background: #e1edff;

          .iconfont {
            color: #0070d2;
          }
        }

        .iconfont {
          font-size: 16px;
          color: #808080;
          cursor: pointer;

          &.font18 {
            font-size: 18px;
          }
        }
      }
    }
  }

  .el-dialog__body {
    height: calc(100% - 116px) !important;
    padding: 10px;
    background: #f6f6f6 !important;
  }
}

.dialog-footer-btn-group {
  margin-bottom: 0 !important;
  text-align: right !important;

  .el-form-item__content {
    justify-content: end !important;
  }
}

/* 自定义滚动条的样式 */

/* 针对于 Firefox 的滚动条样式 */
// * {
//   scrollbar-width: thin; /* "auto" or "thin" */
//   scrollbar-color: #bfbfbf #f0f0f0; /* 滑块颜色 轨道颜色 */
// }

/* 滚动条整体部分 */
::-webkit-scrollbar {
  width: 6px; /* 宽度 */
  height: 6px; /* 高度 */
}

::-moz-scrollbar {
  width: 6px;

  /* 宽度 */
  height: 6px;

  /* 高度 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: #bfbfbf; /* 滑块颜色 */
  border-radius: 3px; /* 圆角 */
}

::-moz-scrollbar-thumd {
  background-color: #bfbfbf; /* 滑块颜色 */
  border-radius: 3px; /* 圆角 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #fff; /* 轨道颜色 */
}

::-moz-scrollbar-track {
  background-color: #fff; /* 轨道颜色 */
}

.el-collapse.order-form-edit-collapse {
  border: 0 none;

  .el-collapse-item {
    margin-bottom: 11px;
    border: 1px solid #e6eaf0;
    border-radius: 4px;

    .el-collapse-item__header {
      padding: 0 20px;
      font-size: 14px;
      font-weight: bold;
      line-height: 16px;
      color: #262626;
      text-align: left;
      background: #f6f9fb;
    }

    .el-collapse-item__wrap {
      padding: 20px;
      border: 0 none;

      .el-collapse-item__content {
        padding: 0;
      }
    }
  }
}

.no-horizontal-scrollbar {
  .el-scrollbar__bar.is-horizontal {
    display: none !important;
  }
}

.no-scrollbar {
  .el-scrollbar__bar.is-horizontal {
    display: none !important;
  }

  .el-scrollbar__bar.is-vertical {
    display: none !important;
  }
}

.no-vertical-scrollbar {
  .el-scrollbar__bar.is-vertical {
    display: none !important;
  }
}

.el-avatar.avatarFont16 {
  font-size: 16px !important;
}

.avatarfont16 {
  .el-avatar {
    font-size: 16px !important;
  }
}

.avatarfont14 {
  .el-avatar {
    font-size: 14px !important;
  }
}

.draw-header-title {
  width: 100%;
  padding: 0 20px;
  overflow: hidden;
  font-size: 16px;
  font-weight: bolder;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// .el-table {
//   max-height: calc(100% - 80px);
// }

input::-webkit-outer-spin-button {
  appearance: none !important;
}

input::-webkit-inner-spin-button {
  appearance: none !important;
}

.svg-icon-coop {
  display: inline-block;
  width: 12px;
  height: 12px;
  vertical-align: middle;
}

.el-dialog__title {
  font-weight: bolder;
}

.order-detail-descriptions-row {
  max-width: 100%;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  // 新增样式：最后一个但不是第一个的元素
  &:last-child:not(:first-child) {
    // 在这里定义你需要的样式
    margin-bottom: 8px; // 示例样式
  }

  .el-row {
    .el-col {
      .widget-form-label {
        display: flex;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 14px;
        line-height: 1.25;
        color: #797979;
        text-align: left;
        word-wrap: break-word;

        .widget-form-label-text {
          flex: 1;
        }

        .widget-form-label-content {
          margin-right: 10px;

          .iconfont {
            cursor: pointer;
          }
        }
      }

      .widget-form-value {
        font-size: 14px;
        font-weight: bolder;
        line-height: 14px;
        color: #262626;
        text-align: left;
        word-break: break-all;
      }
    }
  }
}

// 新增表单添加内边距
.create-team-dialog-form {
  padding: 16px;
}

.el-cascader-panel {
  .el-cascader-menu__list {
    .el-cascader-node {
      max-width: 400px !important;
    }
  }
}

.lk-maximum-dialog {
  width: 90% !important;
  min-width: 1000px;
  max-width: 1600px;
  height: 92% !important;

  .el-dialog__body {
    height: calc(100% - 90px);
    overflow: auto;
  }
}

.lk-maximum-drawer {
  .el-drawer__header {
    height: 54px;
    padding: 0 21px 0 23px;
    margin: 0;
    border-bottom: 1px solid #e6eaf0;

    .el-drawer__title {
      font-size: 18px;
      font-weight: bolder;
      color: #202020;
    }
  }

  .el-drawer__body {
    background: #f6f6f6 !important;
  }

  .el-drawer__footer {
    display: flex;
    align-items: center;
    justify-content: end;
    height: 60px;
    padding: 0 20px;
    border-top: 1px solid #e6eaf0;
  }
}

.lk-preview-dialog {
  padding: 0 !important;
  background: transparent !important;

  .el-dialog__header {
    padding: 0;
  }

  .el-dialog__body {
    height: 100%;
  }

  .el-dialog__footer {
    padding: 0;
  }
}

.lk-middle-dialog {
  width: 840px !important;
  height: 80vh !important;

  .el-dialog__body {
    height: calc(100% - 90px);
    overflow: auto;
  }
}

.lk-mini-dialog {
  width: 620px !important;
  max-height: 50vh !important;

  .el-dialog__body {
    max-height: calc(100% - 90px);
    overflow: auto;
  }
}

.vxe-table-column-checkbox-main,
.vxe-table-column-radio-main {
  .select-v2-text {
    display: inline-block;
    padding: 5px;
    margin-right: 10px;
    margin-bottom: 5px;
    font-size: 12px;
    line-height: 12px;
    color: #262626;
    background: #f2f2f2;
    border-radius: 3px;
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
