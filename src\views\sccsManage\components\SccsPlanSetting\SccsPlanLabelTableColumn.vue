<template>
  <div class="sccs-plan-table-column-body">
    <div
      v-for="(planCondition, planIndex) in planConditionsList"
      :key="planCondition"
    >
      <div class="sccs-plan-table-column-row">
        <div
          v-for="(item, index) in planCondition"
          :key="item"
          class="sccs-plan-table-column-col"
        >
          <div class="sccs-plan-table-column">
            <LkCustomConditions
              ref="conditionRef"
              :condition="item"
              :conditionList="planSettingSelect"
              @handleChangeConditions="
                condition => handleChangeConditions(item, condition)
              "
            />
            <i
              v-if="planCondition.length > 1"
              class="iconfont link-close"
              @click="handleDelete(planCondition, index)"
            />
          </div>
          <div v-if="item.validate" style="display: block; color: red">
            {{ t("trade_common_ImproveSettings") }}
          </div>
        </div>
        <span
          class="sccs-plan-text-btn"
          @click="handleAddConditionsCol(planCondition)"
        >
          <i class="iconfont link-add" />{{ t("trade_common_and2") }}
        </span>
      </div>
      <span
        v-if="planIndex === planConditionsList.length - 1"
        class="sccs-plan-text-btn marginTextTop"
        @click="handleAddConditionsRow"
      >
        <i class="iconfont link-add" />{{ t("trade_common_or2") }}
      </span>
      <span v-else class="sccs-plan-text-title">
        {{ t("trade_common_or") }}
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watchEffect, watch } from "vue";
import { useI18n } from "vue-i18n";
import LkCustomConditions from "@/components/lkCustomConditions/index";

const props = defineProps({
  sccsId: {
    type: String as PropType<string>,
    default: ""
  },
  planSetting: {
    type: Object as PropType<any>,
    default: () => {}
  },
  conditions: {
    type: Object as PropType<any>,
    default: () => {}
  },
  planSettingSelect: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();
const conditionRef = ref<HTMLElement | any>(null);
const planConditionsList = ref<any>([
  [
    {
      firstData: [],
      twoData: [],
      operator: "",
      fieldTypeEnum: "",
      workOrderRange: "",
      rangeDateType: "",
      customerDateType: "",
      value: "",
      validate: true
    }
  ]
]);

watchEffect(() => {
  planConditionsList.value =
    props.conditions &&
    props.conditions instanceof Array &&
    props.conditions.length > 0
      ? props.conditions
      : [
          [
            {
              firstData: [],
              twoData: [],
              operator: "",
              fieldTypeEnum: "",
              workOrderRange: "",
              rangeDateType: "",
              customerDateType: "",
              value: "",
              validate: true
            }
          ]
        ];
});

watch(
  planConditionsList,
  () => {
    emit("handleChange", planConditionsList.value);
  },
  { deep: true }
);

const emit = defineEmits<{
  (e: "handleChange", data: any): void;
}>();

const handleAddConditionsCol = (col: any): void => {
  col.push({
    firstData: [],
    twoData: [],
    operator: "",
    fieldTypeEnum: "",
    workOrderRange: "",
    rangeDateType: "",
    customerDateType: "",
    value: "",
    validate: true
  });
};

const handleChangeConditions = (rowData: any, conditionData: any) => {
  const {
    firstDataObject,
    firstData,
    operator,
    fieldTypeEnum,
    twoDataObject,
    value,
    customerDateType,
    workOrderRange,
    rangeDateType
  } = conditionData;

  if (
    !firstDataObject.value ||
    !operator ||
    !fieldTypeEnum ||
    (fieldTypeEnum === "OTHER_FIELD" && !twoDataObject.value) ||
    (fieldTypeEnum === "CUSTOM" &&
      !value &&
      firstDataObject.fieldType !== "DrDatePicker" &&
      !["IS_NULL", "IS_NOT_NULL"].includes(operator)) ||
    (fieldTypeEnum === "CUSTOM" &&
      firstDataObject.fieldType === "DrDatePicker" &&
      !["IS_NULL", "IS_NOT_NULL"].includes(operator) &&
      customerDateType &&
      ![
        "THIS_WEEK",
        "LAST_WEEK",
        "NEXT_WEEK",
        "THIS_MONTH",
        "LAST_MONTH",
        "NEXT_MONTH",
        "THIS_YEAR",
        "LAST_YEAR",
        "NEXT_YEAR",
        "TODAY",
        "TOMORROW",
        "YESTERDAY"
      ].includes(customerDateType) &&
      !value) ||
    (fieldTypeEnum === "CUSTOM" &&
      firstDataObject.fieldType === "DrDatePicker" &&
      !["IS_NULL", "IS_NOT_NULL"].includes(operator) &&
      rangeDateType &&
      ![
        "THIS_WEEK",
        "LAST_WEEK",
        "NEXT_WEEK",
        "THIS_MONTH",
        "LAST_MONTH",
        "NEXT_MONTH",
        "THIS_YEAR",
        "LAST_YEAR",
        "NEXT_YEAR",
        "TODAY",
        "TOMORROW",
        "YESTERDAY"
      ].includes(rangeDateType) &&
      !value) ||
    (firstData[1] === "formSysField" && !workOrderRange)
  ) {
    // ElMessage.error("请完善设置");
    rowData["validate"] = true;
    return;
  }
  Object.assign(rowData, { validate: false }, conditionData);
};

const handleAddConditionsRow = (): void => {
  planConditionsList.value.push([{}]);
};

const handleDelete = (col: any, index: number) => {
  col.splice(index, 1);
};
</script>
<style lang="scss" scoped>
.sccs-plan-table-column-body {
  .sccs-plan-table-column-row {
    padding: 14px 10px;
    background: #f7f7f7;
    border-radius: 4px;

    .sccs-plan-table-column-col {
      display: block;
      width: 100%;
      margin-bottom: 8px;

      .sccs-plan-table-column {
        display: flex;
        align-items: center;

        .sccs-plan-table-column-select {
          width: 110px;
          margin-right: 5px;
        }

        .sccs-plan-table-column-cascader {
          margin-right: 10px;
        }

        .iconfont {
          align-items: baseline;
          margin-left: 10px;
          font-size: 12px;
          cursor: pointer;
        }
      }
    }
  }

  .sccs-plan-text-btn {
    display: block;
    font-size: 14px;
    line-height: 20px;
    color: #0070d2;
    cursor: pointer;

    .iconfont {
      margin-right: 6px;
      font-size: 14px;
    }
  }

  .marginTextTop {
    margin: 8px 0;
  }

  .sccs-plan-text-title {
    display: inline-block;
    margin-top: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 20px;
    color: #262626;
    cursor: pointer;
  }

  .sccs-plan-icon {
    cursor: pointer;

    .iconfont {
      margin-left: 10px;
      font-size: 10px;
      color: #a6a6a6;
    }
  }
}
</style>
