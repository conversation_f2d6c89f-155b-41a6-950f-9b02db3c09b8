export enum requireResponseCode {
  "0" = "成功",
  "1001" = "您已被移出该团队。如有疑问，请联系团队管理员",
  "1002" = "您已经加入该团队了",
  "1101" = "noRegisterTip",
  "1102" = "mailInRegister",
  "1103" = "errorTitleMax",
  "1104" = "errorPassword",
  "1105" = "errorVerifyCode",
  "1106" = "登录失败，账号被禁用"
}

export interface LoginSuccessResponseDataProp {
  userId: string;
  accessToken: string;
  refreshToken: string;
  expiresTime: string;
}
