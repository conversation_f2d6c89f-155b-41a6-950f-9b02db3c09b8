<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_common_resetPassword')"
    :formRef="ruleFormRef"
    @confirm="confirmFun"
    @close="handleClose"
  >
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="auto"
      label-position="top"
    >
      <el-form-item :label="t('trade_common_currentAccount')">
        <el-descriptions>
          <el-descriptions-item>
            {{ username }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form-item>
      <el-form-item
        :label="t('trade_common_oldPassword')"
        required
        prop="oldPassword"
      >
        <el-input
          v-model="ruleForm.oldPassword"
          type="password"
          clearable
          :placeholder="t('trade_common_setOldPasswordTip')"
          :show-password="true"
        />
      </el-form-item>
      <el-form-item
        :label="t('trade_common_newPassword')"
        required
        prop="newPassword"
      >
        <el-input
          v-model="ruleForm.newPassword"
          type="password"
          clearable
          :placeholder="t('trade_common_setPasswordTip')"
          :show-password="true"
        />
      </el-form-item>
      <el-form-item
        :label="t('trade_common_confirmPassword')"
        required
        prop="newNextPassword"
      >
        <el-input
          v-model="ruleForm.newNextPassword"
          type="password"
          clearable
          :placeholder="t('trade_common_confirmPasswordTip')"
          :show-password="true"
        />
      </el-form-item>
    </el-form>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";
import type { FormInstance, FormRules } from "element-plus";
import { updateUserPassword } from "@/api/common";
import { useNav } from "@/layout/hooks/useNav";
import { message } from "@/utils/message";

interface userInfoProp {
  oldPassword: string;
  newPassword: string;
  newNextPassword: string;
}

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const ruleFormRef = ref<FormInstance>();
const username = ref<string>("");
const ruleForm = reactive<userInfoProp>({
  oldPassword: "",
  newPassword: "",
  newNextPassword: ""
});
const validatePass = (rule: any, value: any, callback: any) => {
  const regex =
    /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9,./<>?;\'\:\"\[\]\\\{\}\|\`\-\=\~\!\@#\$%\^&*()_+]{6,16}$/;
  if (regex.test(value)) {
    callback();
  } else {
    callback(new Error(t("trade_common_setPasswordTip")));
  }
};

const validatePwd = (rule: any, value: any, callback: any) => {
  if (value === ruleForm.newPassword) {
    callback();
  } else {
    callback(new Error(t("trade_login_passwordInconsistency")));
  }
};

const rules = reactive<FormRules<typeof ruleForm>>({
  oldPassword: [
    {
      required: true,
      message: t("trade_common_setOldPasswordTip"),
      trigger: "blur"
    },
    { validator: validatePass, trigger: "blur" }
  ],
  newPassword: [
    {
      required: true,
      message: t("trade_users_enterNewPassword"),
      trigger: "blur"
    },
    { validator: validatePass, trigger: ["blur", "change"] }
  ],
  newNextPassword: [
    {
      required: true,
      message: t("trade_common_confirmPasswordTip"),
      trigger: "blur"
    },
    { validator: validatePass, trigger: ["blur", "change"] },
    { validator: validatePwd, trigger: ["blur", "change"] }
  ]
});

const handleClose = (): void => {
  ruleForm.oldPassword = "";
  ruleForm.newPassword = "";
  ruleForm.newNextPassword = "";
  ruleFormRef.value.clearValidate();
};

const props = defineProps({
  userInfo: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { logout } = useNav();
const emit = defineEmits<{ (e: "updateUserInfo"): void }>();

watch(
  () => ruleForm.newPassword,
  () => {
    if (ruleForm.newPassword) {
      ruleFormRef.value?.validateField("newNextPassword");
    }
  }
);

const open = (): void => {
  username.value = props.userInfo.email;
  LkDialogRef.value.open();
};

const confirmFun = (): void => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      updateUserPassword(ruleForm).then(res => {
        if (res.code === 0) {
          message(t("trade_common_updateSuccess"), {
            customClass: "el",
            type: "success"
          });
          LkDialogRef.value.close();
          logout();
        }
      });
    }
  });
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
::v-deep(.el-descriptions) {
  .el-descriptions__cell {
    padding: 0 !important;
  }
}
</style>
