<template>
  <el-drawer
    v-model="drawer"
    class="team-manage-drawer-body"
    size="94%"
    :before-close="handleBeforeClose"
    @close="handleClose"
  >
    <template #header="{ titleId, titleClass }">
      <el-button
        disabled
        style="flex: none; height: 24px; padding: 2px 4px 2px 7px"
      >
        {{ t("trade_sccs_sccsSetting") }}
      </el-button>
      <div :id="titleId" :class="titleClass" class="draw-header-title">
        {{ sccsBasicInfo.sccsName }}
      </div>
    </template>
    <el-tabs
      v-model="activeName"
      class="left-tabs-body w210"
      tab-position="left"
      style="height: 100%"
      :before-leave="handleBeforeLeave"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="(item, index) in tabPaneList"
        :key="index"
        :name="item.name"
        :lazy="true"
      >
        <template #label>
          <span class="custom-tabs-label">
            <FontIcon :icon="item.iconfont" />
            <span class="custom-tabs-text">{{ t(item.title) }}</span>
          </span>
        </template>
        <component
          :is="item.component"
          v-if="activeName === item.name"
          ref="tabComponentRef"
          :basicInfo="sccsBasicInfo"
          :groupId="groupId"
          lazy
          @updateSccsSuccess="updateSccsSuccess"
        />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import SccsBasicInfo from "./components/SccsBasicInfo.vue";
import SccsRoleManageInfo from "./components/SccsRoleManageInfo.vue";
import SccsMembershipInfo from "./components/SccsMembershipInfo.vue";
import SccsCollaboratorsMembershipInfo from "./components/SccsCollaboratorsMembershipInfo.vue";
import SccsPlanSetting from "./components/SccsPlanSetting.vue";
import SccsMoreOperation from "./components/SccsMoreOperation.vue";
import { getSccsDetail } from "@/api/sccs";
import type { TabPaneName, TabsPaneContext } from "element-plus";
import { useRoute } from "vue-router";

const { t } = useI18n();
const route = useRoute();
const tabComponentRef = ref<HTMLElement | any>(null);
const drawer = ref<boolean>(false);
const drawerTeamName = ref<string>("");
const activeName = ref("basicInfo");
const sccsBasicInfo = ref<any>({});
const groupId = ref<string>("");
const tabPaneList = ref<any[]>([
  {
    title: "trade_team_baseInfo",
    iconfont: "link-basic-info",
    component: SccsBasicInfo,
    name: "basicInfo"
  },
  {
    title: "trade_sccs_permissionMag",
    iconfont: "link-rights-manage",
    component: SccsRoleManageInfo,
    name: "membersInfo"
  },
  {
    title: "trade_team_membersManage",
    iconfont: "link-team-members",
    component: SccsMembershipInfo,
    name: "membershipInfo"
  },
  {
    title: "trade_team_collaborateTeamManage",
    iconfont: "link-coop-team-manage",
    component: SccsCollaboratorsMembershipInfo,
    name: "collaborationInfo"
  },
  {
    title: "trade_sccs_planMag",
    iconfont: "link-plan-manage",
    component: SccsPlanSetting,
    name: "sccsPlan"
  },
  {
    title: "trade_team_moreSet",
    iconfont: "link-more-operation",
    component: SccsMoreOperation,
    name: "moreOperation"
  }
]);

const props = defineProps({
  teamName: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleBeforeLeave = (
  newActiveName: TabPaneName,
  oldActiveName: TabPaneName
) => {
  if (oldActiveName === "sccsPlan") {
    if (tabComponentRef.value[0].activeName === "planTime") {
      const { isChange, addBtnDisabled } =
        tabComponentRef.value[0].handleGetTabPanelState();
      if (isChange && !addBtnDisabled) {
        tabComponentRef.value[0].handleBeforeLeaveFn(() => {
          //@ts-ignore
          activeName.value = newActiveName;
        });
        return false;
      }
    }
  }
};

const handleClick = (tab: TabsPaneContext) => {
  //@ts-ignore
  if (tab && tab.name) {
    //@ts-ignore
    activeName.value = tab.name;
  }
};

const handleBeforeClose = (done): void => {
  if (activeName.value === "sccsPlan") {
    const { isChange, addBtnDisabled, activeName } =
      tabComponentRef.value[0].handleGetTabPanelState();
    if (activeName === "planTime") {
      if (isChange && !addBtnDisabled) {
        tabComponentRef.value[0].handleBeforeLeaveFn(() => {
          done();
        });
      } else {
        done();
      }
    } else {
      done();
    }
  } else {
    done();
  }
};

const handleClose = (): void => {
  activeName.value = "basicInfo";
};

const open = async (id: string, sccsGroupId: string): Promise<void> => {
  drawer.value = true;
  drawerTeamName.value = props.teamName;
  groupId.value = sccsGroupId;
  const res = await getSccsDetail({ id: id });
  if (res.code === 0) {
    sccsBasicInfo.value = res.data;
  }
};

const updateSccsSuccess = async (id): Promise<void> => {
  const res = await getSccsDetail({ id: id });
  if (res.code === 0) {
    sccsBasicInfo.value = res.data;
  }
};

onMounted(() => {
  const { action } = route.query;
  const targetTab = tabPaneList.value.find(item => item.name === action);
  if (targetTab) {
    drawer.value = true;
    activeName.value = targetTab.name;
  }
});

defineExpose({
  open
});
</script>
<style lang="scss">
.team-manage-drawer-body {
  max-width: 1200px;

  .el-drawer__header {
    height: 42px;
    padding: 0 16px 0 13px;
    margin: 0;
    line-height: 42px;
    border-bottom: 1px solid #ebebeb;

    .el-drawer__close-btn {
      i {
        color: #8c8c8c;
      }
    }
  }

  .el-drawer__body {
    padding: 0;

    .left-tabs-body {
      .el-tabs__header {
        width: 210px;

        .el-tabs__nav-wrap {
          width: 100%;
          padding-top: 12px;

          .el-tabs__nav {
            width: 100%;

            .el-tabs__item {
              .custom-tabs-label {
                display: inline-block;
                width: 100%;
                text-align: left;

                .iconfont {
                  margin-right: 3px;
                  font-size: 14px;
                }

                .custom-tabs-text {
                  font-size: 14px;
                  color: #262626;
                }

                .custom-tabs-number {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  margin-left: 6px;
                  font-size: 12px;
                  line-height: 16px;
                  color: #fff;
                  text-align: center;
                  background: #e62412;
                  border-radius: 50%;
                }
              }

              &:hover {
                .custom-tabs-label {
                  .iconfont {
                    color: #262626;
                  }
                }
              }
            }

            .el-tabs__item.is-active {
              background: #e6f1ff;

              .custom-tabs-label {
                .custom-tabs-text {
                  color: #0070d2;
                }
              }

              &:hover {
                .custom-tabs-label {
                  .iconfont {
                    color: #0070d2;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .el-tab-pane {
    height: 100% !important;
  }
}
</style>
