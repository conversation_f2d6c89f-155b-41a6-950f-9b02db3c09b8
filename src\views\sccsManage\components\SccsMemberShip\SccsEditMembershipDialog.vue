<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_common_settingRole')"
    class="lk-mini-dialog"
    @confirm="confrimLkDialog"
  >
    <template #default>
      <div class="create-team-dialog-form">
        <el-form ref="ruleFormRef" label-position="top" label-width="auto">
          <el-form-item :label="t('trade_common_name')">
            <el-input v-model="username" filterable clearable disabled />
          </el-form-item>
          <el-form-item :label="t('trade_sccs_businessRole')">
            <el-select-v2
              v-model="form.sccsRoleIds"
              filterable
              :options="sccsRoleList"
              clearable
              :placeholder="t('trade_common_selectText')"
              multiple
            />
          </el-form-item>
          <el-form-item :label="t('trade_sccs_dataFence')">
            <el-select-v2
              v-model="form.dataFenceIds"
              filterable
              :options="sccsFenceList"
              clearable
              :placeholder="t('trade_common_selectText')"
              multiple
            />
          </el-form-item>
        </el-form>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { ref } from "vue";
import LkDialog from "@/components/lkDialog/index";
import { message } from "@/utils/message";
import {
  getSccsFenceList,
  getSccsRoleList,
  updateSccsMember
} from "@/api/sccs";

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

let form = ref({
  sccsRoleIds: "",
  dataFenceIds: ""
});
const { t } = useI18n();
let sccsFenceList = ref<any[]>([]);
let sccsRoleList = ref<any[]>([]);
const username = ref<string>("");
const LkDialogRef = ref<any>(null);
const ruleFormRef = ref<any>(null);
const sccsRow = ref<any>(null);

const emit = defineEmits<{
  (e: "handleSuccess"): void;
}>();

const confrimLkDialog = async (): Promise<void> => {
  const { id } = sccsRow.value;
  const { code, data } = await updateSccsMember({
    id,
    sccsId: props.basicInfo.id,
    ...form.value
  });
  if (code === 0) {
    message(t("trade_common_updateSuccess"), {
      customClass: "el",
      type: "success"
    });
    LkDialogRef.value.close();
    emit("handleSuccess");
  }
};

const open = async (row): Promise<void> => {
  LkDialogRef.value.open();
  const sccsId = props.basicInfo.id;
  username.value = row.username;
  sccsRow.value = row;
  const { sccsMemberRoleList, sccsDataFenceList } = row;
  form.value.sccsRoleIds = sccsMemberRoleList.map(obj => obj.id);
  form.value.dataFenceIds = sccsDataFenceList.map(obj => obj.id);
  Promise.all([
    getSccsFenceList({ sccsId: sccsId }),
    getSccsRoleList({ sccsId: sccsId })
  ]).then(res => {
    sccsFenceList.value = res[0].data;
    sccsRoleList.value = res[1].data;
  });
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.el-form {
  ::v-deep(.el-form-item.widthfixRow) {
    width: 100% !important;

    .el-form-item__content {
      width: 100% !important;
    }
  }
}

.itemCenter {
  display: flex;
  align-items: center;
  height: 100%;

  .iconfont {
    cursor: pointer;
  }
}

.sccs-member-manage-col {
  box-sizing: border-box;
  width: 98%;
  // box-shadow: 0px 2px 18px 0px rgba(0, 0, 0, 0.34);
  padding: 13px 11px;
  margin-bottom: 16px;
  background: #f7f7f7;
  border-radius: 4px;

  .sccs-member-manage-col-top {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .sccs-member-manage-col-username {
      padding: 0 5px 0 8px;
      font-size: 14px;
      line-height: 16px;
      color: #262626;
      text-shadow: 0 2px 18px 0 rgb(0 0 0 / 34%);
    }

    .sccs-member-manage-col-account {
      font-size: 14px;
      line-height: 16px;
      color: #595959;
      text-shadow: 0 2px 18px 0 rgb(0 0 0 / 34%);
    }
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.fence-list-tip {
  height: 38px;
  padding-left: 14px;
  margin-bottom: 14px;
  font-size: 14px;
  line-height: 38px;
  color: #fa8d0a;
  text-align: left;
  background: #fdf6ec;
  border-radius: 2px;

  .iconfont {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
  }
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
