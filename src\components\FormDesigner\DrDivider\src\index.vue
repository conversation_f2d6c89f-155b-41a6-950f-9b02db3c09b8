<template>
  <el-divider :content-position="widgetConfigure.props.contentPosition">
    <span v-if="widgetConfigure.props.title"> {{ widgetConfigureTitle }}</span>
  </el-divider>
</template>
<script setup lang="ts">
import { computed } from "vue";
import { storageLocal } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const widgetConfigureTitle = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  return translationLang === "en"
    ? props.widgetConfigure.props?.titleEn || props.widgetConfigure.props?.title
    : props.widgetConfigure.props?.title;
});
</script>
