<template>
  <div class="create-team-guide-dialog">
    <LkDialog
      ref="LkDialogRef"
      :title="t('trade_team_inviteMembers')"
      :noFooter="true"
      @close="handleClose"
    >
      <template #default>
        <el-form
          ref="formRef"
          style="height: 427px"
          :model="inviteMembersForm"
          label-width="auto"
          class="dynamic-form-body"
        >
          <el-scrollbar
            ref="ScrollbarRef"
            :always="true"
            max-height="405px"
            height="auto"
          >
            <el-row
              v-for="(inviteMember, index) in inviteMembersForm.domains"
              :key="index"
              :gutter="10"
            >
              <el-col :span="12">
                <el-form-item
                  :prop="'domains.' + index + '.email'"
                  :rules="[
                    {
                      validator: validateEmail,
                      trigger: 'blur'
                    }
                  ]"
                >
                  <el-input
                    v-model="inviteMember.email"
                    size="large"
                    :placeholder="t('trade_login_mailText')"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="inviteMembersForm.domains.length > 1 ? 10 : 12">
                <el-form-item :prop="'domains.' + index + '.teamRoleIds'">
                  <el-select-v2
                    v-model="inviteMember.teamRoleIds"
                    filterable
                    size="large"
                    :options="options"
                    :placeholder="t('trade_select_team_role')"
                    style="width: 100%"
                    multiple
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-if="inviteMembersForm.domains.length > 1"
                class="link-ashbin-body"
                :span="1"
              >
                <FontIcon
                  icon="link-ashbin"
                  @click="deleteInviteMemeberRow(index)"
                />
              </el-col>
            </el-row>
          </el-scrollbar>
          <el-row>
            <el-col :offset="22" :span="2">
              <span class="invite-add-btn" @click="addInviteMemeberRow">
                <FontIcon icon="link-add" />{{ t("trade_common_increase") }}
              </span>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template #footer>
        <el-button type="primary" color="#0070D2" @click="confirmFun">
          {{ t("trade_team_inviteMembers") }}
        </el-button>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import LkDialog from "@/components/lkDialog/index";
import { useI18n } from "vue-i18n";
import { message } from "@/utils/message";
import { getTeamRoleOption } from "@/api/team";
import type { FormInstance } from "element-plus";
import { inviteMember } from "@/api/team";
import { isEmail } from "@pureadmin/utils";

interface inviteMember {
  email: string;
  teamRoleIds: string[];
}

interface TeamRoleOptions {
  label?: string | null;
  value?: string | null;
}

const formRef = ref<FormInstance>();
const options = ref<TeamRoleOptions[]>([]);
const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const ScrollbarRef = ref<any>(null);
const teamId = ref<string>("");

const validateEmail = (rule: any, value: any, callback: any) => {
  const index = rule.field.split(".")[1];
  if (inviteMembersForm.domains[index].teamRoleIds.length > 0) {
    if (value === "") {
      callback(new Error(t("trade_login_mailText")));
    } else {
      if (!isEmail(value)) {
        callback(new Error(t("trade_login_truemail")));
      } else {
        callback();
      }
    }
  } else {
    if (!value) {
      callback(new Error(t("trade_login_mailText")));
    } else if (value && !isEmail(value)) {
      callback(new Error(t("trade_login_truemail")));
    } else {
      callback();
    }
  }
};

const handleClose = (): void => {
  inviteMembersForm.domains = [
    {
      email: "",
      teamRoleIds: []
    }
  ];
  formRef.value.clearValidate();
};

const inviteMembersForm = reactive<{
  domains: inviteMember[];
}>({
  domains: [
    {
      email: "",
      teamRoleIds: []
    }
  ]
});

const emit = defineEmits<{
  (e: "createSuccess", id: string): void;
}>();

const addInviteMemeberRow = (): void => {
  inviteMembersForm.domains.push({
    email: "",
    teamRoleIds: []
  });
  nextTick(() => {
    ScrollbarRef.value.setScrollTop(9999999999999999999);
  });
};

const deleteInviteMemeberRow = (index: number): void => {
  inviteMembersForm.domains.splice(index, 1);
};

const open = async (id: string): Promise<void> => {
  teamId.value = id;
  LkDialogRef.value.open();
  const res = await getTeamRoleOption({ withOwner: false });
  options.value = res.data;
};

const confirmFun = (): void => {
  if (!formRef.value) return;
  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      const submitMembersForm = inviteMembersForm.domains.filter(
        domain => !(!domain.email && domain.teamRoleIds.length === 0)
      );
      if (submitMembersForm.length === 0) {
        return;
      }
      const res = await inviteMember(submitMembersForm);
      if (res.code === 0) {
        message(t("trade_team_invitationSuccessful"), {
          customClass: "el",
          type: "success"
        });
        LkDialogRef.value.close();
        formRef.value.resetFields();
        inviteMembersForm.domains = [
          {
            email: "",
            teamRoleIds: []
          }
        ];
        emit("createSuccess", teamId.value);
      }
    }
  });
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.invite-add-btn {
  font-size: 14px;
  line-height: 20px;
  color: #0070d2;
  text-align: left;
  cursor: pointer;

  .iconfont {
    margin-right: 4px;
    font-size: 12px;
  }
}

.link-ashbin-body {
  margin-top: 8px;

  .iconfont {
    font-size: 12px;
    color: #808080;
    cursor: pointer;
  }
}

.el-row {
  width: 100%;
  overflow: hidden;
}

.dynamic-form-body {
  .el-scrollbar {
    height: auto !important;
  }
}
</style>
