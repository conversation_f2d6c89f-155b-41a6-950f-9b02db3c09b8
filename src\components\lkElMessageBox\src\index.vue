<template>
  <Teleport to="body">
    <!-- 遮罩层 -->
    <div v-if="dialogVisible" class="message-box-mask">
      <!-- 对话框主体 -->
      <div class="message-box">
        <!-- 标题 -->
        <div class="message-box-header">
          <div class="message-box-left">
            <svg class="icon svg-icon" aria-hidden="true">
              <use xlink:href="#link-tips-warm" />
            </svg>
            <div class="message-box-text">{{ message }}</div>
          </div>
          <div class="message-box-right">
            <i class="iconfont link-close" @click="handleCancel" />
          </div>
        </div>
        <!-- 底部按钮 -->
        <div class="message-box-footer">
          <el-button v-if="showCancelButton" info @click="handleCancel">
            {{ cancelButtonText }}
          </el-button>
          <el-button type="primary" info @click="handleNextConfirm">
            {{ nextButtonText }}
          </el-button>
          <el-button type="primary" info @click="handleConfirm">
            {{ confirmButtonText }}
          </el-button>
        </div>
      </div>
    </div>
  </Teleport>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { ElButton } from "element-plus";

const props = defineProps({
  visible: Boolean,
  message: String,
  confirmButtonText: { type: String, default: "提交" },
  cancelButtonText: { type: String, default: "取消" },
  nextButtonText: { type: String, default: "不提交" },
  showCancelButton: { type: Boolean, default: true },
  closeOnClickMask: { type: Boolean, default: true }
});

const dialogVisible = ref<boolean>(false);

watch(
  () => props,
  () => {
    dialogVisible.value = props.visible;
  },
  {
    deep: true,
    immediate: true
  }
);

const emit = defineEmits([
  "confirm",
  "nextConfirm",
  "cancel",
  "update:visible"
]);

const handleConfirm = () => {
  emit("confirm");
  close();
};

const handleNextConfirm = () => {
  emit("nextConfirm");
  close();
};

const handleCancel = () => {
  emit("cancel");
  close();
};

const close = () => {
  emit("update:visible", false);
};
</script>
<style lang="scss" scoped>
.message-box-mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 30000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: rgb(0 0 0 / 50%);
}

.message-box {
  width: 480px;
  padding: 14px 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 12px 42px 0 rgb(38 38 38 / 24%);

  .message-box-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 53px;

    .message-box-left {
      display: flex;
      align-items: center;

      .svg-icon {
        width: 24px;
        height: 24px;
      }

      .message-box-text {
        margin-left: 8px;
        font-size: 16px;
        font-weight: bolder;
        line-height: 24px;
        color: #262626;
        text-align: justify;
      }
    }

    .message-box-right {
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      border-radius: 4px;

      .iconfont {
        font-size: 12px;
        color: #8c8c8c;
        cursor: pointer;
      }

      &:hover {
        background: #e5e5e5;
      }
    }
  }
}

.message-box-content {
  margin-bottom: 20px;
  font-size: 14px;
}

.message-box-footer {
  text-align: right;
}

.btn {
  padding: 8px 16px;
  margin-left: 10px;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.btn-confirm {
  color: white;
  background: #409eff;
}

.btn-cancel {
  color: #606266;
  background: white;
}
</style>
