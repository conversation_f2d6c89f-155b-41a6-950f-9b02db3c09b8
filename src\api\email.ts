import { http } from "@/utils/http";
import type { Result } from "./type";

/**
 * 邮件协作用户获取登录邮箱验证码
 * @returns
 */
export const sendWorkOrderCoopVerifyCode = (params: { email: string }) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-coop-email/send-verify-code",
    { params }
  );
};

/**
 * 校验邮件验证码
 * @returns
 */
export const checkWorkOrderCoopVerifyCode = (params: {
  email: string;
  code: string;
}) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-coop-email/check-mail-code",
    { params }
  );
};

/**
 * 获得贸易端工单邮件协作控件列表
 * @returns
 */
export const getWidgetList = (params: { workOrderId: string }) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-coop-email/widget-item-list",
    { params }
  );
};

/**
 * 获取邮件协作创建时结构
 * @returns
 */
export const getCoopEmailStructure = (params: {
  sccsId: string;
  milestoneId: string;
  workOrderId: string;
}) => {
  return http.request<Result>("get", "/trade/work-order-coop-email/structure", {
    params
  });
};

/**
 * 文件控件列表
 * @returns
 */
export const getSccsFileWidgetList = (params: any) => {
  return http.request<Result>("get", "/trade/sccs/file-widget-item-list", {
    params
  });
};

/**
 * 创建贸易端工单邮件协作
 * @returns
 */
export const createEmailCollaborate = (data: any) => {
  return http.request<Result>("post", "/trade/work-order-coop-email/create", {
    data
  });
};

/**
 * 获取文件控件对应数据
 * @returns
 */
export const getFileWidgetData = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order-coop-email/file-widget-data",
    {
      data
    }
  );
};

/**
 * 获得贸易端工单邮件协作列表
 * @returns
 */
export const getWorkOrderEmailList = (params: any) => {
  return http.request<Result>("get", "/trade/work-order-coop-email/list", {
    params
  });
};

/**
 * 删除贸易端工单邮件协作
 * @returns
 */
export const deleteWorkOrderCoopEmail = (params: any) => {
  return http.request<Result>("delete", "/trade/work-order-coop-email/delete", {
    params
  });
};

/**
 * 获取接收方邮件协作结构-详情/编辑（邮件接收方）
 * @returns
 */
export const getEmailCoopReceiverStructure = (params: { id: string }) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-coop-email/receiver-structure",
    { params }
  );
};

/**
 * 获取接收方邮件协作控件数据
 * @returns
 */
export const getEmailCoopReceiverDetail = (params: { id: string }) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-coop-email/receiver-detail",
    { params }
  );
};

/**
 * 提交贸易端工单邮件协作
 * @returns
 */
export const submitWorkOrderCoopEmail = (data: any) => {
  return http.request<Result>("post", "/trade/work-order-coop-email/submit", {
    data
  });
};

/**
 * 我收到的邮件协作列表
 * @returns
 */
export const getReceiveCoopEmail = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-coop-email/receive-coop-email-page",
    { params }
  );
};

/**
 * 发送方查看邮件协作提交详情
 * @returns
 */
export const getReceiveCoopDetail = (params: any) => {
  return http.request<Result>("get", "/trade/work-order-coop-email/detail", {
    params
  });
};

/**
 * 邮件协作历史提交记录
 * @returns
 */
export const getCoopEmailHistoryList = (data: any) => {
  return http.request<Result>("post", "/trade/work-order-coop-email/history", {
    data
  });
};
