<template>
  <Transition
    leave-active-class="animate__animated animate__fadeOut animate__faster"
    enter-active-class="animate__animated animate__fadeIn animate__faster"
  >
    <div v-if="emailInfoVisible" class="email-form-container">
      <el-scrollbar class="horizontal-scrollbar-only" max-height="400px">
        <EmailCoopForm
          v-if="emailFormData"
          :workOrderId="emailFormData.workOrderId"
          :milestoneId="emailFormData.milestoneId"
          :sccsId="emailFormData.sccsId"
          :worderName="emailFormData.worderName"
          :quoteVisible="true"
          :btnVisible="btnVisible"
          @handleQuoteData="handleQuoteData"
          @handleQuoteSubTableData="handleQuoteSubTableData"
        />
      </el-scrollbar>
    </div>
  </Transition>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import type { PropType } from "vue";
import Email<PERSON>oopForm from "@/views/emailCoop/components/emailCoopForm.vue";

const props = defineProps({
  btnVisible: {
    type: Boolean,
    default: true
  },
  emailFormData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const emit = defineEmits<{
  (e: "handleUpdateEmailQuoteData", data: any): void;
  (e: "handleUpdateTableData", data: any): void;
}>();

const emailInfoVisible = ref<boolean>(false);

const open = () => {
  emailInfoVisible.value = true;
};

const close = () => {
  emailInfoVisible.value = false;
};

const handleQuoteData = (data: any) => {
  emailInfoVisible.value = false;
  const { widgetDataList, widgetDataSubMap, linkedReferenceMap } =
    data.workOrderCoopEmailDataVO;
  emit("handleUpdateEmailQuoteData", {
    widgetDataList,
    widgetDataSubMap,
    linkedReferenceMap
  });
};

const handleQuoteSubTableData = (data: any) => {
  emailInfoVisible.value = false;
  let dataRecord = [];
  data.records.forEach(record => {
    // dataRecord.push(record.originalData);// emailCoopSubTable.vue 版本返回数据需要这样处理
    dataRecord.push(record);
  });
  emit("handleUpdateTableData", {
    widgetId: data.widgetField,
    records: dataRecord
  });
};

defineExpose({
  open,
  close,
  visible: emailInfoVisible,
  toggle: () => (emailInfoVisible.value ? close() : open())
});
</script>

<style lang="scss" scoped>
.email-form-container {
  position: absolute;
  top: 54px;
  z-index: 2200;
  width: 100%;
  padding: 43px 10px 43px 34px;
  margin-right: 24px;
  background: #fff;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 8px 13px 0 rgb(0 0 0 / 10%);
}
</style>
