<template>
  <div class="order-detail-region-body">
    <OrderDetailHeader
      :widgetButtons="detailWorkOrderRegionBtn"
      :milestoneData="milestoneRow"
      :relationIconType="relationIconType"
      @handleTrigger="handleTrigger"
    >
      <template #relation>
        <slot name="milestoneRelations" />
      </template>
    </OrderDetailHeader>
    <div
      v-if="
        milestoneRow.visible ||
        (milestoneRow.workOrderGroupList &&
          milestoneRow.workOrderGroupList.length > 0)
      "
      class="order-detail-describle-body"
    >
      <el-tabs
        v-model="activeName"
        class="order-detail-region-tabs"
        @tab-click="handleWorkOrderChange"
      >
        <el-tab-pane
          v-if="milestoneRow.visible"
          v-loading="msVisibleFormLoading[milestoneRow.msReplyId]"
          :label="t('trade_template_replyBtnName')"
          name="replyForm"
        >
          <div class="milestone-reply-form-body">
            <div class="milestone-reply-form-text">
              <div
                class="milestone-reply-form-text-col"
                style="margin-bottom: 10px"
              >
                <span class="milestone-reply-form-tip">
                  {{ t("trade_home_instigation") }}
                </span>
                <div class="milestone-reply-form-span">
                  <LkAvatar
                    :teamInfo="{
                      avatar: milestoneRow.createUserAvatar,
                      username: milestoneRow.createUserName,
                      email: milestoneRow.creatorEmail
                    }"
                    :size="20"
                  />
                  <span class="milestone-reply-form-span-text">
                    {{ getTimeLineStampFormat(milestoneRow.createTime) }}
                  </span>
                </div>
              </div>
              <div
                class="milestone-reply-form-text-col"
                style="margin-bottom: 10px"
              >
                <span class="milestone-reply-form-tip">
                  {{ t("trade_home_approval") }}
                </span>
                <div class="milestone-reply-form-span">
                  <LkAvatarGroupNext
                    :size="22"
                    :avatarListGroup="milestoneRow.msReplyUser"
                    :maxAvatar="4"
                    mode="reply"
                  />
                  <span
                    v-if="!milestoneRow.replied"
                    class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
                  >
                    {{ t("trade_order_notSubmit") }}
                  </span>
                  <span v-else class="milestone-reply-form-span-text">
                    {{ getTimeLineStampFormat(milestoneRow.replyTime) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="milestone-reply-form-button-group">
              <el-badge
                v-for="item in replayMileStoneBtnList"
                :key="item.type"
                :value="item.badge"
                color="#E62412"
                :show-zero="false"
                :badge-class="'milestone-badege-class'"
              >
                <span
                  class="milestone-btn"
                  :data-ms-id="milestoneRow.msId"
                  :data-action="item.type"
                  @click="handleTrigger(item)"
                >
                  <i :class="['iconfont', item.icon]" />
                  {{ t(item.label) }}
                </span>
              </el-badge>
            </div>
          </div>
          <OrderDetailDescWidget
            v-if="milestoneCard.replyForm"
            :relateFormId="milestoneRow.msReplyId"
            :widgetForm="milestoneCard.replyForm.widgetJsonList"
            :widgetData="
              getMilestoneReplyWidgetData(
                milestoneRow.replyWidgetList,
                milestoneRow.subWidgetMap,
                milestoneRow.linkedReferenceMap
              )
            "
          />
        </el-tab-pane>
        <el-tab-pane
          v-for="workOrder in milestoneRow.workOrderGroupList"
          :key="workOrder.workOrderId"
          v-loading="msVisibleFormLoading[workOrder.workOrderId]"
          :label="workOrder.workOrderName"
          :name="workOrder.workOrderId"
          lazy
        >
          <template #label>
            <div class="custom-work-order-tab">
              <span
                v-if="!milestoneRow.cloneSourceId && workOrder.cloneSourceId"
                class="triangle-topright triangle-topright-two"
              >
                s
              </span>
              <span class="custom-tabs-label">
                <span>{{ workOrder.workOrderName }}</span>
              </span>
            </div>
          </template>
          <div class="milestone-reply-form-body">
            <OrderDetailPersonnel
              :workOrder="workOrder"
              style="margin-bottom: 10px"
            />
            <slot name="workOrderRelations" :workOrderInfo="workOrder" />
            <div class="milestone-reply-form-button-group">
              <OrderDetailMileStoneBtnGroup
                :ref="(el: refItem) => setRefMap(el, workOrder.workOrderId)"
                :milestoneButtons="getWorkOrderBtnList(workOrder)"
                :workOrderData="workOrder"
                @handleTrigger="typeName => handleTrigger(typeName, workOrder)"
              />
            </div>
          </div>
          <el-collapse v-model="activeNames" class="milestone-widget-collapse">
            <el-collapse-item
              v-if="milestoneCard.workOrderReplyForm"
              :title="t('trade_order_taskReply')"
              name="1"
            >
              <OrderDetailDescWidget
                :relateFormId="workOrder.workOrderId"
                :widgetForm="workOrderReplyWidgetJsonList"
                :widgetData="
                  getWorkOrderReplyWidgetData(
                    workOrder.widgetList,
                    workOrder.subWidgetMap,
                    workOrder.linkedReferenceMap
                  )
                "
              />
            </el-collapse-item>
            <el-collapse-item :title="t('trade_work_order')" name="2">
              <template #title>
                {{ t("trade_work_order") }}
                <span v-if="workOrder.stopped" class="work-order-icon-stop">
                  <i class="iconfont link-zhongzhi" />
                  {{ t("trade_common_stopped") }}
                </span>
              </template>
              <div
                v-for="item in milestoneCard.formList"
                :key="item.id"
                class="order-detail-desc-col"
              >
                <div v-if="!workOrder.hiddenFormIdList.includes(item.id)">
                  <div class="order-detail-desc-title">{{ item.name }}</div>
                  <OrderDetailDescWidget
                    :relateFormId="workOrder.workOrderId"
                    :widgetForm="formWidgetJsonList[item.id]"
                    :widgetData="
                      getWorkOrderWidgetData(
                        workOrder.widgetList,
                        workOrder.subWidgetMap,
                        workOrder.linkedReferenceMap
                      )
                    "
                  />
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-empty
      v-else
      style="background: #fff"
      :description="t('trade_common_emptyTip')"
      :image-size="314"
      :image="sccsGroupNoImage"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import dayjs from "dayjs";
import { storageLocal } from "@pureadmin/utils";
import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils.tsx";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";
import LkAvatar from "@/components/lkAvatar/index";
import sccsGroupNoImage from "@/assets/images/sccs/sccs-no-image.png";
import { obtainBtnPermission } from "@/views/orderDetail/components/utils/auth.ts";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import OrderDetailHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDetailHeader.vue";
import OrderDetailMileStoneBtnGroup from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDetailMileStoneBtnGroup.vue";
import OrderDetailPersonnel from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDetailPersonnel.vue";
import {
  calculateVisibleFormList,
  ExecuteFormVisibleExpression
} from "@/utils/formVisibleExpression";
import { cloneDeep, isEqual } from "lodash-es";
import { useOnceWhen } from "@/hooks";

type refItem = HTMLElement | null;

const { t } = useI18n();
const route = useRoute();

const props = defineProps({
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneRow: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneCard: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderStateFlag: {
    type: String as PropType<string>,
    default: ""
  },
  relationMap: {
    type: Object as PropType<any>,
    default: () => {}
  },
  relationIconType: {
    type: String as PropType<string>,
    default: ""
  },
  orderCountMap: {
    type: Object as PropType<any>,
    default: () => new Map()
  },
  activeWorkOrderId: {
    type: String as PropType<string>,
    default: ""
  },
  visibleFormData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  msVisibleIdsList: {
    type: Array as PropType<any>,
    default: () => []
  },
  msVisibleFormLoading: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const activeName = ref("replyForm");
const activeNames = ref(["1", "2"]);
const refMap: Record<string, refItem> = {};
const workOrderDynamicCount = ref<number>(0);
const workOrderDynamicReplyCount = ref<number>(0);
const workOrderReplyWidgetJsonList = ref<any[]>([]);
const formWidgetJsonList = ref<any>({});

const detailWorkOrderRegionBtn = computed(() => {
  return [
    // 创建工单
    {
      type: "addTaskOrder",
      icon: "link-addWorkOrder",
      label: "trade_order_create_task",
      class: "add-task-order-class",
      btnPermission: {
        coopTeam: ["create_work_order"],
        mainTeam: ["create_work_order"],
        customPermission: createWorkOrderBtnPerFn() && cloneMislestoneBtnPerFn()
      }
    },
    // 添加互用工单
    {
      type: "addMutuallyUseWorkOrder",
      icon: "link-batch-add-orders",
      label: "trade_common_mutuallyUseOrder",
      btnPermission: {
        coopTeam: ["clone_work_order"],
        mainTeam: ["clone_work_order"],
        customPermission: cloneWorkOrderBtnPerFn() && cloneMislestoneBtnPerFn()
      }
    },
    // 里程碑互用
    {
      icon: "link-shared-in",
      label: "trade_order_mutuallyUseMilestone",
      btnPermission: {
        coopTeam: ["clone_milestone"],
        mainTeam: ["clone_milestone"],
        customPermission: cloneMislestoneBtnPerFn()
      },
      dropdownList: [
        // 此里程碑互用与其他订单
        {
          type: "milesStoneSharedIn",
          icon: "link-shared-in",
          label: "trade_ms_use_in_other_order",
          btnPermission: {
            coopTeam: ["clone_milestone"],
            mainTeam: ["clone_milestone"],
            customPermission: true
          }
        },
        // 从其他订单互用里程碑
        {
          type: "cloneMilesStoneFromOtherOrder",
          icon: "link-interuse-from-others",
          label: "trade_order_otherOrderUser",
          class:
            getMilestoneState(["COMPLETE", "CANCEL"]) || getSelectBtnStatus()
              ? "disabled"
              : "",
          btnPermission: {
            coopTeam: ["clone_milestone"],
            mainTeam: ["clone_milestone"],
            customPermission: true
          }
        }
      ]
    },
    // // 完成里程碑
    // {
    //   type: "confirmMilestone",
    //   icon: "link-manual-completion",
    //   label: "trade_order_confrimMilestone",
    //   btnPermission: {
    //     coopTeam: [],
    //     mainTeam: ["update_milestone_status"],
    //     customPermission:
    //       getMilestoneState(["ON_GOING"]) && cloneMislestoneBtnPerFn()
    //   }
    // },
    // // 取消里程碑
    // {
    //   type: "cancelMilestone",
    //   icon: "link-cancel",
    //   label: "trade_order_cancelMilestone",
    //   btnPermission: {
    //     coopTeam: [],
    //     mainTeam: ["update_milestone_status"],
    //     customPermission:
    //       getMilestoneState(["NOT_START", "ON_GOING"]) &&
    //       getOrderState() &&
    //       cloneMislestoneBtnPerFn()
    //   }
    // },
    // // 重启里程碑
    // {
    //   type: "restartMilestone",
    //   icon: "link-restart",
    //   label: "trade_order_restartMilestone",
    //   btnPermission: {
    //     coopTeam: [],
    //     mainTeam: ["update_milestone_status"],
    //     customPermission:
    //       getMilestoneState(["COMPLETE", "CANCEL"]) &&
    //       getOrderState() &&
    //       cloneMislestoneBtnPerFn()
    //   }
    // },
    // 更多操作
    {
      label: "trade_common_more",
      icon: "link-more-operation",
      badge: 0,
      customPermission: cloneMislestoneBtnPerFn(),
      dropdownList: [
        {
          type: "confirmMilestone",
          icon: "link-manual-completion",
          label: "trade_order_confrimMilestone",
          // class:
          //   getMilestoneState(["ON_GOING"]) && getOrderState()
          //     ? ""
          //     : "disabled",
          btnPermission: {
            coopTeam: [],
            mainTeam: ["update_milestone_status"],
            customPermission: getMilestoneState(["ON_GOING"]) && getOrderState()
          }
        },
        {
          type: "cancelMilestone",
          icon: "link-cancel",
          label: "trade_order_cancelMilestone",
          // class:
          //   getMilestoneState(["NOT_START", "ON_GOING"]) && getOrderState()
          //     ? ""
          //     : "disabled",
          btnPermission: {
            coopTeam: [],
            mainTeam: ["update_milestone_status"],
            customPermission:
              getMilestoneState(["NOT_START", "ON_GOING"]) && getOrderState()
          }
        },
        {
          type: "restartMilestone",
          icon: "link-restart",
          label: "trade_order_restartMilestone",
          class:
            getMilestoneState(["COMPLETE", "CANCEL"]) && getOrderState()
              ? ""
              : "disabled",
          btnPermission: {
            coopTeam: [],
            mainTeam: ["update_milestone_status"],
            customPermission: true
          }
        }
      ]
    }
  ].filter(item => {
    if (item.dropdownList) {
      item.dropdownList = item.dropdownList.filter(dropdownItem => {
        return handleObtainBtnPermission(dropdownItem);
      });
      return item.dropdownList.length > 0;
    }
    return handleObtainBtnPermission(item);
  });
});

const replayMileStoneBtnList = computed(() => {
  console.log(props.milestoneRow.msReplyOperation.reAssignReplyAbleForTeam);
  return [
    // 主团队指派
    {
      label: "trade_order_assign",
      icon: "link-distribute",
      type: "milestoneAssign",
      badge: 0,
      btnPermission: {
        coopTeam: ["assign_milestone_approval"],
        mainTeam: [],
        customPermission:
          props.milestoneRow.msReplyOperation.reAssignReplyAbleForTeam
      }
    },
    // 协作方里程碑指派
    {
      label: "trade_order_assign",
      icon: "link-distribute",
      type: "updateMilestoneCoopPersonnel",
      badge: 0,
      btnPermission: {
        coopTeam: ["assign_milestone_approval"],
        mainTeam: [],
        customPermission:
          props.milestoneRow.msReplyOperation.reAssignReplyAbleForTeamMember
      }
    },
    // 里程碑批复
    {
      label: "trade_home_approval",
      icon: "link-reply",
      type: "milestoneReply",
      badge: 0,
      btnPermission: {
        coopTeam: ["All"],
        mainTeam: ["All"],
        customPermission: props.milestoneRow.msReplyOperation.replyAble
      }
    },
    // 修改里程碑处理人
    {
      label: "trade_home_collectAssignPerson",
      icon: "link-modify-processor",
      type: "updateMilestonePersonnel",
      badge: 0,
      btnPermission: {
        coopTeam: ["update_processor"],
        mainTeam: ["update_processor"],
        customPermission: props.milestoneRow.msReplyOperation.updateProcessor
      }
    },
    // 动态
    {
      label: "trade_order_dynamic",
      icon: "link-dynamics-ms",
      type: "milestoneDynamic",
      badge: getMoudleDynamicCount(
        props.milestoneRow.cloneSourceId || props.milestoneRow.msReplyId,
        "MS_REPLY_DYNAMIC"
      ),
      btnPermission: {
        coopTeam: ["All"],
        mainTeam: ["All"],
        customPermission: true
      }
    }
  ].filter(item => {
    return handleObtainBtnPermission(item);
  });
});

const setRefMap = (el: refItem, workOrderId: string) => {
  el && (refMap[`${workOrderId}`] = el);
};

/**
 * 当前里程碑批复的批复权限
 */
const getMilestoneReplyRole = (): boolean => {
  // bug:Lk2-4827 要求里程碑关闭时不显示按钮
  if (
    props.milestoneRow.status === "CANCEL" ||
    props.milestoneRow.status === "COMPLETE"
  ) {
    return false;
  }
  const userInfo = storageLocal().getItem("user-info") as any;
  if (userInfo) {
    const { latestLoginTeamId, latestLoginTeamMemberId } = userInfo;

    const msReplyTeamList = props.milestoneRow.msReplyUser.filter(
      msReplyUsers => !msReplyUsers.user
    );
    const index = msReplyTeamList.findIndex(
      teamItem => teamItem.teamId === latestLoginTeamId
    );
    const { orderId } = route.query;
    const userTeamRole = storageLocal().getItem(`${orderId}_userTeamRole`);
    const teamRoleLists = userTeamRole
      ? userTeamRole[props.milestoneRow.msId]
      : [];
    if (index !== -1) {
      // 里程碑批复人有当前团队
      return teamRoleLists.includes("assign_milestone_approval");
    } else {
      // 里程碑批复人直接是具体某个人
      const index = props.milestoneRow.msReplyUser.findIndex(
        msReply => msReply.teamMemberId === latestLoginTeamMemberId
      );
      return index !== -1;
    }
  }
};

/**
 * 创建工单权限
 */
const createWorkOrderBtnPerFn = () => {
  if (
    props.milestoneRow.status === "CANCEL" ||
    props.milestoneRow.status === "COMPLETE"
  ) {
    return false;
  }
  return (
    ["NOT_START", "ON_GOING"].includes(props.orderStateFlag) &&
    ["NOT_START", "ON_GOING"].includes(props.milestoneRow.status)
  );
};

/**
 * 添加互用权限
 */
const cloneWorkOrderBtnPerFn = () => {
  if (
    props.milestoneRow.status === "CANCEL" ||
    props.milestoneRow.status === "COMPLETE"
  ) {
    return false;
  }
  return true;
};

/**
 * 当前里程碑是否是被互用出来的
 */
const cloneMislestoneBtnPerFn = () => {
  if (props.milestoneRow.cloneSourceId) {
    return false;
  }
  return true;
};

/**
 * 订单状态是否是进行中
 */
const getOrderState = () => {
  return props.orderStateFlag === "ON_GOING";
};
/**
 * 判断是否可以里程碑互用自其他订单
 */
const getSelectBtnStatus = () => {
  let status = false;
  if (
    props.milestoneRow.cloneSourceId ||
    props.milestoneRow.relationIconType === "CLONE_MS" ||
    props.milestoneRow.workOrderGroupList.some(
      e => e.relationIconType === "CLONE_WORK_ORDER"
    ) ||
    props.milestoneRow.workOrderGroupList.some(e => e.cloneSourceId)
  ) {
    status = true;
  }
  return status;
};

/**
 * 里程碑状态
 */
const getMilestoneState = milestoneStateList => {
  return milestoneStateList.includes(props.milestoneRow.status);
};

/**
 * 当前工单采集人是否是本人
 */
const workOrderCollectStatus = (workOrder, userList) => {
  // bug:Lk2-4827 要求里程碑关闭时不显示按钮
  if (
    props.milestoneRow.status === "CANCEL" ||
    props.milestoneRow.status === "COMPLETE" ||
    workOrder.stopped
  ) {
    return false;
  }
  if (workOrder.replied && userList === "editUserList") {
    return false;
  }
  const { latestLoginTeamMemberId, latestLoginTeamId } = storageLocal().getItem(
    "user-info"
  ) as any;
  const teamIndex = workOrder[userList].findIndex(
    msReplyUser => msReplyUser.teamId === latestLoginTeamId && !msReplyUser.user
  );
  const teamRoleObject = storageLocal().getItem(
    `${workOrder.orderId}_userTeamRole`
  );
  if (!teamRoleObject) {
    return false;
  }
  const teamRoleLists = teamRoleObject[workOrder.msId];
  if (teamIndex !== -1) {
    return teamRoleLists.includes("assign_work_order_collection");
  } else {
    const userIndex = workOrder[userList].findIndex(
      msReplyUser =>
        msReplyUser.teamMemberId === latestLoginTeamMemberId && msReplyUser.user
    );
    return userIndex !== -1;
  }
};

// 里程碑中tab切换请求对应的动态数量
const handleWorkOrderChange = async (tabItem: any) => {
  activeName.value = tabItem.props.name;

  calculateVisibleFormWorkOrderData();

  const label = tabItem.props.label;
  let type = "";
  let paramId = "";
  let msId = props.milestoneRow.msId;
  if (label === t("trade_template_replyBtnName")) {
    type = "MS_REPLY_DYNAMIC";
    paramId = props.milestoneRow.msId;
  } else {
    type = "WORK_ORDER_DYNAMIC";
    paramId = tabItem.props.name;
  }
  emit("upDataCount", msId, paramId, type);
};

// 获取当前模块的动态数量("WORK_ORDER_DYNAMIC":工单, "MS_REPLY_DYNAMIC":里程碑批复 )
const getMoudleDynamicCount = (id, type) => {
  return props.orderCountMap.get(id + ":" + type);
};

/**
 * 里程碑批复的指派权限：当前登录的这个团队的，而不是这个团队的人
 */
const getMilestoneReplyWhetherTeam = () => {
  if (
    props.milestoneRow.status === "CANCEL" ||
    props.milestoneRow.status === "COMPLETE"
  ) {
    return true;
  }

  const userInfo = storageLocal().getItem("user-info") as any;
  if (userInfo) {
    const { latestLoginTeamId } = userInfo;
    const msReplyTeamList = props.milestoneRow.msReplyUser.filter(
      msReplyUsers => !msReplyUsers.user
    );
    const index = msReplyTeamList.findIndex(
      teamItem => teamItem.teamId === latestLoginTeamId
    );
    return msReplyTeamList && msReplyTeamList.length === 0
      ? false
      : index !== -1;
  }
};

/**
 * 工单批复的指派权限：当前登录的这个团队的，而不是这个团队的人
 */
const getWorkOrderReplyWhetherTeam = (workOrder: any, userList: any[]) => {
  if (workOrder.edited) {
    return false;
  }
  const { latestLoginTeamId } = storageLocal().getItem("user-info") as any;
  const msReplyTeamList = userList.filter(msReplyUsers => !msReplyUsers.user);

  const index = msReplyTeamList.findIndex(
    teamItem => teamItem.teamId === latestLoginTeamId
  );

  return msReplyTeamList && msReplyTeamList.length === 0 ? false : index !== -1;
};

/**
 * 工单批复的指派权限：当前登录的这个团队的，而不是这个团队的人
 */
const getWorkOrderReplyWhetherTeamMember = (
  workOrder: any,
  userList: any[]
) => {
  if (workOrder.edited) {
    return false;
  }
  const { latestLoginTeamId } = storageLocal().getItem("user-info") as any;
  const index = userList.findIndex(
    member => member.teamId === latestLoginTeamId
  );
  return index !== -1;
};

const getWorkOrderBtnList = (workOrder): any => {
  return [
    // 批复指派
    {
      label: "trade_home_approvalAssign",
      icon: "link-distribute",
      type: "approvalDistribute",
      badge: 0,
      btnPermission: {
        coopTeam: ["assign_work_order_approval"],
        mainTeam: [],
        customPermission: workOrder.workOrderOperation.reAssignReplyAbleForTeam
      }
    },
    // 批复指派
    {
      label: "trade_home_approvalAssign",
      icon: "link-distribute",
      type: "approvalAssignPerson",
      badge: 0,
      btnPermission: {
        coopTeam: ["assign_work_order_approval"],
        mainTeam: [],
        customPermission:
          workOrder.workOrderOperation.reAssignReplyAbleForTeamMember
      }
    },
    {
      label: "trade_home_approval",
      icon: "link-reply",
      type: "workOrderReply",
      badge: 0,
      btnPermission: {
        coopTeam: ["All"],
        mainTeam: ["All"],
        customPermission:
          workOrderCollectStatus(workOrder, "replyUserList") &&
          !workOrder.cloneSourceId &&
          !["COMPLETE", "CANCEL"].includes(props.milestoneRow.status)
      }
    },
    // 采集指派
    {
      label: "trade_common_gatherAssign",
      icon: "link-distribute",
      type: "collectDistribute",
      badge: 0,
      btnPermission: {
        coopTeam: ["assign_work_order_collection"],
        mainTeam: [],
        customPermission:
          workOrder.workOrderOperation.reAssignCollectableForTeam
      }
    },
    // 采集指派
    {
      label: "trade_common_gatherAssign",
      icon: "link-distribute",
      type: "collectorsPerson",
      badge: 0,
      btnPermission: {
        coopTeam: ["assign_work_order_collection"],
        mainTeam: [],
        customPermission:
          workOrder.workOrderOperation.reAssignCollectableForTeamMember
      }
    },
    {
      label: "trade_common_gather",
      icon: "link-collect",
      type: "workOrderCollect",
      badge: 0,
      btnPermission: {
        coopTeam: ["All"],
        mainTeam: ["All"],
        customPermission:
          workOrderCollectStatus(workOrder, "editUserList") &&
          !workOrder.cloneSourceId &&
          !["COMPLETE", "CANCEL"].includes(props.milestoneRow.status)
      }
    },
    // 修改处理人(采集人)
    {
      label: "trade_order_updatePersonnel",
      icon: "link-modify-processor",
      type: "modifyProcessor",
      badge: 0,
      btnPermission: {
        coopTeam: ["update_processor"],
        mainTeam: ["update_processor"],
        customPermission: workOrder.workOrderOperation.updateProcessor
      }
    },
    {
      label: "trade_order_updateWorkOrderApprover",
      icon: "link-modify-processor",
      type: "modifyWorkOrderProcessor",
      badge: 0,
      btnPermission: {
        // 修改工单批复人按钮不受里程碑批复影响 2025/2/26 辉荣、谢坚、陈询确认
        coopTeam: ["update_processor"],
        mainTeam: ["update_processor"],
        customPermission:
          !["COMPLETE", "CANCEL"].includes(props.milestoneRow.status) &&
          workOrder.workOrderReplyId &&
          !workOrder.stopped &&
          !workOrder.cloneSourceId
      }
    },
    {
      label: "trade_order_emailCollaborate",
      icon: "link-youjianxiezuo",
      type: "emailCollaborate",
      badge: 0,
      btnPermission: {
        coopTeam: ["All"],
        mainTeam: ["All"],
        customPermission: !workOrder.cloneSourceId
      }
    },
    // 工单互用与
    {
      label: "trade_order_MutualUse",
      icon: "link-shared-in",
      type: "workOrderSharedIn",
      badge: 0,
      btnPermission: {
        coopTeam: ["clone_work_order"],
        mainTeam: ["clone_work_order"],
        customPermission: !workOrder.cloneSourceId
      }
    },
    {
      label: "trade_order_dynamic",
      icon: "link-dynamics-ms",
      type: "workOrderDynamic",
      id: workOrder.workOrderId,
      badge: getMoudleDynamicCount(
        workOrder.cloneSourceId || workOrder.workOrderId,
        "WORK_ORDER_DYNAMIC"
      ),
      btnPermission: {
        coopTeam: ["All"],
        mainTeam: ["All"],
        customPermission: true
      }
    },
    {
      label: "trade_common_more",
      icon: "link-more-operation",
      badge: 0,
      customPermission:
        props.milestoneRow.status !== "CANCEL" &&
        props.milestoneRow.status !== "COMPLETE" &&
        !workOrder.cloneSourceId,
      dropdownList: [
        {
          type: "stopCollect",
          icon: "link-zhongzhi",
          label: "trade_order_stopCollect",
          class: "delete_order_class",
          btnPermission: {
            coopTeam: [],
            mainTeam: ["stop_work_order"],
            customPermission: !workOrder.stopped && !workOrder.replied
          }
        },
        {
          type: "deleteWorkOrder",
          icon: "link-ashbin",
          label: "trade_order_removeWorkOrder",
          class: "delete_order_class",
          btnPermission: {
            coopTeam: [],
            mainTeam: ["delete_work_order"],
            customPermission: true
          }
        }
      ]
    }
  ].filter(item => {
    if (item.dropdownList) {
      item.dropdownList = item.dropdownList.filter(dropdownItem => {
        return handleObtainBtnPermission(dropdownItem);
      });
      return item.dropdownList.length > 0;
    }
    return handleObtainBtnPermission(item);
  });
};

const getTimeLineStampFormat = (dateTime: any) => {
  if (dateTime) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "";
  }
};

const getWorkOrderReplyWidgetData = (
  widgetList,
  subWidgetMap,
  linkedReferenceMap
): any => {
  let widgetFormData = TransformSubmitDataStructure(
    widgetList.filter(widget => widget.workOrderType === "WORK_ORDER_REPLY"),
    subWidgetMap,
    linkedReferenceMap
  );
  return widgetFormData;
};

const getWorkOrderWidgetData = (
  widgetList,
  subWidgetMap,
  linkedReferenceMap
): any => {
  let widgetFormData = TransformSubmitDataStructure(
    widgetList.filter(widget => widget.workOrderType === "WORK_ORDER"),
    subWidgetMap,
    linkedReferenceMap
  );
  return widgetFormData;
};

const getMilestoneReplyWidgetData = (
  widgetList,
  subWidgetMap,
  linkedReferenceMap
): any => {
  let widgetFormData = TransformSubmitDataStructure(
    widgetList,
    subWidgetMap,
    linkedReferenceMap
  );
  return widgetFormData;
};

const emit = defineEmits<{
  (e: "upDataCount", msId, id, type): void;
  (e: "handleTrigger", data, dataRow): void;
  (e: "initOrderDetail", data, dataRow): void;
  (e: "handleTriggerWorkOrder", data): void;
}>();

const handleTrigger = (item: any, data?: any) => {
  emit("handleTrigger", item.type || item, data);
  // 动态数量置0
  if (item.type === "workOrderDynamic" && item.id) {
    workOrderDynamicCount.value = 0;
  } else if (item.type === "milestoneDynamic") {
    workOrderDynamicReplyCount.value = 0;
  }
};

const calculateVisibleFormWorkOrderData = () => {
  if (props.visibleFormData.hasOwnProperty(activeName.value)) {
    handleSettingWidgetJsonList(props.visibleFormData[activeName.value]);
  } else {
    const operationalFactorData = getEntireFormData(
      props.milestoneCard.msId,
      activeName.value
    );

    const bindFormIdList = props.milestoneCard.workOrderReplyForm
      ? [
          ...props.milestoneCard.formList,
          props.milestoneCard.workOrderReplyForm
        ].map(form => form.id)
      : props.milestoneCard.formList.map(form => form.id);

    const operationFormVisibleList =
      operationalFactorData.formVisibleTemplateFormConfigure.filter(relate =>
        bindFormIdList.includes(relate.formId)
      );

    if (operationFormVisibleList.length > 0) {
      let formVisibleTrendsObject = {};
      for (let visibleTemplateItem of operationFormVisibleList) {
        const formVisibleBoolean = ExecuteFormVisibleExpression(
          visibleTemplateItem.conditions,
          operationalFactorData
        );
        for (let widgetId of visibleTemplateItem.widgetIdList) {
          formVisibleTrendsObject[widgetId] = formVisibleBoolean;
        }
      }

      handleSettingWidgetJsonList(formVisibleTrendsObject);
      emit("handleTriggerWorkOrder", {
        [activeName.value]: formVisibleTrendsObject
      });
    }
  }
};

const handleObtainBtnPermission = (
  btnItem: any,
  msId: string = props.milestoneRow.msId,
  orderId: string = route.query.orderId as string
) => {
  if (!btnItem.btnPermission) {
    return true;
  }

  return obtainBtnPermission(btnItem.btnPermission, msId, orderId);
};

const handleSettingWidgetJsonList = (formVisibleTrendsObject: any) => {
  if (props.milestoneCard.workOrderReplyForm) {
    const workOrderReplyWidgetList = calculateVisibleFormList(
      props.milestoneCard.workOrderReplyForm.widgetJsonList,
      formVisibleTrendsObject
    );
    workOrderReplyWidgetJsonList.value = workOrderReplyWidgetList;
  }

  let milestoneFormObject = {};
  const milestoneCardFormData = cloneDeep(props.milestoneCard.formList);
  if (milestoneCardFormData.length > 0) {
    for (let formItem of milestoneCardFormData) {
      const formWidgetList = calculateVisibleFormList(
        formItem.widgetJsonList,
        formVisibleTrendsObject
      );

      milestoneFormObject = {
        ...milestoneFormObject,
        [formItem.id]: formWidgetList
      };
    }
  }
  formWidgetJsonList.value = milestoneFormObject;
};

watch(
  () => props.milestoneRow,
  (newVal, oldVal) => {
    if (
      props.milestoneRow.workOrderGroupList &&
      props.milestoneRow.workOrderGroupList.length > 0 &&
      !props.milestoneRow.visible &&
      (!props.activeWorkOrderId ||
        !props.milestoneRow.workOrderGroupList.some(
          workOrder => workOrder.workOrderId === props.activeWorkOrderId
        ))
    ) {
      // 如果没有activeWorkOrderId，或者activeWorkOrderId不在当前工单组，则默认选中第一个工单
      activeName.value = props.milestoneRow.workOrderGroupList[0].workOrderId;
    }
    workOrderReplyWidgetJsonList.value = props.milestoneCard.workOrderReplyForm
      ? props.milestoneCard.workOrderReplyForm.widgetJsonList
      : [];

    if (!isEqual(newVal, oldVal)) {
      formWidgetJsonList.value = props.milestoneCard.formList.reduce(
        (obj, item) => {
          obj[item.id] = item.widgetJsonList;
          return obj;
        },
        {}
      );
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.activeWorkOrderId,
  () => {
    const { activeWorkOrderId, milestoneRow } = props;
    if (activeWorkOrderId) {
      if (
        milestoneRow.workOrderGroupList?.some(
          workOrder => workOrder.workOrderId === activeWorkOrderId
        )
      ) {
        activeName.value = activeWorkOrderId;
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  [() => props.visibleFormData, () => props.msVisibleIdsList],
  () => {
    if (props.visibleFormData.hasOwnProperty(activeName.value)) {
      handleSettingWidgetJsonList(props.visibleFormData[activeName.value]);
    }
  },
  {
    deep: true
  }
);

defineExpose({
  activeName,
  replayMileStoneBtnList,
  getWorkOrderBtnList
});
</script>
<style lang="scss" scoped>
@use "../style/index.scss";

.work-order-icon-stop {
  display: inline-block;
  height: 20px;
  padding-right: 10px;
  margin-left: 36px;
  font-size: 12px;
  line-height: 20px;
  color: #262626;
  vertical-align: baseline;
  background: #e1e1e1;
  border-radius: 12px;

  .iconfont {
    font-size: 12px;
    color: #595959;
  }
}
</style>
<style lang="scss">
.order-region-popper {
  .el-dropdown-menu__item {
    height: 36px;
    line-height: 36px;

    &.delete_order_class {
      color: #cf1421;

      &:hover {
        color: #cf1421 !important;
      }
    }

    &.hide-btn {
      display: none !important;
    }
  }
}

.milestone-widget-collapse {
  .el-collapse-item__content {
    padding-bottom: 0 !important;
  }

  .order-detail-desc-col {
    margin: 0 10px;

    .order-detail-desc-title {
      position: relative;
      height: 42px;
      font-size: 14px;
      font-weight: bolder;
      line-height: 42px;
      color: #262626;
      text-indent: 18px;
      border-bottom: 1px solid #e4e4e4;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        display: inline-block;
        width: 4px;
        height: 13px;
        content: "";
        background: #2082ed;
        transform: translateY(-50%);
      }
    }

    // .order-detail-descriptions-widget {
    //   padding: 27px 16px 0 !important;
    // }
  }

  &:last-child {
    margin-bottom: 0 !important;
  }
}
</style>
