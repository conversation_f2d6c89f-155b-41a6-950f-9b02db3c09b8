<template>
  <div class="email-receive-table-container">
    <div class="email-receive-table-body">
      <div class="email-receive-table-header">
        <div class="email-receive-table-left">
          <div class="email-receive-table-title">
            <div class="email-receive-table-title-name">
              {{ t("trade_email_receiveEmailCoop") }}
            </div>
            <div class="email-receive-table-title-email">
              &lt;{{ route.query.email }}&gt;
            </div>
          </div>
          <div class="email-receive-table-tip">
            <el-image class="sign-image" :src="svgImage" />
            {{ t("trade_email_coopReceiveTip") }}
          </div>
        </div>
        <div class="email-receive-table-right">
          <el-image class="sign-image" :src="logoImage" :width="179" />
        </div>
      </div>
      <div class="email-receive-table-list">
        <div class="email-receive-table-search-header">
          <div class="email-receive-table-search-header-left">
            <el-segmented
              v-model="sortByTime"
              :options="emailSearchOptions"
              @change="handleInitEmailList"
            >
              <template #default="{ item }">
                <div>{{ t(item.label) }}</div>
              </template>
            </el-segmented>
          </div>
          <div class="email-receive-table-search-header-right">
            <el-input
              v-model="keyword"
              style="width: 322px"
              :placeholder="t('trade_email_searchTip')"
              clearable
              @change="handleInitEmailList"
            >
              <template #prefix>
                <i class="iconfont link-search" />
              </template>
            </el-input>
          </div>
        </div>
        <div class="email-receive-table-search-body">
          <div class="email-receive-table-theader">
            <div class="email-receive-table-th">
              {{ t("trade_email_subject2") }}
            </div>
            <div class="email-receive-table-th">
              {{ t("trade_email_orderMark") }}
            </div>
            <div class="email-receive-table-th">
              {{ t("trade_home_sccsName") }}
            </div>
            <div class="email-receive-table-th">
              {{ t("trade_template_milestoneName") }}
            </div>
            <div class="email-receive-table-th">
              {{ t("trade_common_creator") }}
            </div>
            <div class="email-receive-table-th">
              {{ t("trade_common_creatorTime") }}
            </div>
            <div class="email-receive-table-th">
              {{ t("trade_common_updateTime") }}
            </div>
            <div class="email-receive-table-th">
              {{ t("trade_common_operate") }}
            </div>
          </div>
          <ul
            v-infinite-scroll="loadReceiveEmailList"
            class="email-receive-table-tbody-container"
            :infinite-scroll-disabled="disabled"
          >
            <li
              v-for="item in receiveTableData"
              :key="item.id"
              class="email-receive-table-tbody"
            >
              <div class="email-receive-table-tr">
                <ReText type="info" :tippyProps="{ delay: 50000 }">
                  {{ item.subject }}
                </ReText>
              </div>
              <div class="email-receive-table-tr">
                <ReText type="info" :tippyProps="{ delay: 50000 }">
                  {{ item.orderMark }}
                </ReText>
              </div>
              <div class="email-receive-table-tr">
                <ReText type="info" :tippyProps="{ delay: 50000 }">
                  {{ item.sccsName }}
                </ReText>
              </div>
              <div class="email-receive-table-tr">
                <ReText type="info" :tippyProps="{ delay: 50000 }">
                  {{ item.milestoneName }}
                </ReText>
              </div>
              <div class="email-receive-table-tr">
                <ReText type="info" :tippyProps="{ delay: 50000 }">
                  {{ item.creatorName }}
                </ReText>
                <span class="email-receive-table-tr-assistant-text">
                  {{ item.email }}
                </span>
              </div>
              <div class="email-receive-table-tr">
                <span class="email-receive-table-tr-assistant-text">
                  {{ item.createTime }}
                </span>
              </div>
              <div class="email-receive-table-tr">
                <span class="email-receive-table-tr-assistant-text">
                  {{ item.submitTime }}
                </span>
              </div>
              <div class="email-receive-table-tr">
                <span
                  v-if="item.workOrderCoopEmailStatus === 'COMPLETE'"
                  class="email-receive-btn"
                  @click="handleReadEmail(item.id)"
                >
                  <i class="iconfont link-display" />
                  {{ t("trade_common_view") }}
                </span>
                <span
                  v-else
                  class="email-receive-btn"
                  @click="handleEditEmail(item.id)"
                >
                  <i class="iconfont link-edit" />
                  {{ t("trade_common_handle") }}
                </span>
              </div>
              <div
                v-if="item.workOrderCoopEmailStatus === 'COMPLETE'"
                class="email-receive-tag"
              >
                {{ t("trade_order_stateCompleted") }}
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="tsx" setup>
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import logoImage from "@/assets/images/email/linkincreate.png";
import svgImage from "@/assets/images/email/svgIcon.png";
import { getReceiveCoopEmail } from "@/api/email";
import { ReText } from "@/components/ReText";
import dayjs from "dayjs";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const keyword = ref<string>("");
const receiveTableData = ref<any>([]);
const emailSearchOptions = ref<any[]>([
  {
    label: "trade_email_createTimeSearch",
    value: "create_time"
  },
  {
    label: "trade_email_updateTimeSearch",
    value: "submit_time"
  }
]);
const sortByTime = ref<string>("create_time");
const loading = ref<boolean>(false);
const pageNo = ref<number>(0);
const pageSize = ref<number>(10);
const total = ref<number>(-1);
const disabled = computed(
  () =>
    loading.value ||
    (total.value <= receiveTableData.value.length && total.value !== -1)
);

const handleReadEmail = (id: string) => {
  window.open(`/emailCoop/emailDetail?id=${id}&receiveFlag=true`);
};

const handleEditEmail = (id: string) => {
  router.push(`/emailCoop/writeEmailCoop?id=${id}`);
};

const loadReceiveEmailList = () => {
  pageNo.value++;
  handleReceiveEmailList();
};

const handleInitEmailList = () => {
  receiveTableData.value = [];
  pageNo.value = 1;
  handleReceiveEmailList();
};

const handleReceiveEmailList = async () => {
  const email = route.query.email;
  loading.value = true;
  const { data } = await getReceiveCoopEmail({
    email: email,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    keyword: keyword.value,
    descending: true,
    sortBy: sortByTime.value
  });
  data.list.forEach(receiveRow => {
    receiveRow.createTime = dayjs(new Date(receiveRow.createTime)).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    receiveRow.submitTime = dayjs(new Date(receiveRow.submitTime)).format(
      "YYYY-MM-DD HH:mm:ss"
    );
  });
  receiveTableData.value.push(...data.list);
  total.value = data.total;
  loading.value = false;
};
</script>
<style lang="scss" scoped>
.email-receive-table-container {
  overflow: hidden;
  background: #f7f7f7;

  .email-receive-table-body {
    width: 1300px;
    margin: 22px auto;

    .email-receive-table-header {
      display: flex;
      align-items: center;

      .email-receive-table-left {
        flex: 1;

        .email-receive-table-title {
          display: flex;
          align-items: center;
          margin-bottom: 17px;

          .email-receive-table-title-name {
            font-size: 18px;
            font-weight: bold;
            color: #262626;
            text-align: left;
          }

          .email-receive-table-title-email {
            margin-left: 8px;
            font-size: 14px;
            line-height: 16px;
            color: #797979;
            text-align: left;
          }
        }

        .email-receive-table-tip {
          font-size: 14px;
          background: linear-gradient(90deg, #4851e5, #935ad8);
          background-clip: text; /* 将渐变应用到文字上 */
          -webkit-text-fill-color: transparent; /* 设置文字颜色为透明，显示渐变效果 */

          .el-image {
            width: 16px;
            height: 16px;
            vertical-align: middle;
          }
        }
      }

      .email-receive-table-right {
        .el-image {
          width: 179px;
          vertical-align: middle;
        }
      }
    }

    .email-receive-table-list {
      .email-receive-table-search-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 40px 0 24px;

        .email-receive-table-search-header-left {
          flex: 1;

          .el-segmented {
            padding: 4px;
            background: #fff;
            border-radius: 4px;

            ::v-deep(.el-segmented__item) {
              .el-segmented__item-label {
                font-size: 14px;
                font-weight: normal;
                line-height: 20px;
              }
            }

            ::v-deep(.el-segmented__item.is-selected) {
              color: #0070d2;
              background: #ddefff;
            }

            ::v-deep(.el-segmented__item-selected) {
              color: #0070d2;
              background: #ddefff;
            }
          }
        }

        .email-receive-table-search-header-right {
          flex: 1;
          text-align: right;
        }
      }

      .email-receive-table-search-body {
        margin-top: 24px;

        .email-receive-table-theader {
          display: flex;
          align-items: center;
          width: 100%;
          height: 40px;
          margin-bottom: 8px;

          .email-receive-table-th {
            flex: 1;
            padding-left: 20px;
            font-size: 14px;
            font-weight: bolder;
            color: #797979;

            &:first-child {
              flex: none;
              width: 267px;
            }

            &:nth-last-child(1) {
              flex: none;
              width: 100px;
            }

            &:nth-last-child(2) {
              flex: none;
              width: 135px;
            }

            &:nth-last-child(3) {
              flex: none;
              width: 135px;
            }

            &:nth-last-child(4) {
              flex: none;
              width: 135px;
            }
          }
        }

        .email-receive-table-tbody-container {
          height: calc(100vh - 253px);
          padding: 0;
          margin: 0;
          overflow-y: auto;
          list-style: none;

          .email-receive-table-tbody {
            position: relative;
            display: flex;
            align-items: center;
            height: 84px;
            padding: 0 10px;
            margin-bottom: 16px;
            text-align: center;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 3px 13px 0 rgb(0 0 0 / 11%);

            .email-receive-table-tr {
              flex: 1;
              padding-left: 10px;
              font-size: 14px;
              font-weight: bolder;
              color: #797979;
              text-align: left;

              .email-receive-table-tr-assistant-text {
                font-size: 12px;
                line-height: 17px;
                color: #797979;
              }

              .email-receive-btn {
                font-size: 14px;
                font-weight: normal;
                line-height: 20px;
                color: #0070d2;
                text-align: left;
                cursor: pointer;

                .iconfont {
                  font-size: 13px;
                  vertical-align: middle;
                }
              }

              &:first-child {
                flex: none;
                width: 268px;
                padding-right: 20px;
              }

              &:nth-child(2) {
                flex: none;
                width: 177px;
                padding-right: 20px;
              }

              &:nth-child(3) {
                flex: none;
                width: 175px;
                padding-right: 20px;
              }

              &:nth-child(4) {
                flex: none;
                width: 175px;
                padding-right: 20px;
              }

              &:nth-child(5) {
                flex: none;
                width: 135px;
              }

              &:nth-child(6) {
                flex: none;
                width: 135px;
              }

              &:nth-child(7) {
                flex: none;
                width: 135px;
              }
            }

            .email-receive-tag {
              position: absolute;
              top: 0;
              right: 0;
              height: 20px;
              padding: 0 8px;
              font-size: 12px;
              line-height: 20px;
              color: #32a645;
              text-align: center;
              background: #d9f4f0;
              border-radius: 4px 4px 0;
            }
          }
        }

        .email-receive-tag {
          position: absolute;
        }
      }
    }
  }
}
</style>
