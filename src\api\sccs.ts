import { http } from "@/utils/http";
import type { Result } from "./type";
import type {
  SavePlanListProp,
  SccsUpdateProp,
  SccsFenceListProp,
  SccsFenceProp,
  SccsRoleProp,
  SccsEditRoleProp,
  SccsCoopTeamRoleProp,
  SccsCoopEditProp
} from "@/types/sccs/type";

/**
 * 获取sccs设置-计划管理-里程碑计划列表
 * @param data
 * @returns
 */
export const getSccsMilestonePlanList = (params: { sccsId: any }) => {
  return http.request<Result>("get", "/trade/sccs-milestone-planned/list", {
    params
  });
};

/**
 * 获取sccs设置-计划管理-里程碑计划列表-select下拉数据
 * @returns
 */
export const getMileStonePlanListDateField = (params: { sccsId: string }) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-milestone-planned/milestone-date-field",
    { params }
  );
};

/**
 * 保存sccs设置-计划管理-里程碑计划列表
 * @returns
 */
export const saveSccsMilestonePlanList = (data: SavePlanListProp) => {
  return http.request<Result>("put", "/trade/sccs-milestone-planned/update", {
    data
  });
};

/**
 * 获取sccs详情
 * @returns
 */
export const getSccsDetail = (params: { id: string }) => {
  return http.request<Result>("get", "/trade/sccs/get", {
    params
  });
};

/**
 * 更新贸易端项目
 * @returns
 */
export const updateSccs = (data: SccsUpdateProp) => {
  return http.request<Result>("put", "/trade/sccs/update", { data });
};

/**
 * 数据围栏分页数据
 * @returns
 */
export const getDataFenceList = (params: SccsFenceListProp) => {
  return http.request<Result>("get", "/trade/sccs-data-fence/page", { params });
};

/**
 * 获取数据围栏详情
 * @returns
 */
export const getDataFenceDetail = (params: { id: string }) => {
  return http.request<Result>("get", "/trade/sccs-data-fence/get", { params });
};

/**
 * 保存数据围栏
 * @returns
 */
export const saveDataFence = (data: SccsFenceProp) => {
  return http.request<Result>("post", "/trade/sccs-data-fence/save", { data });
};

/**
 * 删除数据围栏
 * @returns
 */
export const deleteDataFence = (params: { id: string; sccsId: string }) => {
  return http.request<Result>("delete", "/trade/sccs-data-fence/delete", {
    params
  });
};

/**
 * 获取数据围栏的组件列表
 * @returns
 */
export const getFindWidgetList = (params: {
  userId?: string | null;
  teamId?: string | null;
  sccsId: any;
}) => {
  return http.request<Result>("get", "/trade/sccs-data-fence/findWidgetList", {
    params
  });
};

/**
 * 获得SCCS成员列表
 * @returns
 */
export const getSccsMemberList = (params: { sccsId?: string | null }) => {
  return http.request<Result>("get", "/trade/sccs-member/list", {
    params
  });
};

/**
 * 获取sccs的协作团队列表
 * @returns
 */
export const getSccsCoopTeamList = (params: { sccsId?: string | null }) => {
  return http.request<Result>("get", "/trade/sccs-coop-team-invite/list", {
    params
  });
};

/**
 * 取sccs角色分页列表
 * @returns
 */
export const getSccsRole = (params: SccsRoleProp) => {
  return http.request<Result>("get", "/trade/sccs-role/get-sccs-role-page", {
    params
  });
};

/**
 * 取sccs角色详情
 * @returns
 */
export const getRoleDetail = (params: { id: string }) => {
  return http.request<Result>("get", "/trade/sccs-role/get", {
    params
  });
};

/**
 * 创建贸易端Sccs角色
 * @returns
 */
export const createSccsRole = (data: SccsEditRoleProp) => {
  return http.request<Result>("post", "/trade/sccs-role/create", {
    data
  });
};

/**
 * 更新贸易端Sccs角色
 * @returns
 */
export const updateSccsRole = (data: SccsEditRoleProp) => {
  return http.request<Result>("post", "/trade/sccs-role/update", {
    data
  });
};

/**
 * 删除贸易端Sccs业务角色
 * @returns
 */
export const deleteSccsRole = (params: { id: string; sccsId: string }) => {
  return http.request<Result>("delete", "/trade/sccs-role/delete", {
    params
  });
};

/**
 * 取sccs协作方角色分页列表
 * @returns
 */
export const getSccsCoopTeamRole = (params: SccsCoopTeamRoleProp) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-coop-team-role/get-sccs-role-page",
    {
      params
    }
  );
};

/**
 * 取sccs协作方角色详情
 * @returns
 */
export const getSccsCoopTeamDetail = (params: { id: string }) => {
  return http.request<Result>("get", "/trade/sccs-coop-team-role/get", {
    params
  });
};

/**
 * 创建贸易端Sccs协作方角色
 * @returns
 */
export const createSccsCoopTeam = (data: SccsCoopEditProp) => {
  return http.request<Result>("post", "/trade/sccs-coop-team-role/create", {
    data
  });
};

/**
 * 修改贸易端Sccs协作方角色
 * @returns
 */
export const updateSccsCoopTeam = (data: SccsCoopEditProp) => {
  return http.request<Result>("post", "/trade/sccs-coop-team-role/update", {
    data
  });
};

/**
 * 删除贸易端Sccs协作方角色
 * @returns
 */
export const deleteSccsCoopTeam = (params: {
  id: string[];
  sccsId: string;
}) => {
  return http.request<Result>("delete", "/trade/sccs-coop-team-role/delete", {
    params
  });
};

/**
 * 取sccs所有的权限列表
 * @returns
 */
export const getSccsRolePermissionTree = (params: { sccsId: string[] }) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-role/get-sccs-role-permission-tree",
    {
      params
    }
  );
};

/**
 * 取sccs所有的权限列表
 * @returns
 */
export const getSccsCoopRolePermissionTree = (params: { sccsId: string }) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-coop-team-role/get-sccs-role-permission-tree",
    {
      params
    }
  );
};

/**
 * 获取当前用户在当前团队可见sccs列表
 * @returns
 */
export const getSccsListByUser = (params: {
  sccsSearch: string;
  topFlag?: boolean;
  sccsGroupId: string;
  isGroup: boolean;
}) => {
  return http.request<Result>("get", "/trade/sccs/sccs-list-by-user", {
    params
  });
};

/**
 * 获得贸易端项目分组列表
 * @param params
 * @returns
 */
export const getSccsGroupList = (params: {
  isEmpty?: boolean;
  isContainSccsInfo?: boolean;
}) => {
  return http.request<Result>("get", "/trade/sccs-group/list", {
    params
  });
};

/**
 * 用户置顶sccs
 * @param params
 * @returns
 */
export const setSccsTopById = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/sccs/top-by-user", {
    params
  });
};

/**
 * 批量更新sccs分组顺序
 * @param data
 * @returns
 */
export const updateSccsGroupSort = (data: string[]) => {
  return http.request<Result>("post", "/trade/sccs-group/update-all-sort", {
    data
  });
};

/**
 * 移入sccs分组
 * @param data
 * @returns
 */
export const moveSccsGroup = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-group/move", {
    data
  });
};

/**
 * 创建贸易端项目分组
 * @param data
 * @returns
 */
export const createSccsGroup = (data: { id: string; groupName: string }) => {
  return http.request<Result>("post", "/trade/sccs-group/create", {
    data
  });
};

/**
 * 更新贸易端项目分组
 * @param data
 * @returns
 */
export const updateSccsGroup = (data: { id: string; groupName: string }) => {
  return http.request<Result>("put", "/trade/sccs-group/update", {
    data
  });
};

/**
 * 删除贸易端项目分组
 * @param data
 * @returns
 */
export const deleteSccsGroup = (params: { id: string }) => {
  return http.request<Result>("delete", "/trade/sccs-group/delete", {
    params
  });
};

/**
 * 获取所有的行业列表
 * @param data
 * @returns
 */
export const getTemplateIndustryList = () => {
  return http.request<Result>("get", "/trade/template/industry-list");
};

/**
 * 获取对应行业下的业务模版列表
 * @returns
 */
export const getBizTemplateList = () => {
  return http.request<Result>("get", "/trade/template/biz-template-list");
};

/**
 * 创建贸易端项目
 * @returns
 */
export const createSccs = (data: any) => {
  return http.request<Result>("post", "/trade/sccs/create", { data });
};

/**
 * 获得新的项目code
 * @returns
 */
export const getNewSccsCode = () => {
  return http.request<Result>("get", "/trade/sccs/getNewSccsCode");
};

/**
 * 数据围栏列表数据
 * @param params
 * @returns
 */
export const getSccsFenceList = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/sccs-data-fence/list", {
    params
  });
};

/**
 * 取当前sccs的角色下拉选项
 * @param params
 * @returns
 */
export const getSccsRoleList = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/sccs-role/options", {
    params
  });
};

/**
 * 获得SCCS团队成员数据分页
 * @param params
 * @returns
 */
export const getSccsMemberListPage = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/sccs-member/page", {
    params
  });
};

/**
 * SCCS添加团队成员
 * @param params
 * @returns
 */
export const createSccsMember = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-member/create", {
    data
  });
};

/**
 * 删除SCCS团队成员关系
 * @param params
 * @returns
 */
export const deleteSccsMember = (params: any) => {
  return http.request<Result>("delete", "/trade/sccs-member/delete", {
    params
  });
};

/**
 * 获得未加入的团队成员列表
 * @param params
 * @returns
 */
export const getSccsNotAddList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-member/team-member-not-add-list",
    {
      params
    }
  );
};

/**
 * 设置业务角色
 * @param params
 * @returns
 */
export const updateSccsMember = (data: any) => {
  return http.request<Result>("put", "/trade/sccs-member/update", {
    data
  });
};

/**
 * 获得协作SCCS的角色列表
 * @param params
 * @returns
 */
export const getSccsCoopRoleList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-member/role-list-for-coop-sccs",
    {
      params
    }
  );
};

/**
 * 获取sccs下的里程碑列表
 * @param params
 * @returns
 */
export const getSccsMileStoneList = (params: any) => {
  return http.request<Result>("get", "/trade/milestone/list", {
    params
  });
};

/**
 * 获得贸易端sccs邀请协作团队记录分页
 * @param params
 * @returns
 */
export const getSccsCoopTeamListPage = (params: any) => {
  return http.request<Result>("get", "/trade/sccs-coop-team-invite/page", {
    params
  });
};

/**
 * 获得未加入的团队成员列表
 * @param params
 * @returns
 */
export const getCoopSccsNotAddList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-coop-team-invite/coop-team-not-add-list",
    {
      params
    }
  );
};

/**
 * 获得未加入的团队成员列表
 * @param params
 * @returns
 */
export const createCoopTeamSccs = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-coop-team-invite/create", {
    data
  });
};

/**
 * 设置权限
 * @param params
 * @returns
 */
export const updateCoopTeamSccs = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-coop-team-invite/set", {
    data
  });
};

/**
 * 获取标签列表
 * @param params
 * @returns
 */
export const getSccsTradeLableList = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/sccs-label/getLabelList", {
    params
  });
};

/**
 * 获取标签下拉组件
 * @param params
 * @returns
 */
export const getSccsLableAllFields = (params: {
  sccsId: string;
  fieldType?: string;
  milestoneId?: string;
  workOrder: boolean;
}) => {
  return http.request<Result>("get", "/trade/sccs-label/getAllField", {
    params
  });
};

/**
 * 添加标签设置
 * @param params
 * @returns
 */
export const addSccsLabel = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-label/addLabel", {
    data
  });
};

/**
 * 删除标签设置
 * @param params
 * @returns
 */
export const deleteSccsLabel = (params: { id: string }) => {
  return http.request<Result>("delete", "/trade/sccs-label/deleteLabel", {
    params
  });
};

/**
 * 获取标签详情
 * @param params
 * @returns
 */
export const getSccsLableDetail = (params: { labelId: string }) => {
  return http.request<Result>("get", "/trade/sccs-label/detail", {
    params
  });
};

/**
 * 获取控件属性
 * @param params
 * @returns
 */
export const getWidgetItem = (params: {
  widgetId: string;
  formPubId: string;
}) => {
  return http.request<Result>("get", "/trade/widget/getItem", {
    params
  });
};

/**
 * 编辑标签设置
 * @param data
 * @returns
 */
export const editSccsLabel = (data: any) => {
  return http.request<Result>("put", "/trade/sccs-label/editLabel", {
    data
  });
};

/**
 * 获取sccs下面的主表单标识字段
 * @param data
 * @returns
 */
export const getMainFormFields = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/sccs/main-form-mark-filed", {
    params
  });
};

/**
 * 创建sccs订单标识
 * @param params
 * @returns
 */
export const saveOrderMarkWidget = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-order-mark-widget/save", {
    data
  });
};

/**
 * 获得sccs订单标识列表
 * @param params
 * @returns
 */
export const getOrderMarkWidget = (params: { sccsId: string }) => {
  return http.request<Result>("get", "/trade/sccs-order-mark-widget/list", {
    params
  });
};

/**
 * 获得sccs订单作废原因列表
 * @param params
 * @returns
 */
export const getSccsOrderCancelReason = (params: {
  sccsId: string;
  orderCancelReason: string;
}) => {
  return http.request<Result>("get", "/trade/sccs-order-cancel-reason/list", {
    params
  });
};

/**
 * 创建sccs订单标识
 * @param params
 * @returns
 */
export const createSccsOrderCancelReason = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/sccs-order-cancel-reason/create",
    {
      data
    }
  );
};

export const updateSccsOrderCancelReason = (data: any) => {
  return http.request<Result>("put", "/trade/sccs-order-cancel-reason/update", {
    data
  });
};

export const deleteSccsOrderCancelReason = (params: {
  id: string;
  sccsId: string;
}) => {
  return http.request<Result>(
    "delete",
    "/trade/sccs-order-cancel-reason/delete",
    {
      params
    }
  );
};

/**
 * 获取sccs协作角色分页列表
 * @param params
 * @returns
 */
export const getSccsCoopRoles = (params: any) => {
  return http.request<Result>("get", "/trade/sccs-coop-role/page", {
    params
  });
};

/**
 * 获取sccs协作角色分页列表
 * @param params
 * @returns
 */
export const getSccsCoopRolePermissionTrees = (params: any) => {
  return http.request<Result>("get", "/trade/sccs-coop-role/permission-tree", {
    params
  });
};

/**
 * 取sccs协作角色详情
 * @param params
 * @returns
 */
export const getSccsCoopRoleDetails = (params: any) => {
  return http.request<Result>("get", "/trade/sccs-coop-role/get", {
    params
  });
};

/**
 * 创建贸易端sccs协作角色
 * @param params
 * @returns
 */
export const createSccsCoopRole = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-coop-role/create", {
    data
  });
};

/**
 * 修改贸易端sccs协作角色
 * @param params
 * @returns
 */
export const updateSccsCoopRole = (data: any) => {
  return http.request<Result>("post", "/trade/sccs-coop-role/update", {
    data
  });
};

/**
 * 删除贸易端sccs协作角色
 * @param params
 * @returns
 */
export const deleteSccsCoopRole = (params: { id: string; sccsId: string }) => {
  return http.request<Result>("delete", "/trade/sccs-coop-role/delete", {
    params
  });
};

/**
 * sccs协作团队处修改协作方角色内成员
 * @returns
 */
export const updateSccsCoopTeamRoleInCoopTeam = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/sccs-coop-team-role/update-sccs-coop-team-role",
    {
      data
    }
  );
};

/**
 * sccs协作团队处获取协作方角色分页
 * @param params
 * @returns
 */
export const getSccsCoopTeamRoleInCoopTeam = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/sccs-coop-team-role/sccs-coop-team-role-page",
    {
      params
    }
  );
};

/**
 * 删除数据围栏
 * @returns
 */
export const deleteSccsCoopTeamInvite = (params: {
  id: string;
  sccsId: string;
}) => {
  return http.request<Result>("delete", "/trade/sccs-coop-team-invite/delete", {
    params
  });
};

/**
 * 获取sccs协作方角色列表
 * @param params
 * @returns
 */
export const getSccsCoopTeamRoleList = (params: any) => {
  return http.request<Result>("get", "/trade/sccs-coop-team-role/list", {
    params
  });
};

/**
 * 获取sccs标签列表
 * @param params
 * @returns
 */
export const getSccsDataFenceLabelList = (params: any) => {
  return http.request<Result>("get", "/trade/sccs-data-fence/label-list", {
    params
  });
};

/**
 * 首页取常用的订单视图列表
 * @param params
 * @returns
 */
export const getCommonOrderViewList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/orderView/get-common-order-view-list",
    {
      params
    }
  );
};
