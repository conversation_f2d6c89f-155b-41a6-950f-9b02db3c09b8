<template>
  <div
    v-loading="loading"
    class="upload-main"
    style="pointer-events: auto; cursor: pointer"
    @paste.stop="handlePasteUpload"
  >
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      class="avatar-uploader"
      :class="{
        'avatar-uploader-hide':
          widgetConfigure.props &&
          widgetConfigure.props.limit &&
          fileList.length >= widgetConfigure.props.limit
      }"
      :action="uploadRouter"
      :data="uploadParams"
      list-type="picture-card"
      accept=".bmp,.gif,.jpeg,.jpg,.pjpeg,.png,.tiff,.webp,.jfif"
      drag
      :limit="
        widgetConfigure.props && widgetConfigure.props.limit
          ? widgetConfigure.props.limit
          : null
      "
      :multiple="true"
      :on-success="handleUploadSuccess"
      :on-remove="handleRemove"
      :on-preview="handlePictureCardPreview"
      :on-exceed="handleFileExceed"
      :before-upload="beforeUpload"
      style="pointer-events: auto; cursor: pointer"
    >
      <template #trigger>
        <div class="upload-tip-body">
          <div class="upload-tip-icon">
            <i class="iconfont link-add" />
          </div>
          <div class="upload-tip-tip">
            {{ t("trade_component_uploadtip") }}
          </div>
        </div>
      </template>
      <template #tip>
        <div class="el-upload__tip text-red">
          <span v-if="widgetConfigure.props && widgetConfigure.props.limit">
            {{
              t("trade_component_upload_limit", {
                limit: widgetConfigure.props.limit
              })
            }}
          </span>
          <span
            v-if="
              widgetConfigure.props &&
              widgetConfigure.props.limit &&
              widgetConfigure.props.maxFileSize
            "
          >
            ，
          </span>
          <span
            v-if="widgetConfigure.props && widgetConfigure.props.maxFileSize"
          >
            {{
              t("trade_component_upload_max_image_size", {
                size: widgetConfigure.props.maxFileSize
              })
            }}
          </span>
        </div>
      </template>
    </el-upload>
  </div>
  <LkDialog
    ref="widgetDialogRef"
    class="widget-dialog"
    append-to-body
    :no-footer="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <template #default>
      <el-image :src="widgetUrl" />
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, inject, watch } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage, ElUpload, ElLoading } from "element-plus";
import type { UploadProps } from "element-plus";
import { emitter } from "@/utils/mitt";
import LkDialog from "@/components/lkDialog/src/index.vue";
import { cloneDeep, isEqual } from "@pureadmin/utils";

const vLoading = ElLoading.directive;

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  modelValue: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();
const route = useRoute();
const uploadRef = ref<HTMLElement | any>(null);
const loading = ref<boolean>(false);
const fileList = ref<any[]>([]);
const widgetDialogRef = ref<HTMLElement | any>(null);
const widgetUrl = ref<string>("");
const uploadFileNumber = ref<number>(0);
const uploadRouter = inject("uploadFileURL", null);
const uploadParams = rawFile => {
  const fileSize = rawFile.size / 1024 / 1024;
  return {
    ossFileType: "TRADE_SCCS",
    sccsId: route.query.sccsId,
    size: Math.floor(fileSize)
  };
};

const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);

const emit = defineEmits(["handleUpdateWidgetData"]);

const beforeUpload: UploadProps["beforeUpload"] = rawFile => {
  loading.value = true;
  emitter.emit("widgetUploadLoading", true);

  if (props.widgetConfigure.props) {
    if (
      props.widgetConfigure.props &&
      props.widgetConfigure.props.limit < fileList.value.length + 1
    ) {
      emitter.emit("widgetUploadLoading", false);
      ElMessage.error(
        `最多仅允许上传${props.widgetConfigure.props.limit}个文件`
      );
      loading.value = false;
      return false;
    }
    const fileType = rawFile.name.split(".").slice(-1)[0].toLowerCase();
    if (
      ![
        "bmp",
        "gif",
        "jpeg",
        "jpg",
        "pjpeg",
        "png",
        "tiff",
        "webp",
        "jfif"
      ].includes(fileType)
    ) {
      emitter.emit("widgetUploadLoading", false);
      ElMessage.error(
        `上传文件仅支持格式：.bmp,.gif,.jpeg,.jpg,.pjpeg,.png,.tiff,.webp,.jfif`
      );
      loading.value = false;
      return false;
    }
    if (rawFile.size / 1024 / 1024 > props.widgetConfigure.props.maxFileSize) {
      emitter.emit("widgetUploadLoading", false);
      ElMessage.error(
        `${props.widgetConfigure.title}大小不能超过${props.widgetConfigure.props.maxFileSize}MB!`
      );
      loading.value = false;
      return false;
    }
  }

  return true;
};

const handleUploadSuccess: UploadProps["onSuccess"] = (uploadFile, File) => {
  loading.value = false;
  const index = fileList.value.findIndex(file => file.uid === File.uid);
  const oldFile = fileList.value[index];
  const fileSize = Math.floor(oldFile.size / 1024 / 1024);

  fileList.value.splice(index, 1, {
    url: uploadFile.data,
    name: decodeURIComponent(uploadFile.data)
      .split("?")[0]
      .split("/")
      .slice(-1)[0],
    size: fileSize
  });
  emitter.emit("widgetUploadLoading", false);
  handleWigetFormData();
};

const handlePictureCardPreview: UploadProps["onPreview"] = uploadFile => {
  widgetUrl.value = uploadFile.url;
  widgetDialogRef.value.open();
};

const handleFileExceed: UploadProps["onExceed"] = uploadFileList => {
  if (
    props.widgetConfigure.props &&
    props.widgetConfigure.props.limit <
      fileList.value.length + uploadFileList.length
  ) {
    ElMessage.error(`最多仅允许上传${props.widgetConfigure.props.limit}个文件`);
    return false;
  }
};

const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  fileList.value = uploadFiles;
  handleWigetFormData();
};

const handleClearFiles = () => {
  uploadRef.value.abort();
  uploadRef.value.clearFiles();
};

const handleWigetFormData = (): void => {
  let fileValue = "";
  fileList.value.forEach(file => {
    fileValue += `,${file.name}`;
  });
  if (props.widgetRowIndex === -1) {
    handleWidgetFormsValue(
      props.widgetConfigure._fc_id,
      {
        obj: fileList.value,
        label: fileValue.substr(1),
        widgetId: props.widgetConfigure._fc_id,
        widgetType: props.widgetConfigure.type,
        $rowIndex: props.widgetRowIndex
      },
      fileList.value
    );
  } else {
    emit("handleUpdateWidgetData", "DrImagesUpload", {
      obj: fileList.value,
      label: fileValue.substr(1),
      widgetId: props.widgetConfigure._fc_id,
      $rowIndex: props.widgetRowIndex
    });
  }
};

const handlePasteUpload = event => {
  const items = (event.clipboardData || window.Clipboard).items;
  event.preventDefault();
  event.returnValue = false;

  const fileCount = items.length + fileList.value.length;
  if (
    props.widgetConfigure.props &&
    props.widgetConfigure.props.limit < fileCount
  ) {
    emitter.emit("widgetUploadLoading", false);
    ElMessage.error(`最多仅允许上传${props.widgetConfigure.props.limit}张图片`);
    return false;
  }
  for (let item of items) {
    const file = item.getAsFile();
    const fileType = file.name.split(".").slice(-1)[0].toLowerCase();
    if (
      ![
        "bmp",
        "gif",
        "jpeg",
        "jpg",
        "pjpeg",
        "png",
        "tiff",
        "webp",
        "jfif"
      ].includes(fileType)
    ) {
      emitter.emit("widgetUploadLoading", false);
      ElMessage.error(
        `上传文件仅支持格式：.bmp,.gif,.jpeg,.jpg,.pjpeg,.png,.tiff,.webp,.jfif`
      );
      return false;
    }
    if (
      props.widgetConfigure.props &&
      props.widgetConfigure.props.maxFileSize &&
      file.size / 1024 / 1024 > props.widgetConfigure.props.maxFileSize
    ) {
      emitter.emit("widgetUploadLoading", false);
      ElMessage.error(
        `${props.widgetConfigure.title}大小不能超过${props.widgetConfigure.props.maxFileSize}MB!`
      );
      return false;
    }
  }

  for (let i = 0, len = items.length; i < len; i++) {
    if (items[i].kind !== "file") {
      return;
    }
    const file = items[i].getAsFile();
    uploadRef.value?.handleStart(file);
    uploadRef.value?.submit();
  }
};

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    const widgetFileList = newVal;
    if (widgetFileList instanceof Array && widgetFileList.length > 0) {
      fileList.value = cloneDeep(widgetFileList);
    } else {
      fileList.value = [];
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  handleClearFiles
});
</script>
<style lang="scss" scoped>
::v-deep(.el-form-item__content) {
  display: block;
  width: 100%;
}

.text-red {
  font-size: 12px;
  line-height: 17px;
  color: #bfbfbf;
  text-align: left;
}

.upload-main {
  width: 100%;
  padding: 9px;
  border: 1px dashed #bfbfbf;
  border-radius: 4px;

  &:hover {
    border-color: #409eff;
  }

  ::v-deep(.el-upload-list) {
    .el-upload {
      width: 116px !important;
      height: 122px !important;
      border: 0 none;

      .el-upload-dragger {
        padding: 24px 10px;
      }
    }

    .el-upload-list__item {
      width: 116px !important;
      height: 122px !important;
    }

    .el-progress {
      width: 110px !important;

      .el-progress-circle {
        width: 110px !important;
        height: 110px !important;
      }
    }
  }
}

.upload-tip-body {
  text-align: center;

  .iconfont {
    font-size: 24px;
    color: #808080;
  }

  .upload-tip-tip {
    width: 80%;
    margin: 0 auto;
    font-size: 12px;
    line-height: 17px;
    color: #bfbfbf;
    text-align: center;
  }
}

::v-deep(.avatar-uploader-hide) {
  .el-upload--picture-card {
    display: none;
  }
}
</style>
