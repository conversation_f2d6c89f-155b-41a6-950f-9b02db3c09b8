<template>
  <div class="email-receive-table-container">
    <div class="email-receive-table-body">
      <div class="email-receive-table-list">
        <div
          v-if="receiveTableData.length > 0"
          class="email-receive-table-search-body"
        >
          <el-row class="email-receive-table-theader">
            <el-col :span="4" class="email-receive-table-th">
              {{ t("trade_email_subject2") }}
            </el-col>
            <el-col :span="3" class="email-receive-table-th">
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                {{ t("trade_order_identification") }}
              </ReText>
            </el-col>
            <el-col :span="3" class="email-receive-table-th">
              {{ t("trade_home_sccsName") }}
            </el-col>
            <el-col :span="3" class="email-receive-table-th">
              {{ t("trade_template_milestoneName") }}
            </el-col>
            <el-col :span="3" class="email-receive-table-th">
              {{ t("trade_common_creator") }}
            </el-col>
            <el-col :span="2" class="email-receive-table-th">
              {{ t("trade_common_endTimeText") }}
            </el-col>
            <el-col :span="2" class="email-receive-table-th">
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                {{ t("trade_common_creatorTime") }}
              </ReText>
            </el-col>
            <el-col :span="2" class="email-receive-table-th">
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                {{ t("trade_common_updateTime") }}
              </ReText>
            </el-col>
            <el-col :span="2" class="email-receive-table-th">
              {{ t("trade_common_operate") }}
            </el-col>
          </el-row>
          <div class="email-receive-list-wrapper" style="overflow: auto">
            <ul
              v-infinite-scroll="loadReceiveEmailList"
              class="email-receive-table-tbody-container"
              :infinite-scroll-disabled="disabled"
            >
              <li
                v-for="item in receiveTableData"
                :key="item.id"
                class="email-receive-table-tbody"
              >
                <el-row style="align-items: center; width: 100%">
                  <el-col :span="4" class="email-receive-table-tr">
                    <ReText type="info" :tippyProps="{ delay: 50000 }">
                      {{ item.subject }}
                    </ReText>
                  </el-col>
                  <el-col :span="3" class="email-receive-table-tr">
                    <ReText type="info" :tippyProps="{ delay: 50000 }">
                      {{ item.orderMark || item.serialNumber }}
                    </ReText>
                  </el-col>
                  <el-col :span="3" class="email-receive-table-tr default">
                    <ReText type="info" :tippyProps="{ delay: 50000 }">
                      {{ item.sccsName }}
                    </ReText>
                  </el-col>
                  <el-col :span="3" class="email-receive-table-tr">
                    <ReText type="info" :tippyProps="{ delay: 50000 }">
                      {{ item.milestoneName }}
                    </ReText>
                  </el-col>
                  <el-col :span="3" class="email-receive-table-tr">
                    <div>
                      <ReText type="info" :tippyProps="{ delay: 50000 }">
                        {{ item.creatorName }}
                      </ReText>
                    </div>
                    <div>
                      <ReText type="info" :tippyProps="{ delay: 50000 }">
                        <span class="email-receive-table-tr-assistant-text">
                          {{ item.creatorEmail }}
                        </span>
                      </ReText>
                    </div>
                  </el-col>
                  <el-col :span="2" class="email-receive-table-tr">
                    <span class="email-receive-table-tr-assistant-text">
                      {{ getTimeLineStampFormat(item.plannedEndDate) }}
                    </span>
                  </el-col>
                  <el-col :span="2" class="email-receive-table-tr">
                    <span class="email-receive-table-tr-assistant-text">
                      {{ getTimeLineStampFormat(item.createTime) }}
                    </span>
                  </el-col>
                  <el-col :span="2" class="email-receive-table-tr">
                    <span class="email-receive-table-tr-assistant-text">
                      {{ getTimeLineStampFormat(item.submitTime) }}
                    </span>
                  </el-col>
                  <el-col :span="2" class="email-receive-table-tr">
                    <span
                      v-if="item.workOrderCoopEmailStatus === 'COMPLETE'"
                      class="email-receive-btn"
                      @click="handleReadEmail(item)"
                    >
                      <i class="iconfont link-display" />
                      {{ t("trade_common_view") }}
                    </span>
                    <span
                      v-else
                      class="email-receive-btn"
                      @click="handleEditEmail(item.id)"
                    >
                      <i class="iconfont link-edit" />
                      {{ t("trade_common_handle") }}
                    </span>
                  </el-col>
                </el-row>
                <div
                  v-if="item.workOrderCoopEmailStatus === 'COMPLETE'"
                  :span="2"
                  class="email-receive-tag"
                >
                  {{ t("trade_order_stateCompleted") }}
                  <span>{{ getTimeLineStampFormat(item.completeTime) }}</span>
                </div>
              </li>
              <div
                v-if="receiveTableData.length > 0 && loading"
                class="list-tip loading"
              >
                {{ `${t("trade_common_loadingText")}...` }}
              </div>
              <div
                v-if="receiveTableData.length > 0 && noMore"
                class="list-tip"
              >
                {{ t("trade_no_more_tip") }}
              </div>
            </ul>
          </div>
        </div>
        <el-empty
          v-else-if="!receiveTableData.length && !loading"
          :description="t('trade_no_task')"
          :image-size="342"
          :image="taskNoImage"
        />
      </div>
    </div>
  </div>
  <LkDialog
    ref="WidgetDialogRef"
    :show-close="false"
    width="80%"
    style="padding: 0"
    destroy-on-close
    class="lk-receive-email-dialog lk-maximum-dialog"
    :no-footer="true"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <template #header="{ close }">
      <div class="work-order-flex-header">
        <div class="work-order-tabs-flex-left">
          <div class="work-order-tabs-flex-left-title">
            {{ worderName }}
          </div>
        </div>
        <div class="work-order-tabs-flex-right">
          <div class="work-order-header-col">
            <div
              class="work-order-header-small-button work-order-header-default-button"
            >
              <i class="iconfont link-close" @click="close" />
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <el-scrollbar style="height: 100%; padding: 10px">
        <EmailDetail ref="EmailDetailRef" />
      </el-scrollbar>
    </template>
  </LkDialog>
  <WriteEmailCoopDialog ref="WriteEmailCoopDialogRef" @refresh="update" />
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";
import dayjs from "dayjs";
import taskNoImage from "@/assets/images/taskCenter/task-center-no-task.png";
import LkDialog from "@/components/lkDialog";
import EmailDetail from "@/views/emailCoop/emailDetail.vue";
import WriteEmailCoopDialog from "@/views/taskCenter/components/WriteEmailCoopDialog.vue";

import { nextTick, ref } from "vue";
const { t } = useI18n();
const props = defineProps({
  receiveTableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  noMore: {
    type: Boolean,
    default: false
  }
});
const WidgetDialogRef = ref<HTMLElement | any>(null);
const EmailDetailRef = ref<HTMLElement | any>(null);
const WriteEmailCoopDialogRef = ref<HTMLElement | any>(null);
const worderName = ref("");
const emit = defineEmits(["loadReceiveEmailList", "update", "editEmail"]);
const getTimeLineStampFormat = (dateTime: any) => {
  if (dateTime) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "";
  }
};
const handleEditEmail = (id: string) => {
  WriteEmailCoopDialogRef.value.open(id);
};
const handleReadEmail = (item: any) => {
  worderName.value = item.subject;
  WidgetDialogRef.value.open();
  nextTick(() => {
    EmailDetailRef.value.initRenderTemplate(item.id);
  });
};
const loadReceiveEmailList = () => {
  if (props.loading || props.noMore) return;
  emit("loadReceiveEmailList");
};
const handleConfirm = () => {
  WidgetDialogRef.value.close();
};
const handleClose = (): void => {
  WidgetDialogRef.value.close();
};
const update = () => {
  emit("update");
};
</script>
<style lang="scss">
.email-receive-table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .email-receive-table-body {
    height: 100%;

    .email-receive-table-header {
      display: flex;
      align-items: center;

      .email-receive-table-left {
        flex: 1;

        .email-receive-table-title {
          display: flex;
          align-items: center;
          margin-bottom: 17px;

          .email-receive-table-title-name {
            font-size: 18px;
            font-weight: bold;
            color: #262626;
            text-align: left;
          }

          .email-receive-table-title-email {
            margin-left: 8px;
            font-size: 14px;
            line-height: 16px;
            color: #797979;
            text-align: left;
          }
        }

        .email-receive-table-tip {
          font-size: 14px;
          background: linear-gradient(90deg, #4851e5, #935ad8);
          background-clip: text; /* 将渐变应用到文字上 */
          -webkit-text-fill-color: transparent; /* 设置文字颜色为透明，显示渐变效果 */

          .el-image {
            width: 16px;
            height: 16px;
            vertical-align: middle;
          }
        }
      }

      .email-receive-table-right {
        .el-image {
          width: 179px;
          vertical-align: middle;
        }
      }
    }

    .email-receive-table-list {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      height: 100%;

      .email-receive-table-search-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 40px 0 24px;

        .email-receive-table-search-header-left {
          flex: 1;

          .el-segmented {
            padding: 4px;
            background: #fff;
            border-radius: 4px;

            ::v-deep(.el-segmented__item) {
              .el-segmented__item-label {
                font-size: 14px;
                font-weight: normal;
                line-height: 20px;
              }
            }

            ::v-deep(.el-segmented__item.is-selected) {
              color: #0070d2;
              background: #ddefff;
            }

            ::v-deep(.el-segmented__item-selected) {
              color: #0070d2;
              background: #ddefff;
            }
          }
        }

        .email-receive-table-search-header-right {
          flex: 1;
          text-align: right;
        }
      }

      .email-receive-table-search-body {
        .email-receive-table-theader {
          display: flex;
          align-items: center;
          width: 100%;
          height: 40px;
          padding: 0 20px;

          .email-receive-table-th {
            height: 100%;
            padding-left: 20px;
            font-size: 14px;
            font-weight: bolder;
            line-height: 40px;
            color: #797979;

            .el-text.is-truncated {
              font-size: 14px;
              font-weight: bolder;
              color: #797979;
            }
          }
        }

        .email-receive-list-wrapper {
          .email-receive-table-tbody-container {
            height: calc(100vh - 196px);
            padding: 20px 14px 20px 20px;
            margin: 0;
            overflow-y: auto;
            list-style: none;

            .email-receive-table-tbody {
              position: relative;
              display: flex;
              align-items: center;
              height: 84px;
              margin-bottom: 16px;
              text-align: center;
              background: #fff;
              border-radius: 4px;
              box-shadow: 0 3px 13px 0 rgb(0 0 0 / 11%);

              .email-receive-table-tr {
                flex: 1;
                padding-left: 20px;
                font-size: 14px;
                font-weight: 500;
                color: #797979;
                text-align: left;

                &.default {
                  .el-text.is-truncated {
                    color: #797979;
                  }
                }

                .el-text.is-truncated {
                  font-size: 14px;
                  color: #262626;
                }

                .email-receive-table-tr-assistant-text {
                  width: 100%;
                  font-size: 12px;
                  line-height: 17px;
                  color: #797979;
                }

                .email-receive-btn {
                  font-size: 14px;
                  font-weight: normal;
                  line-height: 20px;
                  color: #0070d2;
                  text-align: left;
                  cursor: pointer;

                  .iconfont {
                    font-size: 13px;
                    vertical-align: middle;
                  }
                }
              }

              .email-receive-tag {
                position: absolute;
                top: 0;
                right: 0;
                height: 20px;
                padding: 0 8px;
                font-size: 12px;
                line-height: 20px;
                color: #32a645;
                text-align: center;
                background: #d9f4f0;
                border-radius: 4px 4px 0;
              }
            }

            .list-tip {
              width: 100%;
              margin-top: 20px;
              font-size: 14px;
              color: #bfbfbf;
              text-align: center;
            }
          }

          .email-receive-table-tbody-container::-webkit-scrollbar-track {
            background-color: #f5f6fa;
          }
        }

        .email-receive-tag {
          position: absolute;
        }
      }
    }
  }
}

.lk-receive-email-dialog {
  .el-dialog__header {
    padding: 0 !important;
    background: #fff !important;
    border-bottom: 1px solid #e8e8e8;

    .dialog-header {
      display: flex;
      align-items: center;
      height: 60px;
      padding-top: 6px;

      .dialog-header-left {
        flex: 4;
        height: 100%;
        padding-left: 4px;

        .dialog-header-tabs {
          display: flex;
          align-items: center;
          height: 100%;
          border-radius: 4px 0 0;

          .dialog-header-tabs-item {
            align-items: center;
            height: 100%;
            padding: 0 16px;
            line-height: 52px;
            border: 1px solid #e5e7ee;
            border-right: 0 none;

            &.active {
              border-bottom: 0 none;

              .dialog-header-tabs-item-text {
                color: #0070d2;
              }
            }

            &:first-child {
              border-radius: 4px 0 0;
            }

            &:last-child {
              border-right: 1px solid #e5e7ee;
              border-radius: 0 4px 0 0;
            }

            .dialog-header-tabs-item-text {
              margin-right: 7px;
              font-size: 14px;
              color: #262626;
            }

            .dialog-header-tabs-item-icon {
              display: inline-block;
              width: 22px;
              height: 22px;
              margin-right: 4px;
              line-height: 22px;
              text-align: center;
              cursor: pointer;
              border-radius: 2px;

              &:hover {
                background: #e5e5e5;
              }

              .iconfont {
                font-size: 12px;
                color: #808080;
              }
            }
          }
        }
      }

      .dialog-header-right {
        flex: 1;
        text-align: right;

        .dialog-header-box {
          height: 30px;
          padding: 7px 10px;
          font-size: 14px !important;
          color: #595959 !important;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: #e5e5e5;
          }

          .iconfont {
            margin-right: 3px;
            font-size: 12px;
          }
        }

        .dialog-header-info-box {
          margin: 0 20px;

          .dialog-header-icon-box {
            display: inline-block;
            width: 32px;
            height: 30px;
            padding: 4px 0;
            text-align: center;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
              background: #e5e5e5;
            }

            .iconTip {
              font-size: 16px;
              color: #808080 !important;
            }
          }

          &.active {
            background: #e1edff;
          }
        }

        .dialog-header-close-icon {
          position: relative;
          padding: 0 20px;

          .dialog-header-icon-tip {
            // padding: 0 20px;
            box-sizing: border-box;
            display: inline-block;
            height: 30px !important;
            border-radius: 4px;

            .iconfont {
              font-size: 13px;
              color: #808080;
              cursor: pointer;
            }
            // &:hover {
            //   background: #e5e5e5;
            // }
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              display: inline-block;
              width: 1px;
              height: 16px;
              content: "";
              background: #e5e5e5;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
    }
  }

  .el-dialog__body {
    height: calc(100% - 55px);
    background: #e9eaec;

    .dialog-content-flex {
      display: flex;
      height: 100%;
      padding: 10px;
      background: #f6f6f6;

      .dialog-content-flex-left {
        width: 358px;
        padding: 30px 14px;
        margin-right: 13px;
        background: #fff;
        border-radius: 4px 4px 0 0;

        .dialog-content-form {
          .dialog-content-form-row {
            margin-bottom: 25px;

            .dialog-content-title {
              display: flex;
              align-items: center;

              .dialog-content-text {
                margin-bottom: 9px;
                font-size: 14px;
                font-weight: bolder;
                line-height: 16px;
                color: #262626;
                text-align: left;

                &::before {
                  display: inline-block;
                  margin-top: 6px;
                  margin-right: 6px;
                  font-size: 22px;
                  font-weight: bold;
                  line-height: 16px;
                  color: #e61b1b;
                  vertical-align: middle;
                  content: "*";
                }
              }

              .dialog-content-coordination {
                flex: 1;
                margin-right: 3px;
                margin-bottom: 9px;
                font-size: 12px;
                color: #f6974f;
                text-align: right;

                .iconfont {
                  font-size: 13px;
                  color: #808080;
                  cursor: pointer;
                }
              }
            }

            .work-order-personnel-control-operation {
              display: block;
              align-items: center;
              width: 100%;

              .work-order-personnel-control-btn {
                cursor: pointer;

                .iconfont {
                  color: #0070d2;
                }

                .work-order-personnel-control-text {
                  margin-left: 5px;
                  font-size: 13px;
                  font-weight: bold;
                  color: #0070d2;
                }
              }

              .work-order-personnel-control-group {
                .work-order-personnel-control-group-col {
                  display: inline-flex;
                  align-items: center;
                  max-width: 270px;
                  padding: 3px 4px;
                  margin-right: 3px;
                  margin-bottom: 7px;
                  background: #ebf5fb;
                  border-radius: 3px;

                  .work-order-personnel-control-group-name {
                    max-width: 230px;
                    margin: 0 4px;
                    overflow: hidden;
                    font-size: 12px;
                    font-weight: bold;
                    color: #000;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }

                  .iconfont {
                    font-size: 12px;
                    color: #babec6;
                  }
                }

                .iconfont {
                  margin-left: 7px;
                  font-size: 14px;
                  color: #0070d2;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }

      .dialog-content-flex-right {
        flex: 1;

        .dialog-content-flex-right-header {
          margin: 7px 0 12px;
          font-size: 12px;
          font-weight: bolder;
          line-height: 17px;
          color: #262626;
          text-align: left;
        }
      }
    }
  }

  .el-dialog__footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 45px;
    padding: 0 !important;
    padding-right: 10px !important;
  }

  .work-order-flex-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 54px;
    padding-left: 25px;

    .work-order-tabs-flex-left {
      display: flex;
      flex: 1;
      align-items: center;
      max-width: 70%;

      .work-order-tabs-flex-left-title {
        max-width: calc(100% - 35px);
        overflow: hidden;
        font-size: 18px;
        font-weight: bolder;
        color: #202020;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .work-order-tabs-flex-tag {
        padding: 2px 4px;
        margin-left: 5px;
        font-size: 12px;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        background: #fa8d0a;
        border: 1px solid #fff;
        border-radius: 3px;
      }
    }

    .work-order-tabs-flex-right {
      display: flex;
      align-items: center;

      .work-order-header-col {
        position: relative;
        display: flex;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        &:last-child {
          &::after {
            display: none;
            content: "";
          }
        }

        &.work-order-header-col-next {
          margin: 0 9px;
        }

        .work-order-header-button {
          padding: 3px 10px;
          margin-right: 12px;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: #e5e5e5;
          }

          .iconfont {
            font-size: 13px;
            color: #595959 !important;

            &:first-child {
              margin-right: 6px;
            }

            &:last-child {
              margin-left: 6px;
            }
          }

          .work-order-header-text {
            font-size: 14px;
            line-height: 20px;
            color: #595959 !important;
            text-align: left;
          }
        }

        .work-order-header-small-button {
          display: inline-block;
          width: 30px;
          height: 30px;
          margin-right: 8px;
          line-height: 30px;
          text-align: center;
          border-radius: 4px;

          &.work-order-header-default-button {
            width: 42px;

            &:hover {
              background: transparent;
            }

            .iconfont {
              font-size: 14px;
            }
          }

          &:hover {
            background: #e5e5e5;
          }

          &.active {
            background: #e1edff;

            .iconfont {
              color: #0070d2;
            }
          }

          .iconfont {
            font-size: 16px;
            color: #808080;
            cursor: pointer;

            &.font18 {
              font-size: 18px;
            }
          }
        }
      }
    }
  }

  .work-order-container {
    position: relative;
    display: flex;
    height: 100%;
    overflow: hidden;

    .work-order-container-left {
      position: relative;
      width: 59%;
      background: #f5f6fa;
      transition: width linear 0.2s;

      .anchor-icon {
        top: 48px;
      }

      .anchor-list-container {
        top: 48px;
      }

      .work-order-container-top {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: calc(100% - 60px);
        padding: 0 10px;

        .work-order-container-top-header {
          display: flex;
          align-items: center;
          width: 100%;
          padding: 12px 27px;
          margin-top: 9px;

          .work-order-header-col {
            display: inline-flex;
            align-items: center;
            margin-right: 35px;

            .work-order-header-col-span {
              padding-left: 14px;
              font-size: 14px;
              line-height: 20px;
              color: #797979;
              text-align: left;

              &::after {
                content: "：";
              }
            }

            .work-order-header-col-main {
              display: flex;
              align-items: center;

              .work-order-header-create-time {
                margin-left: 8px;
                font-size: 14px;
                color: #262626;
              }

              .work-order-header-create-status-text {
                margin-left: 8px;
                font-size: 12px;
                font-weight: bold;
                color: #e62412;
              }
            }
          }
        }

        .work-order-container-top-main {
          flex: 1;
          height: calc(100% - 145px);
        }
      }

      .work-order-container-bottom {
        height: 60px;
        padding: 14px 18px;
        text-align: right;
        background: #fff;
      }
    }

    .work-order-container-right {
      position: absolute;
      width: 41%;
      height: 100%;
      background: #fff;
      border-left: 1px solid #e8e8e8;
      transition: right linear 0.2s;

      .work-order-container-dynamics-card {
        height: 100%;
      }
    }
  }

  .work-order-tabs-container {
    position: absolute;
    top: 54px;
    left: 0;
    z-index: 3333;
    box-sizing: border-box;
    width: 100%;
    height: 480px;
    padding: 33px 43px;
    background: #fff;
    border-top: 1px solid #e5e5e5;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 8px 13px 0 rgb(0 0 0 / 10%);

    &.nopadding {
      padding: 16px 26px !important;
    }
  }
}
</style>
