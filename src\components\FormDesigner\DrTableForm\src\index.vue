<template>
  <div ref="tableContainerRef" class="sub-table-container">
    <div class="sub-table-top">
      <span class="sub-table-btn" @click="handleAddTableRow">
        <i class="iconfont link-add" />{{ subTableAddBtnText }}
      </span>
      <span
        v-if="quoteTableColumn"
        class="sub-table-btn"
        @click="handleAddSubTableRelation(false)"
      >
        <i class="iconfont link-quote-third-party" />
        {{ quoteTableColumnRelateBtnText }}
      </span>
    </div>
    <div
      :class="{ 'sub-table-main': tableFormData.length === 0 }"
      :style="{
        'z-index': isHovered || isFocused.focused ? dtTableZIndex : 'inherit'
      }"
    >
      <ListTable
        ref="tableInstanceRef"
        class="tableListInstance lk-grid-table lk-dr-table"
        :options="option"
        :data-id="widgetConfigure._fc_id"
        :style="{
          'max-height': `${maxHeight}px`,
          height: `${subTableHeight}px`
        }"
      />
    </div>
  </div>

  <el-dialog
    v-model="subTableRelationVisible"
    class="lk-middle-dialog"
    :title="subTablePropData.widgetConfigure?.title"
    width="620"
    append-to-body
    align-center
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <LKSubTable
      ref="subTableRef"
      :widgetConfig="relateColumns"
      :widgetData="tableRelationData"
      tableFirstType="rowCheckbox"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="subTableRelationVisible = false">
          {{ t("trade_common_cancel") }}
        </el-button>
        <el-button type="primary" @click="handleAddSubTableRelationData">
          {{ t("trade_common_confirm") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="tsx" setup>
import {
  computed,
  createApp,
  h,
  inject,
  markRaw,
  nextTick,
  onMounted,
  ref,
  toRaw,
  getCurrentInstance,
  watch
} from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { cloneDeep, storageLocal } from "@pureadmin/utils";
import { dayjs, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { VTable } from "@visactor/vue-vtable";
import { getSubTableFormRefList } from "@/api/common";
import {
  DrInput,
  DrTextarea,
  DrInputNumber,
  DrPercentage,
  DrRadio,
  DrCheckbox,
  DrDatePicker,
  DrRate,
  DrDialog,
  DrSCCSMemberSelect,
  DrSCCSGroupMemberSelect,
  LKSubTable,
  getColumnCustomLayoutTemplate,
  subTableOperateList,
  tableColumnWidgetNameList,
  tableColumnEditorConfigure
} from "./relate.ts";
import { useElementHover, useFocusWithin } from "@vueuse/core";
import { formatThousandNumber, formatNumberValueOf } from "@/utils/common.ts";
import { isEqual } from "date-fns";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  staffPerson: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderId: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
const route = useRoute();
const instance = getCurrentInstance();
const subTableRelationVisible = ref<boolean>(false);
const tableInstanceRef = ref<HTMLElement | any>(null);
const subTableRef = ref<HTMLElement | any>(null);
const tableFormData = ref<any>([]);
const maxHeight = ref<number>(786);
const columns = ref<any[]>([]);
const tableRecordLists = ref<any[]>([]);
const subTablePropData = ref<any>({});
const tableRelationData = ref<any[]>([]);
const tableContainerRef = ref<HTMLElement | any>(null);
const dtTableZIndex = ref<number>(0);
const firstLoadingRelationData = ref<boolean>(true);
const isHovered = useElementHover(tableContainerRef);
const isFocused = useFocusWithin(tableContainerRef);
const widgetDateMap = ref<any>({
  year: "YYYY",
  month: "YYYY-MM",
  date: "YYYY-MM-DD",
  datetime: "YYYY-MM-DD HH:mm",
  datetimesecond: "YYYY-MM-DD HH:mm:ss"
});

const option = markRaw({
  records: [],
  columns: [],
  defaultHeaderRowHeight: 36,
  defaultRowHeight: 60,
  rightFrozenColCount: 1,
  editCellTrigger: "click",
  hover: {
    highlightMode: "row",
    disableHover: true
  },
  select: {
    highlightMode: "row",
    disableSelect: true
  },
  customConfig: {
    createReactContainer: true
  },
  resize: {
    columnResizeMode: "header"
  },
  tooltip: {
    isShowOverflowTextTooltip: true,
    overflowTextTooltipDisappearDelay: 500
  },
  rowSeriesNumber: {
    title: t("trade_common_SerialNumber"),
    width: "60"
  },
  emptyTip: {
    text: t("trade_common_emptyTip"), // 空数据提示文本
    textStyle: {
      color: "#797979",
      fontSize: 12,
      fontWeight: "bolder"
    },
    icon: {
      width: 0,
      height: 0
    },
    displayMode: "basedOnContainer"
  },
  theme: {
    headerStyle: {
      color: "#797979",
      fontSize: "13",
      fontWeight: "bolder",
      textAlign: "center",
      bgColor: "#F7F7F7",
      borderColor: "#e8e8e8"
    },
    bodyStyle: {
      color: "#262626",
      fontSize: "13",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    scrollStyle: {
      visible: "always",
      hoverOn: false,
      width: 10,
      barToSide: true,
      scrollRailColor: "#fbfbfb",
      scrollSliderColor: "#d3d3d3"
    },
    buttonStyle: {
      buttonColor: "transparent",
      buttonBorderColor: "transparent",
      buttonHoverColor: "transparent",
      buttonHoverBorderColor: "transparent"
    }
  }
});

const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const actuatorDefaultValue: any = inject("ActuatorDefaultValue", null); // 表单是否需要执行默认值功能
const invocationDefaultValueExecutor: any = inject(
  "invocationDefaultValueExecutor",
  null
);
const invocationFormulaCalculateExecutor: any = inject(
  "invocationFormulaCalculateExecutor",
  null
);

const emit = defineEmits(["handleSubTableWidgetValueChange"]);

const subTableAddBtnText = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.addButtonTextEn ||
        props.widgetConfigure.props?.addButtonText
    : props.widgetConfigure.props?.addButtonText;
});

const subTableHeight = computed(() => {
  return tableRecordLists.value.length === 0
    ? 100
    : (tableRecordLists.value.length + 1) * 60;
});

const subTableWidgetId = computed(() => {
  return props.widgetConfigure._fc_id;
});

const relateColumns = computed(() => {
  const widgetConfigureList = cloneDeep(props.widgetConfigure);
  const childrenList = widgetConfigureList.children.filter(child => {
    return child.children && !!child.children[0].dataSourceId;
  });
  childrenList.unshift({});
  return Object.assign(widgetConfigureList, {
    children: childrenList
  });
});

const tableInstance: any = computed(() => {
  return tableInstanceRef.value.vTableInstance;
});

const quoteTableColumnRelateBtnText = computed(() => {
  const relateColumn = props.widgetConfigure.props.columns
    .slice()
    .reverse()
    .find(column => column.relateButtonText || column.relateButtonTextEn);

  const translationLang = storageLocal().getItem("translationLang");

  return relateColumn
    ? translationLang === "en"
      ? relateColumn.relateButtonTextEn || relateColumn.relateButtonText
      : relateColumn.relateButtonText
    : "引用数据";
});

const quoteTableColumn = computed(() => {
  const quoteColumn = props.widgetConfigure.props.columns.filter(
    column => column.dataSourceId
  );
  return quoteColumn.length > 0;
});

const drFormulasList = computed(() => {
  return props.widgetConfigure.children
    .slice(1, props.widgetConfigure.children.length)
    .map(column => column.children && column.children[0])
    .filter(widget => widget.type === "DrFormulas")
    .filter(formulas => formulas.props.formulaObject);
});

class DrVueEditor {
  root = null;
  rootName = null;
  element = null;
  container = null;
  currentValue = null;
  hasChange = false; // 用来判断用户是否有修改过值，来跳过 validateValue 早于change前触发，导致异常的问题
  tableWidgetObject = null;

  onStart(editorContext) {
    const { container, referencePosition, value, row, col, table } =
      editorContext;

    this.container = container;
    const fieldName = table.getCellHeaderPaths(col, row).colHeaderPaths[0]
      .field;

    const column = table.options.columns.find(
      columnItem => columnItem.field === fieldName
    );

    this.tableWidgetObject = table.records[row - 1];

    let formatValue = value;
    if (["DrInputNumber", "DrPercentage"].includes(column.type)) {
      formatValue =
        value === "" || value === null || value === undefined
          ? null
          : typeof value === "string" && value.indexOf(",") > -1
            ? formatNumberValueOf(value)
            : value;
    }

    this.createElement(formatValue, row, column, col, this.tableWidgetObject);
    if (referencePosition?.rect) this.adjustPosition(referencePosition.rect);
  }

  createElement(defaultValue, row, column, col, tableWidgetObject) {
    const div = document.createElement("div");
    div.style.position = "absolute";
    div.style.width = "100%";
    div.style.padding = "1px";
    div.style.zIndex = "10";
    div.style.boxSizing = "border-box";
    this.container?.appendChild(div);

    const app = this.createVueApp(
      defaultValue,
      row,
      column,
      col,
      tableWidgetObject
    );
    app.mount(div);
    this.rootName = column;
    this.root = app;
    this.element = div;
  }

  createVueApp(defaultValue, row, column, col, tableWidgetObject) {
    const self = this;
    const widgetColumn = {
      DrInput: DrInput,
      DrTextarea: DrTextarea,
      DrInputNumber: DrInputNumber,
      DrPercentage: DrPercentage,
      DrRadio: DrRadio,
      DrCheckbox: DrCheckbox,
      DrDatePicker: DrDatePicker,
      DrRate: DrRate,
      DrSCCSMemberSelect: DrSCCSMemberSelect,
      DrSCCSGroupMemberSelect: DrSCCSGroupMemberSelect,
      DrSCCSMemberSingleSelect: DrSCCSMemberSelect,
      DrSCCSGroupMemberSingleSelect: DrSCCSGroupMemberSelect,
      DrSCCSMemberMultipleSelect: DrSCCSMemberSelect,
      DrSCCSGroupMemberMultipleSelect: DrSCCSGroupMemberSelect,
      DrFilesUpload: DrDialog,
      DrImagesUpload: DrDialog,
      DrAddress: DrDialog,
      DrEditor: DrDialog,
      DrExchangeRates: DrDialog
    };

    return createApp({
      data() {
        return {
          componentValue: defaultValue
        };
      },
      render() {
        const isNormalRadioOrCheckbox =
          ["DrRadio", "DrCheckbox"].includes(column.type) &&
          column.props.layout !== "select";
        self.setValue(defaultValue);
        return h(
          "div",
          {
            style: {
              padding: "2px",
              background: "#FFF",
              ...(isNormalRadioOrCheckbox
                ? {
                    boxShadow: "0 0 16px 0 rgba(0, 0, 0, 0.15)"
                  }
                : {})
            }
          },
          [
            h(widgetColumn[column.type], {
              style: isNormalRadioOrCheckbox
                ? { minHeight: "52px", maxHeight: "120px", overflowY: "auto" }
                : { height: "30px" },
              modelValue: this.componentValue,
              widgetConfigure: column,
              trendsForm: tableWidgetObject,
              tableParentWidgetId: props.widgetConfigure._fc_id,
              widgetRowIndex: row,
              widgetColIndex: col,
              staffPerson: props.staffPerson,
              "onUpdate:modelValue": value => {
                this.componentValue = value;
                self.hasChange = value !== defaultValue;
              },
              onHandleSubTableWidgetValueChange: (widgetId, data, value) => {
                const dataValue = {
                  widgetId: widgetId,
                  data: data,
                  value: value
                };

                self.setValue(dataValue);
                tableInstance.value.changeCellValue(
                  col,
                  row,
                  dataValue,
                  true,
                  true
                );
                // table 在失焦的时候会提前执行了 validateValue 方法，但并为执行onChange事件，所以这里在change后强制校验一次。
                self.validateValue(dataValue);
              },
              onHandleRenderFinish: component => {
                component.focus();
              }
            }),
            h(
              "div",
              {
                class: "validate-tip",
                style: {
                  display: "none"
                }
              },
              column.$required === true ? "请输入" : column.$required
            )
          ]
        );
      }
    });
  }

  // 获取编辑器值
  getValue() {
    return this.currentValue === null ||
      this.currentValue === undefined ||
      (typeof this.currentValue === "object" &&
        this.currentValue.value === null)
      ? ""
      : this.currentValue;
  }

  // 设置编辑器值
  setValue(value) {
    this.currentValue = value;
  }

  // 调整编辑器位置
  adjustPosition(rect) {
    if (this.element) {
      this.element.style.top = `${rect.top}px`;
      this.element.style.left = `${rect.left}px`;
      this.element.style.width = `${rect.width}px`;
      this.element.style.height = `${rect.height}px`;
    }
  }

  // 编辑器结束时的清理工作
  onEnd() {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.element && this.container) {
      this.container.removeChild(this.element);
      this.element = null;
    }
  }

  // 判断是否为编辑器元素
  isEditorElement(target) {
    return this.element?.contains(target) || this.isClickPopUp(target);
  }

  // 判断是否点击了弹出框
  isClickPopUp(target) {
    while (target) {
      if (target.classList && target.classList.contains("arco-select-vtable")) {
        return true;
      }

      if (
        target instanceof HTMLElement &&
        !!target.closest(".el-date-picker")
      ) {
        return true;
      }
      target = target.parentNode;
    }
    return false;
  }

  validateValue(newValue?: any, oldValue?: any, position?: any, table?: any) {
    const isEmpty = value => {
      if (Array.isArray(value)) return value.length === 0;
      if (typeof value === "string") return value.trim() === "";
      return value === null || value === undefined;
    };

    if (this.rootName?.$required && this.element) {
      if (isEqual(newValue, oldValue) && this.hasChange) return false;

      let empty;

      if (newValue !== null && newValue.widgetId !== undefined) {
        empty = isEmpty(newValue.value);
      } else {
        empty = isEmpty(newValue);
      }

      if (empty) {
        this.element.classList.add("widget-validate-failed");
        return false;
      } else {
        this.element.classList.remove("widget-validate-failed");
        return true;
      }
    }
    return true;
  }
}

const handleTransformFormulasValue = (data: any, formatConfig: any) => {
  const { type, formatDateType } = formatConfig;
  if (type === "number") {
    const formatNumberType = formatConfig.formatNumberType;
    const formulasWidgetValue = Number(data);
    if (!isNaN(formulasWidgetValue)) {
      let widgetFormulasValueData = `${formulasWidgetValue.toFixed(
        formatConfig.precision
      )}`;
      if (formatNumberType === "useThousandSeparator") {
        return formatThousandNumber(widgetFormulasValueData);
      } else {
        return widgetFormulasValueData;
      }
    }
  } else if (type === "date") {
    const formulasWidgetValue = new Date(data);

    if (!isNaN(formulasWidgetValue.getTime())) {
      return dayjs(formulasWidgetValue).format(
        widgetDateMap.value[formatDateType]
      );
    }
  } else if (type === "text") {
    const formulasWidgetValue =
      data instanceof Array ? data.join("") : "" + data;

    if (formulasWidgetValue !== "Error: #VALUE!") {
      return formulasWidgetValue;
    }
  } else if (type === "percentage") {
    const formulasWidgetValue = new BigNumber(data)
      .multipliedBy(100)
      .toNumber();

    if (!isNaN(formulasWidgetValue)) {
      return `${formulasWidgetValue.toFixed(formatConfig.precision)}%`;
    }
  }
};

/**
 * 子表新增一行数据
 */
const handleAddTableRow = () => {
  const tableRecordLength = tableRecordLists.value.length;
  const recordIndex = tableInstance.value.getRecordShowIndexByCell(
    0,
    tableRecordLength
  );
  const tableData = cloneDeep(tableFormData.value);
  const endIndex = columns.value.length - 1;
  const tableColumns = columns.value.slice(0, endIndex);
  let columnData = [];
  let columnTemplate = {};

  let formulasColumns = [];
  let relateColumns = [];
  // 遍历表格列，生成默认数据
  for (let column of tableColumns) {
    let defaultValue =
      column.type === "DrAddress"
        ? {
            areaCodeList: [],
            address: ""
          }
        : column.type === "DrCheckbox"
          ? []
          : "";

    // 如果列有默认值配置，执行默认值计算
    if (
      column.props &&
      column.props.defaultValueConfig &&
      column.props.defaultValueConfig.type !== "none"
    ) {
      const defaultValueConfig = column.props.defaultValueConfig;
      const columnIds = tableColumns.map(column => column._fc_id);

      if (
        defaultValueConfig.type === "relate" &&
        columnIds.includes(defaultValueConfig.content)
      ) {
        relateColumns.push(column);
      } else {
        const defaultValueData = invocationDefaultValueExecutor(
          defaultValueConfig,
          column
        );
        defaultValue = defaultValueData ? defaultValueData : "";
      }
    } else if (column && column.type === "DrFormulas") {
      if (
        column.props.formulaObject &&
        column.props.formulaObject?.formulasParams &&
        column.props.formulaObject?.formulasParams.some(item =>
          item.includes(props.widgetConfigure._fc_id)
        )
      ) {
        formulasColumns.push(column);
      } else {
        const formulasData = invocationFormulaCalculateExecutor(
          column.props.formulaObject
        );
        defaultValue = handleTransformFormulasValue(
          formulasData,
          column.props.formatConfig
        );
      }
    }

    columnTemplate[column.field] = defaultValue;
    columnData.push({
      dataSource: false,
      label: defaultValue,
      obj: defaultValue,
      widgetId: column._fc_id,
      $rowIndex: tableData.length + 1
    });
  }

  let tableFormObject = {};
  const otherWidgetJsonList = props.widgetConfigure.children
    .slice(1)
    .map(column => column.children[0]);

  const tableRecord = cloneDeep(columnData);
  let traverseWidgetData = {};

  // 遍历其他组件数据
  for (let otherWidget of otherWidgetJsonList) {
    if (["DrInputNumber", "DrPercentage"].includes(otherWidget.type)) {
      if (!tableRecord[otherWidget._fc_id]) {
        traverseWidgetData[otherWidget._fc_id] = undefined;
      } else {
        traverseWidgetData[otherWidget._fc_id] =
          tableRecord[otherWidget._fc_id];
        if (otherWidget.type === "DrPercentage") {
          traverseWidgetData[otherWidget._fc_id] =
            tableRecord[otherWidget._fc_id] / 100;
        }
      }
    } else if (["DrCheckbox", "DrRadio"].includes(otherWidget.type)) {
      if (
        tableRecord[otherWidget._fc_id] &&
        tableRecord[otherWidget._fc_id].length > 0
      ) {
        const trendsFormListData =
          tableRecord[otherWidget._fc_id] instanceof Array
            ? tableRecord[otherWidget._fc_id]
            : otherWidget.type === "DrRadio"
              ? tableRecord[otherWidget._fc_id]
              : [tableRecord[otherWidget._fc_id]];

        let trendsFormOption;
        if (otherWidget.type !== "DrRadio") {
          trendsFormOption = trendsFormListData.map(otherWidgetValue => {
            const optionItem = otherWidget.props.options.find(
              option =>
                option.value === otherWidgetValue ||
                option.label === otherWidgetValue
            );
            return optionItem ? optionItem.label : "";
          });
        } else {
          const optionItem = otherWidget.props.options.find(
            option =>
              option.value === trendsFormListData ||
              option.label === trendsFormListData
          );
          trendsFormOption = optionItem ? optionItem.label : "";
        }
        traverseWidgetData[otherWidget._fc_id] = trendsFormOption;
      }
    } else {
      traverseWidgetData[otherWidget._fc_id] = tableRecord[otherWidget._fc_id];
    }
  }
  tableFormObject[props.widgetConfigure._fc_id] = traverseWidgetData;

  let formulasColumnTemplate = {};
  for (let formulasColumn of formulasColumns) {
    let defaultValue = "";

    const formulasData = invocationFormulaCalculateExecutor(
      formulasColumn.props.formulaObject,
      tableFormObject
    );
    defaultValue = handleTransformFormulasValue(
      formulasData,
      formulasColumn.props.formatConfig
    );
    formulasColumnTemplate[formulasColumn.field] = defaultValue;
    const index = columnData.findIndex(
      column => formulasColumn._fc_id === column.widgetId
    );

    columnData.splice(index, 1, {
      dataSource: false,
      label: defaultValue,
      obj: defaultValue,
      widgetId: formulasColumn._fc_id,
      $rowIndex: tableData.length + 1
    });
  }

  // 子表内组件关联
  let relateTemplate = {};
  for (let relateColumn of relateColumns) {
    relateTemplate[relateColumn._fc_id] =
      columnTemplate[relateColumn.props.defaultValueConfig.content];

    const index = columnData.findIndex(
      column => relateColumn._fc_id === column.widgetId
    );

    columnData.splice(index, 1, {
      dataSource: false,
      label: columnTemplate[relateColumn.props.defaultValueConfig.content],
      obj: columnTemplate[relateColumn.props.defaultValueConfig.content],
      widgetId: relateColumn._fc_id,
      $rowIndex: tableData.length + 1
    });
  }
  let tableRecordObject = Object.assign(
    columnTemplate,
    formulasColumnTemplate,
    relateTemplate
  );
  // 添加记录到表格并更新数据
  tableData.push(columnData);
  tableFormData.value = tableData;
  tableInstance.value.addRecord(tableRecordObject, recordIndex + 1);
  handleWidgetFormsValue(
    subTableWidgetId.value,
    {
      widgetId: subTableWidgetId.value,
      widgetType: "DrTableForm",
      label: null,
      obj: null,
      childrenList: tableData
    },
    tableData
  );
};

/**
 * 添加子表关联数据
 * @param autoRelateBool 是否自动关联
 */
const handleAddSubTableRelation = async (autoRelateBool: boolean = false) => {
  // 如果关联数据为空，获取数据
  if (tableRelationData.value.length === 0) {
    const orderId = route.query.orderId || props.orderId;
    //@ts-ignore
    const { code, data } = await getSubTableFormRefList({
      orderId: orderId,
      tableFormWidgetId: subTableWidgetId.value
    });

    if (code === 0 && data.length > 0) {
      let subTableColumnList = props.widgetConfigure.children
        .slice(1)
        .map(child => child.children[0]);
      let tableTrData = [];

      for (let tableRow of data) {
        let tableTdData = [];

        // 按行索引分组数据
        const subTableAssemblyData = Object.values(
          tableRow.widgetList.reduce((res, item) => {
            res[item.rowIndex]
              ? res[item.rowIndex].push(item)
              : (res[item.rowIndex] = [item]);
            return res;
          }, {})
        );

        // 处理每个单元格数据
        for (let subTdData of subTableAssemblyData) {
          //@ts-ignore
          for (let widget of subTdData) {
            const column = props.widgetConfigure.props.columns.find(
              dataSourceColumn =>
                dataSourceColumn.dataSourceWidgetId === widget.widgetId
            );

            const subTableTdData = subTableColumnList.find(
              subTableColumn => subTableColumn.field === column.fieldId
            );

            tableTdData.push(
              Object.assign(cloneDeep(widget), {
                widgetId: subTableTdData._fc_id,
                dataSource: true
              })
            );
          }
          tableTrData.push(tableTdData);
        }
      }
      tableRelationData.value = tableTrData;
    }
  }

  // 处理自动关联或显示关联弹窗
  if (!!autoRelateBool) {
    const autoRelate = props.widgetConfigure.props.autoRelate;
    if (autoRelate && actuatorDefaultValue) {
      const relateData = cloneDeep(tableRelationData.value);
      handleRelateSubTableData(relateData);
      firstLoadingRelationData.value = false;
    }
  } else {
    subTableRelationVisible.value = true;
  }
};

/**
 * 处理关联子表数据
 * @param tableData 表格数据
 */
const handleRelateSubTableData = (tableData: any) => {
  const endIndex = columns.value.length - 1;
  const tableColumns = columns.value.slice(0, endIndex);
  let tableRecords: any[] = [];
  let tableColumnRecords: any[] = [];

  // 处理每行选中数据
  for (let checkRow of tableData) {
    let columnData = [];
    let columnTemplate = {};

    // 处理每列数据
    for (let column of tableColumns) {
      let defaultValue =
        column.type === "DrAddress"
          ? {
              areaCodeList: [],
              address: ""
            }
          : "";

      // 执行默认值计算
      if (
        column.props &&
        column.props.defaultValueConfig &&
        column.props.defaultValueConfig.type !== "none"
      ) {
        const defaultValueConfig = column.props.defaultValueConfig;
        defaultValue = invocationDefaultValueExecutor(
          defaultValueConfig,
          column
        );
      }

      // 查找对应的行数据项
      const checkRowItem = checkRow.find(
        rowData => rowData.widgetId === column.field
      );

      columnTemplate[column.field] = checkRowItem
        ? checkRowItem.obj
        : defaultValue;

      if (checkRowItem) {
        columnData.push({
          dataSource: true,
          label: checkRowItem.label,
          obj: checkRowItem.obj,
          widgetId: column._fc_id,
          $rowIndex: tableData.length + 1
        });
      } else {
        columnData.push({
          dataSource: false,
          label: defaultValue,
          obj: defaultValue,
          widgetId: column._fc_id,
          $rowIndex: tableData.length + 1
        });
      }
    }
    tableRecords.push(columnTemplate);
    tableColumnRecords.push(columnData);
  }

  // 添加记录到表格并更新数据
  tableInstance.value.addRecords(tableRecords);
  let tableFormDataList = cloneDeep(tableFormData.value);
  tableFormDataList.push(...tableColumnRecords);
  tableFormData.value = tableFormDataList;
  subTableRelationVisible.value = false;
  handleWidgetFormsValue(
    subTableWidgetId.value,
    {
      widgetId: subTableWidgetId.value,
      widgetType: "DrTableForm",
      label: null,
      obj: null,
      childrenList: tableFormDataList
    },
    tableFormDataList
  );
};

/**
 * 添加子表关联数据的确认处理
 */
const handleAddSubTableRelationData = () => {
  const checkboxList = subTableRef.value.getCheckboxCheckedRow();
  const tableData = cloneDeep(checkboxList);
  handleRelateSubTableData(tableData);
};

/**
 * 加载子表数据
 * @param records 子表数据集合
 */
const handleReloadVTableData = (records: any) => {
  let tableRecords = [];

  // 处理每行记录
  for (let tableRow of records) {
    let tableRecordObject = {};

    // 处理每列数据
    for (let column of columns.value) {
      if (column.field === "operate") {
        continue;
      }

      const tableColData = tableRow.find(
        tableCol => tableCol.widgetId === column._fc_id
      );

      let tableValue: any = "";
      if (tableColData) {
        // 根据不同组件类型处理数据
        if (["DrRadio", "DrCheckbox"].includes(column.type)) {
          tableValue = tableColData.obj;
        } else if (
          [
            "DrSCCSMemberSelect",
            "DrSCCSGroupMemberSelect",
            "DrSCCSMemberSingleSelect",
            "DrSCCSGroupMemberSingleSelect",
            "DrSCCSMemberMultipleSelect",
            "DrSCCSGroupMemberMultipleSelect"
          ].includes(column.type)
        ) {
          if (column.props.multiple) {
            tableValue =
              tableColData.obj && typeof tableColData.obj === "string"
                ? tableColData.obj.split(",")
                : tableColData.obj || [];
          } else {
            tableValue = tableColData.obj ?? undefined;
          }
        } else if (
          [
            "DrImagesUpload",
            "DrFilesUpload",
            "DrEditor",
            "DrExchangeRates"
          ].includes(column.type)
        ) {
          tableValue = tableColData.obj;
        } else if (["DrAddress"].includes(column.type)) {
          tableValue = tableColData.obj
            ? JSON.stringify({
                label: tableColData.label,
                obj: tableColData.obj
              })
            : "";
        } else {
          tableValue = tableColData.label;
        }
      }

      tableRecordObject[column._fc_id] = tableValue;
    }
    tableRecords.push(tableRecordObject);
  }

  // 更新表格数据
  tableRecordLists.value = tableRecords;
  tableFormData.value = records;
  tableInstance.value && tableInstance.value.setRecords(tableRecords);

  if (!!firstLoadingRelationData.value) {
    // 处理自动关联
    handleAddSubTableRelation(true);
  }
  handleInitTabEditor();
};

/**
 * 打开子表弹窗处理
 * @param widgetData 组件数据
 * @param rowIndex 行索引
 */
const handleOpenSubTableDialog = (widgetData: any, rowIndex: number) => {
  if (!widgetData) {
    return;
  }
  let tableData = cloneDeep(tableFormData.value);
  const widgetId = widgetData.widgetId;
  const cloneTableData = tableData[rowIndex - 1];
  const index = cloneTableData.findIndex(
    widget => widget.widgetId === widgetId
  );
  cloneTableData.splice(index, 1, toRaw(widgetData));
  tableData.splice(rowIndex - 1, 1, cloneTableData);
  tableFormData.value = tableData;

  handleWidgetFormsValue(
    subTableWidgetId.value,
    {
      widgetId: subTableWidgetId.value,
      widgetType: "DrTableForm",
      label: null,
      obj: null,
      childrenList: tableData
    },
    tableData
  );
};

/**
 * 编辑器功能函数
 * @param args 参数
 * @param column 列配置
 */
const editorFun = (args: any, column: any) => {
  if (!tableFormData.value[args.row - 1]) {
    return false;
  }
  const argColumn = tableFormData.value[args.row - 1].find(
    columnConfig => columnConfig.widgetId === column._fc_id
  );
  return argColumn ? !!argColumn.dataSource : true;
};

/**
 * 初始化子表单元格配置
 */
const handleInitTableData = () => {
  const translationLang = storageLocal().getItem("translationLang");
  let subTableColumns: any[] = [];

  const parentTableWidget = props.widgetConfigure;
  const parentTableWidgetId = props.widgetConfigure._fc_id;

  // 获取子表列配置
  const widgetColumns = props.widgetConfigure.children
    .slice(1)
    .map(column => column.children[0]);

  // 合并列配置
  for (let widgetColumn of widgetColumns) {
    const columnConfigure = props.widgetConfigure.props.columns.find(
      column => column.fieldId === widgetColumn.field
    );
    subTableColumns.push(Object.assign(widgetColumn, columnConfigure));
  }

  // 生成列配置列表
  let columnList = subTableColumns.map(column => {
    let customLayoutTemplate = {};
    column.title =
      translationLang === "en"
        ? column.props?.titleEn || column.title
        : column.title;

    // 如果是支持自定义布局的组件，添加自定义布局模板
    if (tableColumnWidgetNameList.includes(column.type)) {
      customLayoutTemplate = getColumnCustomLayoutTemplate(
        column,
        handleOpenSubTableDialog
      );
    }

    // 添加编辑器配置
    let columnEditorTemplate = tableColumnEditorConfigure(
      column,
      editorFun,
      parentTableWidget
    );
    const { $required, display, _fc_id, props, title, type } = column;

    return Object.assign(
      {
        $required: $required,
        display: display,
        field: _fc_id,
        props: props,
        title: title,
        type: type,
        _fc_id: _fc_id,
        parentTableWidgetId: parentTableWidgetId
      },
      customLayoutTemplate,
      columnEditorTemplate
    );
  });

  // 添加操作列
  columnList.push(subTableOperateList());
  columns.value = columnList;

  nextTick(() => {
    const tableInstanceWidth = tableInstance.value.container.offsetWidth;

    // 计算列宽
    columnList = columnList.map(column => {
      if (column.field !== "operate") {
        const columnWidth =
          (columnList.length - 1) * 300 < tableInstanceWidth - 150
            ? (tableInstanceWidth - 150) / (columnList.length - 1)
            : 300;
        return Object.assign(column, { width: columnWidth });
      } else {
        return column;
      }
    });
    const nodeList = Array.from(document.querySelectorAll(".lk-dr-table"));
    const tableWidgetIds = nodeList
      .map(node => {
        //@ts-ignore
        return node.dataset.id;
      })
      .reverse();

    const index = tableWidgetIds.findIndex(
      widget => widget === props.widgetConfigure._fc_id
    );
    dtTableZIndex.value = 2000 + index;

    // 更新表格选项
    let tableOptions = Object.assign(option, {
      columns: columnList
    });
    tableInstance.value.updateColumns(tableOptions.columns);

    // 处理表格数据
    const tableData = cloneDeep(props.trendsForm[subTableWidgetId.value]) || [];
    const columnFieldsList = columns.value.slice(0, columns.value.length - 1);

    const trendsTableData = tableData.map(trData => {
      return columnFieldsList.map(column => {
        const tdItemData = trData.find(item => item.widgetId === column._fc_id);
        return tdItemData
          ? {
              dataSource: tdItemData.dataSource,
              formId: tdItemData.formId,
              obj: tdItemData.obj,
              label: tdItemData.label,
              widgetId: tdItemData.widgetId,
              rowIndex: tdItemData.rowIndex
            }
          : {
              dataSource: false,
              formId: trData[0].formId,
              obj: "",
              label: "",
              widgetId: column._fc_id,
              rowIndex: trData[0].rowIndex
            };
      });
    });
    handleReloadVTableData(trendsTableData);
  });
};

const handleInitTabEditor = () => {
  const editor = new DrVueEditor();
  // @ts-ignore
  VTable.register.editor(`${props.widgetConfigure._fc_id}-dr-editor`, editor);
};

watch(
  [() => props.widgetConfigure, () => props.trendsForm[subTableWidgetId.value]],
  () => {
    if (props.widgetConfigure.children.length > 0) {
      handleInitTableData();
    }
  },
  {
    immediate: true
  }
);

onMounted(() => {
  tableInstance.value.on("click_cell", args => {
    const { col, row, targetIcon } = args;

    // 如果数据来源是数据源，完成编辑
    if (!!tableFormData.value[row - 1]?.[0]?.dataSource) {
      tableInstance.value.completeEditCell();
    }

    if (targetIcon) {
      if (targetIcon.name === "edit") {
        // 处理编辑按钮点击
        const record = tableInstance.value.getRecordByCell(col, row);
        tableInstance.value.addRecord(record, row);
        const tableData = cloneDeep(tableFormData.value);
        tableData.splice(row - 1, 0, tableData[row - 1]);
        handleWidgetFormsValue(
          subTableWidgetId.value,
          {
            widgetId: subTableWidgetId.value,
            widgetType: "DrTableForm",
            label: null,
            obj: null,
            childrenList: tableData
          },
          tableData
        );
      } else if (targetIcon.name === "delete") {
        // 处理删除按钮点击
        ElMessageBox.confirm(
          t("trade_common_deleteTableFormRowTip"),
          t("trade_common_deleteTip"),
          {
            confirmButtonText: t("trade_common_confirm"),
            cancelButtonText: t("trade_common_cancel"),
            confirmButtonClass: "confrim-message-btn-class",
            cancelButtonClass: "cancel-message-btn-class",
            type: "error",
            icon: markRaw(WarningFilled),
            center: true
          }
        ).then(() => {
          const recordIndex = tableInstance.value.getRecordShowIndexByCell(
            args.col,
            args.row
          );
          tableInstance.value.deleteRecords([recordIndex]);
          const tableData = cloneDeep(tableFormData.value);
          tableData.splice(args.row - 1, 1);
          handleWidgetFormsValue(
            subTableWidgetId.value,
            {
              widgetId: subTableWidgetId.value,
              widgetType: "DrTableForm",
              label: null,
              obj: null,
              childrenList: tableData
            },
            tableData
          );
        });
      }
    }
  });

  // 监听单元格值变化事件
  tableInstance.value.on("change_cell_value", args => {
    const { changedValue, col, row } = args;

    // 获取变更的组件ID
    const changedWidgetId =
      changedValue?.data?.widgetId ??
      tableInstance.value.getCellHeaderPaths(col, row).colHeaderPaths?.[0]
        .field;

    // 构建变更的组件数据
    // FIXME 当清空组件数据时候 changedValue是空字符串，暂时先用这种方式构建空值时候的数据。好的做法应该是子组件去处理空值时候的返回值。
    const isEmpty =
      changedValue.value === "" ||
      changedValue.value === null ||
      changedValue.value === undefined;

    const changedWidghtData = changedValue.data ?? {
      label: isEmpty ? "" : changedValue.value,
      obj: isEmpty ? undefined : changedValue.value,
      widgetType: props.widgetConfigure.children
        .flatMap(item => item.children || [])
        .find(item => item._fc_id === changedWidgetId)?.type,
      widgetId: changedWidgetId,
      $rowIndex: row - 1
    };

    // 克隆表格数据并计算行列索引
    const tableData = cloneDeep(tableFormData.value);

    const tableRowIndex = changedValue?.data
      ? changedValue.data.$rowIndex - 1
      : row - 1;
    const colIndex = tableData[tableRowIndex].findIndex(
      widget => widget.widgetId === changedWidgetId
    );

    // 太疑惑了！！ Vtable 如果不编辑字段值，失焦后触发 change_cell_value，值是空字符串。只有有一次变更后，changedValue 就不会为空，但可能这个值是上个单元格的值。
    // 目前的处理方式是，判断 changedValue 是否为空字符串，就强制那旧的表格数据去覆盖当前的表格数据。
    if (changedValue === "") {
      handleWidgetFormsValue(
        subTableWidgetId.value,
        {
          widgetId: subTableWidgetId.value,
          widgetType: "DrTableForm",
          label: null,
          obj: null,
          childrenList: tableData
        },
        tableData
      );
      return;
    }

    // 更新表格数据
    tableData[tableRowIndex].splice(
      colIndex,
      1,
      Object.assign(changedWidghtData, { dataSource: false })
    );

    handleWidgetFormsValue(
      subTableWidgetId.value,
      {
        widgetId: subTableWidgetId.value,
        widgetType: "DrTableForm",
        label: null,
        obj: null,
        childrenList: tableData
      },
      tableData
    );

    // 子表控件值改变需要判断当行是否有需要计算的公式运算
    let tableFormObject = {};
    let widgetRecord = {};
    widgetRecord[changedWidgetId] = changedValue.value;
    tableFormObject[props.widgetConfigure._fc_id] = Object.assign(
      tableInstance.value.records[tableRowIndex],
      widgetRecord
    );

    // 查找需要计算的公式
    const drNeedFormulas = drFormulasList.value.filter(formulas => {
      if (formulas.props.formulaObject.formulasParams) {
        return formulas.props.formulaObject.formulasParams.some(item =>
          item.includes(changedWidgetId)
        );
      } else {
        return !!changedWidgetId;
      }
    });

    // 如果有需要计算的公式，执行公式计算
    if (drNeedFormulas.length > 0) {
      const otherWidgetJsonList = props.widgetConfigure.children
        .slice(1)
        .map(column => column.children[0])
        .filter(column => column.type !== "DrFormulas");

      const tableRecord = tableInstance.value.records[tableRowIndex];
      let traverseWidgetData = {};

      // 遍历其他组件数据
      for (let otherWidget of otherWidgetJsonList) {
        if (["DrInputNumber", "DrPercentage"].includes(otherWidget.type)) {
          if (!tableRecord[otherWidget._fc_id]) {
            traverseWidgetData[otherWidget._fc_id] = undefined;
          } else {
            traverseWidgetData[otherWidget._fc_id] =
              tableRecord[otherWidget._fc_id];
            if (otherWidget.type === "DrPercentage") {
              traverseWidgetData[otherWidget._fc_id] =
                tableRecord[otherWidget._fc_id] / 100;
            }
          }
        } else if (["DrCheckbox", "DrRadio"].includes(otherWidget.type)) {
          if (
            tableRecord[otherWidget._fc_id] &&
            tableRecord[otherWidget._fc_id].length > 0
          ) {
            const trendsFormListData =
              tableRecord[otherWidget._fc_id] instanceof Array
                ? tableRecord[otherWidget._fc_id]
                : otherWidget.type === "DrRadio"
                  ? tableRecord[otherWidget._fc_id]
                  : [tableRecord[otherWidget._fc_id]];

            let trendsFormOption;
            if (otherWidget.type !== "DrRadio") {
              trendsFormOption = trendsFormListData.map(otherWidgetValue => {
                const optionItem = otherWidget.props.options.find(
                  option =>
                    option.value === otherWidgetValue ||
                    option.label === otherWidgetValue
                );
                return optionItem ? optionItem.label : "";
              });
            } else {
              const optionItem = otherWidget.props.options.find(
                option =>
                  option.value === trendsFormListData ||
                  option.label === trendsFormListData
              );
              trendsFormOption = optionItem ? optionItem.label : "";
            }
            traverseWidgetData[otherWidget._fc_id] = trendsFormOption;
          }
        } else {
          traverseWidgetData[otherWidget._fc_id] =
            tableRecord[otherWidget._fc_id];
        }
      }
      tableFormObject[props.widgetConfigure._fc_id] = traverseWidgetData;

      // 执行公式计算
      for (let formulas of drNeedFormulas) {
        const formulasValueData = invocationFormulaCalculateExecutor(
          formulas.props.formulaObject,
          tableFormObject
        );

        const formulasData = handleTransformFormulasValue(
          formulasValueData,
          formulas.props.formatConfig
        );

        const index = tableData[tableRowIndex].findIndex(
          widget => widget.widgetId === formulas._fc_id
        );
        tableData[tableRowIndex].splice(index, 1, {
          dataSource: false,
          formId: undefined,
          label: formulasData,
          obj: formulasData,
          rowIndex: tableRowIndex + 1,
          widgetId: formulas._fc_id
        });
        handleWidgetFormsValue(
          subTableWidgetId.value,
          {
            widgetId: subTableWidgetId.value,
            widgetType: "DrTableForm",
            label: null,
            obj: null,
            childrenList: tableData
          },
          tableData
        );
      }
    }
  });
});

defineExpose({
  tableFormData
});
</script>
<style lang="scss" scoped>
.sub-table-container {
  width: 100%;

  .sub-table-top {
    display: inline-flex;
    justify-content: end;
    width: 100%;

    .sub-table-btn {
      margin-right: 24px;
      margin-left: 9px;
      font-size: 14px;
      color: #0070d2;
      cursor: pointer;

      .iconfont {
        margin-right: 5px;
        font-size: 12px;
      }
    }
  }

  .sub-table-main {
    box-shadow: 0 0 0 1px #e8e8e8;
  }
}
</style>
<style lang="scss">
.widget-validate-failed {
  .el-input {
    .el-input__wrapper {
      border: 1px solid #e62412;

      &.is-focus {
        box-shadow: 0 0 0 #e62412;
      }
    }
  }

  .el-textarea {
    .el-textarea__inner {
      border: 1px solid #e62412;
      box-shadow: none;
      transition: none;
    }
  }

  .validate-tip {
    display: block !important;
    margin-top: 4px;
    font-size: 12px;
    line-height: 16px;
    color: #e62412;
  }
}
</style>
