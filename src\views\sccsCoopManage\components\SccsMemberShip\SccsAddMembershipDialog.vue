<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_add_member_tip')"
    class="lk-middle-dialog"
    @confirm="confrimLkDialog"
    @close="closeLkDialog"
  >
    <template #default>
      <div class="create-team-dialog-form">
        <el-form ref="ruleFormRef" label-position="top" label-width="auto">
          <el-form-item>
            <LkTeamSelectV2
              v-model="teamMemberIdList"
              filterable
              :options="sccsMemberList"
              clearable
              :placeholder="t('trade_common_searchAccount')"
              multiple
              :props="{ label: 'username', value: 'teamMemberId' }"
              :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
              value-id="userId"
              @change="handleAddFormRow"
            />
          </el-form-item>
          <div v-if="teamMemberIdList.length === 0" class="fence-list-tip">
            <i class="iconfont link-tips-warm" />
            {{ t("trade_sccs_memberMangeTip") }}
          </div>
          <el-row
            v-for="(item, index) in teamMembers"
            :key="index"
            style="width: 99.6%; padding: 10px; margin: 0 !important"
          >
            <el-col :span="23">
              <div class="sccs-member-manage-col">
                <div class="sccs-member-manage-col-top">
                  <LKAvatar
                    :teamInfo="{
                      avatar: item.avatar,
                      username: item.username
                    }"
                    :size="18"
                  />
                  <ReText
                    class="sccs-member-manage-text-row"
                    :tippyProps="{ delay: 50000 }"
                  >
                    <div class="sccs-member-manage-col-username">
                      {{ item.username }}
                    </div>
                    <div class="sccs-member-manage-col-account">
                      ({{ item.email }})
                    </div>
                  </ReText>
                </div>
                <div class="sccs-member-manage-col-bottom">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-select-v2
                        v-model="roleForm[index].sccsRoleIds"
                        filterable
                        :options="sccsRoleList"
                        clearable
                        :props="{ label: 'name', value: 'id' }"
                        :placeholder="t('trade_common_coopTeamRoles2')"
                        multiple
                      >
                        <template #default="{ item }">
                          <div
                            class="text-ellipsis"
                            :style="{ color: item.isDocking ? '#5f66e5' : '' }"
                          >
                            {{ item.name }}
                          </div>
                        </template>
                      </el-select-v2>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-col>
            <el-col :span="1">
              <div class="itemCenter" @click="handleDelete(index)">
                <i class="iconfont link-ashbin" />
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";
import LKAvatar from "@/components/lkAvatar/index";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import { teamCooperationCache } from "@/utils/cooperatioTeam";
import { ReText } from "@/components/ReText";
import {
  getSccsNotAddList,
  getSccsFenceList,
  getSccsCoopRoleList,
  createSccsMember
} from "@/api/sccs";

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
let roleForm = ref([
  {
    sccsRoleIds: [],
    dataFenceIds: []
  }
]);
const LkDialogRef = ref<any | HTMLElement>(null);
const ruleFormRef = ref<any | HTMLElement>(null);
const sccsFenceList = ref<any[]>([]);
const sccsRoleList = ref<any[]>([]);
const teamMemberIdList = ref<any>([]);
const teamMembers = ref<any>([]);
const sccsMemberList = ref<any[]>([]);

const emit = defineEmits<{
  (e: "handleSuccess"): void;
}>();

const getMemberInfo = (id: string) => {
  const memberItem = sccsMemberList.value.find(memberLi => memberLi.id === id);
  return memberItem;
};

const handleRenderTeamMember = teamMemberIds => {
  let teamMemberData = [];
  for (let i = 0, len = teamMemberIds.length; i < len; i++) {
    const { avatar, username, email, teamMemberId, userId } = getMemberInfo(
      teamMemberIds[i]
    );
    teamMemberData.push({
      avatar: avatar,
      username: username,
      email: email,
      teamMemberId: teamMemberId,
      userId: userId
    });
  }
  teamMembers.value = teamMemberData;
};

const handleAddFormRow = teamMemberIds => {
  let data = [];
  for (let i = 0, len = teamMemberIds.length; i < len; i++) {
    const item = roleForm.value.find(
      (teamFormItem: any) => teamFormItem.id === teamMemberIds[i]
    );
    if (!item) {
      data.push({
        id: teamMemberIds[i],
        sccsRoleIds: [],
        dataFenceIds: []
      });
    } else {
      data.push(item);
    }
  }
  roleForm.value = data;
  handleRenderTeamMember(teamMemberIds);
};

const closeLkDialog = (): void => {
  teamMemberIdList.value = [];
  roleForm.value = [];
  teamMembers.value = [];
};

const confrimLkDialog = async (): Promise<void> => {
  let submitData = [];
  //@ts-ignore
  const { id: teamId } = await teamCooperationCache.currentlyUsedTeam();
  roleForm.value.map(async (item, index) => {
    const { teamMemberId, userId } = getMemberInfo(
      teamMemberIdList.value[index]
    );

    let sccsCoopRoleDTOList = [];
    //@ts-ignore
    item.sccsRoleIds.map(sccsRoleId => {
      const { isMainTeamRole } = sccsRoleList.value.find(
        role => role.id === sccsRoleId
      );
      sccsCoopRoleDTOList.push({
        id: sccsRoleId,
        isMainTeamRole: isMainTeamRole
      });
    });
    submitData.push({
      teamId: teamId,
      sccsId: props.basicInfo.id,
      teamMemberId,
      userId,
      teamOwner: false,
      sccsCoopRoleDTOList: sccsCoopRoleDTOList
    });
  });
  const { code } = await createSccsMember(submitData);
  if (code === 0) {
    LkDialogRef.value.close();
    emit("handleSuccess");
  }
};

const handleDelete = (index: number) => {
  teamMemberIdList.value.splice(index, 1);
  roleForm.value.splice(index, 1);
  handleRenderTeamMember(teamMemberIdList.value);
};

const open = async (): Promise<void> => {
  const sccsId = props.basicInfo.id;
  Promise.all([
    getSccsNotAddList({ sccsId: sccsId }),
    getSccsFenceList({ sccsId: sccsId }),
    getSccsCoopRoleList({ sccsId: sccsId })
  ]).then(res => {
    sccsMemberList.value = res[0].data;
    sccsFenceList.value = res[1].data;
    sccsRoleList.value = res[2].data;
    LkDialogRef.value.open();
  });
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.el-form {
  ::v-deep(.el-form-item.widthfixRow) {
    width: 100% !important;

    .el-form-item__content {
      width: 100% !important;
    }
  }
}

.itemCenter {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .iconfont {
    cursor: pointer;
  }
}

.sccs-member-manage-col {
  box-sizing: border-box;
  width: 98%;
  // box-shadow: 0px 2px 18px 0px rgba(0, 0, 0, 0.34);
  padding: 13px 11px;
  background: #f7f7f7;
  border-radius: 4px;

  .sccs-member-manage-col-top {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .sccs-member-manage-text-row {
      display: flex;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;

      .sccs-member-manage-col-username {
        padding: 0 4px;
        font-size: 14px;
        line-height: 16px;
        color: #262626;
        text-shadow: 0 2px 18px 0 rgb(0 0 0 / 34%);
      }

      .sccs-member-manage-col-account {
        overflow: hidden;
        font-size: 14px;
        line-height: 16px;
        color: #595959;
        text-overflow: ellipsis;
        text-shadow: 0 2px 18px 0 rgb(0 0 0 / 34%);
        white-space: nowrap;
      }
    }
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.fence-list-tip {
  height: 38px;
  padding-left: 14px;
  margin-bottom: 14px;
  font-size: 14px;
  line-height: 38px;
  color: #fa8d0a;
  text-align: left;
  background: #fdf6ec;
  border-radius: 2px;

  .iconfont {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
  }
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
