<template>
  <el-drawer
    v-model="drawerVisible"
    class="lk-maximum-drawer"
    size="83%"
    header-class="work-order-collection-header"
    body-class="work-order-collect-container"
    footer-class="create-work-order-drawer-footer"
    :before-close="handleBeforeCloseInquiry"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <template #header>
      <div class="drawer-header">
        <div class="drawer-header-left">
          {{ t("trade_order_editworkorder") }}_
          {{ workOrderCollectionData.workOrderName }}
          <span v-if="workOrderTodoState" class="drawer-header-tag">
            {{ t("trade_order_todo") }}
          </span>
        </div>
        <div class="drawer-header-right">
          <div class="drawer-header-btn-next-group">
            <div class="drawer-header-col">
              <i class="iconfont link-quote-third-party" />
              <span class="drawer-header-text">
                {{ t("trade_order_referenceOtherWorkOrders") }}
              </span>
            </div>
            <div class="drawer-header-col">
              <i class="iconfont link-quote-third-party" />
              <span class="drawer-header-text">
                {{ t("trade_order_DataReference") }}
              </span>
              <el-tooltip
                effect="dark"
                :content="t('trade_order_quoteTip')"
                placement="top"
                :showAfter="500"
              >
                <i class="iconfont link-explain" />
              </el-tooltip>
            </div>
          </div>
          <div class="drawer-header-btn-group">
            <el-tooltip :content="t('trade_order_emailCollaborate')">
              <div
                class="drawer-header-col"
                :class="{
                  'create-work-order-active': emailCoopFormVisible
                }"
                @click.stop="handleToggleEmailHeaderVisible"
              >
                <i class="iconfont link-youjianxiezuo" />
              </div>
            </el-tooltip>
            <el-tooltip :content="t('trade_common_orderMessage')">
              <div
                class="drawer-header-col"
                :class="{ 'create-work-order-active': mainFormVisible }"
                @click.stop="handleOpenMainFormDialog"
              >
                <i class="iconfont link-basic-info" />
              </div>
            </el-tooltip>
            <el-tooltip :content="t('trade_work_order_dynamics')">
              <div
                class="drawer-header-col"
                :class="{ 'create-work-order-active': dynamicVisible }"
                @click.stop="dynamicVisible = !dynamicVisible"
              >
                <i class="iconfont link-dynamics-wo" />
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <div
        class="drawer-container-left"
        :style="{ width: dynamicVisible ? '65%' : '100%' }"
      >
        <!-- <div class="drawer-container-top">
          <OrderDetailPersonnel :workOrder="workOrderCollectionData" />
        </div> -->
        <div class="drawer-container-main-body">
          <el-scrollbar class="horizontal-scrollbar-only" max-height="100%">
            <LkTrendsAggregationForm
              ref="TrendsAggregationFormRef"
              :formList="widgetWorkOrderFormData"
              :formWidgetData="orderWorkWidgetFormData"
              :sccsId="route.query.sccsId"
              :workOrderId="workOrderCollectionData.workOrderId"
              :operationalFactorData="operationalFactorData"
              :defaultValueActuatorFlag="workOrderCollectState"
              :workOrderAllowOperation="workOrderAllowOperation"
            >
              <template #collapseFormHeader="{ collapseData }">
                <div
                  style="display: inline-flex; margin-left: 20px"
                  @click.stop
                >
                  <LkAvatarGroupNext
                    :size="22"
                    mode="collect"
                    :avatarListGroup="
                      getWorkOrderFormOperateMember(collapseData.id)
                    "
                    :maxAvatar="4"
                  />
                </div>
              </template>
            </LkTrendsAggregationForm>
          </el-scrollbar>
        </div>
        <div class="drawer-container-footer">
          <el-button plain @click="handleClose">
            {{ t("trade_common_cancel") }}
          </el-button>
          <el-button
            type="primary"
            color="#0070d2"
            :disabled="btnOperationState"
            @click="handleSubmit"
          >
            {{ t("trade_common_confirm") }}
          </el-button>
        </div>
      </div>
      <div v-show="!!dynamicVisible" class="drawer-container-right">
        <OrderDynamicsCard
          type="WORK_ORDER"
          cardShadow="never"
          :noHeader="true"
          :currentWorkOrderInfo="workOrderCollectionData"
          :currentAllMembers="currentAllMembers"
          :workOrderForm="widgetWorkOrderFormData"
          :milestoneCardForm="workOrderReplyDynamicJsonList"
        />
      </div>
      <OrderMainFormHeader
        ref="OrderMainFormRef"
        :mainFormData="orderMainForm"
      />
      <OrderEmailCoopFormHeader
        ref="OrderEmailCoopFormRef"
        :emailFormData="emailFormData"
        @handleUpdateEmailQuoteData="handleUpdateEmailQuoteData"
        @handleUpdateTableData="handleUpdateTableData"
      />
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { computed, getCurrentInstance, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { cloneDeep, isEqual, storageLocal } from "@pureadmin/utils";
import { uniqWith } from "lodash-es";
import { getDefaultConfigWidgetData } from "@/utils/formulasActuator/formulasActuator";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";
import OrderDynamicsCard from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDynamicsCard.vue";
import OrderMainFormHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderMainFormHeader.vue";
import OrderEmailCoopFormHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderEmailCoopFormHeader.vue";
import {
  getEntireFormData,
  handleObtainDynamicDefaultValue,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";
import { getWorkOrderStructure, workOrderCollection } from "@/api/order";
import { emitter } from "@/utils/mitt";

interface RouteParamProp {
  sccsId: string;
  templateId: string;
}

const { t } = useI18n();
const route = useRoute();
const { proxy } = getCurrentInstance();
const userInfo: any = storageLocal().getItem("user-info");
const TrendsAggregationFormRef = ref<HTMLElement | any>(null);
const drawerVisible = ref<boolean>(false);
const btnOperationState = ref<boolean>(false);
const dynamicVisible = ref<boolean>(true);
const collectDisabled = ref<boolean>(false);
const workOrderCollectionData = ref<any>({});
const widgetWorkOrderFormData = ref<any>([]);
const orderWorkWidgetOriginalFormData = ref<any>({});
const orderWorkWidgetFormData = ref<any>({});
const workOrderCollectId = ref<string>("");
const workOrderReplyDynamicJsonList = ref<any>([]);
const orderMainForm = ref<any>({});
const operationalFactorData = ref<any>({});
const currentLinkedReferenceMap = ref<any>({});
const routeParams = ref<RouteParamProp>({
  sccsId: "",
  templateId: ""
});

// 当前用户可编辑的表单字段列表
const editableFormFieldList = ref<{ widgetId: string; widgetType: string }[]>(
  []
);

// 主表单引用和可见状态管理
const OrderMainFormRef = ref<HTMLElement | any>(null);
const mainFormVisible = ref<boolean>(false);
watch(
  () => OrderMainFormRef.value?.visible,
  (newVal: boolean) => {
    mainFormVisible.value = newVal;
  }
);

// 邮件协作表单引用和可见状态管理
const OrderEmailCoopFormRef = ref<HTMLElement | any>(null);
const emailCoopFormVisible = ref<boolean>(false);
watch(
  () => OrderEmailCoopFormRef.value?.visible,
  (newVal: boolean) => {
    emailCoopFormVisible.value = newVal;
  }
);

const getWorkOrderFormOperateMember = computed(() => {
  return formId => {
    const editUserList = workOrderCollectionData.value.editUserList;
    const operateMemberList = editUserList.filter(editUser =>
      editUser.assignedFormIdList.includes(formId)
    );
    return operateMemberList;
  };
});

const workOrderAllowOperation = computed(() => {
  const editUserList = workOrderCollectionData.value.editUserList;
  //@ts-ignore
  const { latestLoginTeamId, latestLoginTeamMemberId } =
    storageLocal().getItem("user-info");

  const operationEditUser = editUserList.find(
    editUser => editUser.teamMemberId === latestLoginTeamMemberId
  );

  const operationEditTeam = editUserList.find(
    editUser => editUser.teamId === latestLoginTeamId
  );
  return operationEditUser
    ? operationEditUser.assignedFormIdList
    : operationEditTeam
      ? operationEditTeam.assignedFormIdList
      : [];
});

// 邮件表单数据计算属性
const emailFormData = computed(() => {
  const sccsId = routeParams.value?.sccsId || route.query?.sccsId;

  if (!workOrderCollectionData.value || !sccsId) {
    return undefined;
  }

  return {
    workOrderId: workOrderCollectionData.value.workOrderId,
    milestoneId: workOrderCollectionData.value.msId,
    sccsId,
    worderName: workOrderCollectionData.value.workOrderName
  };
});

const emit = defineEmits(["handleUpdateCollection"]);

const workOrderCollectState = computed(() => {
  return orderWorkWidgetFormData.value.length === 0;
});

const workOrderTodoState = computed(() => {
  const editUserList = workOrderCollectionData.value.editUserList;
  if (!editUserList) {
    return false;
  }
  const userItem = editUserList.find(
    user => user.teamMemberId === userInfo.latestLoginTeamMemberId
  );

  return userItem ? !userItem.edited : false;
});

const currentAllMembers = computed(() => {
  let res = [];
  // 当前工单创建人
  const createUser = {
    label: workOrderCollectionData.value.createTeamMemberId,
    value: workOrderCollectionData.value.creatorUsername,
    avatar: workOrderCollectionData.value.creatorAvatar,
    coopTeamUser: workOrderCollectionData.value.coopTeamUser,
    email: workOrderCollectionData.value.creatorEmail,
    remindType: "person",
    shortName: ""
  };
  if (createUser.coopTeamUser) {
    createUser.shortName = workOrderCollectionData.value.createTeamShortName;
  }
  res.push(createUser);
  // 当前工单创建人所属团队
  res.push({
    label: workOrderCollectionData.value.createTeamId,
    value: workOrderCollectionData.value.createTeamName,
    avatar: workOrderCollectionData.value.createTeamAvatar,
    remindType: "team"
  });
  // 当前工单批复人以及所属团队
  workOrderCollectionData.value.replyUserList?.forEach(item => {
    if (item.user) {
      const replyUser = {
        label: item.teamMemberId,
        value: item.userName,
        avatar: item.userAvatar,
        coopTeamUser: item.coopTeamUser,
        email: item.email,
        remindType: "person",
        shortName: ""
      };
      if (replyUser.coopTeamUser) {
        replyUser.shortName = item.shortName;
      }
      res.push(replyUser);
    }
    res.push({
      label: item.teamId,
      value: item.teamName,
      avatar: item.teamAvatar,
      remindType: "team"
    });
  });
  // 当前工单采集人以及所属团队
  workOrderCollectionData.value.editUserList?.forEach(item => {
    if (item.user) {
      const editUser = {
        label: item.teamMemberId,
        value: item.userName,
        avatar: item.userAvatar,
        coopTeamUser: item.coopTeamUser,
        email: item.email,
        remindType: "person",
        shortName: ""
      };
      if (editUser.coopTeamUser) {
        editUser.shortName = item.shortName;
      }
      res.push(editUser);
    }
    res.push({
      label: item.teamId,
      value: item.teamName,
      avatar: item.teamAvatar,
      remindType: "team"
    });
  });
  res = uniqWith(res, (item1, item2) => {
    return item1.label === item2.label;
  });
  return res;
});

const open = async (
  milestoneData: any,
  mainFormData: any,
  collectId?: string,
  customParams?: RouteParamProp
) => {
  const { msId, workOrderId } = milestoneData;
  const { sccsId, orderId } = route.query;
  routeParams.value = customParams;
  workOrderCollectId.value = collectId ? collectId : (orderId as string);

  try {
    // 并行获取工单结构和默认配置数据
    const [workOrderStructureResp, defaultConfigResp] = await Promise.all([
      getWorkOrderStructure({
        sccsId: customParams?.sccsId || (sccsId as string),
        milestoneId: msId,
        orderId: workOrderCollectId.value
      }),
      getDefaultConfigWidgetData(workOrderCollectId.value, {
        sccsId,
        ...customParams
      })
    ]);

    // 解构响应数据
    const { workOrderFormList, mainForm, workOrderReplyForm } =
      workOrderStructureResp.data;
    const { milestoneGroupList } = (defaultConfigResp as any).orderData;

    // 设置表单数据
    widgetWorkOrderFormData.value = workOrderFormList;
    orderMainForm.value = mainFormData;
    workOrderReplyDynamicJsonList.value =
      workOrderReplyForm?.widgetJsonList || [];

    // 查找里程碑和工单数据
    const milestoneData = milestoneGroupList.find(
      milestone => milestone.msId === msId
    );
    const workOrderData = milestoneData?.workOrderGroupList.find(
      workOrder => workOrder.workOrderId === workOrderId
    );

    workOrderCollectionData.value = workOrderData;

    // 过滤隐藏的表单项
    if (workOrderData?.hiddenFormIdList?.length > 0) {
      widgetWorkOrderFormData.value = widgetWorkOrderFormData.value.filter(
        item => !workOrderData.hiddenFormIdList.includes(item.id)
      );
    }

    // 转换表单数据结构
    orderWorkWidgetFormData.value = TransformSubmitDataStructure(
      workOrderData.widgetList,
      workOrderData.subWidgetMap,
      workOrderData.linkedReferenceMap
    );
    currentLinkedReferenceMap.value = workOrderData.linkedReferenceMap;

    // 计算当前用户可编辑的表单字段
    const currentUserEditInfo = workOrderData.editUserList?.find(item =>
      item.userId
        ? item.userId === userInfo.id
        : item.teamId === userInfo.latestLoginTeamId
    );

    if (currentUserEditInfo?.assignedFormIdList) {
      const getAllWidgets = widgets => {
        const result = [];
        for (const widget of widgets) {
          result.push(widget);
          if (widget.children && widget.children.length > 0) {
            result.push(...getAllWidgets(widget.children));
          }
        }
        return result;
      };

      editableFormFieldList.value = workOrderFormList
        .filter(form =>
          currentUserEditInfo.assignedFormIdList.includes(form.id)
        )
        .flatMap(form =>
          (getAllWidgets(form.widgetJsonList || []) || []).map(widget => ({
            widgetId: widget._fc_id,
            widgetType: widget.type,
            formId: form.id
          }))
        )
        .filter(Boolean);
    }

    operationalFactorData.value = getEntireFormData(
      workOrderCollectionData.value.msId,
      workOrderCollectionData.value.workOrderId
    );

    orderWorkWidgetOriginalFormData.value = cloneDeep(
      orderWorkWidgetFormData.value
    );

    // 显示抽屉
    drawerVisible.value = true;
  } catch (error) {
    console.error("加载工单数据失败:", error);
    ElMessage({
      message: "加载工单数据失败，请稍后重试",
      type: "error"
    });
  }
};

const handleToggleEmailHeaderVisible = (): void => {
  OrderEmailCoopFormRef.value.toggle();
  OrderMainFormRef.value.close();
};

const handleOpenMainFormDialog = () => {
  OrderMainFormRef.value.handleOpenMainFormVisible();
  OrderEmailCoopFormRef.value.close();
};

const handleSubmit = async () => {
  const workOrderWidgetData =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(true);
  const linkedReferenceSourceList =
    TrendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  if (!workOrderWidgetData) {
    return false;
  }
  collectDisabled.value = true;

  const sccsIdString = route.query.sccsId || routeParams.value.sccsId;
  const { msId, workOrderId, version } = workOrderCollectionData.value;
  const { code } = await workOrderCollection({
    sccsId: sccsIdString,
    orderId: workOrderCollectId.value,
    msId: msId,
    workOrderId: workOrderId,
    version: version,
    widgetItemDataList: workOrderWidgetData,
    linkedReferenceSaveList: linkedReferenceSourceList
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    collectDisabled.value = false;
    drawerVisible.value = false;
    emit("handleUpdateCollection", workOrderId);
  }
};

const handleClose = (): void => {
  handleBeforeCloseInquiry(() => {
    OrderMainFormRef.value.close();
    OrderEmailCoopFormRef.value.close();
    drawerVisible.value = false;
    mainFormVisible.value = false;
  });
};

// 点击关闭前询问
const handleBeforeCloseInquiry = async (done: () => void) => {
  const workOrderWidgetData =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(false);

  const filteredWorkOrderWidgetData = workOrderWidgetData?.filter(item => {
    const originalItem = orderWorkWidgetOriginalFormData.value.find(
      original => original.widgetId === item.widgetId
    );
    return !originalItem || !isEqual(item.obj, originalItem.obj);
  });

  if (filteredWorkOrderWidgetData && filteredWorkOrderWidgetData.length > 0) {
    //@ts-ignore
    const result = await proxy.$submitMessageBox.confirm(
      t("trade_common_dataUpdateTip"),
      {
        confirmButtonText: t("trade_order_submitBtn"),
        cancelButtonText: t("trade_common_cancel"),
        nextButtonText: t("trade_order_unSubmit")
      }
    );
    if (result === "confirm") {
      handleSubmit();
    } else if (result === "nextConfirm") {
      done();
    }
  } else {
    done();
  }
};

/**
 * 处理邮件报价数据更新
 * @param data 包含组件数据列表、子组件映射和关联引用映射的对象
 */
const handleUpdateEmailQuoteData = (data: {
  widgetDataList: any[];
  widgetDataSubMap: any;
  linkedReferenceMap: any; // TODO 邮件协作的关联引用数据还没有实现
}) => {
  const { widgetDataList, widgetDataSubMap, linkedReferenceMap } = data;

  // 过滤可编辑字段并转换数据结构
  const filteredWidgetList = widgetDataList.filter(item =>
    editableFormFieldList.value.some(
      editableField => editableField.widgetId === item.widgetId
    )
  );

  // 构建引用数据结构
  const transformedData = TransformSubmitDataStructure(
    filteredWidgetList,
    widgetDataSubMap,
    linkedReferenceMap
  );

  const ARRAY_TYPE_FIELDS = [
    "DrRelateCard",
    "DrSCCSMemberSelect",
    "DrSCCSGroupMemberSelect",
    "DrSCCSMemberSingleSelect",
    "DrSCCSGroupMemberSingleSelect",
    "DrSCCSMemberMultipleSelect",
    "DrSCCSGroupMemberMultipleSelect"
  ];

  // 先清空可编辑的字段
  const result = [
    ...(orderWorkWidgetFormData.value.map(item => {
      const targetEditableField = editableFormFieldList.value.find(
        editableField => editableField.widgetId === item.widgetId
      );

      if (targetEditableField) {
        const newItem = {
          ...item,
          label: null,
          obj: ARRAY_TYPE_FIELDS.includes(targetEditableField.widgetType)
            ? []
            : null,
          showObj: ARRAY_TYPE_FIELDS.includes(targetEditableField.widgetType)
            ? []
            : null
        };
        if (targetEditableField.widgetType === "DrTableForm") {
          newItem.childrenList = [];
        }
        return newItem;
      }
      return item;
    }) || [])
  ];

  // 这是一个问题，如果子表没有数据 orderWorkWidgetFormData
  // 清空子表数据
  Object.values(editableFormFieldList.value).forEach(item => {
    if (
      item.widgetType === "DrTableForm" &&
      !result.some(resultItem => resultItem.widgetId === item.widgetId)
    ) {
      result.push({
        ...item,
        label: null,
        obj: null,
        showObj: null,
        childrenList: []
      });
    }
  });

  // 更新最新数据
  transformedData.forEach(newItem => {
    const existingIndex = result.findIndex(
      existing => existing.widgetId === newItem.widgetId
    );

    if (existingIndex !== -1) {
      result[existingIndex] = newItem;
    } else {
      result.push(newItem);
    }
  });

  // 优化处理关联引用数据
  Object.keys(currentLinkedReferenceMap.value).forEach(widgetId => {
    // 仅处理当前用户可编辑的字段
    if (
      !editableFormFieldList.value.some(field => field.widgetId === widgetId)
    ) {
      return;
    }

    // 查找当前 widget 的数据
    const widgetItem = transformedData.find(item => item.widgetId === widgetId);
    const widgetList = widgetItem?.obj || [];

    // 查找 widget 配置
    const targetWidgetConfigure = widgetWorkOrderFormData.value
      .flatMap(form => form.widgetJsonList)
      .find(widget => widget._fc_id === widgetId);

    // 生成 factorRelateCardData
    const factorRelateCardData = {
      [widgetId]: handleObtainDynamicDefaultValue(
        targetWidgetConfigure?.props?.relatedValue?.rules || [],
        widgetList
      )
    };

    // 获取 formId（如果有需要可以补充查找逻辑）
    const formId = "";

    // 判断操作类型
    const actionType = linkedReferenceMap[widgetId] ? "update" : "delete";

    // 调用保存方法
    TrendsAggregationFormRef.value.handleSaveLinkedReferenceSource(
      {
        sccsId: route.query.sccsId,
        orderId: route.query.orderId,
        workOrderId: workOrderCollectionData.value.workOrderId,
        sourceId: "",
        linkedId: widgetId,
        widgetList,
        factoryObject: {
          factoryWidgetId: widgetId,
          factoryData: factorRelateCardData,
          factoryFormData: formId
        }
      },
      actionType
    );
  });

  TrendsAggregationFormRef.value.handleUpdateFormData(result);
};

/**
 * 处理表格数据更新
 * @param data 包含组件ID和记录数组的对象
 */
const handleUpdateTableData = (data: { widgetId: string; records: any[] }) => {
  const { widgetId, records } = data;

  // 查找对应的表单ID
  const formId = widgetWorkOrderFormData.value.find(formItem =>
    formItem.widgetJsonList?.some(widget => widget._fc_id === widgetId)
  )?.formId;

  const result = [...(orderWorkWidgetFormData.value || [])];
  const existingIndex = result.findIndex(item => item.widgetId === widgetId);

  if (existingIndex > -1) {
    // 更新现有数据，合并子项列表
    result[existingIndex] = {
      ...result[existingIndex],
      childrenList: [...(result[existingIndex].childrenList || []), ...records]
    };
  } else {
    // 添加新数据项
    result.push({
      widgetId,
      formId,
      label: null,
      obj: null,
      childrenList: records
    });
  }

  orderWorkWidgetFormData.value = result;
};

onMounted(() => {
  emitter.on("widgetUploadLoading", (isUploading: boolean) => {
    btnOperationState.value = isUploading;
  });
});

defineExpose({
  open
});
</script>

<style lang="scss">
.work-order-collection-header {
  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .drawer-header-left {
      font-size: 18px;
      font-weight: bolder;
      line-height: 20px;
      color: #202020;
    }

    .drawer-header-right {
      display: flex;
      align-items: center;

      .drawer-header-btn-next-group {
        position: relative;
        display: flex;
        flex: 1;
        align-items: center;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        .drawer-header-col {
          padding: 3px 10px;
          cursor: pointer;
          border-radius: 4px;

          &:nth-child(2) {
            margin-right: 12px;
          }

          .iconfont {
            font-size: 13px;
            color: #595959;
          }

          .drawer-header-text {
            margin: 0 6px;
            font-size: 14px;
            color: #595959;
          }

          &:hover {
            background: #e5e5e5;
          }
        }
      }

      .drawer-header-btn-group {
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 5px;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        .drawer-header-col {
          width: 28px;
          height: 28px;
          margin: 0 4px;
          line-height: 28px;
          text-align: center;
          cursor: pointer;
          border-radius: 4px;

          .iconfont {
            font-size: 14px;
          }

          &.create-work-order-active {
            background: #e1edff;

            .iconfont {
              color: #0070d2;
            }
          }

          &:first-child {
            .iconfont {
              font-size: 16px;
            }
          }

          &:hover {
            background: #e5e5e5;
          }
        }

        &:nth-child(2) {
          margin-right: 10px;
        }
      }
    }
  }
}

.work-order-collect-container {
  display: flex;
  padding: 0;

  .drawer-container-left {
    position: relative;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e6eaf0;

    .drawer-container-top {
      display: flex;
      align-items: center;
      height: 45px;
      padding: 0 37px 10px;
    }

    .drawer-container-main-body {
      flex: 1;
      max-height: calc(100% - 60px);
      padding: 10px;
    }

    .drawer-container-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: end;
      width: 100%;
      height: 60px;
      padding: 0 18px;
      background: #fff;
      border-top: 1px solid #e6eaf0;
    }
  }

  .drawer-container-right {
    flex: 1;
  }
}
</style>
