import { cloneDeep, storageSession } from "@pureadmin/utils";
import { BigNumber } from "bignumber.js";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";

/**
 * 判断value的值不为空
 * @param value
 * @returns
 */
const isNotEmptyFunction = value => {
  const widgetValue = value instanceof Array ? value.join(",") : value;
  return !!widgetValue;
};

/**
 * 判断value的值为空
 * @param value
 * @returns
 */
const isEmptyFunction = value => {
  const widgetValue = value instanceof Array ? value.join(",") : value;
  return !widgetValue;
};

/**
 * 判断value是否包含在containList里面
 * @param value
 * @returns
 */
const isContainFunction = (value, containList) => {
  return value && containList.some(substring => value.includes(substring));
};

/**
 * 判断value是否不包含在containList里面
 * @param value
 * @returns
 */
const isNotContainFunction = (value, containList) => {
  if (value instanceof Array) {
    return !value.some(value => containList.includes(value));
  } else {
    return !containList.includes(value);
  }
};

/**
 * 判断value是否完全相等contrastValue
 * @param value
 * @returns
 */
const isEqualFunction = (value, contrastValue) => {
  const widgetValue = value instanceof Array ? value.join(",") : value;
  return widgetValue === contrastValue;
};

/**
 * 判断value是否完全不相等contrastValue
 * @param value
 * @returns
 */
const isNotEqualFunction = (value, contrastValue) => {
  const widgetValue = value instanceof Array ? value.join(",") : value;
  return widgetValue !== contrastValue;
};

/**
 * 判断value是否大于contrastValue
 * @param value
 * @returns
 */
const isGreaterThan = (value, contrastValue) => {
  return new BigNumber(value).isGreaterThan(new BigNumber(contrastValue));
};

/**
 * 判断value是否大于contrastValue
 * @param value
 * @returns
 */
const isGreaterThanOrEqualTo = (value, contrastValue) => {
  return new BigNumber(value).isGreaterThanOrEqualTo(
    new BigNumber(contrastValue)
  );
};

/**
 * 判断value是否小于contrastValue
 * @param value
 * @returns
 */
const isLessThan = (value, contrastValue) => {
  return new BigNumber(value).isLessThan(new BigNumber(contrastValue));
};

/**
 * 判断value是否小于等于contrastValue
 * @param value
 * @returns
 */
const isLessThanOrEqualTo = (value, contrastValue) => {
  return new BigNumber(value).isLessThanOrEqualTo(new BigNumber(contrastValue));
};

/**
 * 判断value是否等于contrastValue
 * @param value
 * @returns
 */
const isEqualTo = (value, contrastValue) => {
  return new BigNumber(value).isEqualTo(new BigNumber(contrastValue));
};

/**
 * 判断value是否不等于contrastValue
 * @param value
 * @returns
 */
const isNotEqualTo = (value, contrastValue) => {
  return !new BigNumber(value).isEqualTo(new BigNumber(contrastValue));
};

/**
 * 日期比较函数
 * @param firstDate 开始日期
 * @param secondDate 结束日期
 * @param judgeSign 枚举值：早于，晚于，等于
 */
const dateCompareFunction = (firstDate, secondDate, judgeSign) => {
  const firstTime = new Date(
    dayjs(firstDate).format("YYYY-MM-DD 00:00:00")
  ).getTime();

  const secondTime = new Date(
    dayjs(secondDate).format("YYYY-MM-DD 00:00:00")
  ).getTime();

  if (judgeSign === "BEFORE") {
    // 早于
    return firstTime > secondTime;
  } else if (judgeSign === "AFTER") {
    // 晚于
    return firstTime < secondTime;
  } else if (judgeSign === "EQ") {
    // 等于
    return firstTime === secondTime;
  }
};

/**
 * 获取比较后的日期
 * @param operator 比较运算符
 * @param dateValue 日期
 * @param dateType 日期类型
 * @returns
 */
const calculateDate = (operator, dateValue, dateType) => {
  if (operator === "ASSIGN") {
    return dateValue;
  } else if (operator === "BEFORE") {
    return dayjs().subtract(dateValue, dateType).format("YYYY-MM-DD");
  } else if (operator === "AFTER") {
    return dayjs().add(dateValue, dateType).format("YYYY-MM-DD");
  } else if (operator === "TODAY") {
    return dayjs();
  } else if (operator === "YESTERDAY") {
    return dayjs().subtract(1, "day").format("YYYY-MM-DD");
  } else if (operator === "TOMORROW") {
    return dayjs().add(1, "day").format("YYYY-MM-DD");
  } else if (operator === "THIS_MONTH") {
    return dayjs().startOf("month").format("YYYY-MM-DD");
  } else if (operator === "LAST_MONTH") {
    return dayjs().subtract(1, "month").startOf("month").format("YYYY-MM-DD");
  } else if (operator === "NEXT_MONTH") {
    return dayjs().add(1, "month").startOf("month").format("YYYY-MM-DD");
  } else if (operator === "THIS_YEAR") {
    return dayjs().startOf("year").format("YYYY-MM-DD");
  } else if (operator === "LAST_YEAR") {
    return dayjs().subtract(1, "year").startOf("year").format("YYYY-MM-DD");
  } else if (operator === "NEXT_YEAR") {
    return dayjs().add(1, "year").startOf("year").format("YYYY-MM-DD");
  }
};

const getRangeDate = (rangeType, dateValue, dateType) => {
  if (["THIS_WEEK", "LAST_WEEK", "NEXT_WEEK"].includes(rangeType)) {
    let date;
    if (rangeType === "THIS_WEEK") {
      date = dayjs();
    } else if (rangeType === "LAST_WEEK") {
      date = dayjs().subtract(1, "week");
    } else if (rangeType === "NEXT_WEEK") {
      date = dayjs().add(1, "week");
    }
    return [
      date.startOf("week").format("YYYY-MM-DD"),
      date.endOf("week").format("YYYY-MM-DD")
    ];
  } else if (["THIS_MONTH", "LAST_MONTH", "NEXT_MONTH"].includes(rangeType)) {
    let date;
    if (rangeType === "THIS_MONTH") {
      date = dayjs();
    } else if (rangeType === "LAST_MONTH") {
      date = dayjs().subtract(1, "month");
    } else if (rangeType === "NEXT_MONTH") {
      date = dayjs().add(1, "month");
    }
    return [
      date.startOf("month").format("YYYY-MM-DD"),
      date.endOf("month").format("YYYY-MM-DD")
    ];
  } else if (["THIS_YEAR", "LAST_YEAR", "NEXT_YEAR"].includes(rangeType)) {
    let date;
    if (rangeType === "THIS_YEAR") {
      date = dayjs();
    } else if (rangeType === "LAST_YEAR") {
      date = dayjs().subtract(1, "year");
    } else if (rangeType === "NEXT_YEAR") {
      date = dayjs().add(1, "year");
    }
    return [
      date.startOf("year").format("YYYY-MM-DD"),
      date.endOf("year").format("YYYY-MM-DD")
    ];
  } else if (["LAST", "NEXT"].includes(rangeType)) {
    let date;
    if (rangeType === "LAST") {
      date = dayjs().subtract(dateValue, dateType).format("YYYY-MM-DD");
      return [date, dayjs().format("YYYY-MM-DD")];
    } else if (rangeType === "NEXT") {
      date = dayjs().add(dateValue, dateType).format("YYYY-MM-DD");
      return [dayjs().format("YYYY-MM-DD"), date];
    }
  }
};

export const getNestedValue = (obj, path) => {
  return path.reduce(
    (currentObj, key) =>
      currentObj !== null && currentObj !== undefined
        ? currentObj[key]
        : undefined,
    obj
  );
};

export const ExecuteFormVisibleExpression = (conditions, conditionsData) => {
  let conditionFlag = false;
  let conditionList = [];

  for (let perhapsCondition of conditions) {
    // 或者条件
    let perhapsFlag = false;
    let perhapsList = [];
    for (let andCondition of perhapsCondition) {
      const conditionEntireDetail = cloneDeep(conditionsData.entireDetail);
      const fieldType = andCondition.firstDataObject.fieldType;

      if (
        ["DrCheckbox", "DrRadio"].includes(fieldType) &&
        conditionsData.checkboxList
      ) {
        const entireCheckboxList = conditionsData.checkboxList;
        const firstDataValue =
          conditionsData.entireDetail[andCondition.firstData[1]][
            andCondition.firstDataObject.value
          ];

        const entireValue = firstDataValue
          ? firstDataValue instanceof Array
            ? firstDataValue
            : firstDataValue.split(",")
          : "";

        const entireOptions =
          entireCheckboxList.find(
            checkbox => checkbox._fc_id === andCondition.firstDataObject.value
          )?.props.options || [];

        const entireValueData = entireValue
          ? entireValue.map(entireItemValue => {
              const item = entireOptions.find(
                option =>
                  option.label === entireItemValue ||
                  option.value === entireItemValue
              );
              return item ? item.label : entireItemValue;
            })
          : [];

        let entireDeepObject = {};
        let childEntireDeepObject = {};
        childEntireDeepObject[andCondition.firstDataObject.value] =
          entireValueData;
        entireDeepObject[andCondition.firstData[1]] = childEntireDeepObject;
        Object.assign(conditionEntireDetail, entireDeepObject);
      }

      const firstDataValue = getNestedValue(
        conditionEntireDetail,
        andCondition.firstData.slice(1)
      );

      const entireOptions =
        conditionsData.checkboxList.find(
          checkbox => checkbox._fc_id === andCondition.firstDataObject.value
        )?.props.options || [];

      let secondDataValue = ["DrCheckbox", "DrRadio"].includes(fieldType)
        ? andCondition.value
            ?.split(",")
            .map(
              andConditionValue =>
                entireOptions.find(option => option.value === andConditionValue)
                  ?.label
            )
            .join(",")
        : andCondition.value;

      if (andCondition.twoData.length > 0) {
        secondDataValue = conditionsData[andCondition.firstData.slice(-1)[0]];
      }

      // 并且条件
      if (fieldType === "DrDatePicker") {
        // 1.日期类型为区间的类型
        if (andCondition.operator === "BETWEEN") {
          let rangeDates;
          if (andCondition.rangeDateType === "DATE_RANGE") {
            //@ts-ignore
            rangeDates = secondDataValue.split(",");
          } else {
            rangeDates = getRangeDate(
              andCondition.rangeDateType,
              secondDataValue,
              andCondition.dateType
            );
          }
          dayjs.extend(isBetween);

          perhapsList.push(
            dayjs(firstDataValue).isBetween(
              dayjs(rangeDates[0]),
              dayjs(rangeDates[1]),
              null,
              "()"
            )
          );
        } else if (
          !["BETWEEN", "IS_NULL", "IS_NOT_NULL"].includes(andCondition.operator)
        ) {
          // 2.日期的类型为值对比
          const secondDateTime = calculateDate(
            andCondition.customerDateType,
            secondDataValue,
            andCondition.dateType
          );
          perhapsList.push(
            dateCompareFunction(
              firstDataValue,
              secondDateTime,
              andCondition.operator
            )
          );
        } else {
          // 3.日期类型对比null IS_NOT_NULL
          andCondition.operator === "IS_NULL"
            ? perhapsList.push(isEmptyFunction(firstDataValue))
            : perhapsList.push(isNotEmptyFunction(firstDataValue));
        }
      } else if (
        ["DrInputNumber", "DrRate", "DrPercentage"].includes(fieldType)
      ) {
        if (andCondition.operator === "EQ") {
          perhapsList.push(isEqualTo(firstDataValue, secondDataValue));
        } else if (andCondition.operator === "NE") {
          perhapsList.push(isNotEqualTo(firstDataValue, secondDataValue));
        } else if (andCondition.operator === "LT") {
          perhapsList.push(isLessThan(firstDataValue, secondDataValue));
        } else if (andCondition.operator === "LE") {
          perhapsList.push(
            isLessThanOrEqualTo(firstDataValue, secondDataValue)
          );
        } else if (andCondition.operator === "GT") {
          perhapsList.push(isGreaterThan(firstDataValue, secondDataValue));
        } else if (andCondition.operator === "GE") {
          perhapsList.push(
            isGreaterThanOrEqualTo(firstDataValue, secondDataValue)
          );
        } else if (andCondition.operator === "IS_NULL") {
          perhapsList.push(isEmptyFunction(secondDataValue));
        } else if (andCondition.operator === "IS_NOT_NULL") {
          perhapsList.push(isNotEmptyFunction(secondDataValue));
        }
      } else {
        if (andCondition.operator === "EQ") {
          perhapsList.push(isEqualFunction(firstDataValue, secondDataValue));
        } else if (andCondition.operator === "NE") {
          perhapsList.push(isNotEqualFunction(firstDataValue, secondDataValue));
        } else if (andCondition.operator === "IN") {
          perhapsList.push(
            //@ts-ignore
            isContainFunction(firstDataValue, secondDataValue.split(","))
          );
        } else if (andCondition.operator === "NOT_IN") {
          perhapsList.push(
            //@ts-ignore
            isNotContainFunction(firstDataValue, secondDataValue.split(","))
          );
        } else if (andCondition.operator === "IS_NULL") {
          perhapsList.push(isEmptyFunction(firstDataValue));
        } else if (andCondition.operator === "IS_NOT_NULL") {
          perhapsList.push(isNotEmptyFunction(firstDataValue));
        }
      }
    }
    perhapsFlag = perhapsList.reduce((acc, value) => acc && value, true);
    conditionList.push(perhapsFlag);
  }
  conditionFlag = conditionList.reduce((acc, value) => acc || value, false);
  return conditionFlag;
};

/**
 * 表单显隐执行器，仅执行本表单的表单显隐
 * @param formId 表单id
 * @param formVisibleObject 整个表单的数据
 */
export const calculateFormVisibleExpression = (
  formId,
  formVisibleObject,
  formVisibleConditions
) => {
  const widgetDataCache = formVisibleConditions
    ? { formVisibleTemplateFormConfigure: formVisibleConditions }
    : storageSession().getItem("widgetData") || {};

  if (Object.keys(widgetDataCache).length === 0) {
    return {};
  } else {
    //@ts-ignore
    const { formVisibleTemplateFormConfigure } = widgetDataCache;
    const needExecuteList = formVisibleTemplateFormConfigure.filter(
      formConfigure => formConfigure.formId === formId
    );
    let formVisibleTrendsObject = {};

    if (needExecuteList.length === 0) {
      return {};
    }

    for (let formVisibleConfigureItem of needExecuteList) {
      const formVisibleBoolean = ExecuteFormVisibleExpression(
        formVisibleConfigureItem.conditions,
        formVisibleObject
      );
      for (let widgetId of formVisibleConfigureItem.widgetIdList) {
        formVisibleTrendsObject[widgetId] = formVisibleBoolean;
      }
    }
    return formVisibleTrendsObject;
  }
};

export const calculateFormBetweenVisibleExpression = formVisibleObject => {
  const widgetDataCache = storageSession().getItem("widgetData") || {};
  if (Object.keys(widgetDataCache).length === 0) {
    return {};
  }
  {
    //@ts-ignore
    const { formVisibleTemplateFormConfigure } = widgetDataCache;
    let formVisibleTrendsObject = {};
    for (let formVisibleConfigureItem of formVisibleTemplateFormConfigure) {
      const formVisibleBoolean = ExecuteFormVisibleExpression(
        formVisibleConfigureItem.conditions,
        formVisibleObject
      );
      for (let widgetId of formVisibleConfigureItem.widgetIdList) {
        formVisibleTrendsObject[widgetId] = formVisibleBoolean;
      }
    }
    return formVisibleTrendsObject;
  }
};

export const calculateVisibleFormList = (
  widgetJsonList,
  formVisibleTrendsObject
) => {
  let widgetList = [];
  for (let widget of widgetJsonList) {
    if (widget.type === "DrCard") {
      let childrenList = [];
      for (let widgetChild of widget.children) {
        if (!widgetChild.children) {
          childrenList.push(widgetChild);
          continue;
        }
        for (let widgetChildren of widgetChild.children) {
          if (
            !formVisibleTrendsObject.hasOwnProperty(widgetChildren._fc_id) ||
            (formVisibleTrendsObject.hasOwnProperty(widgetChildren._fc_id) &&
              formVisibleTrendsObject[widgetChildren._fc_id])
          ) {
            const index = childrenList.findIndex(
              child => child._fc_id === widgetChild._fc_id
            );
            index === -1
              ? childrenList.push(widgetChild)
              : childrenList.splice(index, 1, widgetChild);
          } else {
            const index = childrenList.findIndex(
              child => child._fc_id === widgetChild._fc_id
            );
            index === -1
              ? childrenList.push(
                  Object.assign(widgetChild, {
                    children: []
                  })
                )
              : childrenList.splice(
                  index,
                  1,
                  Object.assign(widgetChild, {
                    children: []
                  })
                );
          }
        }
      }
      widgetList.push(
        Object.assign(widget, {
          children: childrenList
        })
      );
    } else {
      if (
        !formVisibleTrendsObject.hasOwnProperty(widget._fc_id) ||
        (formVisibleTrendsObject.hasOwnProperty(widget._fc_id) &&
          formVisibleTrendsObject[widget._fc_id])
      ) {
        widgetList.push(widget);
      }
    }
  }
  return widgetList;
};
