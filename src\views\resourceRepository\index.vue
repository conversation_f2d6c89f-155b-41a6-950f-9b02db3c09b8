<script setup lang="ts">
import { ref } from "vue";
import emptyImage from "@/assets/images/common/development.png";
import { useI18n } from "vue-i18n";

defineOptions({
  name: "resource"
});

const { t } = useI18n();
</script>

<template>
  <div class="resource-repository-empty-container">
    <el-empty
      :image="emptyImage"
      :description="t('trade_common_development')"
      :image-size="342"
    />
  </div>
</template>

<style lang="scss" scoped>
.resource-repository-empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  :deep(.el-empty__description) {
    p {
      font-size: 16px;
    }
  }
}
</style>
