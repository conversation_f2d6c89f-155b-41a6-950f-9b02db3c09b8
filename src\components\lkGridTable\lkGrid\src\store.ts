import { defineStore } from "pinia";
import { ref } from "vue";

export const useLkGridStore = defineStore("lkGrid", () => {
  const referencedDataForSubTable = ref<Record<string, any>>(undefined);

  function setReferencedDataForSubTable(data: Record<string, any>) {
    referencedDataForSubTable.value = data;
  }
  function clearReferencedDataForSubTable() {
    referencedDataForSubTable.value = undefined;
  }

  return {
    referencedDataForSubTable,
    setReferencedDataForSubTable,
    clearReferencedDataForSubTable
  };
});
