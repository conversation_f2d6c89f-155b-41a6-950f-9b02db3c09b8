<template>
  <ListTable
    ref="tableInstanceRef"
    :options="option"
    :style="{ height: `${height}px`, 'z-index': 1000 }"
  />
</template>
<script lang="tsx" setup>
import { computed, h, markRaw, onMounted, ref } from "vue";
import dayjs from "dayjs";
import { VTable } from "@visactor/vue-vtable";
import { SearchComponent } from "@visactor/vtable-search";
import { ElRate } from "element-plus";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";
import { TableOperateProp } from "@/types/type.d";
import { dict } from "@/utils/dict";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import FileColumn from "../../components/FileColumns";
import TableOperate from "../../components/TableOperates";
import StateColumn from "../../components/StateColumns";
import AvatarGroup from "../../components/AvatarGroup";
import TagColumn from "../../components/TagColumn";
import ImageListColumn from "../../components/ImageListColumn";

const props = defineProps({
  milestoneIdList: {
    type: Array as PropType<any>,
    default: () => []
  },
  height: {
    type: Number as PropType<number>,
    default: 0
  },
  tableOpeateLists: {
    type: Object as PropType<TableOperateProp[]>,
    default: () => {}
  },
  presentView: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const fixedNumber = ref<number>(0);
const tableInstanceRef = ref<HTMLElement | any>(null);
const tableColumns = ref<any>([]);
const tableRecord = ref<any>([]);
const tableSelectedRecords = ref<any[]>([]);
const subTable = ref<any>({
  subOrderFields: [],
  orderId: "",
  tableWidgetId: ""
});

const tableInstance: any = computed(() => {
  return tableInstanceRef.value.vTableInstance;
});

const searchInstance: any = computed(() => {
  return new SearchComponent({
    table: tableInstanceRef.value.vTableInstance,
    skipHeader: true,
    autoJump: true // 搜索完成后是否自动跳转到搜索结果的第一条
  });
});

const COLOR_LIST = [
  "9999ff",
  "ffffcc",
  "00ffff",
  "FF8080",
  "CCCCFF",
  "CCFFFF",
  "CCFFCC",
  "FFFF00",
  "99CCFF",
  "FF99CC",
  "CC99FF",
  "FFCC99",
  "33CCCC",
  "99CC00",
  "FFCC00",
  "FF9900"
];

const option = markRaw({
  records: [],
  columns: [],
  defaultHeaderRowHeight: 36,
  defaultRowHeight: 40,
  columnWidthComputeMode: "only-body",
  limitMaxAutoWidth: 300,
  frozenColCount: 1,
  rightFrozenColCount: 0,
  hover: {
    highlightMode: "row",
    disableHover: true
  },
  select: {
    highlightMode: "row",
    disableSelect: true
  },
  customConfig: {
    createReactContainer: true
  },
  resize: {
    columnResizeMode: "header"
  },
  theme: {
    headerStyle: {
      color: "#797979",
      fontSize: "13",
      fontWeight: "bolder",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    bodyStyle: {
      color: "#262626",
      fontSize: "13",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    scrollStyle: {
      visible: "always",
      hoverOn: false,
      width: 10,
      scrollRailColor: "#fbfbfb",
      scrollSliderColor: "#d3d3d3"
    },
    buttonStyle: {
      buttonColor: "transparent",
      buttonBorderColor: "transparent",
      buttonHoverColor: "transparent",
      buttonHoverBorderColor: "transparent"
    }
  }
});

const columnSlots = ref<any>({
  STATE: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);
      const tableRow = tableInstance.value.getCellOriginRecord(col, row);

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const statusList = dict.getDictByCode("milestone_status");
      const stateItem = statusList.find(
        status => status.value === tableRow[fieldName]
      );

      const container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //@ts-ignore
        vue: {
          element: h(StateColumn, {
            content: value,
            orderText: stateItem?.label
          }),
          container: table.bodyDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  LABELTAG: {
    customRender(args) {
      if (args.row === 0 || args.col === 0) return null;
      const { width } = args.rect;
      const { value } = args;
      const elements = [];
      let top = 30;
      let maxWidth = 0;
      elements.push({
        type: "rect",
        fill: value.style,
        x: 15,
        y: 10,
        radius: 10,
        width: width - 30,
        height: 18
      });
      elements.push({
        type: "text",
        fill: "#262626",
        fontSize: 12,
        textBaseline: "middle",
        text: value.labelValue,
        textAlign: "center",
        width: width - 30,
        height: 18,
        x: 15 + (width - 30) / 2,
        y: 19
      });
      return {
        elements,
        expectedHeight: top + 20,
        expectedWidth: maxWidth + 20
      };
    }
  },
  USERINFO: {
    customRender(args) {
      if (args.row === 0 || args.col === 0) return null;
      const elements = [];
      let top = 30;
      let maxWidth = 0;

      const {
        creatorAvatar,
        creatorUsername,
        updaterUsername,
        orderCreatorAvatar,
        orderCreateTime,
        orderCreatorUsername,
        createTime,
        updaterAvatar,
        updateTime,
        orderUpdaterAvatar,
        orderUpdateTime,
        orderUpdaterUsername
      } = tableInstance.value.getCellOriginRecord(args.col, args.row);

      let avatar = "";
      let timeStamp = "";
      let username = "";
      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      if (fieldName === "orderCreateInfo") {
        avatar =
          props.presentView.viewType === "ORDER"
            ? creatorAvatar
            : orderCreatorAvatar;
        timeStamp =
          props.presentView.viewType === "ORDER" ? createTime : orderCreateTime;
        username =
          props.presentView.viewType === "ORDER"
            ? creatorUsername
            : orderCreatorUsername;
      } else if (fieldName === "orderUpdateInfo") {
        avatar =
          props.presentView.viewType === "ORDER"
            ? updaterAvatar
            : orderUpdaterAvatar;
        timeStamp =
          props.presentView.viewType === "ORDER" ? updateTime : orderUpdateTime;
        username =
          props.presentView.viewType === "ORDER"
            ? updaterUsername
            : orderUpdaterUsername;
      } else if (fieldName === "workOrderCreateInfo") {
        avatar = creatorAvatar;
        timeStamp = createTime;
        username = creatorUsername;
      }

      if (avatar) {
        elements.push({
          type: "image",
          shape: "circle",
          src: avatar,
          width: 24,
          height: 24,
          x: 20,
          y: 8
        });
      } else {
        elements.push({
          type: "circle",
          radius: 12,
          fill: "#0070d2",
          x: 32,
          y: 18
        });
        elements.push({
          type: "text",
          fill: "#ffffff",
          fontSize: 12,
          textBaseline: "middle",
          text: username?.slice(0, 1),
          width: 50,
          height: 24,
          x: 26,
          y: 18
        });
      }

      elements.push({
        type: "text",
        fill: "#595959",
        fontSize: 12,
        textBaseline: "middle",
        text: dayjs(new Date(timeStamp)).format("YYYY-MM-DD HH:mm:ss"),
        width: 50,
        height: 24,
        x: 50,
        y: 18
      });

      return {
        elements,
        expectedHeight: top + 20,
        expectedWidth: maxWidth + 20
      };
    }
  },
  USERIMAGE: {
    customRender(args) {
      if (args.row === 0 || args.col === 0) return null;
      const elements = [];
      let top = 30;
      let maxWidth = 0;

      const tableRow = tableInstance.value.getCellOriginRecord(
        args.col,
        args.row
      );

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const widgetObject = tableRow?.widgetList.find(
        widget => widget.widgetId === fieldName
      );

      if (fieldName.indexOf("manager") === -1) {
        if (widgetObject && widgetObject.showObj.length > 0) {
          widgetObject.showObj.map((widgetItem, widgetIndex) => {
            if (widgetItem.avatar) {
              elements.push({
                type: "image",
                shape: "circle",
                src: widgetItem.avatar,
                width: 24,
                height: 24,
                x: 20 + widgetIndex * 30,
                y: 8
              });
            } else {
              elements.push({
                type: "circle",
                radius: 12,
                fill: "#0070d2",
                x: 32 + widgetIndex * 30,
                y: 18
              });
              elements.push({
                type: "text",
                fill: "#ffffff",
                fontSize: 12,
                textBaseline: "middle",
                text: widgetItem.name.slice(0, 1),
                width: 50,
                height: 24,
                x: 26 + widgetIndex * 30,
                y: 18
              });
            }
          });
        }
      } else {
        if (
          !tableInstance.value.getCellOriginRecord(args.col, args.row)[
            fieldName
          ]
        ) {
          return;
        }
        const { avatar, username, email } =
          tableInstance.value.getCellOriginRecord(args.col, args.row)[
            fieldName
          ];
        if (username && email) {
          if (avatar) {
            elements.push({
              type: "image",
              shape: "circle",
              src: avatar,
              width: 24,
              height: 24,
              x: 20,
              y: 8
            });
          } else {
            elements.push({
              type: "circle",
              radius: 12,
              fill: "#0070d2",
              x: 32,
              y: 18
            });
            elements.push({
              type: "text",
              fill: "#ffffff",
              fontSize: 12,
              textBaseline: "middle",
              text: username.slice(0, 1),
              width: 50,
              height: 24,
              x: 26,
              y: 18
            });
          }
        }
      }

      return {
        elements,
        expectedHeight: top + 20,
        expectedWidth: maxWidth + 20
      };
    }
  },
  RATE: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //@ts-ignore
        vue: {
          element: h(ElRate, {
            modelValue: value ? parseFloat(value) : 0,
            disabled: true,
            max: value ? parseFloat(value) : 0
          }),
          container: table.bodyDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  IMAGE: {
    customRender(args) {
      if (args.row === 0 || args.col === 0) return null;
      const { width } = args.rect;
      const { value } = args;
      const elements = [];
      let top = 30;
      let maxWidth = 0;

      if (!(value instanceof Array)) {
        elements.push({
          type: "image",
          src: value,
          width: width - 30,
          height: 32,
          x: 20,
          y: 4
        });
      }

      return {
        elements,
        expectedHeight: top + 20,
        expectedWidth: maxWidth + 20
      };
    }
  },
  SELECTION: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const regex =
        /<span\s[^>]*?style\s*=\s*["'][^>]*?background:\s*([^;>"']+)[^>]*>([^<]+)<\/span>/gi;

      const matches = [];
      let match;
      while ((match = regex.exec(value)) !== null) {
        const [, bgValue, content] = match;
        matches.push({ bgValue, content });
      }

      let container;
      if (matches instanceof Array && matches.length > 0) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          flexWrap: "wrap",
          justifyContent: "center",
          alignItems: "center",
          //@ts-ignore
          vue: {
            element: h(TagColumn, {
              tagList: matches
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  FILEUPLOAD: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      if (value && value instanceof Array) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(FileColumn, {
              filePreviewList: value,
              fileList: value
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  IMAGELIST: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      if (value && value instanceof Array) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(ImageListColumn, {
              imageList: value
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  AVATARGROUP: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const tableData = tableInstance.value.getCellOriginRecord(
        args.col,
        args.row
      );

      let avatarListData = value;
      if (fieldName === "workOrderReplyInfo") {
        avatarListData = tableData?.workOrderUserInfo?.replyUserList;
      } else if (fieldName === "workOrderCollectInfo") {
        avatarListData = tableData?.workOrderUserInfo?.editUserList;
      }

      let container;
      if (
        avatarListData &&
        avatarListData instanceof Array &&
        avatarListData.length > 0
      ) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(LkAvatarGroupNext, {
              avatarListGroup: avatarListData,
              size: 26
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  AVATARCUSTOMGROUP: {
    customLayout: args => {
      const { table, row, col, rect } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const tableData = tableInstance.value.getCellOriginRecord(
        args.col,
        args.row
      );

      const widgetObject = tableData?.widgetList.find(
        widget => widget.widgetId === fieldName
      );

      let container;
      if (
        widgetObject &&
        widgetObject.showObj &&
        widgetObject.showObj.length > 0
      ) {
        const avatarListData = widgetObject.showObj.map(widget => {
          return Object.assign(widget, {
            coopTeamUser: widgetObject.widgetType !== "MULTIPLE_COOP_TEAM",
            edited: false,
            user: widgetObject.widgetType !== "MULTIPLE_COOP_TEAM"
          });
        });
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(AvatarGroup, {
              avatarListGroup: avatarListData,
              size: 26
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  }
});

const handleListTableSearch = (keyword: string) => {
  const searchResult = searchInstance.value.search(keyword);
  return searchResult;
};

const handleRenderColumnsInfo = (columns: any[]) => {
  tableColumns.value = columns;
  let fieldColumns = [
    {
      field: "isCheck",
      title: "",
      width: 60,
      headerType: "checkbox",
      cellType: "checkbox"
    }
  ];
  for (let column of columns) {
    let color = "";
    if (
      ["MILESTONE", "MS_REPLY", "MS_REPLY_WIDGET", "MS_LABEL"].includes(
        column.type
      )
    ) {
      const index = props.milestoneIdList.findIndex(
        milestone => milestone === column.msId
      );
      color = COLOR_LIST[index % 16];
    }

    let fieldObject: any = {};
    if (column.widgetType === "RATE") {
      fieldObject = columnSlots.value["RATE"];
    } else if (column.widgetType === "FILE_UPLOAD") {
      fieldObject = columnSlots.value["FILEUPLOAD"];
    } else if (column.widgetType === "IMAGE_UPLOAD") {
      fieldObject = columnSlots.value["IMAGELIST"];
    } else if (["RADIO", "CHECKBOX"].includes(column.widgetType)) {
      fieldObject = columnSlots.value["SELECTION"];
    } else if (["SIGNATURE"].includes(column.widgetType)) {
      fieldObject = columnSlots.value["IMAGE"];
    } else if (column.type === "ORDER_LABEL" || column.type === "MS_LABEL") {
      fieldObject = columnSlots.value["LABELTAG"];
    } else if (
      (column.type === "ORDER_SPECIAL" && column.name === "orderState") ||
      (column.type === "MILESTONE" && column.shortName === "status") ||
      (column.type === "MILESTONE" && column.name === "status")
    ) {
      fieldObject = columnSlots.value["STATE"];
    } else if (
      ["orderCreateInfo", "orderUpdateInfo", "workOrderCreateInfo"].includes(
        column.name
      )
    ) {
      fieldObject = columnSlots.value["USERINFO"];
    } else if (
      ["MEMBER", "COOP_TEAM"].includes(column.widgetType) ||
      column.shortName === "manager" ||
      column.name === "manager"
    ) {
      fieldObject = columnSlots.value["USERIMAGE"];
    } else if (
      column.type === "MS_REPLY" ||
      column.name.indexOf("replyInfo") > -1 ||
      ["workOrderReplyInfo", "workOrderCollectInfo"].includes(column.name)
    ) {
      fieldObject = columnSlots.value["AVATARGROUP"];
    } else if (
      ["MULTIPLE_COOP_TEAM", "MULTIPLE_MEMBER"].includes(column.widgetType)
    ) {
      fieldObject = columnSlots.value["AVATARCUSTOMGROUP"];
    }

    fieldColumns.push(
      Object.assign(
        {
          field: column.name,
          title: column.label,
          width: ![
            "orderCreateInfo",
            "orderUpdateInfo",
            "workOrderCreateInfo"
          ].includes(column.name)
            ? column.width
            : 190,
          hide: !column.show,
          headerStyle: {
            bgColor: !!column.parentsName ? "#FFF1E4" : "#FFF"
          },
          style: {
            bgColor: !!column.parentsName ? "#FFF1E4" : "#FFF"
          },
          fieldFormat: record => {
            if (column.widgetType === "PERCENT") {
              return record[column.name] ? `${record[column.name]}%` : "";
            }
            return record[column.name];
          }
        },
        fieldObject
      )
    );
  }
  let operate: any = {
    field: "operate",
    title: "",
    width: 68,
    cellType: "text",
    customLayout: args => {
      const { table, row, col, rect } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);
      const tableRow = tableInstance.value.getCellOriginRecord(col, row);

      let container;
      if (props.tableOpeateLists) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(TableOperate, {
              tableOpeateLists: props.tableOpeateLists,
              row: tableRow,
              onHandleOperate: (slotName: string, data: any) =>
                handleOperate(slotName, data)
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  };
  fieldColumns.push(operate);
  fixedNumber.value = columns.filter(column => column.fixed).length + 1;
  tableInstance.value.updateOption(
    Object.assign(option, {
      columns: fieldColumns,
      frozenColCount: fixedNumber.value
    })
  );
};

const emit = defineEmits([
  "handleOperate",
  "handleDblClick",
  "handleOrderViewUndergo",
  "handleGetCheckboxRecords"
]);

const handleOperate = (slotName: string, data: any) => {
  const { row } = data;
  emit("handleOperate", { slot: slotName, row: row });
};

const handleJumpTableColumn = (type: string) => {
  if (type === "next") {
    return searchInstance.value.next();
  } else if (type === "prev") {
    return searchInstance.value.prev();
  } else if (type === "clear") {
    return searchInstance.value.clear();
  }
};

const handleRenderTableData = async (orderTableData: any, viewType: string) => {
  if (viewType !== "MILESTONE") {
    // 订单视图获取数据
    let tableData = [];
    const dateFormat = "YYYY-MM-DD";
    orderTableData.map(orderList => {
      let orderWidget = {};
      const orderStateText = dict
        .getDictByCode("order_state")
        .find(dictItem => dictItem.value === orderList.orderState)?.label;
      orderWidget["orderStateText"] = orderStateText;

      // 处理主表单控件字段
      orderList.widgetList.map(widgetItem => {
        ["IMAGE_UPLOAD", "FILE_UPLOAD"].includes(widgetItem.widgetType);
        orderWidget[widgetItem["widgetId"]] = [
          "IMAGE_UPLOAD",
          "FILE_UPLOAD"
        ].includes(widgetItem.widgetType)
          ? widgetItem["obj"]
          : widgetItem["label"];
      });

      const tableFormWidgetList = tableColumns.value.filter(
        widget => widget.widgetType === "TABLE_FORM"
      );
      let tableFormObject = {};
      for (let tableForm of tableFormWidgetList) {
        tableFormObject[tableForm.name] = [{}];
      }

      // 处理里程碑系统字段
      const orderMilestoneFields = tableColumns.value.filter(
        orderWidget =>
          orderWidget.type === "MILESTONE" || orderWidget.type === "MS_REPLY"
      );
      let milestoneOrderObject = {};
      orderMilestoneFields.map(orderMilestoneField => {
        const orderMilestoneList = orderMilestoneField.name.split("_");
        const orderMilestoneMsId = orderMilestoneList[0];
        const orderMilestoneOrderItem = orderList.milestoneList.find(
          milestoneItem => milestoneItem.msId === orderMilestoneMsId
        );
        if (orderMilestoneOrderItem) {
          if (["planTime", "actualTime"].includes(orderMilestoneList[1])) {
            let milestoneFields = "";
            let startFieldName =
              orderMilestoneList[1] === "planTime"
                ? "plannedStartDate"
                : "actualStartDate";
            let endFieldName =
              orderMilestoneList[1] === "actualTime"
                ? "plannedEndDate"
                : "actualEndDate";
            if (orderMilestoneOrderItem[startFieldName]) {
              milestoneFields += `${dayjs(new Date(orderMilestoneOrderItem[startFieldName])).format(dateFormat)}`;
            }
            if (
              orderMilestoneOrderItem[startFieldName] &&
              orderMilestoneOrderItem[endFieldName]
            ) {
              milestoneFields += `~`;
            }
            if (orderMilestoneOrderItem[endFieldName]) {
              milestoneFields += `${dayjs(new Date(orderMilestoneOrderItem[endFieldName])).format(dateFormat)}`;
            }
            milestoneOrderObject[orderMilestoneField.name] = milestoneFields;
          } else if (
            ["status", "plannedDay", "actualDay"].includes(
              orderMilestoneList[1]
            )
          ) {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem[orderMilestoneList[1]];
          } else if (orderMilestoneList[1] === "manager") {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem
                ? {
                    avatar: orderMilestoneOrderItem.managerUserAvatar,
                    username: orderMilestoneOrderItem.managerUserName,
                    email: orderMilestoneOrderItem.managerUserEmail
                  }
                : {};
          } else if (orderMilestoneList[1] === "replyInfo") {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem["msReplyUser"];
          } else if (orderMilestoneList[1] === "workOrderNumber") {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem["workOrderUserInfoList"].length;
          }
        }
      });

      // 处理自定义标签字段
      let labelOrderObject = {};
      if (orderList.labelList && orderList.labelList.length > 0) {
        const orderLabelFields = tableColumns.value.filter(
          orderWidget =>
            orderWidget.type === "ORDER_LABEL" ||
            orderWidget.type === "MS_LABEL"
        );

        orderLabelFields.map(labelField => {
          const labelConfigItem = orderList.labelList.find(
            labelItem => labelItem.labelId === labelField.name
          );
          if (labelConfigItem) {
            labelOrderObject[labelField.name] = labelConfigItem;
          }
        });
      }

      tableData.push(
        Object.assign(
          orderList,
          orderWidget,
          tableFormObject,
          milestoneOrderObject,
          labelOrderObject
        )
      );
    });
    return tableData;
  } else {
    let tableData = [];
    const dateFormat = "YYYY-MM-DD";
    orderTableData.map(orderList => {
      let orderWidget = {};
      const orderStateText = dict
        .getDictByCode("order_state")
        .find(dictItem => dictItem.value === orderList.orderState)?.label;
      orderWidget["orderStateText"] = orderStateText;

      // 处理主表单控件字段
      [...orderList.widgetList, ...orderList.orderWidgetList].map(
        widgetItem => {
          ["IMAGE_UPLOAD", "FILE_UPLOAD"].includes(widgetItem.widgetType);
          orderWidget[widgetItem["widgetId"]] = [
            "IMAGE_UPLOAD",
            "FILE_UPLOAD"
          ].includes(widgetItem.widgetType)
            ? widgetItem["obj"]
            : widgetItem["label"];
        }
      );

      const workOrderFields = tableColumns.value.filter(
        widget =>
          widget.type === "WORK_ORDER_SPECIAL" ||
          (widget.type === "MILESTONE" &&
            [
              "manager",
              "workOrderCreateInfo",
              "planTime",
              "plannedDay",
              "actualTime",
              "actualDay"
            ].includes(widget.name))
      );
      let workOrderItemObject = {};
      workOrderFields.forEach(workOrderField => {
        if (workOrderField.name === "manager") {
          workOrderItemObject[workOrderField.name] = {
            avatar: orderList.managerUserAvatar,
            username: orderList.managerUserName,
            email: orderList.managerUserEmail
          };
        } else if (["planTime", "actualTime"].includes(workOrderField.name)) {
          let milestoneFields = "";
          let startFieldName =
            workOrderField.name === "planTime"
              ? "plannedStartDate"
              : "actualStartDate";
          let endFieldName =
            workOrderField.name === "actualTime"
              ? "plannedEndDate"
              : "actualEndDate";
          if (orderList[startFieldName]) {
            milestoneFields += `${dayjs(new Date(orderList[startFieldName])).format(dateFormat)}`;
          }
          if (orderList[startFieldName] && orderList[endFieldName]) {
            milestoneFields += `~`;
          }
          if (orderList[endFieldName]) {
            milestoneFields += `${dayjs(new Date(orderList[endFieldName])).format(dateFormat)}`;
          }
          workOrderItemObject[workOrderField.name] = milestoneFields;
        } else if (["plannedDay", "actualDay"].includes(workOrderField.name)) {
          workOrderItemObject[workOrderField.name] =
            orderList[workOrderField.name];
        }
      });

      // 处理自定义标签字段
      let labelOrderObject = {};
      if (orderList.labelList && orderList.labelList.length > 0) {
        const orderLabelFields = tableColumns.value.filter(
          orderWidget =>
            orderWidget.type === "ORDER_LABEL" ||
            orderWidget.type === "MS_LABEL"
        );

        orderLabelFields.map(labelField => {
          const labelConfigItem = orderList.labelList.find(
            labelItem => labelItem.labelId === labelField.name
          );
          if (labelConfigItem) {
            labelOrderObject[labelField.name] = labelConfigItem;
          }
        });
      }

      tableData.push(
        Object.assign(
          orderList,
          orderWidget,
          workOrderItemObject,
          labelOrderObject
        )
      );
    });
    return tableData;
  }
};

const handleReloadTableData = (
  FormData: any,
  widgetId: string,
  columnInfo: any
) => {
  const columnField = tableInstance.value
    .getAllColumnHeaderCells()[0]
    .find(column => column.field === columnInfo.name);
  tableInstance.value.scrollToCell({ col: columnField?.col });
  fetchEventSource(
    "https://dev-trade-api.linkincrease.com.cn/trade-api/trade/order/search-stream",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "text/event-stream"
      },
      body: JSON.stringify(Object.assign(FormData, { pageSize: 10000 })),
      async onmessage(event) {
        const response = JSON.parse(event.data);

        if (response.code) {
          return;
        }
        if (response.subWidgetMap.hasOwnProperty(widgetId)) {
          const subTableAssemblyData = Object.values(
            response.subWidgetMap[widgetId].reduce((res, item) => {
              res[item.rowIndex]
                ? res[item.rowIndex].push(item)
                : (res[item.rowIndex] = [item]);
              return res;
            }, {})
          );
          let tableRow = [];
          const subTableColumns = tableColumns.value.filter(
            column => column.parentsName === widgetId
          );
          for (let subTableRow of subTableAssemblyData) {
            let subTableWidgetObject = {};
            for (let orderWidget of subTableColumns) {
              //@ts-ignore
              const item = subTableRow.find(
                subRow => subRow.widgetId === orderWidget.name
              );
              subTableWidgetObject[orderWidget.name] = item ? item.label : "";
            }

            const tableRow = await handleRenderTableData([response], "ORDER");
            tableInstance.value.addRecord(
              Object.assign(tableRow[0], subTableWidgetObject)
            );
          }
        } else {
          const tableRow = await handleRenderTableData([response], "ORDER");
          tableInstance.value.addRecord(tableRow[0]);
        }
      },
      //@ts-ignore
      onclose(ee) {
        // 关闭流
      },
      onerror(error) {
        console.info(error);
        //返回流报错
      }
    }
  );
  //   tableInstance.value.setRecords(tableData);
  //   tableInstance.value.setScrollTop(0);
  //   tableInstance.value.setScrollLeft(0);
};

const handleReloadRecord = () => {
  tableInstance.value.setRecords(tableRecord.value);
  tableInstance.value.setScrollTop(0);
  tableInstance.value.setScrollLeft(0);
};

const handleClearCheckboxState = () => {
  tableInstance.value.clearSelected();
};

onMounted(() => {
  // 挂载双击事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.DBLCLICK_CELL, e => {
    const tableRow = tableInstance.value.getCellOriginRecord(e.col, e.row);
    emit("handleDblClick", tableRow);
  });

  // 列宽调整事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.RESIZE_COLUMN, e => {
    const columnList = [];
    const columns = tableInstance.value.getAllColumnHeaderCells()[0];
    for (let column of tableColumns.value) {
      const childColumn = columns.find(
        childColumn => childColumn.field === column.name
      );
      const columnConfig = Object.assign(column, {
        fixed: fixedNumber.value > childColumn.col,
        sortable: true,
        show: !!childColumn,
        width: tableInstance.value.getColWidth(childColumn.col)
      });
      columnList.push(columnConfig);
    }

    let colStatisticsItemList = [];
    // 列统计=>todo
    // orderFilterTableList.value.map(orderFilterItem =>
    //   colStatisticsItemList.push({
    //     colStatisticType: orderFilterItem.type,
    //     colOptWidgetId: orderFilterItem.widgetId,
    //     replyWorkOrderData: orderFilterItem.replyWorkOrderData,
    //     crossReferenceId: null
    //   })
    // );

    emit("handleOrderViewUndergo", {
      colStatistics: colStatisticsItemList,
      column: columnList
    });
  });

  // 更改复选框状态
  tableInstance.value.on(
    VTable.ListTable.EVENT_TYPE.CHECKBOX_STATE_CHANGE,
    e => {
      tableInstance.value.on(
        VTable.ListTable.EVENT_TYPE.CHECKBOX_STATE_CHANGE,
        e => {
          const checkRow = tableInstance.value.getRecordByCell(e.col, e.row);
          const checkBoolean =
            tableInstance.value.getCheckboxState("isCheck")[e.row - 1];

          const areAllTrue = tableInstance.value
            .getCheckboxState("isCheck")
            .every(v => v === true);
          const areAllFalse = tableInstance.value
            .getCheckboxState("isCheck")
            .every(v => v === false || v === undefined);

          if (areAllTrue || areAllFalse) {
            if (areAllFalse) {
              tableSelectedRecords.value = [];
            }
            if (areAllTrue) {
              tableSelectedRecords.value = tableRecord.value;
            }
          } else {
            if (checkBoolean) {
              tableSelectedRecords.value.push(checkRow);
            } else {
              const index = tableSelectedRecords.value.findIndex(
                record => record.orderId === checkRow.orderId
              );
              tableSelectedRecords.value.splice(index, 1);
            }
          }

          emit("handleGetCheckboxRecords", tableSelectedRecords.value);
        }
      );
    }
  );
});

defineExpose({
  handleRenderColumnsInfo,
  handleReloadTableData,
  handleReloadRecord,
  handleListTableSearch,
  handleJumpTableColumn,
  handleClearCheckboxState
});
</script>
