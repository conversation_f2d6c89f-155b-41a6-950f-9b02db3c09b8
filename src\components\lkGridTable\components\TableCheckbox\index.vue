<template>
  <div
    class="custom-checkbox-body"
    style="pointer-events: auto; cursor: pointer"
  >
    <OrderSubscript :rowData="rowData" :presentViewInfo="presentViewInfo" />
  </div>
</template>
<script setup lang="ts">
import OrderSubscript from "@/views/orderManage/components/Subscript.vue";

defineProps({
  rowData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  presentViewInfo: {
    type: Object as PropType<any>,
    default: () => {}
  }
});
</script>
