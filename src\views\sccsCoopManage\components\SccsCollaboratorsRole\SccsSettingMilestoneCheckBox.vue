<template>
  <div class="role-per-col">
    <el-checkbox
      v-model="checkAll"
      class="checkbox-parent checkbox-group-col"
      :indeterminate="isIndeterminate"
      :disabled="formElReadonly"
      @change="handleCheckAllChange"
    >
      <div class="checkbox-span-body">
        <div class="checkbox-flex">{{ sccsSettingData.label }}</div>
        <div class="checkbox-flex">
          <el-radio-group v-model="radio" :disabled="formElReadonly">
            <el-radio value="ALL_COOP_ORDER">
              {{ t("trade_sccs_allCoopOrder") }}
            </el-radio>
            <el-radio value="CUSTOM">
              {{ t("trade_common_custom") }}
            </el-radio>
          </el-radio-group>
        </div>
      </div>
    </el-checkbox>
    <div class="role-notice-col">
      <i class="iconfont link-tips-notice" />
      {{ t("trade_coop_teamTip2") }}
    </div>
    <SccsCoopTeamContactPerson
      ref="SccsSettingMilestoneParentCheckBoxRef"
      :sccsSettingData="sccsSettingData"
      :sccsToolRolePermItemList="
        sccsToolRolePermItemList.milestoneRolePermItemList
      "
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import SccsCoopTeamContactPerson from "./SccsCoopTeamContactPerson.vue";

const { t } = useI18n();
const checkAll = ref(false);
const isIndeterminate = ref(false);
const checkedCities = ref([]);
let cities = [];
const radio = ref();
const SccsSettingMilestoneParentCheckBoxRef = ref<any>(null);

const props = defineProps({
  sccsSettingData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  sccsToolRolePermItemList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  formElReadonly: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const handleCheckAllChange = (val: boolean) => {
  let checkedList = [];
  //@ts-ignore
  cities.childrenList.map(child => checkedList.push(child.value));
  checkedCities.value = val ? checkedList : [];
  isIndeterminate.value = false;
};

watch(
  () => props.sccsToolRolePermItemList,
  () => {
    if (props.sccsToolRolePermItemList.milestonePermItem.orderScope) {
      radio.value = props.sccsToolRolePermItemList.milestonePermItem.orderScope;
    }
    if (props.sccsToolRolePermItemList.milestoneRolePermItemList) {
      const permissionKeyList =
        props.sccsToolRolePermItemList.milestoneRolePermItemList[0]
          .permissionKeyList;

      if (
        permissionKeyList.length === props.sccsSettingData.childrenList.length
      ) {
        isIndeterminate.value = false;
        checkAll.value = true;
      } else if (permissionKeyList.length === 0) {
        isIndeterminate.value = false;
        checkAll.value = false;
      } else {
        isIndeterminate.value = true;
        checkAll.value = false;
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const getData = (): any => {
  let milestonePermItem = {};
  let milestoneRolePermItemLists = [];
  const SccsRef = SccsSettingMilestoneParentCheckBoxRef.value;
  for (let i = 0, len = SccsRef.length; i < len; i++) {
    if (SccsRef[i].checkedCities.length !== 0) {
      milestoneRolePermItemLists.push({
        msId: SccsRef[i].msId,
        permissionKeyList: Array.from(new Set(SccsRef[i].checkedCities))
      });
    }
  }
  if (radio.value) {
    milestonePermItem = {
      orderScope: radio.value,
      permissionKey: "trade_sccs_permission_milestone"
    };
  }
  return {
    milestonePermItem: milestonePermItem,
    milestoneRolePermItemList: milestoneRolePermItemLists
  };
};

defineExpose({
  checkedCities,
  getData
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  margin-bottom: 10px;
  font-weight: bolder;
}

.role-per-col {
  position: relative;
  box-sizing: border-box;

  .role-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2000;
    width: 100%;
    height: 100%;
    cursor: not-allowed;
    background: transparent;
  }
}

.role-notice-col {
  margin-top: 16px;
  font-size: 12px;
  line-height: 16px;
  color: #8c8c8c;

  .iconfont {
    margin-right: 3px;
    font-size: 12px;
    vertical-align: baseline;
  }
}

.checkbox-group-col {
  width: 100% !important;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  ::v-deep(.el-checkbox__label) {
    flex: 1 !important;

    .checkbox-span-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .checkbox-flex {
        flex: 1;

        &:nth-child(2) {
          padding-right: 20px;
          text-align: right;
        }
      }
    }
  }
}
</style>
