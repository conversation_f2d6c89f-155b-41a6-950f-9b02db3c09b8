<template>
  <div class="team-member-info-container team-module-area-container">
    <el-tabs v-model="activeName" editable>
      <template #add-icon>
        <el-button
          v-if="activeName === 'businessRole'"
          type="primary"
          color="#0070D2"
          @click="handleAdd"
        >
          <FontIcon icon="link-add" />{{ t("trade_common_create") }}
        </el-button>
      </template>
      <el-tab-pane :label="t('trade_sccscoop_merole')" name="businessRole" lazy>
        <SccsCoopBusinessRole
          ref="SccsCoopBusinessRoleRef"
          :basicInfo="basicInfo"
          :groupId="groupId"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="t('trade_sccs_mainRole')"
        name="collaborativeRole"
        lazy
      >
        <SccsCoopBusinessMainRole
          ref="SccsCollaboratorsRoleRef"
          :basicInfo="basicInfo"
          :groupId="groupId"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import SccsCoopBusinessRole from "./SccsCoopBusinessRole.vue";
import SccsCoopBusinessMainRole from "./SccsCoopBusinessMainRole.vue";

const { t } = useI18n();
const route = useRoute();
const activeName = ref("businessRole");
const SccsCoopBusinessRoleRef = ref<any | HTMLElement>(null);
const SccsCollaboratorsRoleRef = ref<any | HTMLElement>(null);

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleAdd = (): void => {
  SccsCoopBusinessRoleRef.value.addSccsBusinessRole();
};

onMounted(() => {
  const { tab } = route.query;
  if (tab && ["businessRole", "collaborativeRole"].includes(tab as string)) {
    activeName.value = tab as string;
  }
});
</script>
<style lang="scss" scoped>
.team-module-area-container {
  ::v-deep(.el-tabs) {
    padding: 0 25px !important;

    .team-module-area-btn {
      position: absolute;
      top: 0;
      right: 0;
    }

    .el-tabs__header {
      width: auto !important;

      .el-tabs__item {
        width: auto !important;
        padding: 0 !important;
        margin-right: 22px !important;
        margin-left: 10px !important;

        &.is-active {
          font-weight: bolder;
          color: #0070d2 !important;
          background: transparent !important;
        }

        .el-icon {
          display: none;
        }
      }

      .el-tabs__active-bar {
        height: 3px !important;
        color: #0070d2 !important;
      }

      .el-tabs__new-tab {
        position: absolute;
        top: 50%;
        right: 30px;
        z-index: 1000;
        border: 0 none;
        transform: translateY(-81%);
      }
    }
  }
}
</style>
