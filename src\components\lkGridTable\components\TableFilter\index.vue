<template>
  <div style="display: flex; pointer-events: auto; cursor: pointer">
    <LkHeaderColumn :column="column" :width="width - 30" />
    <el-popover
      placement="bottom-end"
      trigger="click"
      :show-arrow="false"
      :show-after="500"
      :width="338"
      :popper-style="{
        padding: 0
      }"
    >
      <template #reference>
        <i class="iconfont link-more-up" />
      </template>
      <div class="table-sort-container">
        <div class="table-sort-top">
          <div
            class="table-sort-li descending"
            :class="{ active: sortDescending === false }"
            @click.stop="handleChangeSorting(false)"
          >
            <div class="table-sort-text">
              {{ t("trade_common_descending") }}
            </div>
            <el-button
              type="info"
              plain
              size="small"
              class="sort-btn"
              @click.stop="handleChangeSorting(null)"
            >
              {{ t("trade_common_cancel") }}
            </el-button>
          </div>
          <div
            class="table-sort-li ascending"
            :class="{ active: sortDescending === true }"
            @click.stop="handleChangeSorting(true)"
          >
            <div class="table-sort-text">
              {{ t("trade_common_ascending") }}
            </div>
            <el-button
              type="info"
              plain
              size="small"
              class="sort-btn"
              @click.stop="handleChangeSorting(null)"
            >
              {{ t("trade_common_cancel") }}
            </el-button>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import LkHeaderColumn from "@/components/lkHeaderColumn/index";

defineProps({
  column: {
    type: Object as PropType<any>,
    default: () => {}
  },
  width: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
const sortDescending = ref<boolean | any>(null);

const handleChangeSorting = (val: any) => {
  sortDescending.value = val;
};
</script>
<style lang="scss" scoped>
.iconfont {
  font-size: 12px;
  cursor: pointer;
}

.table-sort-container {
  display: flex;
  flex-direction: column;

  .table-sort-top {
    margin: 5px 0;

    .table-sort-li {
      display: flex;
      align-items: center;
      height: 36px;
      padding: 0 14px 0 8px;
      font-size: 14px;
      line-height: 36px;
      color: #595959;
      cursor: pointer;

      .table-sort-text {
        width: 90%;
      }

      .sort-btn {
        display: none;
      }

      &:hover {
        background: #f2f2f2;
      }

      &.descending {
        &::before {
          margin-right: 6px;
          font-family: iconfont !important;
          content: "\e605";
        }
      }

      &.ascending {
        &::before {
          margin-right: 6px;
          font-family: iconfont !important;
          content: "\e604";
        }
      }

      &.active {
        &::after {
          font-family: iconfont !important;
          color: #2082ed !important;
          content: "\e7ad";
        }

        &:hover {
          .sort-btn {
            display: block;
          }

          &::after {
            content: "";
          }
        }
      }
    }
  }
}
</style>
