<template>
  <div class="upload-main" @paste="handlePasteUpload">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      class="avatar-uploader"
      :class="{
        'avatar-uploader-hide':
          widgetConfigure.props &&
          widgetConfigure.props.limit &&
          fileList.length >= widgetConfigure.props.limit
      }"
      :action="uploadRouter"
      :data="uploadParams"
      :multiple="true"
      drag
      :limit="
        widgetConfigure.props && widgetConfigure.props.limit
          ? widgetConfigure.props.limit
          : null
      "
      :on-success="handleUploadSuccess"
      :on-remove="handleRemove"
      :before-upload="beforeAvatarUpload"
      :on-exceed="handleFileExceed"
      :accept="accept"
    >
      <template #trigger>
        <div class="upload-tip-body">
          <i class="iconfont link-upload upload-tip-icon" />
          <div class="upload-tip-text">
            {{ t("trade_component_uploadtip") }}
          </div>
        </div>
      </template>
      <template #tip>
        <div class="el-upload__tip">
          <span
            v-if="
              widgetConfigure.props &&
              widgetConfigure.props.accept &&
              widgetConfigure.props.accept.length > 0
            "
          >
            {{ t("trade_component_upload_format") }}：{{ accept }}
          </span>
          <span
            v-if="
              widgetConfigure.props &&
              widgetConfigure.props.limit &&
              ((widgetConfigure.props.accept &&
                widgetConfigure.props.accept.length > 0) ||
                widgetConfigure.props.limit)
            "
          >
            ,
          </span>
          <span v-if="widgetConfigure.props && widgetConfigure.props.limit">
            {{
              t("trade_component_upload_limit", {
                limit: widgetConfigure.props.limit
              })
            }}
          </span>
          <span
            v-if="
              widgetConfigure.props &&
              widgetConfigure.props.maxFileSize &&
              ((widgetConfigure.props.accept &&
                widgetConfigure.props.accept.length > 0) ||
                widgetConfigure.props.limit)
            "
          >
            ,
          </span>
          <span
            v-if="widgetConfigure.props && widgetConfigure.props.maxFileSize"
          >
            {{
              t("trade_component_upload_max_file_size", {
                size: widgetConfigure.props.maxFileSize
              })
            }}
          </span>
        </div>
      </template>
    </el-upload>
  </div>
</template>
<script lang="ts" setup>
import { ref, inject, computed, watch } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage, ElUpload } from "element-plus";
import type { UploadProps } from "element-plus";
import { emitter } from "@/utils/mitt";
import { cloneDeep, isEqual } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const { t } = useI18n();
const route = useRoute();
const fileList = ref<any[]>([]);
const uploadRef = ref<HTMLElement | any>(null);

const uploadRouter = inject("uploadFileURL", null);
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);

const uploadParams = rawFile => {
  const fileSize = rawFile.size / 1024 / 1024;
  return {
    ossFileType: "TRADE_SCCS",
    sccsId: route.query.sccsId,
    size: Math.floor(fileSize)
  };
};

const emit = defineEmits(["handleUpdateWidgetData"]);

const accept = computed(() => {
  let acceptStr = "";
  if (props.widgetConfigure.props && props.widgetConfigure.props.accept) {
    props.widgetConfigure.props.accept.map(acceptText => {
      acceptStr += `,.${acceptText}`;
    });
  }
  return props.widgetConfigure.props && props.widgetConfigure.props.accept
    ? acceptStr.substr(1)
    : "";
});

const handleFileExceed: UploadProps["onExceed"] = uploadFileList => {
  if (
    props.widgetConfigure.props &&
    props.widgetConfigure.props.limit <
      fileList.value.length + uploadFileList.length
  ) {
    ElMessage.error(`最多仅允许上传${props.widgetConfigure.props.limit}个文件`);
    return false;
  }
};

const beforeAvatarUpload: UploadProps["beforeUpload"] = rawFile => {
  emitter.emit("widgetUploadLoading", true);
  if (
    props.widgetConfigure.props &&
    props.widgetConfigure.props.limit < fileList.value.length + 1
  ) {
    emitter.emit("widgetUploadLoading", false);
    ElMessage.error(`最多仅允许上传${props.widgetConfigure.props.limit}个文件`);
    return false;
  }

  let acceptNameList;
  if (props.widgetConfigure.props && props.widgetConfigure.props.accept) {
    acceptNameList = props.widgetConfigure.props.accept.map((acceptName: any) =>
      acceptName.toLowerCase()
    );
  }

  if (
    props.widgetConfigure.props &&
    acceptNameList &&
    !acceptNameList.includes(rawFile.name.split(".").slice(-1)[0].toLowerCase())
  ) {
    emitter.emit("widgetUploadLoading", false);
    ElMessage.error(`上传文件仅支持格式：${accept.value}`);
    return false;
  }
  if (
    props.widgetConfigure.props &&
    props.widgetConfigure.props.maxFileSize &&
    rawFile.size / 1024 / 1024 > props.widgetConfigure.props.maxFileSize
  ) {
    emitter.emit("widgetUploadLoading", false);
    ElMessage.error(
      `${props.widgetConfigure.title}大小不能超过${props.widgetConfigure.props.maxFileSize}MB!`
    );
    return false;
  }
  return true;
};

const handlePasteUpload = event => {
  const items = (event.clipboardData || window.Clipboard).items;
  event.preventDefault();
  event.returnValue = false;

  const fileCount = items.length + fileList.value.length;
  if (
    props.widgetConfigure.props &&
    props.widgetConfigure.props.limit < fileCount
  ) {
    emitter.emit("widgetUploadLoading", false);
    ElMessage.error(`最多仅允许上传${props.widgetConfigure.props.limit}个文件`);
    return false;
  }
  for (let item of items) {
    const file = item.getAsFile();
    let acceptNameList;
    if (props.widgetConfigure.props) {
      acceptNameList = props.widgetConfigure.props.accept.map(
        (acceptName: any) => acceptName.toLowerCase()
      );
    }

    if (
      props.widgetConfigure.props &&
      props.widgetConfigure.props.accept &&
      !acceptNameList.includes(file.name.split(".").slice(-1)[0].toLowerCase())
    ) {
      emitter.emit("widgetUploadLoading", false);
      ElMessage.error(`上传文件仅支持格式：${accept.value}`);
      return false;
    }
    if (
      props.widgetConfigure.props &&
      props.widgetConfigure.props.maxFileSize &&
      file.size / 1024 / 1024 > props.widgetConfigure.props.maxFileSize
    ) {
      emitter.emit("widgetUploadLoading", false);
      ElMessage.error(
        `${props.widgetConfigure.title}大小不能超过${props.widgetConfigure.props.maxFileSize}MB!`
      );
      return false;
    }
  }

  for (let i = 0, len = items.length; i < len; i++) {
    if (items[i].kind !== "file") {
      return;
    }
    const file = items[i].getAsFile();
    uploadRef.value?.handleStart(file);
    uploadRef.value?.submit();
  }
};

const handleUploadSuccess: UploadProps["onSuccess"] = (uploadFile, File) => {
  const index = fileList.value.findIndex(file => file.uid === File.uid);
  const oldFile = fileList.value[index];
  const fileSize = Math.floor(oldFile.size / 1024 / 1024);
  fileList.value.splice(index, 1, {
    url: uploadFile.data,
    name: decodeURIComponent(uploadFile.data)
      .split("?")[0]
      .split("/")
      .slice(-1)[0],
    size: fileSize
  });
  emitter.emit("widgetUploadLoading", false);
  handleWigetFormData();
};

const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  fileList.value = uploadFiles;
  handleWigetFormData();
};

const handleWigetFormData = (): void => {
  let fileValue = "";
  fileList.value.forEach(file => {
    fileValue += `,${file.name}`;
  });
  if (props.widgetRowIndex === -1) {
    handleWidgetFormsValue(
      props.widgetConfigure._fc_id,
      {
        obj: fileList.value,
        label: fileValue.substr(1),
        widgetType: props.widgetConfigure.type,
        widgetId: props.widgetConfigure._fc_id,
        $rowIndex: props.widgetRowIndex
      },
      fileList.value
    );
  } else {
    emit("handleUpdateWidgetData", "DrFilesUpload", {
      obj: fileList.value,
      label: fileValue.substr(1),
      widgetId: props.widgetConfigure._fc_id,
      $rowIndex: props.widgetRowIndex
    });
  }
};

const handleClearFiles = () => {
  uploadRef.value.abort();
  uploadRef.value.clearFiles();
};

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    const widgetFileList = newVal;
    if (widgetFileList instanceof Array && widgetFileList.length > 0) {
      fileList.value = cloneDeep(widgetFileList);
    } else {
      fileList.value = [];
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  handleClearFiles
});
</script>
<style lang="scss" scoped>
.upload-main {
  // width: 345px;
  width: 100%;
  padding: 9px;
  border: 1px dashed #bfbfbf;
  border-radius: 4px;

  &:hover {
    border-color: #409eff;
  }

  ::v-deep(.el-upload) {
    width: 100%;

    .el-upload-dragger {
      padding: 26px 0 20px;
      text-align: center;
      border: 1px dashed #dedede;
      border-radius: 4px;

      &:hover {
        border-color: #409eff;
      }
    }

    .upload-tip-icon {
      font-size: 27px;
      color: #808080;
    }

    .upload-tip-text {
      margin-top: 10px;
      font-size: 12px;
      line-height: 17px;
      color: #bfbfbf;
      text-align: center;
    }
  }

  .el-upload__tip {
    font-size: 12px;
    line-height: 17px;
    color: #bfbfbf;
    text-align: left;

    span {
      word-break: break-all;
    }
  }
}
</style>
