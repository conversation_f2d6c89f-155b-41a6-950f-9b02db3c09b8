<template>
  <div class="email-collaborate-table-body">
    <div class="email-collaborate-table-header">
      <div class="email-collaborate-table-left">
        <el-input
          v-model="keyword"
          clearable
          style="width: 316px"
          :placeholder="t('trade_email_searchOrderTip')"
          @change="initGridData"
        >
          <template #prefix>
            <i class="iconfont link-search" />
          </template>
        </el-input>
        <div class="email-collaborate-table-left-tip">
          <span class="email-collaborate-table-left-block" />
          {{ t("trade_email_referableFields") }}
        </div>
      </div>
      <div class="email-collaborate-table-right">
        <el-button
          v-if="btnVisible"
          type="primary"
          color="#0070D2"
          @click="handleAdd"
        >
          <FontIcon icon="link-add" />{{ t("trade_common_create") }}
        </el-button>
      </div>
    </div>
    <div class="email-collaborate-table-container">
      <vxe-grid ref="gridRef" v-bind="gridOptions" />
    </div>
  </div>
  <CreateEmailCollaborateDialog
    ref="CreateEmailCollaborateDialogRef"
    @handleCreateSuccess="handleCreateSuccess"
  />
  <LkDialog
    ref="WidgetDialogRef"
    class="lk-maximum-dialog"
    :title="emailDetailDialogTitle"
    append-to-body
  >
    <template #default>
      <el-scrollbar style="height: 100%">
        <EmailDetail ref="EmailDetailRef" />
      </el-scrollbar>
    </template>
  </LkDialog>
  <EmailCoopSubTable
    ref="EmailCoopSubTableRef"
    @handleQuoteSubTableData="handleQuoteSubTableData"
    @handleViewSubTable="handleViewSubTable"
  />
</template>
<script lang="tsx" setup>
import { markRaw, nextTick, onMounted, onUnmounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import type { VxeGridProps, VxeGridInstance } from "vxe-table";
import { dayjs, ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import CreateEmailCollaborateDialog from "./createEmailCoop.vue";
import LkDialog from "@/components/lkDialog";
import EmailDetail from "../emailDetail.vue";
import EmailCoopSubTable from "./emailCoopSubTableV2.vue";
import {
  handleRenderWidget,
  userSlot,
  fileSlot,
  richTextSlot
} from "@/utils/formDesignerUtils.tsx";
import {
  deleteWorkOrderCoopEmail,
  getWidgetList,
  getWorkOrderEmailList
} from "@/api/email";
import { storageLocal } from "@pureadmin/utils";
import { emitter } from "@/utils/mitt";

const props = defineProps({
  sccsId: {
    type: String as PropType<string>,
    default: ""
  },
  workOrderId: {
    type: String as PropType<string>,
    default: ""
  },
  milestoneId: {
    type: String as PropType<string>,
    default: ""
  },
  worderName: {
    type: String as PropType<string>,
    default: ""
  },
  quoteVisible: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  btnVisible: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const emit = defineEmits(["handleQuoteData", "handleQuoteSubTableData"]);

const { t } = useI18n();
const keyword = ref<string>("");
const userInfo: any = storageLocal().getItem("user-info");
const emailDetailDialogTitle = ref<string>("");
const emailListTableData = ref<any>([]);
const WidgetDialogRef = ref<HTMLElement | any>(null);
const EmailDetailRef = ref<HTMLElement | any>(null);
const EmailCoopSubTableRef = ref<HTMLElement | any>(null);
const CreateEmailCollaborateDialogRef = ref<HTMLElement | any>(null);
const gridRef = ref<VxeGridInstance<any>>();
const gridOptions = reactive<VxeGridProps<any>>({
  border: true,
  showOverflow: false,
  height: "auto",
  scrollY: {
    enabled: true,
    gt: 20,
    oSize: 20
  },
  scrollX: {
    enabled: false, // 为 true 会导致固定列情况下，固定列是按全部列的最高来计算，无法做到动态计算
    gt: 20,
    oSize: 20
  },
  cellConfig: {
    height: 80
  },
  resizable: true,
  columns: [],
  headerCellClassName({ column }) {
    return column.params && column.params.widget ? "FAFAE8" : "";
  },
  data: []
});

const handleAdd = (): void => {
  CreateEmailCollaborateDialogRef.value.open(
    props.milestoneId,
    props.workOrderId,
    props.sccsId
  );
};

const handleReadTable = (row: any) => {
  WidgetDialogRef.value.open();
  nextTick(() => {
    emailDetailDialogTitle.value = row.subject;
    EmailDetailRef.value.initRenderTemplate(row.id, row, "table");
  });
};

const handleQuoteData = (row: any) => {
  ElMessageBox.confirm(
    t("trade_email_quoteDataToWorkOrderTip"),
    t("trade_email_quoteDataToWorkOrder"),
    {
      confirmButtonText: t("trade_common_confirm"),
      cancelButtonText: t("trade_common_cancel"),
      confirmButtonClass: "confrim-message-btn-warn-class",
      customClass: "order_confirm_message_box",
      type: "warning",
      icon: markRaw(WarningFilled),
      center: true
    }
  ).then(async () => {
    emit("handleQuoteData", row);
  });
};

const handleQuoteSubTableData = data => {
  emit("handleQuoteSubTableData", data);
};

const handleDeleteTableRow = (row: any) => {
  ElMessageBox.confirm(
    t("trade_email_deleteEmailCollaborateTip"),
    t("trade_email_deleteEmailCollaborate"),
    {
      confirmButtonText: t("trade_common_confirm"),
      cancelButtonText: t("trade_common_cancel"),
      confirmButtonClass: "confrim-message-btn-class",
      customClass: "cancel-message-btn-class",
      type: "error",
      icon: markRaw(WarningFilled),
      center: true
    }
  ).then(async () => {
    const { code } = await deleteWorkOrderCoopEmail({ id: row.id });
    if (code === 0) {
      ElMessage({
        message: t("trade_common_updateSuccess"),
        type: "success"
      });
      initGridData();
    }
  });
};

const handleViewSubTable = (
  fieldName: any,
  childColumns: any,
  rowId?: string
) => {
  EmailCoopSubTableRef.value.open(
    childColumns,
    rowId
      ? emailListTableData.value.filter(item => item.id === rowId)
      : emailListTableData.value,
    fieldName
  );
};

onMounted(() => {
  emitter.on("viewSubTableInEmailCoop", (data: any) => {
    handleViewSubTable(data.fieldName, data.childColumns, data.rowId);
  });
});

onUnmounted(() => {
  emitter.off("viewSubTableInEmailCoop");
});

const initGridConfigData = async () => {
  const { data } = await getWidgetList({ workOrderId: props.workOrderId });
  let gridsColumns: any = [
    {
      type: "seq",
      title: t("trade_common_SerialNumber"),
      width: 60,
      align: "center",
      fixed: "left"
    },
    {
      field: "subject",
      title: t("trade_email_subject"),
      width: 160,
      align: "center",
      headerAlign: "left"
    },
    {
      field: "remark",
      title: t("trade_common_remark"),
      width: 160,
      align: "center",
      headerAlign: "left",
      slots: {
        default({ row }) {
          return richTextSlot(row["remark"]);
          // return fileSlot(row["workOrderCoopEmailAttachmentList"]);
        }
      }
    },
    {
      field: "workOrderCoopEmailAttachmentList",
      title: t("trade_email_files"),
      width: 160,
      align: "center",
      headerAlign: "left",
      slots: {
        default({ row }) {
          return fileSlot(row["workOrderCoopEmailAttachmentList"]);
        }
      }
    }
  ];
  const columnList = data.filter(item => !item.parentsName);
  columnList.forEach(column => {
    const { label, name, widgetType, editable } = column;
    let headerSlot = {};
    if (column.widgetType === "TABLE_FORM") {
      const childColumns = data.filter(
        childColumn => childColumn.parentsName === column.name
      );
      column.childrenList = childColumns;
      headerSlot = {
        header({ row, column }) {
          return (
            <div class="column-title-body">
              <div class="column-title">{column.title}</div>
              {props.quoteVisible ? (
                <span
                  class="column-icon"
                  onclick={() =>
                    handleViewSubTable(column.property, childColumns)
                  }
                >
                  <el-tooltip
                    content={t("trade_email_subTableViews")}
                    placement="top"
                    show-after={500}
                  >
                    <i class="iconfont link-open" />
                  </el-tooltip>
                </span>
              ) : (
                ""
              )}
            </div>
          );
        }
      };
    }
    gridsColumns.push({
      field: name,
      title: label,
      width: 160,
      align: "center",
      headerAlign: "left",
      params: {
        widget: editable
      },
      slots: {
        default({ row }) {
          const rowData = {
            ...row,
            widgetList: row.workOrderCoopEmailDataVO?.widgetDataList || []
          };
          return handleRenderWidget(widgetType, rowData, name, column);
        },
        ...headerSlot
      }
    });
  });
  gridsColumns.push(
    {
      field: "createor",
      title: t("trade_email_createor"),
      width: 160,
      align: "center",
      headerAlign: "left",
      slots: {
        default({ row }) {
          return userSlot(
            row.creatorAvatar,
            row.creatorName,
            row.createTime,
            row.creatorEmail
          );
        }
      }
    },
    {
      field: "updatesor",
      title: t("trade_login_email"),
      width: 180,
      align: "center",
      headerAlign: "left",
      slots: {
        default({ row }) {
          return (
            <div>
              <div>{row.submitEmail}</div>
            </div>
          );
        }
      }
    },
    {
      field: "updatesorTime",
      title: t("trade_common_updateTime"),
      width: 180,
      align: "center",
      headerAlign: "left",
      slots: {
        default({ row }) {
          return (
            <div>
              {row.submitTime
                ? dayjs(new Date(row.submitTime)).format("YYYY-MM-DD HH:mm:ss")
                : ""}
            </div>
          );
        }
      }
    },
    {
      field: "operate",
      title: "",
      width: props.quoteVisible ? 180 : 120,
      showOverflow: false,
      align: "center",
      fixed: "right",
      slots: {
        default({ row }) {
          return (
            <div class="grid-table-operate">
              <div
                class="grid-table-operate-btn"
                onclick={() => handleReadTable(row)}
              >
                {t("trade_common_view")}
              </div>
              {props.quoteVisible ? (
                <div
                  class="grid-table-operate-btn"
                  onclick={() => handleQuoteData(row)}
                >
                  {t("trade_email_quoteData")}
                </div>
              ) : (
                ""
              )}
              {props.btnVisible ? (
                <div
                  class="grid-table-operate-btn"
                  onclick={() => handleDeleteTableRow(row)}
                >
                  {t("trade_common_delete")}
                </div>
              ) : (
                ""
              )}
            </div>
          );
        }
      }
    }
  );
  gridOptions.columns = gridsColumns;
  gridRef.value.reloadColumn(gridsColumns);
  initGridData();
};

const handleCreateSuccess = () => {
  initGridData();
};

const initGridData = async () => {
  const { data } = await getWorkOrderEmailList({
    workOrderId: props.workOrderId,
    keyword: keyword.value
  });
  emailListTableData.value = data;
  let tableData = [];
  data.forEach(row => {
    if (row?.workOrderCoopEmailDataVO) {
      let orderWidget = {};
      row?.workOrderCoopEmailDataVO.widgetDataList.map(widgetItem => {
        orderWidget[widgetItem["widgetId"]] = [
          "IMAGE_UPLOAD",
          "FILE_UPLOAD"
        ].includes(widgetItem.widgetType)
          ? widgetItem["obj"]
          : widgetItem["label"];
      });
      tableData.push(Object.assign(row, orderWidget));
    }
  });

  if (gridRef.value) {
    gridRef.value.reloadData(tableData);
  }
};

defineExpose({
  initGridConfigData
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
