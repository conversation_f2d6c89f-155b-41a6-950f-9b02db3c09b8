<template>
  <loginPage>
    <template #default>
      <component :is="componentName" @handleLoginSuccess="login" />
    </template>
  </loginPage>
</template>
<script setup lang="ts">
import { ref, onMounted, onUpdated, provide } from "vue";
import { useRoute, useRouter } from "vue-router";
import loginPage from "./loginPage.vue";
import userLogin from "./component/UserLogin.vue";
import userRegister from "./component/UserRegister.vue";
import resetPassword from "./component/ResetPassword.vue";
import { setToken } from "@/utils/auth";

const componentName = ref<any>(null);
const route = useRoute();
const router = useRouter();

const setComponentName = (): void => {
  componentName.value =
    route.path.indexOf("/login") > -1
      ? userLogin
      : route.path.indexOf("register") > -1
        ? userRegister
        : resetPassword;
};

const login = (data): void => {
  setToken(data).then((userInfo: any) => {
    if (userInfo.latestLoginTeamId && userInfo.latestLoginTeamMemberId) {
      router.push("/");
    } else if (userInfo.anyTeamMember) {
      router.push("/selectTeam");
    } else {
      router.push("/createTeam");
    }
  });
};

const handleUserNameChange = (userInfo: any): void => {
  sessionStorage.setItem("username", userInfo.username);
};

provide("handleWatchUserName", handleUserNameChange);
onMounted(() => {
  setComponentName();
});

onUpdated(() => {
  setComponentName();
});
</script>
