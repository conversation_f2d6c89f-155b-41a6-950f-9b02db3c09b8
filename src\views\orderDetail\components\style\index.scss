.order-detail-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0 !important;

  .order-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    padding: 0 25px 0 20px;
    background: #fff;
    box-shadow: 0 1px 8px 0 rgb(0 0 0 / 8%);

    .order-detail-header-left {
      flex: 1;

      ::v-deep(.el-breadcrumb) {
        .el-breadcrumb__item {
          .el-breadcrumb__inner.is-link {
            font-size: 12px;
            font-weight: bold !important;
            line-height: 14px;
            color: #262626;
            text-align: left;
          }

          .el-breadcrumb__inner {
            max-width: 300px;
            font-size: 12px;
            font-weight: bold !important;
            line-height: 14px;
            color: #797979;
            text-align: left;
          }
        }
      }
    }

    .order-detail-header-right {
      display: flex;
      flex: 1;
      justify-content: end;

      .order-detail-header-switch {
        display: inline-block;
        align-items: center;

        .el-switch {
          --el-switch-on-color: #0070d2;

          height: 100%;

          ::v-deep(.el-switch__label.is-active) {
            color: #595959;
          }
        }
      }

      .order-detail-header-group {
        display: inline-block;
        margin-left: 24px;

        .order-detail-dropdown-btn {
          padding: 5px 10px;
          border: 0 none;
          border-radius: 4px;

          .iconfont {
            font-size: 14px;
            vertical-align: middle;
          }

          .order-detail-dropdown-btn-text {
            margin-left: 5px;
            font-size: 14px;
            line-height: 20px;
            color: #595959;
            text-align: left;
          }
        }
      }
    }
  }

  .order-detail-body {
    display: flex;
    justify-content: center;

    // 默认宽度
    width: 80%;
    height: calc(100vh - 90px);
    padding: 20px;

    // 全宽
    &.full-width-container {
      width: 100%;

      .order-detail-body-left {
        flex: none !important;
        flex-shrink: 0 !important;
      }

      .order-detail-body-right {
        max-width: calc(100% - 45px);
      }

      &.hideLeft {
        width: calc(100% - 40px);

        .order-detail-body-left {
          display: none !important;
        }

        .order-detail-body-right {
          width: calc(100% - 45px);
          max-width: calc(100% - 45px);
        }

        &.order-detail-body-small {
          .order-detail-body-left {
            display: none !important;
          }

          .order-detail-body-right {
            width: calc(100% - 45px);
          }
        }
      }

      &.order-detail-body-small {
        width: calc(100% - 40px);

        .order-detail-body-left {
          flex: none !important;
          flex-shrink: 0 !important;
        }

        .order-detail-body-right {
          width: calc(100% - 263px);
          max-width: calc(100% - 45px);
        }
      }
    }

    // 隐藏左侧
    &.hideLeft {
      .order-detail-body-left {
        display: none !important;
      }

      .order-detail-body-right {
        width: 1080px;
      }
    }

    // 小屏
    &.order-detail-body-small {
      width: calc(100% - 40px);

      .order-detail-body-left {
        flex: none !important;
        flex-shrink: 0 !important;
      }

      .order-detail-body-right {
        width: calc(100% - 263px);
        max-width: calc(100% - 45px);
      }

      &.hideLeft {
        .order-detail-body-left {
          display: none !important;
        }

        .order-detail-body-right {
          width: calc(100% - 45px);
        }
      }
    }

    .order-detail-telescoping {
      display: inline-block;
      flex: none;
      width: 24px;
      height: 24px;
      color: #8c8c8c;
      text-align: center;
      vertical-align: middle;
      cursor: pointer;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 50%;
      box-shadow: 0 0 8px 0 rgb(0 0 0 / 16%);

      .iconfont {
        font-size: 10px;
      }

      &:hover {
        background: #0070d2;

        .iconfont {
          color: #fff;
        }
      }
    }

    .order-detail-body-left {
      position: relative;
      flex-shrink: 0;
      min-width: 224px;
      max-width: 290px;
      margin-right: 20px;

      ::v-deep(.el-scrollbar__wrap) {
        padding-bottom: 24px;
      }

      ::v-deep(.el-scrollbar) {
        .el-scrollbar__view {
          padding: 2px;
        }
      }

      .anchor-icon-left {
        position: absolute;
        top: 4px;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        padding: 4px;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 1px solid rgb(1 6 62 / 9%);
        border-radius: 50%;

        .link-pack-up {
          font-size: 10px;
          color: #8c8c8c;
        }
      }

      .order-detail-card {
        width: 230px;
        padding: 13px;
        margin-bottom: 12px;
        cursor: pointer;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 6px 0 rgb(10 22 83 / 13%);

        .order-detail-card-name {
          font-size: 14px;
          font-weight: bold;
          line-height: 20px;
          color: #262626;
        }

        .order-detail-card-tag-container {
          margin: 8px 0;

          .order-detail-card-tag {
            display: inline-block;
            padding: 2px 6px;
            margin-right: 5px;
            font-size: 12px;
            color: #fff;
            background: #ff6065;
            border-radius: 10px;
          }
        }

        .order-detail-card-time-container {
          .order-detail-card-time-row {
            display: flex;
            align-items: center;

            .order-detail-card-time-title {
              display: flex;
              align-items: center;
              height: 24px;
              font-size: 12px;
              color: #8c8c8c;
            }

            .order-detail-card-time-date-box {
              font-size: 12px;
              color: #262626;

              .lk-avater-container {
                vertical-align: middle;
              }
            }
          }
        }

        &:hover {
          box-shadow: 0 6px 18px 0 rgb(1 6 62 / 14%);
        }

        &.active {
          border: 1px solid #0070d2;
        }

        &.order-state {
          border-left: 4px solid #358ff0 !important;
        }
      }
    }

    .order-detail-body-right {
      position: relative;
      width: calc(100% - 263px);
      max-width: 1080px;

      .order-detail-region-body {
        width: calc(100% - 10px);
        margin-top: 25px;

        &:first-child {
          margin-top: 0;
        }

        .order-detail-region-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin-bottom: 12px;

          .order-detail-region-title {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bolder;
            line-height: 25px;
            color: #262626;
            text-align: left;

            .order-detail-region-header-titlte {
              font-size: 18px;
              font-weight: bolder;
              line-height: 27px;
              color: #080707;
              text-align: left;
            }

            .order-detail-region-tag {
              display: inline-block;
              width: 16px;
              height: 16px;
              font-size: 14px;
              font-weight: 600;
              line-height: 16px;
              color: #0070d2;
              text-align: center;
              cursor: pointer;
              background: #ddefff;
              border-radius: 3px;

              &.mutually {
                margin-right: 3px;
                color: #fa8d0a;
                background: #fff1cd;
              }

              &.replay {
                margin-left: 5px;
              }
            }
          }

          .order-detail-region-operating-area {
            flex-shrink: 0;
            white-space: nowrap;

            .order-detail-region-operating-btn {
              display: inline-block;
              padding: 5px 10px;
              font-size: 14px;
              line-height: 20px;
              color: #595959;
              cursor: pointer;
              border-radius: 4px;

              &.nopadding {
                padding: 0 !important;
              }

              &.add-task-order-class {
                color: #0070d2;
              }

              .iconfont {
                margin-right: 5px;
                font-size: 13px;

                // vertical-align: middle;
              }

              &:hover {
                background: #e5e5e5;
              }

              &.hide-btn {
                display: none !important;
              }

              .hide-btn {
                display: none !important;
              }
            }
          }
        }

        .order-detail-region-describe {
          margin-top: 14px;
          background: #f0f1f6;
          border-radius: 4px 4px 0 0;

          .order-detail-region-type-body {
            margin-bottom: 12px;

            .order-detail-region-col {
              display: inline-block;
              margin-right: 28px;

              .order-detail-region-title {
                display: inline-flex;
                height: 20px;
                padding: 0 5px;
                margin-right: 10px;
                font-size: 12px;
                line-height: 20px;
                border-radius: 2px;

                .order-detail-region-header-titlte {
                  font-size: 18px;
                  font-weight: bolder;
                  line-height: 27px;
                  color: #080707;
                  text-align: left;
                }

                &.original {
                  color: #0070d2;
                  background: #ddefff;
                }

                &.repeat {
                  color: #2b69f7;
                  background: #dde1ff;
                }

                &.association {
                  color: #fa8d0a;
                  background: #ffedd1;
                }

                &.sccsAssociation {
                  color: #00b699;
                  background: #d9f4f0;
                }

                &.orderUse {
                  color: #9870fa;
                  background: #e6dcff;
                }
              }

              .order-detail-region-content {
                margin-right: 16px;

                .order-detail-region-sccs-text {
                  font-size: 12px;
                  color: #8c8c8c;
                  text-align: left;
                  cursor: pointer;

                  .iconfont {
                    margin-left: 2px;
                    font-size: 12px;
                    color: #8c8c8c;
                    cursor: pointer;
                  }
                }

                .order-detail-region-text {
                  font-size: 12px;
                  color: #262626;
                  text-align: left;
                  cursor: pointer;

                  &:hover {
                    color: #0070d2;
                  }

                  .iconfont {
                    margin-left: 2px;
                    font-size: 12px;
                    color: #babec6;
                    cursor: pointer;
                  }
                }
              }
            }
          }

          .order-detail-region-type-container {
            padding: 18px 23px;

            .order-detail-region-type-row {
              display: flex;
              margin-bottom: 7px;

              .order-detail-region-type-title {
                align-items: center;
                width: auto;
                font-size: 14px;
                line-height: 24px;
                color: #797979;
              }

              .order-detail-region-type-text {
                flex: 1;
                align-items: center;
                word-break: break-all;

                .order-detail-region-tag {
                  box-sizing: border-box;
                  height: 24px !important;
                  padding: 4px 6px;
                  margin-right: 6px;
                  font-size: 12px;
                  color: #262626;
                  background: #e5e5e5 !important;
                  border-color: #e5e5e5;
                  border-radius: 3px;
                }

                .order-detail-region-type-text-desc {
                  font-size: 14px;
                  line-height: 20px;
                  color: #262626;
                  text-align: left;
                }
              }
            }
          }
        }

        .order-detail-region-tabs {
          padding: 10px 15px;
          background: #fff;

          .custom-work-order-tab {
            position: relative;
            display: inline-flex;
            align-items: center;

            .triangle-topright {
              width: 14px;
              height: 14px;
              margin-right: 5px;
              line-height: 12px;
              color: #fa8d0a;
              text-align: center;
              background: #ffedd1;
              border-radius: 4px;
            }
          }

          .milestone-reply-form-body {
            padding: 16px 18px;
            background: #f8fbfc;
            border-radius: 4px;

            .milestone-reply-form-button-group {
              .order-detail-region-operating-btn {
                display: inline-block;
                margin-bottom: 10px;
              }

              .milestone-btn {
                padding: 6px 16px;
                margin-right: 10px;
                font-size: 14px;
                line-height: 20px;
                color: #595959;
                cursor: pointer;
                background: #efefef;
                border-radius: 4px;

                &:hover {
                  color: #0070d2;
                  background: #e1edff;
                }

                .iconfont {
                  font-size: 12px;
                  vertical-align: baseline;
                }
              }

              ::v-deep(.el-badge) {
                .milestone-badege-class {
                  right: calc(10px + var(--el-badge-size) / 2);

                  // display: inline-block;
                  width: 16px;
                  height: 16px;
                  padding: 0 !important;
                  margin: 0 !important;
                  line-height: 16px;
                  text-align: center;
                  border-radius: 50%;
                }

                &.btn-permission-hide {
                  display: none;
                }

                &.hide-btn {
                  display: none !important;
                }
              }
            }
          }

          ::v-deep(.order-detail-descriptions-widget) {
            padding: 30px 4px;
          }

          ::v-deep(.el-tabs__header) {
            padding: 0 15px;

            .el-tabs__nav-scroll {
              .el-tabs__item {
                &:hover {
                  color: #0070d2;
                }
              }

              .el-tabs__item.is-active {
                font-weight: bold;
                color: #0070d2;
              }

              .el-tabs__active-bar {
                background: #0070d2;
              }
            }
          }

          ::v-deep(.milestone-widget-collapse) {
            margin: 24px 0;
            border: 0 none !important;

            .el-collapse-item {
              border: 0 none !important;

              .el-collapse-item__header {
                font-size: 14px;
                font-weight: bolder;
                color: #262626;
                text-indent: 12px;

                // background: red;
                background: linear-gradient(#f6f9fb, #f8f8f8);
                border: 0 none !important;
                border-radius: 2px;
              }

              .el-collapse-item__wrap {
                border: 0 none !important;

                .order-detail-descriptions-widget {
                  padding: 14px 16px;
                  background: transparent !important;
                }
              }
            }
          }
        }
      }

      .anchor-icon-right {
        position: absolute;
        top: 4px;
        left: -30px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        padding: 4px;
        text-align: center;
        cursor: pointer;
        background: #fff;
        border: 1px solid rgb(1 6 62 / 9%);
        border-radius: 50%;

        &:hover {
          color: #fff;
          background: #0070d2;
          border: 1px solid #ddd;
          border-radius: 13px;
          box-shadow: 0 0 8px 0 rgb(0 0 0 / 16%);

          .link-expand {
            font-size: 10px;
            color: #fff;
          }
        }

        .link-expand {
          font-size: 10px;
          color: #0070d2;
        }

        .svg-icon-order-schedule,
        .svg-icon-order-schedule-white {
          width: 100%;
          height: 100%;
        }

        &.orange {
          background: #ff721b;
        }
      }
    }
  }

  .order-detail-descriptions-widget {
    padding: 14px 16px !important;
    background: #fff !important;

    // background: transparent !important;
  }
}

.dr-relateCard-container {
  .order-detail-descriptions-widget {
    background: transparent !important;
  }
}

.order-detail-region-col {
  display: inline-block;
  margin-bottom: 8px;

  .order-detail-region-title {
    display: inline-flex;
    height: 20px;
    padding: 0 5px;
    margin-right: 10px;
    font-size: 12px;
    line-height: 20px;
    border-radius: 2px;

    .order-detail-region-header-titlte {
      font-size: 18px;
      font-weight: bolder;
      line-height: 27px;
      color: #080707;
      text-align: left;
    }

    &.original {
      color: #0070d2;
      background: #ddefff;
    }

    &.repeat {
      color: #2b69f7;
      background: #dde1ff;
    }

    &.association {
      color: #fa8d0a;
      background: #ffedd1;
    }

    &.sccsAssociation {
      color: #00b699;
      background: #d9f4f0;
    }

    &.orderUse {
      color: #9870fa;
      background: #e6dcff;
    }
  }

  .order-detail-region-content {
    margin-right: 16px;

    .order-detail-region-sccs-text {
      font-size: 12px;
      color: #8c8c8c;
      text-align: left;
      cursor: pointer;

      .iconfont {
        margin-left: 2px;
        font-size: 12px;
        color: #8c8c8c;
        cursor: pointer;
      }
    }

    .order-detail-region-text {
      font-size: 12px;
      color: #262626;
      text-align: left;
      cursor: pointer;

      &:hover {
        color: #0070d2;
      }

      .iconfont {
        margin-left: 2px;
        font-size: 12px;
        color: #babec6;
        cursor: pointer;
      }
    }

    .order-detail-region-assistant-text {
      margin-left: 5px;
      font-size: 16px;
    }
  }
}

.order-time-line-body {
  padding-right: 10px;

  .order-time-line-col {
    position: relative;

    // width: calc(100% - 10px);
    padding: 13px 10px 18px 14px;
    margin-bottom: 12px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 6px 0 rgb(10 22 83 / 13%);

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      display: inline-block;
      width: 4px;
      height: 100%;
      content: "";
      border-radius: 4px 0 0 4px;
      transition: width 0.1s;
    }

    &:hover {
      box-shadow: 0 1px 12px 0 rgb(1 6 62 / 14%);
    }

    &.NOT_START {
      &::before {
        background: #ccecff;
      }
    }

    &.ON_GOING {
      &::before {
        background: #358ff0;
      }
    }

    &.COMPLETE {
      &::before {
        background: #64d16d;
      }
    }

    &.CANCEL {
      &::before {
        background: #dcdcdc;
      }

      > * {
        color: #999;
      }

      > div {
        opacity: 0.5;
      }
    }

    &.active {
      &::before {
        width: 7px;
        border-radius: 2px 0 0 2px;
      }

      &::after {
        position: absolute;
        inset: -2px;
        display: block;
        pointer-events: none;
        content: "";
        background: transparent;
        border: 2px solid #0070d2;
        border-radius: 4px;
      }
    }

    .order-time-line-col-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .order-time-line-col-title {
        max-width: 180px;
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        color: #262626;
        text-align: left;
      }

      .iconfont {
        font-size: 12px;
        color: #0071d2;
        cursor: pointer;
      }
    }

    .order-tag-body {
      margin-bottom: 0;

      .order-tag {
        display: inline-block;
        max-width: 100%;
        padding: 2px 6px;
        margin-right: 5px;
        margin-bottom: 5px;
        overflow: hidden;
        font-size: 12px;
        color: #e33e38;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: transparent;
        border: 1px solid #e33e38;
        border-radius: 11px;
      }
    }

    .order-tag-data-form-body {
      .order-tag-data-col {
        display: flex;
        align-items: baseline;
        margin-bottom: 11px;

        &:last-child {
          margin-bottom: 0;
        }

        .order-tag-data-title {
          font-size: 12px;
          line-height: 17px;
          color: #8c8c8c;
          text-align: left;
          white-space: nowrap;

          &::after {
            display: inline-block;
            content: "：";
          }
        }

        .order-tag-data-value {
          display: flex;
          align-items: center;
          margin-right: 5px;
          font-size: 12px;
          line-height: 17px;
          color: #262626;
          text-align: left;

          &:only-child {
            margin-right: 0;
          }

          .workOrderSuccessIconfont {
            color: #00cc7b;
          }

          .order-tag-data-not-submit-text {
            font-size: 12px;
            color: #e62412;
          }

          .order-tag-data-value-date-picker {
            margin-left: 5px;
            font-size: 12px;
            color: #262626;
            white-space: nowrap;
          }
        }

        .order-tag-data-value-flex {
          display: inline-flex;
          flex-wrap: wrap;
          align-items: center;
        }

        .order-data-val-tag-item {
          padding: 3px 5px;
          font-size: 12px;
          line-height: 14px;
          text-align: left;
          white-space: nowrap;
          border-radius: 4px;

          &.inAdvance {
            color: #67c23a;
            border: 1px solid #67c23a;
          }

          &.delay {
            color: #e37f6e;
            border: 1px solid #e37f6e;
          }
        }

        .order-tag-icon {
          margin-left: 5px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .order-state-body {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #f5f6fa;

    .order-state-col {
      position: relative;

      // flex: 1;
      display: inline-flex;
      margin-right: 13px;
      font-size: 12px;
      line-height: 17px;
      color: #8c8c8c;
      text-align: left;
      text-indent: 8px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        display: inline-block;
        width: 3px;
        height: 10px;
        content: "";
        transform: translateY(-50%);
      }

      &.not-started {
        &::before {
          background: #ccecff;
        }
      }

      &.progress {
        &::before {
          background: #358ff0;
        }
      }

      &.completed {
        &::before {
          background: #64d16d;
        }
      }

      &.cancel {
        &::before {
          background: #dcdcdc;
        }
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.milestone-reply-form-text {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .milestone-reply-form-text-col {
    display: inline-flex;
    align-items: center;
    height: 24px;
    margin-right: 16px;
    white-space: nowrap;

    .milestone-reply-form-tip {
      display: inline-block;
      font-size: 12px;
      color: #8c8c8c;

      &::after {
        font-size: 12px;
        color: #8c8c8c;
        content: "：";
      }
    }

    .milestone-reply-form-span {
      display: flex;
      align-items: center;

      .lk-avater-container {
        display: inline-flex;
      }

      .lk-avater-group-container {
        margin-right: 10px;
      }

      .milestone-reply-form-span-text {
        margin-left: 10px;
        font-size: 12px;
        color: #262626;

        &.milestone-reply-form-span-red-text {
          margin-left: 10px;
          font-weight: bolder;
          color: #e62412;
        }
      }
    }
  }
}

.hide-btn {
  display: none !important;
}

.work-order-collapse {
  .el-collapse-item {
    margin-bottom: 17px;
    border: 1px solid #e6eaf0;
    border-radius: 2px;

    .el-collapse-item__header {
      padding: 0 13px;
      background: #f6f9fb;
      border-radius: 2px;

      .order-form-edit-title-header {
        display: flex;
        flex: 1;
        align-items: center;

        .order-form-edit-title {
          margin-right: 60px;

          .el-text {
            font-size: 14px;
            font-weight: bolder;
            line-height: 16px;
            color: #262626;
            text-align: left;
          }
        }

        .order-form-edit-avatar {
          display: flex;
          align-items: center;
        }

        .milestone-reply-form-span-text {
          margin-left: 15px;
        }
      }
    }

    .el-collapse-item__wrap {
      padding: 20px;
    }
  }
}
