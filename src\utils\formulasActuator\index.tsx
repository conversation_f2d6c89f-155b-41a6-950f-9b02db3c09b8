//@ts-nocheck
import {
  SUM,
  AVERAGE,
  MAX,
  MIN,
  CEILING,
  FLOOR,
  INT,
  SUMIF,
  SUMIFS,
  COUNTIF,
  COUNTIFS,
  CONCATENATE,
  LEFT,
  RIGHT,
  SEARCH,
  YEAR,
  MONTH,
  DAY,
  HOUR,
  MINUTE,
  SECOND,
  DAYS,
  DATEDIF,
  WORKDAY,
  NETWORKDAYS,
  IF,
  IFS,
  AND,
  OR,
  TODAY,
  COUNTA,
  TEXTJOIN
} from "@formulajs/formulajs";

export const calculateFormulasValue = (formulas: string, params: any[]) => {
  const formulasValue = calculateFormulas(formulas, params);
  return formulasValue;
};

export const calculateFormulas = (formulas: string, params: any[]) => {
  let paramsList = [];
  for (let param of params) {
    if (param === "!==") {
      paramsList.push("<>");
    } else {
      paramsList.push(param);
    }
  }
  switch (formulas) {
    case "SUM":
      const sumParams = paramsList.filter(param => {
        return param !== null && param !== undefined;
      });
      return SUM(...sumParams);
    case "AVERAGE":
      const averageParams = paramsList.filter(param => {
        return param !== null && param !== undefined;
      });
      return AVERAGE(...averageParams);
    case "INT":
      const intParam = paramsList[0];
      if (intParam === null || intParam === undefined) {
        return 0;
      } else {
        return INT(intParam);
      }
    case "MAX":
      const timeParams = paramsList
        .filter(param => {
          return param !== null && param !== undefined;
        })
        .flat()
        .map(param => {
          return /^(\d{4}-\d{2}-\d{2})(?: (\d{2}:\d{2})(?::\d{2})?)?$/.test(
            param
          )
            ? new Date(param).getTime()
            : param;
        });

      return MAX(...timeParams);
    case "MIN":
      const minTimeParams = paramsList
        .filter(param => {
          return param !== null && param !== undefined;
        })
        .flat()
        .map(param => {
          return /^(\d{4}-\d{2}-\d{2})(?: (\d{2}:\d{2})(?::\d{2})?)?$/.test(
            param
          )
            ? new Date(param).getTime()
            : param;
        });

      return MIN(...minTimeParams);
    case "CEILING":
      const ceilingParamOne = paramsList[0];
      const ceilingParamTwo = paramsList[1];
      if (ceilingParamOne === null || ceilingParamOne === undefined) {
        return 0;
      } else if (ceilingParamTwo === null || ceilingParamTwo === undefined) {
        return "#VALUE!";
      } else {
        return CEILING(...paramsList);
      }
    case "FLOOR":
      const floorParamOne = paramsList[0];
      const floorParamTwo = paramsList[1];
      if (floorParamOne === null || floorParamOne === undefined) {
        return 0;
      } else if (floorParamTwo === null || floorParamTwo === undefined) {
        return "#VALUE!";
      } else {
        return FLOOR(...paramsList);
      }
    case "SUMIF":
      const sumIfParam1 = paramsList[0];
      const sumIfParam2 = paramsList[1];
      const sumIfParam3 = paramsList[2];

      return SUMIF(
        sumIfParam1.flat(),
        sumIfParam2,
        sumIfParam3.flat().map(number => parseFloat(number))
      );
    case "SUMIFS":
      return SUMIFS(...paramsList);
    case "COUNTIF":
      let countIfParam1;
      if (paramsList[0] instanceof Array) {
        countIfParam1 = paramsList[0].map(param => {
          if (param === null || param === undefined || param === "") {
            return " ";
          } else {
            return param;
          }
        });
      } else {
        countIfParam1 =
          paramsList[0] === null || paramsList[0] === undefined
            ? " "
            : paramsList[0];
      }
      const countIfParam2 =
        paramsList[1] === null || paramsList[1] === undefined
          ? " "
          : paramsList[1];

      return COUNTIF(countIfParam1, countIfParam2);
    case "COUNTIFS":
      return COUNTIFS(...paramsList);
    case "CONCATENATE":
      const concatEnateParams = paramsList.filter(
        param => param !== null && param !== undefined
      );
      return CONCATENATE(...concatEnateParams);
    case "TEXTJOIN":
      const textJoinParams = paramsList.filter(
        param => param !== null && param !== undefined
      );
      return TEXTJOIN(...textJoinParams);
    case "LEFT":
      const leftParamOne = paramsList[0];
      const leftParamTwo = paramsList[1];
      if (leftParamOne === null || leftParamOne === undefined) {
        return " ";
      } else if (leftParamTwo === null || leftParamTwo === undefined) {
        return LEFT(leftParamOne, 0);
      } else {
        return LEFT(...paramsList);
      }
    case "RIGHT":
      const rightParamOne = paramsList[0];
      const rightParamTwo = paramsList[1];
      if (rightParamOne === null || rightParamOne === undefined) {
        return " ";
      } else if (rightParamTwo === null || rightParamTwo === undefined) {
        return RIGHT(rightParamOne, 0);
      } else {
        return RIGHT(...paramsList);
      }
    case "SEARCH":
      const searchParamsIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (searchParamsIndex === -1) {
        return "#VALUE!";
      }
      return SEARCH(...paramsList);
    case "DAYS":
      const daysIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (daysIndex === -1) {
        return "#VALUE!";
      }
      return DAYS(...paramsList);
    case "DATEDIF":
      const dateDifIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (dateDifIndex === -1) {
        return "#VALUE!";
      }
      return DATEDIF(...paramsList);
    case "YEAR":
      const yearIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (yearIndex === -1) {
        return "#VALUE!";
      }
      return YEAR(...paramsList);
    case "MONTH":
      const monthIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (monthIndex === -1) {
        return "#VALUE!";
      }
      return MONTH(...paramsList);
    case "DAY":
      const dayIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (dayIndex === -1) {
        return "#VALUE!";
      }
      return DAY(...paramsList);
    case "HOUR":
      const hourIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (hourIndex === -1) {
        return "#VALUE!";
      }
      return HOUR(...paramsList);
    case "MINUTE":
      const minuteIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (minuteIndex === -1) {
        return "#VALUE!";
      }
      return MINUTE(...paramsList);
    case "SECOND":
      const secondIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (secondIndex === -1) {
        return "#VALUE!";
      }
      return SECOND(...paramsList);
    case "WORKDAY":
      const workDayIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (workDayIndex === -1) {
        return "#VALUE!";
      }
      return WORKDAY(paramsList[0], paramsList[1], paramsList[2].flat());
    case "NETWORKDAYS":
      const netWorkDaysIndex = paramsList.findIndex(
        param => param === null || param === undefined
      );
      if (netWorkDaysIndex === -1) {
        return "#VALUE!";
      }
      return NETWORKDAYS(paramsList[0], paramsList[1], paramsList[2].flat());
    case "IF":
      const ifParams = paramsList.map((param, index) => {
        if (param === null || param === undefined || param === "") {
          return index === 0 ? false : " ";
        } else {
          return index === 0 ? !!param : param;
        }
      });
      return IF(...ifParams);
    case "IFS":
      const ifsParams = paramsList.map((param, index) => {
        if (param === null || param === undefined || param === "") {
          return index % 2 ? false : " ";
        } else {
          return index % 2 ? !!param : param;
        }
      });
      return IFS(...ifsParams);
    case "AND":
      const andParams = paramsList.map(param => {
        if (param === null || params === undefined) {
          return false;
        } else {
          return param;
        }
      });
      return AND(...andParams);
    case "OR":
      const orParams = paramsList.map(param => {
        if (param === null || params === undefined) {
          return false;
        } else {
          return param;
        }
      });
      return OR(...orParams);
    case "TODAY":
      return TODAY();
    case "COUNTA":
      return COUNTA(...paramsList);
  }
};
