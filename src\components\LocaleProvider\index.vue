<template>
  <el-config-provider :locale="currentLocale">
    <slot />
  </el-config-provider>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { ElConfigProvider } from "element-plus";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { storageLocal } from "@pureadmin/utils";

// 获取本地存储的 locale 信息
const currentLocale = computed(() => {
  const lang = storageLocal().getItem("translationLang");
  return lang === "zh" ? zhCn : en;
});
</script>
