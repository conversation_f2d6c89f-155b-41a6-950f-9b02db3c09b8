<template>
  <div class="role-per-col">
    <el-checkbox
      v-model="orderCheckAll"
      class="checkbox-parent"
      :disabled="disabled"
      :indeterminate="orderIndeterminate"
      @change="handleCheckAllChange"
    >
      {{ sccsOrderData.label }}
    </el-checkbox>
    <el-checkbox-group v-model="orderCheckIds">
      <el-checkbox
        v-for="orderRoleItem in sccsOrderData.childrenList"
        :key="orderRoleItem"
        class="checkbox-group-col"
        :disabled="disabled"
        :label="orderRoleItem.label"
        :value="orderRoleItem.key"
        @change="val => handleOrderCheckboxValue(val, orderRoleItem.key)"
      >
        <div class="checkbox-span-body">
          <div class="checkbox-flex">{{ orderRoleItem.label }}</div>
          <div
            v-if="!['create_order', 'import_order'].includes(orderRoleItem.key)"
            class="checkbox-flex"
          >
            <el-radio-group
              v-model="permissionMap.get(orderRoleItem.key).orderScope"
              :disabled="handleCountPermissionMap(orderRoleItem.key)"
              @change="val => handleChangeOrderScope(val, orderRoleItem.key)"
            >
              <el-radio
                v-for="item in sccsDataRoleList"
                :key="item.value"
                :value="item.value"
              >
                {{ t(item.label) }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      </el-checkbox>
    </el-checkbox-group>
  </div>
  <div class="role-per-col">
    <el-checkbox
      v-model="milestoneCheckAll"
      class="checkbox-parent checkbox-group-col"
      :indeterminate="milestoneIndeterminate"
      :disabled="disabled"
      @change="handleMilestoneCheckAllChange"
    >
      <div class="checkbox-span-body">
        <div class="checkbox-flex">{{ sccsMilestoneData.label }}</div>
        <div class="checkbox-flex">
          <el-radio-group
            v-model="milestonePermItem.orderScope"
            :disabled="handleCountPermissionMap(milestonePermItem.orderScope)"
            @change="handleChangeMilestoneScope"
          >
            <el-radio
              v-for="item in sccsDataRoleList"
              :key="item.value"
              :disabled="disabled"
              :value="item.value"
            >
              {{ t(item.label) }}
            </el-radio>
          </el-radio-group>
        </div>
      </div>
    </el-checkbox>

    <el-collapse
      v-for="milestone in sccsMilestoneData?.childrenList"
      :key="milestone.key"
      v-model="milestoneRowActives"
    >
      <el-collapse-item :name="milestone.key">
        <template #title>
          <div class="checkbox-collapse-header" @click.stop>
            <el-checkbox
              class="checkbox-parent checkbox-group-col"
              :model-value="milestoneCheckMap.get(milestone.key).checkAll"
              :disabled="disabled"
              :indeterminate="
                milestoneCheckMap.get(milestone.key).isIndeterminate
              "
              @change="
                checked =>
                  handleMilestoneModeCheckAllChange(checked, milestone.key)
              "
            >
              <div class="checkbox-span-body">
                <ReText
                  class="checkbox-flex"
                  type="info"
                  :tippyProps="{ delay: 50000 }"
                >
                  {{ milestone.label }}
                </ReText>
              </div>
            </el-checkbox>
          </div>
        </template>
        <template #icon="{ isActive }">
          <span class="icon-ele">
            <el-icon v-if="isActive"><CaretBottom /></el-icon>
            <el-icon v-else><CaretRight /></el-icon>
          </span>
        </template>

        <el-checkbox-group
          :model-value="milestoneCheckMap.get(milestone.key).checkGroupValue"
        >
          <el-row :gutter="20">
            <el-col
              v-for="milestoneItem in milestone.childrenList"
              :key="milestoneItem.key"
              :span="8"
            >
              <el-checkbox
                class="checkbox-group-col"
                :label="milestoneItem.label"
                :value="milestoneItem.key"
                :disabled="disabled"
                @change="
                  checked =>
                    handleMilestoneItemCheckAllChange(
                      checked,
                      milestoneItem.key,
                      milestone.key
                    )
                "
              >
                <div class="checkbox-span">
                  {{ milestoneItem.label }}
                  <el-tooltip
                    v-if="milestoneItem.remark"
                    effect="dark"
                    :content="milestoneItem.remark"
                    placement="top"
                    :show-after="500"
                  >
                    <i class="iconfont link-explain font14 color80" />
                  </el-tooltip>
                </div>
              </el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script lang="ts" setup>
import {
  ref,
  PropType,
  watch,
  reactive,
  nextTick,
  getCurrentInstance,
  computed
} from "vue";
import { useI18n } from "vue-i18n";
import { CaretRight, CaretBottom } from "@element-plus/icons-vue";
import { ReText } from "@/components/ReText";

const props = defineProps({
  sccsOrderData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  sccsToolRolePermItemList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  sccsMilestoneData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderScope: {
    type: String as PropType<string>,
    default: "person"
  },
  disabled: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const { t } = useI18n();
let milestoneCheckMap = reactive(new Map());
const orderCheckAll = ref<boolean>(false);
const orderIndeterminate = ref<boolean>(false);
const milestoneCheckAll = ref<boolean>(false);
const milestoneIndeterminate = ref<boolean>(false);
const orderCheckIds = ref<string[]>([]);
const permissionMap = ref<any>();
const initFlag = ref<boolean>(false);
const milestoneRowActives = ref<any>([]);
const milestonePermItem = ref<any>({
  orderScope: "",
  permissionKey: "trade_sccs_permission_milestone"
});
const sccsDataRoleList = computed(() => {
  if (props.orderScope === "coopTeam") {
    return [
      { value: "ALL_ORDER", label: "trade_common_allOrder" },
      { value: "COOP_TEAM_CREATE", label: "trade_common_myTeamCreate" },
      { value: "CUSTOM", label: "trade_common_custom" }
    ];
  } else {
    return [
      { value: "ALL_ORDER", label: "trade_common_allOrder" },
      { value: "MY_CREATE", label: "trade_common_createmeorder" },
      { value: "CUSTOM", label: "trade_common_custom" }
    ];
  }
});

const handleCheckAllChange = (val: boolean) => {
  let checkedList = [];
  props.sccsOrderData.childrenList.map(child => checkedList.push(child.key));
  orderCheckIds.value = val ? checkedList : [];
  orderIndeterminate.value = false;

  for (let [key, value] of permissionMap.value) {
    if (!["create_order", "import_order"].includes(key)) {
      value.orderScope = val ? "ALL_ORDER" : "";
    }
  }
};

/**
 * 里程碑权限全选操作
 * @param checked
 */
const handleMilestoneCheckAllChange = (checked: boolean) => {
  milestoneIndeterminate.value = false;

  for (let milestone of milestoneCheckMap) {
    const milestoneConfigRow = props.sccsMilestoneData?.childrenList.find(
      milestoneCol => milestoneCol.key === milestone[0]
    );
    let milestoneCheckGroupValue = [];
    milestoneConfigRow.childrenList.every(child =>
      milestoneCheckGroupValue.push(child.key)
    );

    if (checked) {
      milestoneCheckMap.set(milestone[0], {
        checkAll: true,
        checkGroupValue: milestoneCheckGroupValue,
        isIndeterminate: false
      });

      milestoneConfigRow.childrenList.forEach(child => {
        handleMilestoneRelation(true, child.key, milestone[0]);
      });
    } else {
      milestoneCheckMap.set(milestone[0], {
        checkAll: false,
        checkGroupValue: [],
        isIndeterminate: false
      });
    }
  }
};

/**
 * 单个里程碑区域勾选联动逻辑
 * @param checked 是否选中
 * @param milestoneKey 当前里程碑的key值
 */
const handleMilestoneModeCheckAllChange = (
  checked: any,
  milestoneKey: string
) => {
  const milestoneConfigRow = props.sccsMilestoneData?.childrenList.find(
    milestoneCol => milestoneCol.key === milestoneKey
  );
  let milestoneCheckGroupValue = [];
  milestoneConfigRow.childrenList.every(child =>
    milestoneCheckGroupValue.push(child.key)
  );
  milestoneCheckMap.set(milestoneKey, {
    checkAll: checked,
    checkGroupValue: !checked ? [] : milestoneCheckGroupValue,
    isIndeterminate: false
  });

  let milestoneCheckCount = 0;
  for (let value of milestoneCheckMap.values()) {
    if (!!value.checkAll) {
      milestoneCheckCount += 1;
    }
  }

  if (milestoneCheckCount !== milestoneCheckMap.size) {
    milestoneIndeterminate.value = true;
    milestoneCheckAll.value = false;

    milestoneConfigRow.childrenList.forEach(child => {
      handleMilestoneRelation(true, child.key, milestoneKey);
    });
  } else if (milestoneCheckCount === milestoneCheckMap.size) {
    milestoneIndeterminate.value = false;
    milestoneCheckAll.value = true;
  }
  if (milestoneCheckCount === 0) {
    milestoneIndeterminate.value = false;
    milestoneCheckAll.value = false;
  }
};

/**
 * 单个里程碑权限勾选联动逻辑
 * @param checked 是否选中
 * @param milestoneKey 当前里程碑的key值
 * @param parentPermissionKey: 父级里程碑的key值
 */
const handleMilestoneItemCheckAllChange = (
  checked: any,
  permissionKey: string,
  parentPermissionKey: string
) => {
  const milestoneConfigRow = props.sccsMilestoneData?.childrenList.find(
    milestoneCol => milestoneCol.key === parentPermissionKey
  );

  // 获取当前的 checkGroupValue 数组
  let currentCheckGroupValue = new Set([
    ...milestoneCheckMap.get(parentPermissionKey).checkGroupValue
  ]);

  if (checked) {
    currentCheckGroupValue.add(permissionKey);
  } else {
    // 如果是取消选中，找到并移除该值
    currentCheckGroupValue.delete(permissionKey);
  }

  // 更新 milestoneCheckMap
  const currentMilestoneCount = currentCheckGroupValue.size;

  if (currentMilestoneCount === milestoneConfigRow.childrenList.length) {
    milestoneCheckMap.set(parentPermissionKey, {
      checkAll: true,
      checkGroupValue: Array.from(currentCheckGroupValue),
      isIndeterminate: false
    });
  } else if (currentMilestoneCount === 0) {
    milestoneCheckMap.set(parentPermissionKey, {
      checkAll: false,
      checkGroupValue: [],
      isIndeterminate: false
    });
  } else {
    milestoneCheckMap.set(parentPermissionKey, {
      checkAll: false,
      checkGroupValue: Array.from(currentCheckGroupValue),
      isIndeterminate: true
    });
  }
  milestoneCheckMap.get(parentPermissionKey).checkGroupValue = Array.from(
    currentCheckGroupValue
  );

  let milestoneCheckCount = 0;
  let milestoneIsIndeterminateCount = 0;
  for (let value of milestoneCheckMap.values()) {
    if (!!value.checkAll) {
      milestoneCheckCount += 1;
    }
    if (!!value.isIndeterminate) {
      milestoneIsIndeterminateCount += 1;
    }
  }

  if (milestoneCheckCount !== milestoneCheckMap.size) {
    milestoneIndeterminate.value = true;
    milestoneCheckAll.value = false;
  } else if (milestoneCheckCount === milestoneCheckMap.size) {
    milestoneIndeterminate.value = false;
    milestoneCheckAll.value = true;
  }
  if (milestoneCheckCount === 0) {
    milestoneIndeterminate.value =
      milestoneIsIndeterminateCount === 0 ? false : true;
    milestoneCheckAll.value = false;
  }
  handleMilestoneRelation(checked, permissionKey, parentPermissionKey);
};

/**
 * 里程碑单个权限勾选联动逻辑
 * @param checked
 * @param milestoneKey 当前里程碑的key值
 * @param parentPermissionKey
 */
const handleMilestoneRelation = (
  checked: boolean,
  milestoneKey: string,
  parentPermissionKey: string
) => {
  if (checked && !orderCheckIds.value.includes("view_main_form")) {
    initFlag.value = true;
    orderCheckIds.value.push("view_main_form");
    permissionMap.value.set("view_main_form", {
      orderScope: "ALL_ORDER",
      permissionKey: "view_main_form"
    });
    handleChangeOrderScope("ALL_ORDER", "view_main_form");
  }
  if (checked && milestoneKey === "view_ms_reply") {
    const milestoneMapList =
      milestoneCheckMap.get(parentPermissionKey).checkGroupValue;
    milestoneMapList.push("view_work_order");
    nextTick(() => {
      milestoneCheckMap.set(parentPermissionKey, {
        checkAll: milestoneCheckMap.get(parentPermissionKey).checkAll,
        checkGroupValue: Array.from(new Set(milestoneMapList)),
        isIndeterminate:
          milestoneCheckMap.get(parentPermissionKey).isIndeterminate
      });
    });
  } else if (
    checked &&
    !["view_work_order", "view_ms_reply"].includes(milestoneKey)
  ) {
    const milestoneMapList =
      milestoneCheckMap.get(parentPermissionKey).checkGroupValue;
    milestoneMapList.push("view_work_order", "view_ms_reply");
    nextTick(() => {
      milestoneCheckMap.set(parentPermissionKey, {
        checkAll: milestoneCheckMap.get(parentPermissionKey).checkAll,
        checkGroupValue: Array.from(new Set(milestoneMapList)),
        isIndeterminate:
          milestoneCheckMap.get(parentPermissionKey).isIndeterminate
      });
    });
  }
};

/**
 * 订单权限单个勾选逻辑
 * @param checked
 * @param permissionKey
 */
const handleOrderCheckboxValue = (checked: any, permissionKey: string) => {
  initFlag.value = true;
  // 订单权限关联
  if (
    !["create_order", "import_order", "view_main_form"].includes(
      permissionKey
    ) &&
    !orderCheckIds.value.includes("view_main_form")
  ) {
    orderCheckIds.value.push("view_main_form");
    permissionMap.value.set("view_main_form", {
      orderScope: "ALL_ORDER",
      permissionKey: "view_main_form"
    });
    handleChangeOrderScope("ALL_ORDER", "view_main_form");
  } else if (permissionKey === "view_main_form") {
    permissionMap.value.set("view_main_form", {
      orderScope: "ALL_ORDER",
      permissionKey: "view_main_form"
    });
    if (!checked) {
      for (let milestone of milestoneCheckMap.values()) {
        if (milestone.checkGroupValue.length > 0) {
          orderCheckIds.value.push("view_main_form");
          return;
        }
      }
      const otherOrder = orderCheckIds.value.filter(
        orderCheckStr =>
          !["create_order", "import_order", "view_main_form"].includes(
            orderCheckStr
          )
      );
      if (otherOrder.length > 0) {
        orderCheckIds.value.push("view_main_form");
        return;
      }
      permissionMap.value.set("view_main_form", {
        orderScope: undefined,
        permissionKey: "view_main_form"
      });
    } else {
      handleChangeOrderScope("ALL_ORDER", "view_main_form");
    }
  } else {
    if (!checked) {
      permissionMap.value.set(permissionKey, {
        orderScope: undefined,
        permissionKey: permissionKey
      });
    }
  }
  orderCheckAll.value =
    orderCheckIds.value.length === props.sccsOrderData.childrenList.length;
  orderIndeterminate.value =
    orderCheckIds.value.length !== props.sccsOrderData.childrenList.length &&
    orderCheckIds.value.length !== 0;
};

const handleChangeMilestoneScope = val => {
  milestonePermItem.value.orderScope = val;
};

const handleChangeOrderScope = (val, permissionKey) => {
  permissionMap.value.set(
    permissionKey,
    ["create_order", "import_order"].includes(permissionKey)
      ? { permissionKey: permissionKey }
      : {
          orderScope: val,
          permissionKey: permissionKey
        }
  );
  if (permissionKey === "view_main_form") {
    orderCheckIds.value.map(orderPermission => {
      permissionMap.value.set(
        orderPermission,
        ["create_order", "import_order"].includes(orderPermission)
          ? {
              permissionKey: orderPermission
            }
          : {
              orderScope: permissionMap.value.get("view_main_form").orderScope,
              permissionKey: orderPermission
            }
      );
      milestonePermItem.value.orderScope =
        permissionMap.value.get("view_main_form").orderScope;
    });
  }
};

const handleCountPermissionMap = (permissionKey: string) => {
  if (props.disabled) {
    return true;
  } else if (permissionKey === "view_main_form") {
    return false;
  } else {
    return permissionMap.value.get("view_main_form").orderScope !== "ALL_ORDER";
  }
};

const handleObtainCommitData = () => {
  let orderRolePermItemList = [];
  orderCheckIds.value.forEach(orderPermission => {
    orderRolePermItemList.push(permissionMap.value.get(orderPermission));
  });
  let milestoneRolePermItemList = [];
  for (let milestone of milestoneCheckMap) {
    milestoneRolePermItemList.push({
      msId: milestone[0],
      permissionKeyList: milestone[1].checkGroupValue
    });
  }
  return {
    orderRolePermItemList: unique(orderRolePermItemList),
    milestoneRolePermItemList: milestoneRolePermItemList,
    milestonePermItem: milestonePermItem.value
  };
};

const unique = arr => {
  const res = new Map();
  return arr.filter(
    arr => !res.has(arr.permissionKey) && res.set(arr.permissionKey, 1)
  );
};

watch(
  () => props.sccsOrderData,
  () => {
    if (
      props.sccsOrderData &&
      props.sccsOrderData.childrenList &&
      props.sccsOrderData.childrenList.length > 0
    ) {
      const orderPermissions = props.sccsOrderData.childrenList;
      const orderPermissionMap = new Map();
      orderPermissions.map(orderPermission => {
        orderPermissionMap.set(
          orderPermission.key,
          ["create_order", "import_order"].includes(orderPermission.key)
            ? {
                permissionKey: orderPermission.key
              }
            : {
                permissionKey: orderPermission.key,
                orderScope: undefined
              }
        );
      });
      permissionMap.value = orderPermissionMap;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.sccsMilestoneData,
  () => {
    if (
      props.sccsMilestoneData &&
      props.sccsMilestoneData.childrenList &&
      props.sccsMilestoneData.childrenList.length > 0
    ) {
      const milestonePermissionMap = new Map();
      props.sccsMilestoneData.childrenList.map(milestonePermission => {
        milestoneRowActives.value.push(milestonePermission.key);
        milestonePermissionMap.set(milestonePermission.key, {
          checkGroupValue: [],
          checkAll: false,
          isIndeterminate: false
        });
      });
      // milestoneCheckMap = milestonePermissionMap;
      milestoneCheckMap.clear();
      milestonePermissionMap.forEach((value, key) => {
        milestoneCheckMap.set(key, value);
      });
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.sccsToolRolePermItemList,
  () => {
    if (props.sccsToolRolePermItemList) {
      const { milestoneRolePermItemList, orderRolePermItemList } =
        props.sccsToolRolePermItemList;

      if (milestoneRolePermItemList && milestoneRolePermItemList.length > 0) {
        for (let milestoneItem of milestoneRolePermItemList) {
          const milestoneConfigData =
            props.sccsMilestoneData?.childrenList.find(
              milestone => milestone.key === milestoneItem.msId
            );
          const msPermCheckNumber = milestoneItem.permissionKeyList.length;
          milestoneCheckMap.set(milestoneItem.msId, {
            checkAll:
              msPermCheckNumber === 0
                ? false
                : milestoneConfigData.childrenList.length === msPermCheckNumber,
            checkGroupValue: milestoneItem.permissionKeyList,
            isIndeterminate:
              milestoneConfigData.childrenList.length !== msPermCheckNumber &&
              msPermCheckNumber !== 0
          });
        }

        let milestoneCheckCount = 0;
        let milestoneIsIndeterminateCount = 0;
        for (let value of milestoneCheckMap.values()) {
          if (!!value.checkAll) {
            milestoneCheckCount += 1;
          }
          if (!!value.isIndeterminate) {
            milestoneIsIndeterminateCount += 1;
          }
        }

        if (milestoneCheckCount !== milestoneCheckMap.size) {
          milestoneIndeterminate.value = true;
          milestoneCheckAll.value = false;
        } else if (milestoneCheckCount === milestoneCheckMap.size) {
          milestoneIndeterminate.value = false;
          milestoneCheckAll.value = true;
        }
        if (milestoneCheckCount === 0) {
          milestoneIndeterminate.value =
            milestoneIsIndeterminateCount === 0 ? false : true;
          milestoneCheckAll.value = false;
        }
      }
      if (orderRolePermItemList && orderRolePermItemList.length > 0) {
        orderCheckAll.value =
          unique(orderRolePermItemList).length ===
          props.sccsOrderData.childrenList.length;
        orderIndeterminate.value =
          unique(orderRolePermItemList).length !==
            props.sccsOrderData.childrenList.length &&
          unique(orderRolePermItemList).length !== 0;
        orderRolePermItemList.forEach(orderRolePerm => {
          orderCheckIds.value.push(orderRolePerm.permissionKey);
          permissionMap.value.set(
            orderRolePerm.permissionKey,
            ["create_order", "import_order"].includes(
              orderRolePerm.permissionKey
            )
              ? { permissionKey: orderRolePerm.permissionKey }
              : {
                  permissionKey: orderRolePerm.permissionKey,
                  orderScope: orderRolePerm.orderScope
                }
          );
        });
      }
      if (
        props.sccsToolRolePermItemList &&
        props.sccsToolRolePermItemList.milestonePermItem
      ) {
        milestonePermItem.value.orderScope =
          props.sccsToolRolePermItemList.milestonePermItem.orderScope;
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => [orderCheckIds, initFlag.value],
  () => {
    if (orderCheckIds.value.includes("view_main_form") && initFlag.value) {
      orderCheckIds.value.map(orderPermission => {
        permissionMap.value.set(
          orderPermission,
          ["create_order", "import_order"].includes(orderPermission)
            ? {
                permissionKey: orderPermission
              }
            : {
                orderScope:
                  permissionMap.value.get("view_main_form").orderScope,
                permissionKey: orderPermission
              }
        );
      });
    }
  },
  {
    deep: true
  }
);

watch(
  () => [milestoneCheckMap],
  () => {
    const hasCheckedValue = Array.from(milestoneCheckMap.values()).some(
      value => {
        return value.checkGroupValue.length > 0;
      }
    );
    if (hasCheckedValue) {
      milestonePermItem.value.orderScope =
        permissionMap.value.get("view_main_form").orderScope;
    } else {
      milestonePermItem.value.orderScope = undefined;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  handleObtainCommitData
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  display: inline-flex !important;
  margin-bottom: 10px;
  font-weight: bolder;
}

.checkbox-group-col {
  width: 100% !important;
  padding-left: 8px;
  margin-bottom: 10px;

  &:hover {
    cursor: pointer;
    background: #f5f7fa;
  }

  &:last-child {
    margin-bottom: 0;
  }

  ::v-deep(.el-checkbox__label) {
    flex: 1 !important;

    .checkbox-span-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .checkbox-flex {
        flex: 1;
        max-width: 100%;
        overflow: hidden;

        &:nth-child(2) {
          padding-right: 20px;
          text-align: right;
        }
      }
    }
  }
}

.el-collapse {
  padding: 0 15px;
  border: 0 none;

  ::v-deep(.el-collapse-item__header) {
    flex-direction: row-reverse;
    justify-content: flex-end;
    border: 0 none;

    .icon-ele {
      padding-right: 5px;
    }

    .checkbox-collapse-header {
      width: 100%;

      .el-checkbox__label {
        flex: none !important;
        width: calc(100% - 30px);
        text-align: left !important;

        .checkbox-span-body {
          width: 99% !important;

          .checkbox-flex {
            width: 92% !important;
            overflow: hidden;
            color: #606266 !important;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  ::v-deep(.el-collapse-item__content) {
    padding: 0 18px;
    border: 0 none;
  }
}
</style>
