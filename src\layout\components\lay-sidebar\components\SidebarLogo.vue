<script setup lang="ts">
import TeamManage from "@/views/teamManage/index.vue";
defineProps({
  collapse: Boolean
});
</script>

<template>
  <div class="sidebar-logo-container" :class="{ collapses: collapse }">
    <transition name="sidebarLogoFade">
      <TeamManage />
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.sidebar-logo-container {
  position: relative;
  width: 100%;
  margin: 18px auto;
  text-align: center;

  .sidebar-name {
    width: 32px;
    height: 32px;
    line-height: 32px;
    background: linear-gradient(-45deg, #e1843d, #ec7840);
    border-radius: 8px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
  }
}
</style>
