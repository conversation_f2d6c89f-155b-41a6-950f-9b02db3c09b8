<template>
  <div v-loading="loading" class="lk-upload-container">
    <el-upload
      v-if="imageUrl === '' && fileList.length === 0"
      v-bind="$attrs"
      v-model:file-list="fileList"
      class="avatar-uploader"
      :action="requireURL"
      list-type="picture"
      :headers="headers"
      :data="{ ossFileType: 'TRADE_USER_AVATAR' }"
      :limit="1"
      accept=".bmp,.gif,.jpeg,.jpg,.pjpeg,.png,.tiff,.webp"
      :before-upload="beforeAvatarUpload"
      :on-success="handleAvatarSuccess"
      :on-exceed="handleExceed"
    >
      <div>
        <div v-if="props.mode === 'uploadImage'" class="lk-upload-area">
          <FontIcon icon="link-add" />
          <div class="lk-upload-title">{{ t("trade_common_uploadImage") }}</div>
        </div>
        <div v-else-if="props.mode === 'avatar'">
          <LKAvater :size="props.avatarSize" />
        </div>
      </div>
      <template #file="{ file }">
        <div class="lk-upload-area">
          <el-image
            class="el-upload-list__item-thumbnail"
            :src="file.url"
            fit="contain"
            :style="`width: ${props.avatarSize}px; height: ${props.avatarSize}px`"
          />
          <span class="el-upload-list__item-actions">
            <span v-if="!disabled" class="el-upload-list__item-delete">
              <FontIcon icon="link-add" />
            </span>
          </span>
        </div>
      </template>
    </el-upload>
    <div
      v-else-if="imageUrl"
      class="lk-upload-area"
      @mouseenter="handleMouseOver(true)"
      @mouseleave="handleMouseOver(false)"
    >
      <el-image
        class="el-upload-list__item-thumbnail"
        :src="imageUrl"
        fit="contain"
        :style="`width: ${props.avatarSize}px; height: ${props.avatarSize}px`"
      />
      <span v-if="imageUrlShow" class="lk-upload-area-hover">
        <FontIcon icon="link-delete" @click="handleDelete" />
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, PropType } from "vue";
import type { UploadProps, UploadInstance, UploadUserFile } from "element-plus";
import { getToken, formatToken } from "@/utils/auth";
import LKAvater from "@/components/lkAvatar/index";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const imageUrl = ref("");
const requireURL = ref<string>(
  import.meta.env.VITE_BASE_URL +
    import.meta.env.VITE_API_TRADE_PREFIX +
    "/trade/file/upload"
);
const loading = ref<boolean>(false);
const headers = reactive<any>({
  Authorization: formatToken(getToken().accessToken)
});
const upload = ref<UploadInstance>();
const disabled = ref(false);
const fileList = ref<UploadUserFile[]>([]);
const imageUrlShow = ref<boolean>(false);

const props = defineProps({
  mode: {
    type: String as PropType<string>,
    default: "uploadImage"
  },
  avatarSize: {
    type: Number as PropType<number>,
    default: 86
  }
});

const handleAvatarSuccess: UploadProps["onSuccess"] = uploadFile => {
  loading.value = false;
  imageUrlShow.value = false;
  //@ts-ignore
  imageUrl.value = uploadFile.data;
};

const beforeAvatarUpload: UploadProps["beforeUpload"] = rawFile => {
  loading.value = true;
  const validImageFormats = [
    "image/bmp",
    "image/gif",
    "image/jpeg",
    "image/pjpeg",
    "image/png",
    "image/tiff",
    "image/webp"
  ];
  if (!validImageFormats.includes(rawFile.type)) {
    ElMessage.error(t("trade_common_imageNotRequired"));
    return false;
  }
  if (rawFile.size / 1024 / 1024 > 20) {
    ElMessage.error("Avatar picture size can not exceed 20MB!");
    return false;
  }
  return true;
};

const handleExceed: UploadProps["onExceed"] = files => {
  upload.value!.clearFiles();
};

const handleMouseOver = (bool: boolean) => {
  imageUrlShow.value = bool;
};

const handleDelete = (): void => {
  imageUrl.value = "";
  fileList.value = [];
};

defineExpose({
  imageUrl
});
</script>
<style lang="scss" scoped>
.lk-upload-container {
  width: 88px;
  height: 88px;

  .lk-upload-area {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 88px;
    height: 88px;
    text-align: center;
    border: 1px dashed #dedede;
    border-radius: 4px;

    .iconfont {
      margin-bottom: 3px;
      font-size: 23px;
    }

    .lk-upload-title {
      font-size: 12px;
      line-height: 17px;
      color: #bfbfbf;
      text-align: center;
    }

    .lk-upload-area-hover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #63636380;

      .iconfont {
        font-size: 20px;
        line-height: 84px;
        color: #fff;
        text-align: center;
        cursor: pointer;
      }
    }
  }

  ::v-deep(.el-upload-list) {
    margin: 0;

    .el-upload-list__item {
      padding: 0;
      margin: 0;

      .lk-upload-area {
        border: 0 none;
      }
    }
  }
}
</style>
