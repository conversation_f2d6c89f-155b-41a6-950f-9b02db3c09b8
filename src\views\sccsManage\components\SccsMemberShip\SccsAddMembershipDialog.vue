<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_add_member_tip')"
    class="lk-middle-dialog"
    @confirm="confrimLkDialog"
    @close="closeLkDialog"
  >
    <template #default>
      <div class="create-team-dialog-form">
        <el-form ref="ruleFormRef" label-position="top" label-width="auto">
          <el-form-item>
            <LkTeamSelectV2
              v-model="teamMemberIdList"
              filterable
              :options="sccsMemberList"
              clearable
              :placeholder="t('trade_common_searchAccount')"
              multiple
              :props="{ label: 'username', value: 'teamMemberId' }"
              :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
              value-id="userId"
              @change="handleAddFormRow"
            />
          </el-form-item>
          <div v-if="teamMemberIdList.length === 0" class="fence-list-tip">
            <i class="iconfont link-tips-warm" />
            {{ t("trade_sccs_memberMangeTip") }}
          </div>
          <el-row
            v-for="(item, index) in teamMemberIdList"
            :key="index"
            style="width: 99.6%; padding: 10px; margin: 0 !important"
          >
            <el-col :span="23">
              <div class="sccs-member-manage-col">
                <div class="sccs-member-manage-col-top">
                  <span
                    v-if="!getMemberMsg(teamMemberIdList[index]).activate"
                    class="tag-register-tip"
                  >
                    ({{ t("trade_common_unregistered") }})
                  </span>
                  <LKAvatar
                    :teamInfo="{
                      avatar: getMemberMsg(teamMemberIdList[index])?.avatar,
                      username: getMemberMsg(teamMemberIdList[index])?.username,
                      email:
                        getMemberMsg(teamMemberIdList[index])?.email ||
                        getMemberMsg(teamMemberIdList[index])?.account
                    }"
                    :size="18"
                  />
                  <ReText
                    class="sccs-member-manage-text-row"
                    :tippyProps="{ delay: 50000 }"
                  >
                    <div class="sccs-member-manage-col-username">
                      {{ getMemberMsg(teamMemberIdList[index])?.username }}
                      <span class="sccs-member-manage-col-account">
                        ({{ getMemberMsg(teamMemberIdList[index])?.email }})
                      </span>
                    </div>
                  </ReText>
                </div>
                <div class="sccs-member-manage-col-bottom">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-select-v2
                        v-model="form[index].sccsRoleIds"
                        filterable
                        :options="sccsRoleList"
                        clearable
                        :placeholder="t('trade_sccs_businessRoleNameTip2')"
                        multiple
                      />
                    </el-col>
                    <el-col :span="12">
                      <el-select-v2
                        v-model="form[index].dataFenceIds"
                        filterable
                        :options="sccsFenceList"
                        clearable
                        :placeholder="t('trade_sccs_dataFenceNameSelectTip')"
                        multiple
                      />
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-col>
            <el-col :span="1">
              <div class="itemCenter" @click="handleDelete(index)">
                <i class="iconfont link-ashbin" />
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { ref } from "vue";
import LkDialog from "@/components/lkDialog/index";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import LKAvatar from "@/components/lkAvatar/index";
import { ReText } from "@/components/ReText";
import {
  getSccsNotAddList,
  getSccsFenceList,
  getSccsRoleList,
  createSccsMember
} from "@/api/sccs";

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
let form = ref([
  {
    sccsRoleIds: [],
    dataFenceIds: []
  }
]);
const sccsFenceList = ref<any[]>([]);
const sccsRoleList = ref<any[]>([]);
const LkDialogRef = ref<any>(null);
const ruleFormRef = ref<any>(null);
const teamMemberIdList = ref<any>([]);
const sccsMemberList = ref<any[]>([]);

const emit = defineEmits<{
  (e: "handleSuccess"): void;
}>();

const closeLkDialog = (): void => {
  form.value = [];
  teamMemberIdList.value = [];
};

const getMemberMsg = (id: string) => {
  const memberItem = sccsMemberList.value.find(
    memberLi => memberLi.teamMemberId === id
  );
  return memberItem;
};

const handleAddFormRow = val => {
  let data = [];
  for (let i = 0, len = val.length; i < len; i++) {
    const item = form.value.find(
      (teamFormItem: any) => teamFormItem.id === val[i]
    );
    if (!item) {
      data.push({
        id: val[i],
        sccsRoleIds: [],
        dataFenceIds: []
      });
    } else {
      data.push(item);
    }
  }
  form.value = data;
};

const confrimLkDialog = async (): Promise<void> => {
  let submitData = [];
  form.value.map((item, index) => {
    const { teamMemberId, userId } = getMemberMsg(
      teamMemberIdList.value[index]
    );
    submitData.push({
      sccsId: props.basicInfo.id,
      teamMemberId,
      userId,
      teamOwner: false,
      ...item
    });
  });
  const { code, data } = await createSccsMember(submitData);
  if (code === 0) {
    LkDialogRef.value.close();
    emit("handleSuccess");
  }
};

const handleDelete = (index: number) => {
  teamMemberIdList.value.splice(index, 1);
  form.value.splice(index, 1);
};

const open = async (): Promise<void> => {
  LkDialogRef.value.open();
  const sccsId = props.basicInfo.id;
  Promise.all([
    getSccsNotAddList({ sccsId: sccsId }),
    getSccsFenceList({ sccsId: sccsId }),
    getSccsRoleList({ sccsId: sccsId })
  ]).then(res => {
    sccsMemberList.value = res[0].data;
    sccsFenceList.value = res[1].data;
    sccsRoleList.value = res[2].data;
  });
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.el-form {
  ::v-deep(.el-form-item.widthfixRow) {
    width: 100% !important;

    .el-form-item__content {
      width: 100% !important;
    }
  }
}

.itemCenter {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .iconfont {
    cursor: pointer;
  }
}

.sccs-member-manage-col {
  box-sizing: border-box;
  width: 98%;
  // box-shadow: 0px 2px 18px 0px rgba(0, 0, 0, 0.34);
  padding: 13px 11px;
  background: #f7f7f7;
  border-radius: 4px;

  .sccs-member-manage-col-top {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .sccs-member-manage-text-row {
      display: flex;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;

      .sccs-member-manage-col-username {
        width: 100%;
        padding: 0 4px;
        overflow: hidden;
        font-size: 14px;
        line-height: 16px;
        color: #262626;
        text-overflow: ellipsis;
        text-shadow: 0 2px 18px 0 rgb(0 0 0 / 34%);
        white-space: nowrap;

        .sccs-member-manage-col-account {
          font-size: 14px;
          line-height: 16px;
          color: #595959;
        }
      }
    }
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.fence-list-tip {
  height: 38px;
  padding-left: 14px;
  margin-bottom: 14px;
  font-size: 14px;
  line-height: 38px;
  color: #fa8d0a;
  text-align: left;
  background: #fdf6ec;
  border-radius: 2px;

  .iconfont {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
  }
}

.tag-register-tip {
  width: 60px;
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
