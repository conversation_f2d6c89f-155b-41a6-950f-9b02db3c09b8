<template>
  <div class="team-member-info-container team-module-area-container">
    <el-tabs v-model="activeName" editable :before-leave="handleBeforeLeave">
      <template #add-icon>
        <el-button
          v-if="isChange"
          type="primary"
          color="#0070D2"
          :disabled="addBtnDisabled"
          @click="handleAdd"
        >
          {{ t("trade_common_save") }}
        </el-button>
      </template>
      <el-tab-pane :label="t('trade_plan_time')" name="planTime">
        <SccsPlanTimeSetting
          v-if="activeName === 'planTime'"
          ref="SccsPlanTimeSettingRef"
          :basicInfo="basicInfo"
          :groupId="groupId"
          @handleChange="handleChange"
          @handleSettingDisabled="handleSettingDisabled"
        />
      </el-tab-pane>
      <el-tab-pane :label="t('trade_tag_setting')" name="tagSetting">
        <SccsPlanLabelSetting
          v-if="activeName === 'tagSetting'"
          ref="SccsPlanLabelSettingRef"
          :basicInfo="basicInfo"
          :groupId="groupId"
          @handleChange="handleChange"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
import { getCurrentInstance, nextTick, ref } from "vue";
import { useI18n } from "vue-i18n";
import { ElButton, TabPaneName } from "element-plus";
import SccsPlanTimeSetting from "./SccsPlanSetting/SccsPlanTimeSetting.vue";
import SccsPlanLabelSetting from "./SccsPlanSetting/SccsPlanLabelSetting.vue";

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const activeName = ref("planTime");
const SccsPlanTimeSettingRef = ref<any>(null);
const SccsPlanLabelSettingRef = ref<any>(null);
const isChange = ref<boolean>(false);
const addBtnDisabled = ref<boolean>(false);

defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleChange = (): void => {
  isChange.value = true;
};

const handleAdd = async (): Promise<void> => {
  if (activeName.value === "planTime") {
    await SccsPlanTimeSettingRef.value.handleSaveTableData();
    nextTick(() => {
      isChange.value = false;
    });
  }
};

const handleBeforeLeave = async (
  newActiveName: TabPaneName,
  oldActiveName: TabPaneName
) => {
  if (oldActiveName === "planTime" && isChange.value && !addBtnDisabled.value) {
    //@ts-ignore
    const result = await proxy.$submitMessageBox.confirm(
      t("trade_common_dataUpdateSaveTip"),
      {
        confirmButtonText: t("trade_common_save"),
        cancelButtonText: t("trade_common_cancel"),
        nextButtonText: t("trade_common_notsave")
      }
    );
    if (result === "cancel") {
      return false;
    } else if (result === "confirm") {
      isChange.value = false;
      SccsPlanTimeSettingRef.value.handleSaveTableData();
      //@ts-ignore
      activeName.value = newActiveName;
    } else if (result === "nextConfirm") {
      isChange.value = false;
      //@ts-ignore
      activeName.value = newActiveName;
    }
  }
};

const handleSettingDisabled = (bool: boolean) => {
  addBtnDisabled.value = bool;
};

const handleBeforeLeaveFn = async cb => {
  //@ts-ignore
  const result = await proxy.$submitMessageBox.confirm(
    t("trade_common_dataUpdateSaveTip"),
    {
      confirmButtonText: t("trade_common_save"),
      cancelButtonText: t("trade_common_cancel"),
      nextButtonText: t("trade_common_notsave")
    }
  );
  if (result === "cancel") {
  } else if (result === "confirm") {
    isChange.value = false;
    SccsPlanTimeSettingRef.value.handleSaveTableData();
    cb();
  } else if (result === "nextConfirm") {
    isChange.value = false;
    cb();
  }
};

const handleGetTabPanelState = () => {
  return {
    isChange: isChange.value,
    activeName: activeName.value,
    addBtnDisabled: addBtnDisabled.value
  };
};

defineExpose({
  handleGetTabPanelState,
  handleBeforeLeaveFn,
  activeName: activeName.value
});
</script>
<style lang="scss" scoped>
.team-module-area-container {
  height: 100% !important;

  ::v-deep(.el-tabs) {
    height: 100% !important;
    padding: 0 25px !important;

    .el-tab-pane {
      height: 100% !important;
    }

    .team-module-area-btn {
      position: absolute;
      top: 0;
      right: 0;
    }

    .el-tabs__header {
      width: auto !important;

      .el-tabs__item {
        width: auto !important;
        padding: 0 !important;
        margin-right: 22px !important;
        margin-left: 10px !important;

        &.is-active {
          font-weight: bolder;
          color: #0070d2 !important;
          background: transparent !important;
        }

        .el-icon {
          display: none;
        }
      }

      .el-tabs__active-bar {
        height: 3px !important;
        color: #0070d2 !important;
      }

      .el-tabs__new-tab {
        position: absolute;
        top: 50%;
        right: 30px;
        z-index: 1000;
        border: 0 none;
        transform: translateY(-81%);
      }
    }
  }
}
</style>
