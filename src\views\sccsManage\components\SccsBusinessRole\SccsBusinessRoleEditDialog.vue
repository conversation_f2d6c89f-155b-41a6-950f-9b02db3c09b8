<template>
  <LkDialog
    ref="LkDialogRef"
    :title="dialogTitle"
    class="lk-maximum-dialog"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <template #default>
      <div class="create-team-dialog-form">
        <el-form
          ref="ruleFormRef"
          label-position="top"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          :scroll-to-error="true"
        >
          <!-- 商务角色名称 -->
          <el-form-item :label="labels.businessRoleName" prop="name">
            <el-input
              v-model="ruleForm.name"
              :placeholder="placeholders.businessRoleName"
              show-word-limit
              clearable
              maxlength="100"
              :disabled="isBusinessAdmin"
            />
          </el-form-item>

          <!-- 团队成员选择 -->
          <el-form-item :label="labels.teamMembers">
            <LkTeamSelectV2
              v-model="ruleForm.teamMemberIdList"
              filterable
              :options="memberList"
              clearable
              :placeholder="placeholders.selectText"
              value-key="teamMemberId"
              :props="{ label: 'username', value: 'teamMemberId' }"
              multiple
              value-id="userId"
              :teamRenderFields="{ avatar: 'avatar', label: 'username' }"
              :disabled="false"
            />
          </el-form-item>

          <!-- 权限配置 -->
          <el-form-item class="role-permission-body">
            <SccsBusinessOrderTool
              ref="SccsBusinessOrderToolRef"
              :sccsSettingData="sccsSettingData"
              :disabled="isBusinessAdmin"
              :sccsToolRolePermItemList="SccsRoleBusinessDetails"
            />
            <SccsOrderRoleTool
              ref="sccsOrderRoleToolRef"
              :sccsOrderData="sccsOrderSettingData"
              :disabled="isBusinessAdmin"
              :sccsMilestoneData="sccsOrderMilestoneData"
              :sccsToolRolePermItemList="SccsRoleBusinessDetails"
            />
          </el-form-item>
        </el-form>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { reactive, ref, computed } from "vue";
import LkDialog from "@/components/lkDialog/index";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import type { FormRules } from "element-plus";
import SccsBusinessOrderTool from "./SccsBusinessOrderTool.vue";
import SccsOrderRoleTool from "../SccsOrderRoleTool.vue";
import {
  getSccsRolePermissionTree,
  createSccsRole,
  updateSccsRole,
  getRoleDetail,
  getSccsMemberList
} from "@/api/sccs";
import { message } from "@/utils/message";

// 定义接口
interface RoleListProp {
  name: string;
  teamMemberIdList: string[];
}

// Props 定义
const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

// 定义 emit
const emit = defineEmits(["updateSccsSuccess"]);

// 国际化
const { t } = useI18n();

// Ref 定义
const LkDialogRef = ref<any>(null);
const sccsOrderRoleToolRef = ref<any | HTMLElement>(null);
const SccsBusinessOrderToolRef = ref<any>(null);
const ruleFormRef = ref<any>(null);

// 响应式数据
const sccsSettingData = ref<any[]>([]);
const sccsOrderSettingData = ref<any[]>([]);
const sccsOrderMilestoneData = ref<any[]>([]);
const SccsRoleBusinessDetails = ref<any>({});
const memberList = ref<any[]>([]);
const dialogStatus = ref<string>("add");
const rowDetailId = ref<string>("");
const ruleForm = reactive<RoleListProp>({
  name: "",
  teamMemberIdList: []
});

// 是否为业务管理员角色
const isBusinessAdmin = ref<boolean>(false);

// 表单验证规则
const rules = reactive<FormRules<RoleListProp>>({
  name: [
    {
      required: true,
      message: t("trade_team_inputTeamRoleName"),
      trigger: "blur"
    }
  ]
});

// 动态标题
const dialogTitle = computed(() =>
  dialogStatus.value === "add"
    ? t("trade_common_createBusinessRole")
    : t("trade_sccs_editSccsRole")
);

// 国际化文本提取
const labels = {
  businessRoleName: t("trade_sccs_businessRoleName"),
  teamMembers: t("trade_team_members")
};

const placeholders = {
  businessRoleName: t("trade_sccs_businessRoleNameTip"),
  selectText: t("trade_common_selectText")
};

// 关闭对话框
const handleClose = (): void => {
  ruleForm.name = "";
  ruleForm.teamMemberIdList = [];
  ruleFormRef.value?.clearValidate();
};

// 确认对话框
const handleConfirm = async (): Promise<void> => {
  if (!ruleFormRef.value) return;

  ruleFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const sccsToolRolePermItemList =
        SccsBusinessOrderToolRef.value.SccsOrderChecked.map(
          (value: string) => ({ permissionKey: value })
        );

      const commitData = sccsOrderRoleToolRef.value.handleObtainCommitData();
      const apiCall =
        dialogStatus.value === "add" ? createSccsRole : updateSccsRole;
      const params = {
        ...(dialogStatus.value === "edit" && { id: rowDetailId.value }),
        sccsId: props.basicInfo.id,
        sccsToolRolePermItemList,
        ...commitData,
        ...ruleForm
      };

      const { code } = await apiCall(params);
      if (code === 0) {
        message(t("trade_common_dealSuccess"), { type: "success" });
        LkDialogRef.value.close();
        ruleFormRef.value.resetFields();
        emit("updateSccsSuccess"); // 修复后可正常使用
      }
    }
  });
};

// 打开对话框
const open = async (id?: string): Promise<void> => {
  isBusinessAdmin.value = false;
  const { data } = await getSccsRolePermissionTree({
    sccsId: props.basicInfo.id
  });
  sccsSettingData.value = data[0];
  sccsOrderSettingData.value = data[1];
  sccsOrderMilestoneData.value = data[2];

  const { data: sccsMemberList } = await getSccsMemberList({
    sccsId: props.basicInfo.id
  });
  memberList.value = sccsMemberList;

  if (id) {
    dialogStatus.value = "edit";
    rowDetailId.value = id;
    await setDetailData(id);
    // 判断是否为业务管理员角色
    isBusinessAdmin.value = ruleForm.name === t("trade_common_businessAdmin");
  } else {
    dialogStatus.value = "add";
    SccsRoleBusinessDetails.value = {
      sccsToolRolePermItemList: [],
      orderRolePermItemList: [],
      milestoneRolePermItemList: [],
      milestonePermItem: []
    };
  }
  LkDialogRef.value.open();
};

// 设置详情数据
const setDetailData = async (id: string): Promise<void> => {
  const { data } = await getRoleDetail({ id });
  ruleForm.name = data.name;
  ruleForm.teamMemberIdList = data.teamMemberIdList;
  SccsRoleBusinessDetails.value = {
    sccsToolRolePermItemList: data.sccsToolRolePermItemList,
    orderRolePermItemList: data.orderRolePermItemList,
    milestoneRolePermItemList: data.milestoneRolePermItemList,
    milestonePermItem: data.milestonePermItem
  };
};

// 暴露方法
defineExpose({ open });
</script>
<style lang="scss" scoped>
@use "../index.scss";

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
