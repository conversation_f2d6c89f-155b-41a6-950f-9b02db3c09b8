<template>
  <div>
    <el-select-v2
      v-model="widgetFormData[widgetConfigure._fc_id]"
      filterable
      :options="coopTeamList"
      clearable
      :placeholder="placeholder"
      :props="{ label: 'teamName', value: 'teamId' }"
      :teleported="widgetRowIndex === -1"
      :multiple="widgetConfigure.props.multiple"
      v-bind="containerRef ? { appendTo: containerRef } : {}"
    >
      <template #default="{ item }">
        <LkNoPopoverAvatar
          class="colorOrange"
          shape="square"
          :teamInfo="{
            avatar: item.teamAvatar,
            username: item.teamName
          }"
          :size="18"
        />
        {{ item.teamName }}({{ item.teamShortName }})
      </template>
      <template #label="{ value }">
        <template v-if="getWidgetValue(value)">
          <LkNoPopoverAvatar
            class="colorOrange"
            shape="square"
            :teamInfo="{
              avatar: getWidgetValue(value).teamAvatar,
              username: getWidgetValue(value).teamName
            }"
            :size="18"
          />
          {{ getWidgetValue(value).teamName }}
        </template>
      </template>
    </el-select-v2>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, ref, watch } from "vue";
import { ElSelectV2 } from "element-plus";
import LkNoPopoverAvatar from "@/components/lkNoPopoverAvatar/index";
import {
  cloneDeep,
  isEqual,
  storageLocal,
  storageSession
} from "@pureadmin/utils";

interface WidgetMemberItem {
  userId: string;
  username: string;
  account: string;
}

interface WidgetCoopTeamItem {
  teamId: string;
  teamName: string;
  teamShortName: string;
}

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  staffPerson: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const emit = defineEmits(["handleSubTableWidgetValueChange"]);

// 计算属性
const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

// 响应式数据
const widgetFormData = ref<any>({});
const coopTeamList = ref<any[]>([]);

// 注入的依赖
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const containerRef = inject("containerRef", null);

// 获取控件值对应的团队信息
const getWidgetValue = (value: string) => {
  if (!value || coopTeamList.value.length === 0) {
    return null;
  }

  const sccsMemberItem = coopTeamList.value.find(
    sccsMember => sccsMember.teamId === value
  );
  return sccsMemberItem || null;
};

// 格式化多选数据，按照原始顺序排序
const handleFormatCheckboxData = () => {
  if (
    !props.staffPerson?.sccsCoopTeamList ||
    !Array.isArray(props.staffPerson.sccsCoopTeamList)
  ) {
    return [];
  }

  const coopTeamIds = props.staffPerson.sccsCoopTeamList.map(
    coop => coop.teamId
  );

  const orderMap = {};
  coopTeamIds.forEach((item, index) => {
    orderMap[item] = index;
  });

  const checkboxWidgetValue = cloneDeep(
    widgetFormData.value[props.widgetConfigure._fc_id]
  );

  if (!Array.isArray(checkboxWidgetValue)) {
    return [];
  }

  return [...checkboxWidgetValue].sort((a, b) => {
    const indexA = orderMap[a] !== undefined ? orderMap[a] : Infinity;
    const indexB = orderMap[b] !== undefined ? orderMap[b] : Infinity;
    return indexA - indexB;
  });
};

// 获取控件表单值
const getWidgetFormVal = (): unknown => {
  if (!props.widgetConfigure.props.multiple) {
    const currentValue = widgetFormData.value[props.widgetConfigure._fc_id];
    return props.widgetConfigure._fc_id &&
      currentValue !== null &&
      currentValue !== undefined
      ? [currentValue]
      : [];
  } else {
    return handleFormatCheckboxData();
  }
};

// 获取控件标签显示
const getWidgetFormLabel = (widgetValue: any) => {
  const coopWidgetValue =
    widgetValue instanceof Array
      ? widgetValue
      : widgetValue !== null && widgetValue !== undefined
        ? [widgetValue]
        : [];

  const coopTeamDataList = coopWidgetValue
    .map(coopTeamValue => {
      const coopTeamItem = coopTeamList.value.find(
        coop => coop.teamId === coopTeamValue
      );
      return coopTeamItem
        ? `${coopTeamItem.teamName}(${coopTeamItem.teamShortName})`
        : "";
    })
    .filter(label => label !== "");

  return coopTeamDataList.join(",");
};

// 获取控件显示对象
const getWidgetFormShowObject = (widgetValue: any) => {
  const coopWidgetValue =
    widgetValue instanceof Array
      ? widgetValue
      : widgetValue !== null && widgetValue !== undefined
        ? [widgetValue]
        : [];

  return coopWidgetValue
    .map(coopTeamValue => {
      const coopTeamItem = coopTeamList.value.find(
        coop => coop.teamId === coopTeamValue
      );

      return coopTeamItem
        ? {
            shortName: coopTeamItem.teamShortName,
            id: coopTeamItem.teamId,
            name: coopTeamItem.teamName,
            avatar: coopTeamItem.teamAvatar
          }
        : null;
    })
    .filter(item => item !== null);
};

// 监听员工信息变化，更新团队列表
watch(
  () => props.staffPerson.sccsCoopTeamList,
  () => {
    if (
      props.staffPerson?.sccsCoopTeamList &&
      Array.isArray(props.staffPerson.sccsCoopTeamList) &&
      props.staffPerson.sccsCoopTeamList.length > 0
    ) {
      const isCoopTeam =
        storageSession().getItem("coopTeamMark") === "coopTeam";
      let sccsCoopTeamList = props.staffPerson.sccsCoopTeamList;

      if (isCoopTeam) {
        const userInfo: any = storageLocal().getItem("user-info");
        const latestLoginTeamId = userInfo?.latestLoginTeamId;
        const widgetValue = widgetFormData.value[props.widgetConfigure._fc_id];

        if (latestLoginTeamId) {
          sccsCoopTeamList = widgetValue
            ? sccsCoopTeamList.filter(
                coopTeam =>
                  coopTeam.teamId === widgetValue ||
                  coopTeam.teamId === latestLoginTeamId
              )
            : sccsCoopTeamList.filter(
                coopTeam => coopTeam.teamId === latestLoginTeamId
              );
        }
      }
      coopTeamList.value = sccsCoopTeamList;
    } else {
      coopTeamList.value = [];
    }
  },
  {
    deep: true,
    immediate: true
  }
);

// 监听表单值变化，同步到控件
watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  () => {
    if (!props.widgetConfigure.props.multiple) {
      const formValue = props.trendsForm?.[props.widgetConfigure._fc_id];
      widgetFormData.value[props.widgetConfigure._fc_id] =
        formValue instanceof Array ? cloneDeep(formValue).join("") : formValue;
    } else {
      widgetFormData.value = cloneDeep(props.trendsForm || {});
    }
  },
  {
    immediate: true
  }
);

// 监听控件值变化，触发表单更新
watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    const currentFormValue = props.trendsForm?.[props.widgetConfigure._fc_id];

    const isNewValEmpty =
      newVal == null || (Array.isArray(newVal) && newVal.length === 0);
    const isCurrentFormEmpty =
      currentFormValue == null ||
      (Array.isArray(currentFormValue) && currentFormValue.length === 0);
    const isOldValEmpty =
      oldVal == null || (Array.isArray(oldVal) && oldVal.length === 0);

    // 如果新值和原值都为空，不触发更新
    if (isNewValEmpty && isCurrentFormEmpty) {
      return;
    }

    // 如果新值和旧值都为空，不触发更新
    if (isNewValEmpty && isOldValEmpty) {
      return;
    }

    // 值发生变化时才触发更新
    if (JSON.stringify(newVal) !== JSON.stringify(currentFormValue)) {
      const widgetId = props.widgetConfigure._fc_id;
      const widgetValueData = getWidgetFormVal();

      const updateData = {
        obj: widgetValueData,
        label: getWidgetFormLabel(widgetValueData),
        showObj: getWidgetFormShowObject(widgetValueData),
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      };

      if (props.widgetRowIndex !== -1) {
        emit(
          "handleSubTableWidgetValueChange",
          widgetId,
          updateData,
          widgetFormData.value[widgetId]
        );
      } else {
        handleWidgetFormsValue(
          widgetId,
          updateData,
          widgetFormData.value[widgetId]
        );
      }
    }
  },
  {
    deep: true
  }
);
</script>
