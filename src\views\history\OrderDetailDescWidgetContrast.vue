<template>
  <div class="order-detail-desc-widget-contrast">
    <el-row :gutter="10">
      <el-col
        v-for="(widgeItem, index) in widgetForm"
        :key="index"
        :span="24"
        class="contrast-item"
      >
        <DcText
          v-if="
            [
              'DrInput',
              'DrInputNumber',
              'DrAddress',
              'DrFormulas',
              'DrDatePicker',
              'DrPercentage',
              'DrExchangeRates',
              'DrLocation'
            ].includes(widgeItem.type)
          "
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcFilesUpload
          v-else-if="widgeItem.type === 'DrFilesUpload'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcRate
          v-else-if="widgeItem.type === 'DrRate'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcTextarea
          v-else-if="widgeItem.type === 'DrTextarea'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcCheckbox
          v-else-if="widgeItem.type === 'DrCheckbox'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcEditor
          v-else-if="widgeItem.type === 'DrEditor'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcRadio
          v-else-if="widgeItem.type === 'DrRadio'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcImagesUpload
          v-else-if="widgeItem.type === 'DrImagesUpload'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <DcTableForm
          v-else-if="widgeItem.type === 'DrTableForm'"
          :widgetForm="widgeItem"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
        <el-card
          v-else-if="widgeItem.type === 'DrCard'"
          :shadow="widgeItem.props.shadow"
        >
          <template #header>
            <div class="card-header">
              <span>{{ widgeItem.props.header }}</span>
            </div>
          </template>
          <template #default>
            <el-row :gutter="widgeItem.props.gutter">
              <el-col
                v-for="(child, index) in widgeItem.children"
                :key="index"
                :span="child.props.span"
              >
                <div
                  v-for="(widgeItem, index) in child.children"
                  :key="index"
                  class=""
                >
                  <DcText
                    v-if="
                      [
                        'DrInput',
                        'DrInputNumber',
                        'DrAddress',
                        'DrFormulas',
                        'DrDatePicker',
                        'DrPercentage',
                        'DrExchangeRates',
                        'DrLocation'
                      ].includes(widgeItem.type)
                    "
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcFilesUpload
                    v-else-if="widgeItem.type === 'DrFilesUpload'"
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcRate
                    v-else-if="widgeItem.type === 'DrRate'"
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcTextarea
                    v-else-if="widgeItem.type === 'DrTextarea'"
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcCheckbox
                    v-else-if="widgeItem.type === 'DrCheckbox'"
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcEditor
                    v-else-if="widgeItem.type === 'DrEditor'"
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcRadio
                    v-else-if="
                      ['DrRadio', 'DrSCCSMemberSelect'].includes(widgeItem.type)
                    "
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcImagesUpload
                    v-else-if="
                      ['DrImagesUpload', 'DrSignature'].includes(widgeItem.type)
                    "
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                  <DcTableForm
                    v-else-if="widgeItem.type === 'DrTableForm'"
                    :widgetForm="widgeItem"
                    :widgetData="widgetData"
                    :lastHistoryObj="lastHistoryObj"
                  />
                </div>
              </el-col>
            </el-row>
          </template>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import DcText from "@/components/DataComparisonDesigner/DcText";
import DcCheckbox from "@/components/DataComparisonDesigner/DcCheckbox";
import DcRate from "@/components/DataComparisonDesigner/DcRate";
import DcTextarea from "@/components/DataComparisonDesigner/DcTextarea";
import DcEditor from "@/components/DataComparisonDesigner/DcEditor";
import DcRadio from "@/components/DataComparisonDesigner/DcRadio";
import DcImagesUpload from "@/components/DataComparisonDesigner/DcImagesUpload";
import DcFilesUpload from "@/components/DataComparisonDesigner/DcFilesUpload";
import DcTableForm from "@/components/DataComparisonDesigner/DcTableForm";

const props = defineProps({
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  lastHistoryObj: {
    type: Object as PropType<any>,
    default: () => {}
  }
});
</script>
<style lang="scss" scoped>
.el-card {
  border: none;
}

::v-deep(.el-card__header) {
  padding: 0;
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: bolder;
  color: #262626;
}

::v-deep(.el-card__body) {
  padding: 10px 0;
}

.card-header {
  font-size: 16px;
  font-weight: bolder;
  color: #262626;
}
</style>
