<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="task-center-header">
    <div class="task-center-header-tab">
      <div
        v-for="(item, index) in tabList"
        :key="index"
        class="task-center-header-tab-item"
        :class="{
          'is-active': item.type === taskCenterHeaderParams.currentTab
        }"
        @click="handleTabClick(item)"
      >
        <el-badge
          :hidden="!item.badge"
          :value="item.badge"
          :max="99"
          class="item"
        >
          <span>{{ t(item.title) }}</span>
        </el-badge>
      </div>
    </div>
    <div class="task-center-header-search">
      <el-input
        v-model:model-value="taskCenterHeaderParams.searchValue"
        :placeholder="t(taskCenterHeaderParams.searchPlaceholder)"
        clearable
        style="width: 217px; height: 30px"
        :prefix-icon="Search"
        @clear="handleSearchClear"
        @change="handleSearchChange"
      />
      <div class="task-center-header-search-tab">
        <span
          v-for="searchType in taskCenterHeaderParams.searchTypeOptions"
          :key="searchType.value"
          class="task-center-header-search-tabItem"
          :class="{
            'is-active': searchType.value === taskCenterHeaderParams.searchType
          }"
          @click="handleSearchTypeClick(searchType.value)"
          >{{ t(searchType.label) }}</span
        >
      </div>
      <TaskListFilterWidget
        :orderFilterList="
          taskCenterHeaderParams.menuConditionMap.get(menuActive)
        "
        :creatorData="creatorData"
        @beforeEnter="beforeEnter"
        @handleSearchTableByConditions="handleSearchTableByConditions"
      />
    </div>
  </div>
  <div class="task-center-header-tip">
    <div class="task-center-header-flex">
      <i class="iconfont link-tips-notice" />
      <span class="task-center-header-tip-text">
        <ReText
          type="info"
          class="order-detail-region-header-titlte"
          :tippyProps="{ delay: 50000 }"
        >
          {{ t(taskCenterHeaderParams.tip) }}
        </ReText></span
      >
    </div>
    <div
      v-if="taskCenterHeaderParams.showRightText"
      class="task-center-header-tip-card-desc"
    >
      <span class="task-center-header-tip-card-desc-line order" />
      <span class="task-center-header-tip-card-desc-text">{{
        t("trade_work_order")
      }}</span>
      <span class="task-center-header-tip-card-desc-line orderReply" />
      <span class="task-center-header-tip-card-desc-text">{{
        t("trade_order_taskReply")
      }}</span>
      <span class="task-center-header-tip-card-desc-line milestoneReply" />
      <span class="task-center-header-tip-card-desc-text">{{
        t("trade_template_replyBtnName")
      }}</span>
    </div>
    <div
      v-if="taskCenterHeaderParams.showViewUpdata"
      class="task-center-header-tip-btn"
      :class="{
        'is-active': taskCenterHeaderParams.submitNew
      }"
      @click="viewUpdata"
    >
      <span
        ><i class="iconfont link-display" />{{
          t("trade_check_for_updates")
        }}</span
      >
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";

import TaskListFilterWidget from "@/views/taskCenter/components/TaskListFilterWidget.vue";

const { t } = useI18n();
const props = defineProps({
  tabList: {
    type: Array as PropType<any>,
    default: () => []
  },
  taskCenterHeaderParams: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  menuActive: {
    type: String,
    default: ""
  },
  creatorData: {
    type: Object as PropType<any>,
    default: () => ({})
  }
});
const emit = defineEmits([
  "beforeEnter",
  "handleTabClick",
  "handleSearchTypeClick",
  "handleSearchClear",
  "handleSearchChange",
  "viewUpdata",
  "handleSearchTableByConditions"
]);
const viewUpdata = () => {
  emit("viewUpdata");
};
const beforeEnter = () => {
  emit("beforeEnter");
};
const handleSearchClear = () => {
  emit("handleSearchClear");
};
const handleSearchChange = val => {
  emit("handleSearchChange", val);
};
const handleTabClick = (item?: {}) => {
  emit("handleTabClick", item);
};
const handleSearchTypeClick = (type: string) => {
  emit("handleSearchTypeClick", type);
};

const handleSearchTableByConditions = (conditions: any) => {
  emit("handleSearchTableByConditions", conditions);
};
</script>
<style lang="scss" scoped>
.task-center-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;
  border-bottom: 1px solid #ebebeb;

  .task-center-header-tab {
    display: inline-flex;
    align-items: center;

    .task-center-header-tab-item {
      padding: 0 4px 10px;
      margin-right: 24px;
      font-weight: bold;
      color: #797979;
      cursor: pointer;

      &.is-active {
        color: #0070d2;
        border-bottom: 3px solid #0070d2;
      }

      &:hover {
        color: #0070d2;
      }
    }
  }

  .task-center-header-search {
    display: inline-flex;
    align-items: center;
    margin-top: -12px;

    .task-center-header-search-tab {
      display: inline-flex;
      align-items: center;
      justify-content: space-around;
      height: 32px;
      padding: 0 8px;
      margin-left: 10px;
      background: #fff;
      border-radius: 4px;

      .task-center-header-search-tabItem {
        height: 24px;
        padding: 0 8px;
        margin-right: 4px;
        font-size: 13px;
        font-weight: 500;
        line-height: 24px;
        color: #262626;
        text-align: center;
        white-space: nowrap;
        cursor: pointer;
        border-radius: 4px;

        &:last-child {
          margin-right: 0;
        }

        &.is-active {
          color: #0070d2;
          background: #ddefff;
        }

        &:hover {
          color: #0070d2;
          background: #ddefff;
        }
      }
    }

    .task-center-header-search-filter {
      display: flex;
      align-items: center;
      margin-left: 22px;
      font-size: 13px;
      cursor: pointer;

      .link-filter {
        font-size: 13px;
      }
    }
  }
}

.task-center-header-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  font-size: 12px;
  color: #8c8c8c;

  .task-center-header-flex {
    display: flex;
    align-items: center;

    .link-tips-notice {
      margin-right: 6px;
      font-size: 12px;
    }

    .task-center-header-tip-text {
      display: inline-flex;
      align-items: center;
      max-width: 800px;
      font-size: 12px;
    }
  }

  .task-center-header-tip-card-desc {
    display: flex;
    align-items: center;

    .task-center-header-tip-card-desc-line {
      width: 3px;
      height: 11px;
      margin-right: 4px;

      &.order {
        background: #0070d2;
      }

      &.orderReply {
        background: #f59348;
      }

      &.milestoneReply {
        background: #edd057;
      }
    }

    .task-center-header-tip-card-desc-text {
      margin-right: 18px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .task-center-header-tip-btn {
    display: flex;
    align-items: center;
    padding: 3px 6px;
    font-size: 14px;
    color: #595959;
    cursor: pointer;
    background: #efefef;
    border-radius: 4px;

    .link-display {
      margin-right: 4px;
      font-size: 12px;
    }

    &.is-active {
      color: #0070d2;
      background: #ddefff;
    }

    &:hover {
      color: #0070d2;
      background: #ddefff;
    }
  }
}
</style>
