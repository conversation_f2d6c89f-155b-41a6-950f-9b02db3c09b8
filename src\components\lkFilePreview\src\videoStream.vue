<template>
  <div id="mse" />
</template>
<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue";
import { deviceDetection } from "@pureadmin/utils";
import Player from "xgplayer";
import "xgplayer/dist/index.min.css";

const props = defineProps({
  videoURL: {
    type: String as PropType<string>,
    default: ""
  }
});

const player = ref<any>({});

watch(
  () => props.videoURL,
  () => {
    nextTick(() => {
      if (props.videoURL && player.value) {
        player.value?.setConfig({
          url: props.videoURL
        });
      }
    });
  },
  {
    immediate: true
  }
);

onMounted(() => {
  player.value = new Player({
    id: "mse",
    lang: "zh",
    width: "calc(100% - 120px)",
    height: "calc(100vh - 184px)",
    // 默认静音
    volume: 0,
    autoplay: false,
    screenShot: true,
    videoAttributes: {
      crossOrigin: "anonymous"
    },
    url: "",
    poster: "",
    fluid: deviceDetection(),
    //传入倍速可选数组
    playbackRate: [0.5, 0.75, 1, 1.5, 2]
  });
});
</script>
