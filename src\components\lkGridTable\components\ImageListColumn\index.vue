<template>
  <div
    class="table-col"
    style="pointer-events: auto; cursor: pointer"
    :style="{ width: `${size.width}px`, height: `${size.height}px` }"
  >
    <el-image
      v-for="(imageItem, index) in imageList"
      :key="imageItem.url"
      :src="imageItem.url"
      fit="contain"
      :previewSrcList="imageList.map(image => image.url)"
      :previewTeleported="true"
      :initial-index="index"
    />
  </div>
</template>
<script setup lang="ts">
import { ElImage } from "element-plus";

defineProps({
  imageList: {
    type: Array as PropType<any>,
    default: () => []
  },
  size: {
    type: Object as PropType<any>,
    default: () => {}
  }
});
</script>
<style lang="scss">
.table-col {
  display: block;
  padding: 6px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  .el-tag {
    margin-right: 10px;
  }

  .el-image {
    width: 30px;
    height: 30px;
    margin-right: 10px;
    border: 1px solid #e3e3e3;
  }
}
</style>
