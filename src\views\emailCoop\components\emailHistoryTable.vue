<template>
  <div class="team-module-area-body">
    <el-form
      ref="FormRef"
      label-position="top"
      label-width="auto"
      :model="form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="`${t('trade_team_inputSearch')}：`">
            <el-input
              v-model="form.keyword"
              clearable
              :placeholder="t('trade_email_searchHistoryTip')"
              @change="handleSearchTable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="`${t('trade_email_sendTime')}：`">
            <el-date-picker
              v-model="form.sendTime"
              type="daterange"
              range-separator="-"
              :start-placeholder="t('trade_common_startTimeText')"
              :end-placeholder="t('trade_common_endTimeText')"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="`${t('trade_email_operateTime')}：`">
            <el-date-picker
              v-model="form.submitTime"
              type="daterange"
              range-separator="-"
              :start-placeholder="t('trade_common_startTimeText')"
              :end-placeholder="t('trade_common_endTimeText')"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="margin-top: 30px">
            <el-button @click="resetForm">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchTable"
            >
              {{ t("trade_common_query") }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      :data="tableData"
      :border="true"
      stripe
      :tooltip-options="{ showAfter: 500 }"
    >
      <el-table-column
        :label="t('trade_email_subject')"
        width="150"
        prop="subject"
        show-overflow-tooltip
      />
      <el-table-column
        :label="t('trade_common_remark')"
        prop="remark"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div class="grid-table-remark" v-html="row.remark" />
        </template>
      </el-table-column>
      <el-table-column
        :label="t('trade_email_createor')"
        width="255"
        prop="remark"
        align="center"
      >
        <template #default="{ row }">
          <LkAvatar
            :size="18"
            fit="contain"
            :teamInfo="{
              avatar: row.sendUserInfo.avatar,
              username: row.sendUserInfo.username
            }"
          />
          <div>
            {{ dayjs(new Date(row.sendTime)).format("YYYY-MM-DD HH:mm:ss") }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('trade_email_submitorEmail')"
        prop="submitEmail"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        :label="t('trade_email_submitTime')"
        prop="submitTime"
        width="180"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div>
            {{ dayjs(new Date(row.submitTime)).format("YYYY-MM-DD HH:mm:ss") }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="70"
        align="center"
        :label="t('trade_common_operate')"
      >
        <template #default="{ row }">
          <div class="grid-table-operate-btn" @click="handleView(row)">
            {{ t("trade_common_view") }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <LkPagination
      ref="LkPaginationRef"
      :total="total"
      @updatePagination="handleSearchTable"
    />
    <LkDialog
      ref="WidgetDialogRef"
      class="lk-maximum-dialog"
      :title="worderName"
      append-to-body
    >
      <template #default>
        <el-scrollbar max-height="100%">
          <EmailDetail ref="EmailDetailRef" />
        </el-scrollbar>
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { nextTick, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import LkAvatar from "@/components/lkAvatar/index";
import LkPagination from "@/components/lkPagination/index";
import dayjs from "dayjs";
import { getCoopEmailHistoryList } from "@/api/email";
import LkDialog from "@/components/lkDialog";
import EmailDetail from "../emailDetail.vue";

const { t } = useI18n();
const LkPaginationRef = ref<any>(null);
const FormRef = ref<any>(null);
const WidgetDialogRef = ref<HTMLElement | any>(null);
const EmailDetailRef = ref<HTMLElement | any>(null);
let tableData = ref<any[]>([]);
let total = ref<number>(0);
const form = reactive({
  keyword: "",
  sendTime: [],
  submitTime: []
});

const props = defineProps({
  workOrderId: {
    type: String as PropType<string>,
    default: ""
  },
  worderName: {
    type: String as PropType<string>,
    default: ""
  }
});

const resetForm = () => {
  form.keyword = "";
  form.sendTime = [];
  form.submitTime = [];
  handleSearchTable();
};

const handleSearchTable = async (): Promise<void> => {
  const pageParams = LkPaginationRef.value.pageParams;
  const sendStartTime = form.sendTime[0]
    ? dayjs(form.sendTime[0]).valueOf()
    : null;
  const sendEndTime = form.sendTime[1]
    ? dayjs(form.sendTime[1]).valueOf()
    : null;
  const submitStartTime = form.submitTime[0]
    ? dayjs(form.submitTime[0]).valueOf()
    : null;
  const submitEndTime = form.submitTime[1]
    ? dayjs(form.submitTime[1]).valueOf()
    : null;
  const res = await getCoopEmailHistoryList({
    workOrderId: props.workOrderId,
    keyword: form.keyword,
    sendStartTime: sendStartTime,
    sendEndTime: sendEndTime,
    submitStartTime: submitStartTime,
    submitEndTime: submitEndTime,
    ...pageParams
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
};

const handleView = (row: any): void => {
  WidgetDialogRef.value.open();
  nextTick(() => {
    EmailDetailRef.value.initRenderTemplate(
      row.workOrderCoopEmailId,
      row,
      "history"
    );
  });
};

defineExpose({
  handleSearchTable
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.grid-table-operate-btn {
  color: #2082ed;
  cursor: pointer;
}
</style>
