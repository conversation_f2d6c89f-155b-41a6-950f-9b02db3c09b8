import { storageLocal, storageSession } from "@pureadmin/utils";

export const obtainBtnPermission = (
  btnPermission: any,
  msId: string,
  orderId: string
) => {
  let teamSignName: string = storageSession().getItem("coopTeamMark");

  if (!teamSignName) {
    const urlParams: any = getUrlParams();
    storageSession().setItem(
      "coopTeamMark",
      urlParams.coopTeamMark === "2" ? "mainTeam" : "coopTeam"
    );
    teamSignName = storageSession().getItem("coopTeamMark");
  }

  const teamRoleObject = storageLocal().getItem(`${orderId}_userTeamRole`);
  if (!teamRoleObject) {
    return false;
  }
  const teamRoleLists = teamRoleObject[msId ? msId : "order"];

  if (
    btnPermission &&
    btnPermission.hasOwnProperty(teamSignName) &&
    btnPermission[teamSignName] &&
    btnPermission[teamSignName].length > 0
  ) {
    if (btnPermission[teamSignName][0] === "All") {
      return btnPermission["customPermission"];
    } else {
      return (
        btnPermission[teamSignName].every(item =>
          teamRoleLists ? teamRoleLists.includes(item) : false
        ) && btnPermission["customPermission"]
      );
    }
  } else {
    return false;
  }
};

const getUrlParams = () => {
  const reg = /(\w+)=([^&]+)/g;
  const params = {};
  let match;

  while ((match = reg.exec(window.location.href)) !== null) {
    params[match[1]] = match[2];
  }

  return params;
};
