<template>
  <el-popover
    placement="bottom-end"
    :show-arrow="false"
    :width="285"
    trigger="click"
    popper-style="padding: 0"
  >
    <template #reference>
      <span>
        <el-tooltip
          :content="t('trade_common_fieldConfig')"
          :disabled="!!buttonTextVisible"
        >
          <span
            class="order-manage-btn"
            :class="[currentScreenWidth <= 1366 ? 'Screen_1366' : '']"
          >
            <i class="iconfont link-field-settings" />
            <span v-if="buttonTextVisible">{{
              t("trade_common_fieldConfig")
            }}</span>
          </span>
        </el-tooltip>
      </span>
    </template>
    <div class="order-field-popover-main">
      <div class="order-field-popover-top">
        <div class="order-field-settings">
          {{ t("trade_common_freezeColumnTip") }}
        </div>
        <div class="order-field-draggable-body">
          <draggable
            :list="freezeList"
            :disabled="!enabled"
            item-key="groupName"
            group="freeGroup"
            class="order-field-draggable-container"
            ghost-class="ghost"
            chosen-class="chosen"
            animation="300"
            @start="dragging = true"
            @add="handleFreezeTable"
            @change="handleDragable"
          >
            <template #item="{ element }">
              <div
                class="order-field-draggable-col"
                :class="{ 'not-draggable': !enabled }"
                :data-draggable-name="element.name"
              >
                <n-tag
                  :key="element.id"
                  class="item"
                  :class="{ 'not-draggable': !enabled }"
                >
                  <div class="order-field-draggable-col-left">
                    <i class="iconfont link-drag" />
                    <span
                      class="order-field-draggable-col-title"
                      :class="{ 'hide-col-title': element.hide }"
                    >
                      {{ element.label }}
                    </span>
                  </div>
                  <div class="order-field-draggable-col-right">
                    <i
                      v-if="element.hide"
                      class="iconfont link-hide font14"
                      @click="handleSettingTableVisible(element)"
                    />
                    <i
                      v-else
                      class="iconfont link-display font14"
                      @click="handleSettingTableVisible(element)"
                    />
                    <i
                      class="iconfont link-fixed"
                      @click="handleSettingFixed(element)"
                    />
                  </div>
                </n-tag>
              </div>
            </template>
            <template #footer>
              <div v-if="freezeList.length === 0" class="freeze-tip">
                {{ t("trade_common_freezeListNot") }}
              </div>
            </template>
          </draggable>
        </div>
      </div>
      <div class="order-field-popover-bottom">
        <div class="order-field-settings">
          {{ t("trade_common_unFreezeColumnTip") }}
        </div>
        <div class="order-field-draggable-body">
          <el-scrollbar max-height="400px">
            <draggable
              :list="unFreezeList"
              :disabled="!enabled"
              item-key="groupName"
              group="freeGroup"
              class="order-field-draggable-container"
              ghost-class="ghost"
              chosen-class="chosen"
              animation="300"
              @start="dragging = true"
              @add="handleCancelFreezeTable"
              @change="handleDragable"
            >
              <template #item="{ element }">
                <div
                  class="order-field-draggable-col"
                  :class="{ 'not-draggable': !enabled }"
                  :data-draggable-name="element.name"
                >
                  <n-tag
                    :key="element.id"
                    class="item"
                    :class="{ 'not-draggable': !enabled }"
                  >
                    <div class="order-field-draggable-col-left">
                      <i class="iconfont link-drag" />
                      <span
                        class="order-field-draggable-col-title"
                        :class="{ 'hide-col-title': !element.show }"
                      >
                        {{ element.label }}
                      </span>
                    </div>
                    <div class="order-field-draggable-col-right">
                      <i
                        v-if="!element.show"
                        class="iconfont link-hide font14"
                        @click="handleSettingTableVisible(element)"
                      />
                      <i
                        v-else
                        class="iconfont link-display font14"
                        @click="handleSettingTableVisible(element)"
                      />
                      <i
                        class="iconfont link-anchoring"
                        @click="handleSettingFree(element)"
                      />
                    </div>
                  </n-tag>
                </div>
              </template>
            </draggable>
          </el-scrollbar>
        </div>
      </div>
      <div class="order-field-operate-body">
        <el-button @click="handleVisibleTableField(true)">
          {{ t("trade_common_showAll") }}
        </el-button>
        <el-button @click="handleVisibleTableField(false)">
          {{ t("trade_common_hideAll") }}
        </el-button>
      </div>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import { inject, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import draggable from "vuedraggable";
import { emitter } from "@/utils/mitt";
import { ElMessage } from "element-plus";

const props = defineProps({
  orderFields: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  tableRef: {
    type: Object as PropType<any>,
    default: () => {}
  },
  currentScreenWidth: {
    type: Number as PropType<number>,
    default: 0
  }
});

const buttonTextVisible = inject("buttonTextVisible");

const { t } = useI18n();
const enabled = ref<boolean>(true);
const dragging = ref<boolean>(false);
const freezeList = ref<any[]>([]);
const unFreezeList = ref<any[]>([]);

const emit = defineEmits(["handleChangeColumnVisible"]);

const handleSettingFixed = (element: any) => {
  const freezeItem = freezeList.value.find(
    unFreeze => unFreeze.name === element.name
  );
  const freezeIndex = freezeList.value.findIndex(
    unFreeze => unFreeze.name === element.name
  );
  freezeList.value.splice(freezeIndex, 1);

  unFreezeList.value.unshift(
    Object.assign(freezeItem, { fixed: !freezeItem.fixed })
  );
  const fields = [...freezeList.value, ...unFreezeList.value];
  props.tableRef.handleRenderColumnsInfo(fields);
  props.tableRef.handleReloadRecord();
};

const handleSettingFree = (element: any) => {
  if (freezeList.value.length > 4) {
    ElMessage.error(t("trade_common_freezelistTip"));
    return;
  }
  const unFreezeItem = unFreezeList.value.find(
    unFreeze => unFreeze.name === element.name
  );
  const unFreezeIndex = unFreezeList.value.findIndex(
    unFreeze => unFreeze.name === element.name
  );
  unFreezeList.value.splice(unFreezeIndex, 1);
  freezeList.value.push(
    Object.assign(unFreezeItem, { fixed: !unFreezeItem.fixed, show: true })
  );
  const fields = [...freezeList.value, ...unFreezeList.value];

  props.tableRef.handleRenderColumnsInfo(fields);
  props.tableRef.handleReloadRecord();
};

const handleSettingTableVisible = (element: any): void => {
  const fields = [...freezeList.value, ...unFreezeList.value];
  const elementField = fields.find(field => field.name === element.name);
  const elementIndex = fields.findIndex(field => field.name === element.name);
  fields.splice(
    elementIndex,
    1,
    Object.assign(elementField, { show: !elementField.show })
  );
  props.tableRef.handleRenderColumnsInfo(fields);
  props.tableRef.handleReloadRecord();
  emit("handleChangeColumnVisible", fields);
};

const handleFreezeTable = (data): void => {
  if (freezeList.value.length > 5) {
    ElMessage.error(t("trade_common_freezelistTip"));
    return;
  }
  const columnName = data.clone.getAttribute("data-draggable-name");
  const fields = [...freezeList.value, ...unFreezeList.value];
  const elementField = fields.find(field => field.name === columnName);
  const elementIndex = fields.findIndex(
    field => field.name === columnName.name
  );
  fields.splice(
    elementIndex,
    1,
    Object.assign(elementField, { show: true, fixed: true })
  );
  props.tableRef.handleRenderColumnsInfo(fields);
  props.tableRef.handleReloadRecord();
  emit("handleChangeColumnVisible", fields);
};

const handleCancelFreezeTable = (data): void => {
  const columnName = data.clone.getAttribute("data-draggable-name");
  const fields = [...freezeList.value, ...unFreezeList.value];
  const elementField = fields.find(field => field.name === columnName);
  const elementIndex = fields.findIndex(
    field => field.name === columnName.name
  );
  fields.splice(elementIndex, 1, Object.assign(elementField, { fixed: false }));
  props.tableRef.handleRenderColumnsInfo(fields);
  props.tableRef.handleReloadRecord();
  emit("handleChangeColumnVisible", fields);
};

const handleVisibleTableField = (bool: boolean) => {
  const tableField = unFreezeList.value.map(field =>
    Object.assign(field, { show: bool })
  );
  const fields = [...freezeList.value, ...tableField];
  props.tableRef.handleRenderColumnsInfo(fields);
  props.tableRef.handleReloadRecord();
};

const handleDragable = () => {
  console.log("顺序变更");
  const fields = [...freezeList.value, ...unFreezeList.value];
  emit("handleChangeColumnVisible", fields);
  props.tableRef.handleRenderColumnsInfo(fields);
  props.tableRef.handleReloadRecord();
};

watch(
  () => props.orderFields,
  () => {
    if (props.orderFields.length > 0) {
      unFreezeList.value = props.orderFields.filter(
        orderField => !orderField.fixed
      );
      freezeList.value = props.orderFields.filter(
        orderField => orderField.fixed
      );
    }
  },
  {
    deep: true
  }
);

onMounted(() => {
  emitter.on("handleUpdateFieldFixed", data => {
    if (data.length > 0) {
      unFreezeList.value = data.filter(orderField => !orderField.fixed);
      freezeList.value = data.filter(orderField => orderField.fixed);
    }
  });
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.freeze-tip {
  padding: 0 14px;
  font-size: 12px;
  line-height: 17px;
  color: #bfbfbf;
  text-align: center;
}
</style>
