<template>
  <Transition>
    <el-dialog
      v-model="visible"
      width="620"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :title="title"
      v-bind="$attrs"
    >
      <template #header="{ close, titleId, titleClass }">
        <slot v-bind="{ close, titleId, titleClass }" name="header" />
      </template>
      <slot name="default" />
      <template #footer>
        <div v-if="!noFooter" class="dialog-footer">
          <slot name="dialogFooterLeft" />
          <el-button @click="close">{{ t("trade_common_cancel") }}</el-button>
          <el-button
            type="primary"
            color="#0070D2"
            :disabled="confirmDisabled"
            @click="handleConfirm"
          >
            {{ t(confrimText) }}
          </el-button>
        </div>
        <slot v-else name="footer" />
      </template>
    </el-dialog>
  </Transition>
</template>
<script lang="ts" setup>
import { nextTick, type PropType, ref, useAttrs } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const attrs = useAttrs();
const visible = ref<boolean>(false);
const confirmDisabled = ref<boolean>(false);

defineProps({
  noFooter: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  title: {
    type: String as PropType<string>,
    default: ""
  },
  confrimText: {
    type: String as PropType<string>,
    default: "trade_common_sure"
  }
});

const emit = defineEmits<{
  (e: "confirm"): void;
  (e: "before-close"): void;
}>();

const open = () => {
  visible.value = true;
};

const close = () => {
  if (attrs.formRef) {
    //@ts-ignore
    attrs.formRef.clearValidate();
  }
  visible.value = false;
};

const handleSetConfirmBtnDisabled = (bool: boolean) => {
  nextTick(() => {
    confirmDisabled.value = bool;
  });
};

const handleConfirm = (): void => {
  emit("confirm");
};

defineExpose({
  open,
  close,
  handleSetConfirmBtnDisabled
});
</script>
<style lang="scss" scoped>
.lk-dialog-container {
  display: inline-block;
  padding: 0 !important;

  ::v-deep(.el-dialog) {
    border-radius: 10px;

    .el-dialog__header {
      span {
        font-size: 18px;
        font-weight: bolder;
        line-height: 20px;
        color: #202020;
      }
    }

    .el-dialog__headerbtn {
      i {
        font-size: 16px;
        color: #8c8c8c;
      }
    }
  }

  &.lk-dialog-no-footer-container {
    ::v-deep(.el-dialog__footer) {
      padding: 0 !important;
    }
  }
}
</style>
