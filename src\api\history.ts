import { http } from "@/utils/http";
import type { Result } from "./type";

/**
 * 获取订单跟踪记录
 * @returns
 */
export const getOrderDynamicList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/order-dynamic/get-order-dynamic-list",
    {
      params
    }
  );
};

/**
 * 获取工单历史(包含主表单)
 * @returns
 */
export const getWorkOrderHistoryList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-history/get-work-order-history-list",
    {
      params
    }
  );
};
/**
 * 获取工单历史详情
 * @returns
 */
export const getWorkOrderHistoryDetail = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-history/get-work-order-history-detail",
    {
      params
    }
  );
};

/**
 * 获取工单修改统计字段(包含主表单)
 * @returns
 */
export const getWorkOrderFiledStatsList = (params: any) => {
  return http.request<Result>(
    "get",
    "/trade/work-order-history/get-work-order-filed-stats-list",
    {
      params
    }
  );
};

/**
 * 获取工单动态列表
 * @returns
 */
export const getWorkOrderDynamicList = (params: any) => {
  return http.request<Result>("get", "/trade/work-order-dynamic/list-dynamic", {
    params
  });
};

/**
 * 获取当前用户扩展数据个数|邮件协作|动态
 * params: {
    workOrderId: "",
    dataTypeEnum:"", //   团队成员扩展数据
                          COOP_EMAIL :COOP_EMAIL
                          WORK_ORDER_DYNAMIC :WORK_ORDER_DYNAMIC
                          MS_REPLY_DYNAMIC :MS_REPLY_DYNAMIC
                          MS_REPLY_EMAIL_DYNAMIC :MS_REPLY_EMAIL_DYNAMIC
 }
 * @returns
 */
export const getWorkOrderCount = (params: any) => {
  return http.request<Result>("get", "/trade/work-order/count", {
    params
  });
};

/**
 * 增加工单动态
 * @param data {
                    "workOrderId": "68",
                    "workOrderDynamicRemindDataList": "new ArrayList<>()",
                    "content": "aliqua sed eiusmod",
                    "dynamicModule": "WORK_ORDER"
                }
 * @returns
 */
export const addDynamic = data => {
  return http.request<Result>("post", "/trade/work-order-dynamic/add-dynamic", {
    data
  });
};
