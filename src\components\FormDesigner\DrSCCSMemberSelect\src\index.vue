<template>
  <div>
    <el-select-v2
      v-model="widgetFormData[widgetConfigure._fc_id]"
      filterable
      :options="staffPerson.sccsMemberList"
      clearable
      :placeholder="placeholder"
      :props="{ label: 'username', value: 'userId' }"
      :teleported="widgetRowIndex === -1"
      :multiple="widgetConfigure.props.multiple"
      v-bind="containerRef ? { appendTo: containerRef } : {}"
    >
      <template #default="{ item }">
        <span v-if="!item.activate" class="tag-register-tip">(未注册)</span>
        <LkAvatar
          :teamInfo="{
            avatar: item.avatar,
            username: item.username
          }"
          :size="18"
        />
        {{ item.username }}({{ item.account }})
      </template>
      <template #label="{ value }">
        <span v-if="!getWidgetValue(value)?.activate" class="tag-register-tip">
          (未注册)
        </span>
        <LkAvatar
          :teamInfo="{
            avatar: getWidgetValue(value)?.avatar,
            username: getWidgetValue(value)?.username
          }"
          :size="18"
        />
        {{ getWidgetValue(value)?.username }}
      </template>
    </el-select-v2>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, ref, watch } from "vue";
import { ElSelectV2 } from "element-plus";
import LkAvatar from "@/components/lkAvatar/index";
import { cloneDeep, isEqual, storageLocal } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  staffPerson: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const emit = defineEmits(["handleSubTableWidgetValueChange"]);

const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const containerRef = inject("containerRef", null);

/**
 * 对多选成员数据进行排序，保证顺序与成员列表一致
 */
const handleFormatCheckboxData = () => {
  const sccsMemberList = props.staffPerson.sccsMemberList;
  if (!sccsMemberList?.length) return [];

  const userIdOrderMap = sccsMemberList.reduce(
    (map, member, idx) => {
      map[member.userId] = idx;
      return map;
    },
    {} as Record<string, number>
  );

  const checkboxWidgetValue = cloneDeep(
    widgetFormData.value[props.widgetConfigure._fc_id]
  );
  if (!checkboxWidgetValue?.length) return [];

  return [...checkboxWidgetValue].sort((a, b) => {
    const indexA = userIdOrderMap[a] ?? Infinity;
    const indexB = userIdOrderMap[b] ?? Infinity;
    return indexA - indexB;
  });
};

/**
 * 获取控件的值（单选返回数组，多选排序后返回数组）
 */
const getWidgetFormVal = (): unknown => {
  const widgetValue = widgetFormData.value[props.widgetConfigure._fc_id];
  if (!props.widgetConfigure.props.multiple) {
    return widgetValue ? [widgetValue] : [];
  }
  return handleFormatCheckboxData();
};

/**
 * 根据 userId 获取成员对象
 */
const getWidgetValue = (value: any) => {
  if (!value || !Array.isArray(props.staffPerson.sccsMemberList)) return null;
  return (
    props.staffPerson.sccsMemberList.find(
      sccsMember => sccsMember.userId === value
    ) || null
  );
};

/**
 * 获取控件显示的 label 字符串
 */
const getWidgetFormLabel = (widgetValue: any) => {
  if (
    !widgetValue ||
    (Array.isArray(widgetValue) && widgetValue.length === 0)
  ) {
    return "";
  }
  const memberWidgetValue = Array.isArray(widgetValue)
    ? widgetValue
    : [widgetValue];
  const validValues = memberWidgetValue.filter(Boolean);
  if (!validValues.length) return "";

  const memberList = validValues
    .map(val => {
      const memberItem = props.staffPerson.sccsMemberList?.find(
        member => member.userId === val
      );
      return memberItem ? `${memberItem.username}(${memberItem.account})` : "";
    })
    .filter(Boolean);

  return memberList.join(",");
};

/**
 * 获取控件显示的成员对象数组
 */
const getWidgetFormShowObject = (widgetValue: any) => {
  if (
    !widgetValue ||
    (Array.isArray(widgetValue) && widgetValue.length === 0)
  ) {
    return [];
  }
  const memberWidgetValue = Array.isArray(widgetValue)
    ? widgetValue
    : [widgetValue];
  const validValues = memberWidgetValue.filter(v => v != null);
  if (!validValues.length) return [];

  return validValues
    .map(val => {
      const memberItem = props.staffPerson.sccsMemberList?.find(
        member => member.userId === val
      );
      return memberItem
        ? {
            avatar: memberItem.avatar,
            email: memberItem.account,
            id: memberItem.userId,
            name: memberItem.username,
            activate: memberItem.activate
          }
        : null;
    })
    .filter(Boolean);
};

// 监听 trendsForm 的值变化，自动同步到组件
watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    if (!props.widgetConfigure.props.multiple) {
      widgetFormData.value[props.widgetConfigure._fc_id] = Array.isArray(newVal)
        ? newVal.length > 0
          ? newVal[0]
          : null
        : newVal || null;
    } else {
      if (Array.isArray(newVal)) {
        if (newVal[0] === "") {
          widgetFormData.value[props.widgetConfigure._fc_id] = [];
        } else {
          widgetFormData.value[props.widgetConfigure._fc_id] =
            cloneDeep(newVal);
        }
      } else if (newVal != null && newVal !== "") {
        widgetFormData.value[props.widgetConfigure._fc_id] = [newVal];
      } else {
        widgetFormData.value[props.widgetConfigure._fc_id] = [];
      }
    }
  },
  { immediate: true }
);

// 监听成员列表变化，确保有成员时能正确显示初始值
watch(
  () => props.staffPerson?.sccsMemberList,
  () => {
    const trendsFormValue = props.trendsForm?.[props.widgetConfigure._fc_id];
    const hasMemberList = props.staffPerson?.sccsMemberList?.length > 0;

    if (!hasMemberList) {
      widgetFormData.value[props.widgetConfigure._fc_id] = props.widgetConfigure
        .props.multiple
        ? []
        : null;
      return;
    }

    const currentWidgetValue =
      widgetFormData.value[props.widgetConfigure._fc_id];
    const hasFormValue =
      trendsFormValue != null &&
      !(Array.isArray(trendsFormValue) && trendsFormValue.length === 0);
    const hasWidgetValue =
      currentWidgetValue != null &&
      !(Array.isArray(currentWidgetValue) && currentWidgetValue.length === 0);

    if (hasFormValue && !hasWidgetValue) {
      if (!props.widgetConfigure.props.multiple) {
        widgetFormData.value[props.widgetConfigure._fc_id] = Array.isArray(
          trendsFormValue
        )
          ? trendsFormValue.length > 0
            ? trendsFormValue[0]
            : null
          : Object.keys(trendsFormValue).length !== 0
            ? trendsFormValue
            : null;
      } else {
        if (Array.isArray(trendsFormValue)) {
          widgetFormData.value[props.widgetConfigure._fc_id] =
            cloneDeep(trendsFormValue);
        } else if (trendsFormValue != null) {
          widgetFormData.value[props.widgetConfigure._fc_id] = [
            trendsFormValue
          ];
        } else {
          widgetFormData.value[props.widgetConfigure._fc_id] = [];
        }
      }
    }
  },
  { immediate: true }
);

// 监听控件值变化，触发表单更新
watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    const trendsFormValue = props.trendsForm?.[props.widgetConfigure._fc_id];
    const isEmpty = (val: any) =>
      val == null || (Array.isArray(val) && val.length === 0);

    if (
      (isEmpty(newVal) && isEmpty(trendsFormValue)) ||
      (isEmpty(newVal) && isEmpty(oldVal))
    ) {
      return;
    }

    if (!isEqual(newVal, trendsFormValue)) {
      const widgetId = props.widgetConfigure._fc_id;
      const widgetValueData = getWidgetFormVal();

      const updateData = {
        obj: widgetValueData,
        label: getWidgetFormLabel(widgetValueData),
        showObj: getWidgetFormShowObject(widgetValueData),
        widgetId,
        $rowIndex: props.widgetRowIndex
      };

      if (props.widgetRowIndex !== -1) {
        emit(
          "handleSubTableWidgetValueChange",
          widgetId,
          updateData,
          widgetFormData.value[widgetId]
        );
      } else {
        handleWidgetFormsValue(
          widgetId,
          updateData,
          widgetFormData.value[widgetId]
        );
      }
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a !important;
}
</style>
