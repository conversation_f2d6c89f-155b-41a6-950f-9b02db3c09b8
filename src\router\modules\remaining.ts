import { $t } from "@/plugins/i18n";
const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    component: () => import("@/views/login-v2/login.vue"),
    meta: {
      title: $t("trade_common_login"),
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/resetPassword",
    name: "ResetPassword",
    component: () => import("@/views/login-v2/resetPassword.vue"),
    meta: {
      title: $t("trade_common_resetPassword"),
      showLink: false,
      rank: 102
    }
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("@/views/login-v2/register.vue"),
    meta: {
      title: $t("trade_login_userRegistration"),
      showLink: false,
      rank: 103
    }
  },
  {
    path: "/createTeam",
    component: () => import("@/views/createTeam/createTeam.vue"),
    meta: {
      title: $t("trade_perCenter_createTeam"),
      showLink: false,
      rank: 104
    }
  },
  {
    path: "/selectTeam",
    component: () => import("@/views/selectTeam/selectTeam.vue"),
    meta: {
      title: $t("trade_select_team_role"),
      showLink: false,
      rank: 105
    }
  },
  {
    path: "/inviteUser",
    component: () => import("@/views/inviteUser/index.vue"),
    meta: {
      title: $t("trade_select_team_role"),
      showLink: false,
      rank: 106
    }
  },
  {
    path: "/inviteTeam",
    component: () => import("@/views/inviteTeam/index.vue"),
    meta: {
      title: $t("trade_select_team_role"),
      showLink: false,
      rank: 106
    }
  },
  {
    path: "/emailValidate",
    component: () => import("@/views/emailCoop/emailValidate.vue"),
    meta: {
      title: "",
      showLink: false,
      rank: 106
    }
  },
  {
    path: "/emailCoop/writeEmailCoop",
    component: () => import("@/views/emailCoop/writeEmailCoop.vue"),
    meta: {
      title: "",
      showLink: false,
      rank: 106
    }
  },
  {
    path: "/emailCoop/emailReceiveCoopTable",
    component: () => import("@/views/emailCoop/emailReceiveCoopTable.vue"),
    meta: {
      title: "",
      showLink: false,
      rank: 107
    }
  },
  {
    path: "/emailCoop/emailDetail",
    component: () => import("@/views/emailCoop/emailDetail.vue"),
    meta: {
      title: "",
      showLink: false,
      rank: 108
    }
  },
  {
    path: "/",
    // name: "Home",
    component: Layout,
    meta: {
      icon: "IF-link-dashboard",
      title: $t("trade_home_workbenches"),
      rank: 0
    },
    children: [
      {
        path: "/",
        name: "Home",
        component: () => import("@/views/home/<USER>"),
        meta: {
          title: $t("trade_home_workbenches")
        }
      }
    ]
  },
  {
    path: "/resource",
    // name: "resource",
    component: Layout,
    meta: {
      icon: "IF-link-resource",
      title: $t("trade_home_resource"),
      rank: 1
    },
    children: [
      {
        path: "/resource",
        name: "resource",
        component: () => import("@/views/resourceRepository/index.vue"),
        meta: {
          title: $t("trade_home_resource")
        }
      }
    ]
  },
  {
    path: "/orderManage",
    // name: "orderManage",
    component: Layout,
    meta: {
      showLink: false,
      title: ""
    },
    children: [
      {
        path: "/orderManage",
        name: "orderManage",
        component: () => import("@/views/orderManage/index.vue"),
        meta: {
          title: $t("trade_order_orderList")
        }
      }
    ]
  },
  {
    path: "/orderDetail",
    // name: "orderManage",
    component: Layout,
    meta: {
      showLink: false,
      title: ""
    },
    children: [
      {
        path: "/orderDetail",
        name: "orderDetail",
        component: () => import("@/views/orderDetail/index.vue"),
        meta: {
          title: $t("trade_order_details")
        }
      }
    ]
  },
  {
    path: "/taskCenter",
    // name: "orderManage",
    component: Layout,
    meta: {
      showLink: false,
      title: ""
    },
    children: [
      {
        path: "/taskCenter",
        name: "taskCenter",
        component: () => import("@/views/taskCenter/index.vue"),
        meta: {
          showLink: true,
          title: $t("trade_task_center")
        }
      }
    ]
  }
] satisfies Array<RouteConfigsTable>;
