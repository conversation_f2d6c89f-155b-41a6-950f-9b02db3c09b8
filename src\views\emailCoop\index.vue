<template>
  <el-drawer
    v-model="drawerVisible"
    class="lk-maximum-drawer"
    size="83%"
    append-to-body
    :title="t('trade_order_emailCollaborate')"
    body-class="work-order-collection-container"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
  >
    <EmailCoopForm
      :workOrderId="workOrderId"
      :milestoneId="milestoneId"
      :worderName="msWorkOrderName"
      :btnVisible="visible"
    />
    <template #footer>
      <el-button plain @click="handleClose">
        {{ t("trade_common_cancel") }}
      </el-button>
      <el-button type="primary" color="#0070d2" @click="handleConfirm">
        {{ t("trade_common_confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import EmailCoopForm from "./components/emailCoopForm.vue";

const { t } = useI18n();
const drawerVisible = ref<boolean>(false);
const workOrderId = ref<string>("");
const milestoneId = ref<string>("");
const msWorkOrderName = ref<string>("");
const visible = ref<boolean>(false);

const open = (
  id: string,
  msId: string,
  workOrderName: string,
  btnVisible: boolean
) => {
  workOrderId.value = id;
  milestoneId.value = msId;
  msWorkOrderName.value = workOrderName;
  visible.value = btnVisible;
  drawerVisible.value = true;
};

const handleConfirm = () => {
  drawerVisible.value = false;
};

const handleClose = () => {
  drawerVisible.value = false;
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "./components/index.scss";
</style>
<style lang="scss">
.work-order-collection-container.el-drawer__body {
  display: inline-block;
  width: 100%;
  padding: 0 20px;
  background: #fff !important;

  .el-tabs__content {
    height: 95%;
  }
}
</style>
