<template>
  <el-dialog
    ref="CreateOrderDialogRef"
    v-model="dialogVisible"
    class="create-order-dialog"
    :no-footer="true"
    :show-close="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    align-center
    height="300"
    width="480"
  >
    <template #default>
      <div class="create-order-container">
        <i
          class="iconfont link-close create-order-icon"
          @click="handleCloseOrderDialog"
        />
        <svg class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#link-work-order-success" />
        </svg>
        <div class="create-order-btn-text">
          <span class="create-order-tip">{{ orderMarkName }}</span>
          {{ t("trade_common_createSuccess") }}
        </div>
        <div class="create-work-order-btn-group">
          <el-button plain @click="handleAgainCreateOrder">
            <i class="iconfont link-add font13 mr-1" />
            {{ t("trade_common_nextCreateOrder") }}
          </el-button>
          <el-button
            type="primary"
            color="#0070D2"
            class="create-work-order-plain-btn"
            @click="handleOrderDetail"
          >
            <i class="iconfont link-display font13 mr-1" />
            {{ t("trade_common_orderDetail") }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";
import { debounce, storageLocal } from "@pureadmin/utils";
import {
  getOrderMark,
  getTradeCoopSccsRolePermission,
  getTradeSccsRolePermission
} from "@/api/order";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const dialogVisible = ref<boolean>(false);
const CreateOrderDialogRef = ref<HTMLElement | any>(null);
const orderMarkName = ref<string>("");
const orderDialogId = ref<string>("");

const emit = defineEmits(["handleAgainCreateOrder", "handleCloseOrderDialog"]);

const handleCloseOrderDialog = () => {
  dialogVisible.value = false;
  emit("handleCloseOrderDialog");
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleOrderDetail = debounce(
  async () => {
    const { sccsName, sccsId, templateId, coopTeamMark } = route.query;
    const params = {
      sccsName: sccsName,
      sccsId: sccsId,
      templateId: templateId,
      orderId: orderDialogId.value,
      orderMark: orderMarkName.value,
      coopTeamMark: coopTeamMark
    };
    let resp: any = {};
    if (coopTeamMark === "2") {
      resp = await getTradeSccsRolePermission({
        sccsId: sccsId,
        orderIdList: [orderDialogId.value]
      });
    } else {
      resp = await getTradeCoopSccsRolePermission({
        sccsId: sccsId,
        orderIdList: [orderDialogId.value]
      });
    }
    //@ts-ignore
    storageLocal().setItem(
      `${orderDialogId.value}_userTeamRole`,
      resp.data[orderDialogId.value]
    );
    router.push({ name: "orderDetail", query: params });
  },
  1000,
  true
);

const handleAgainCreateOrder = debounce(
  () => {
    dialogVisible.value = false;
    emit("handleAgainCreateOrder");
  },
  1000,
  true
);

const open = async (orderId: string) => {
  orderDialogId.value = orderId;
  const { code, data } = await getOrderMark({ orderId: orderId });
  if (code === 0) {
    orderMarkName.value = data;
    dialogVisible.value = true;
  }
};

defineExpose({
  open,
  handleClose
});
</script>
<style lang="scss">
.create-order-dialog {
  .el-dialog__header {
    padding: 0 !important;
  }

  .create-order-container {
    position: relative;
    display: flex;
    flex-flow: column;
    justify-content: center;
    text-align: center;

    .create-order-icon {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 12px;
      cursor: pointer;
    }

    .icon {
      width: 72px;
      height: 72px;
      margin: 25px auto 0;
    }

    .create-order-btn-text {
      margin: 20px 0 56px;
      font-size: 14px;
      color: #8c8c8c;
      text-align: center;

      .create-order-tip {
        font-size: 14px;
        line-height: 14px;
        color: #262626;
      }
    }
  }
}
</style>
