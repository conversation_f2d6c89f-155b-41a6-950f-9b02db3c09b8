<template>
  <div class="email-validate-body">
    <div class="email-validate-container">
      <div class="email-validate-title">{{ t("trade_email_validate") }}</div>
      <div class="email-validate-tip">{{ t("trade_email_validateTip") }}</div>
      <div class="email-validate-sheet">
        <div class="email-validate-form-title">
          <span class="email-validate-form-name">
            {{ t("trade_login_email") }}
          </span>
          <span class="email-validate-form-code">
            {{ userEmailForm.email }}
          </span>
        </div>
        <div class="email-validate-mail-form-title">
          {{ t("trade_common_captcha") }}
        </div>
        <div class="email-validate-mail-form">
          <div class="email-validate-mail-container">
            <el-input
              v-model="userEmailForm.code"
              :placeholder="t('trade_common_inputCaptcha')"
              maxlength="6"
              clearable
            />
          </div>
          <el-button
            v-show="!beginCountDown"
            type="primary"
            class="send-verify-code-btn"
            @click="handleSendVerifyCode"
          >
            {{ t("trade_common_getCaptcha") }}
          </el-button>
          <el-button
            v-show="beginCountDown"
            class="count-down-body"
            type="primary"
            disabled
            color="#edf1fd"
          >
            <el-countdown
              :value="timeCode"
              format="ss"
              suffix="秒后重新获取"
              @finish="handleCountDownFinish"
            />
          </el-button>
        </div>
        <div class="email-validate-mail-form-bottom-title">
          {{ t("trade_login_emailCodeTip") }}
        </div>
        <div class="email-validate-btn-body">
          <el-button type="primary" color="#0070D2" @click="handleVerifyEmail">
            {{ t("trade_login_verifyEmail") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import {
  sendWorkOrderCoopVerifyCode,
  checkWorkOrderCoopVerifyCode
} from "@/api/email";
import { storageLocal } from "@pureadmin/utils";
import { ElMessage } from "element-plus";

interface userEmailFormProp {
  email: string;
  code: string;
}

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const timeCode = ref<number>(Date.now() + 1000 * 60);
const beginCountDown = ref<boolean>(false);
const userEmailForm = ref<userEmailFormProp>({
  email: "",
  code: ""
});

const handleCountDownFinish = (): void => {
  beginCountDown.value = false;
};

const handleSendVerifyCode = async (): Promise<void> => {
  const { code } = await sendWorkOrderCoopVerifyCode({
    email: userEmailForm.value.email
  });
  if (code === 0) {
    beginCountDown.value = true;
    timeCode.value = Date.now() + 1000 * 60;
  }
};

const handleVerifyEmail = async (): Promise<void> => {
  const { data } = await checkWorkOrderCoopVerifyCode(userEmailForm.value);
  if (data) {
    storageLocal().setItem(
      "validateEmailDeadline",
      new Date().getTime() + 7 * 24 * 60 * 60 * 1000
    );
    router.push(`/emailCoop/emailReceiveCoopTable?email=${route.query.email}`);
  } else {
    ElMessage.error(t("trade_users_mail_code_is_not_correct"));
  }
};

onMounted(() => {
  const deadLineTime: string =
    storageLocal().getItem("validateEmailDeadline") || "";
  if (deadLineTime) {
    if (new Date().getTime().toString() < deadLineTime) {
      // 跳转链接
      router.push(
        `/emailCoop/emailReceiveCoopTable?email=${route.query.email}`
      );
    }
  }
  userEmailForm.value.email = route.query.email as string;
});
</script>
<style lang="scss" scoped>
@use "./components/index.scss";
</style>
