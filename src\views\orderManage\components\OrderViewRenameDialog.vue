<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_order_renameViews')"
    @confirm="confirm"
    @close="close"
  >
    <template #default>
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
      >
        <div class="order-rename-dialog-tip">
          {{ t("trade_order_renameDialogTip") }}
        </div>
        <el-form-item prop="renameViewName">
          <el-input
            v-model="ruleForm.renameViewName"
            show-word-limit
            maxlength="20"
            clearable
          />
        </el-form-item>
      </el-form>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, reactive, markRaw } from "vue";
import { useI18n } from "vue-i18n";
import type { FormInstance, FormRules } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import LkDialog from "@/components/lkDialog/index";

interface RenameViewProp {
  renameViewName: string;
}

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const ruleFormRef = ref<FormInstance>();
const ruleForm = ref<RenameViewProp>({
  renameViewName: ""
});

const rules = reactive<FormRules<RenameViewProp>>({
  renameViewName: [
    {
      required: true,
      message: t("trade_order_viewsRenamePlaceholder"),
      trigger: "blur"
    }
  ]
});

const confirm = (): void => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async valid => {
    if (valid) {
      ElMessageBox.confirm(
        t("trade_order_coverViewsTip", [3333]),
        t("trade_order_coverViews"),
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          cancelButtonClass: "cancel-message-btn-class",
          type: "warn",
          icon: markRaw(WarningFilled),
          center: true
        }
      ).then(() => {});
    }
  });
};

const close = (): void => {
  ruleFormRef.value.resetFields();
};

const open = (): void => {
  LkDialogRef.value.open();
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
.order-rename-dialog-tip {
  margin: 14px 0;
  font-size: 14px;
  text-align: left;
  color: #262626;
  line-height: 16px;
}
</style>
