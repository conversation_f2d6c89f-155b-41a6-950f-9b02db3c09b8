import type { Directive, DirectiveBinding } from "vue";
import { formatThousandNumber, formatNumberValueOf } from "@/utils/common";

export const thousands: Directive = {
  mounted: (
    el: HTMLElement,
    binding: DirectiveBinding<string | Array<string>>
  ) => {
    const inputEl = el.querySelector(".el-input__inner") as HTMLInputElement;

    if (!binding.value || !inputEl) return;
    inputEl.type = "text";
    inputEl.value = formatThousandNumber(inputEl.value);

    inputEl.onfocus = () => {
      const value = formatNumberValueOf(inputEl.value);
      if (!value) return;
      inputEl.type = "number";
      inputEl.value = value;
    };
    inputEl.onblur = () => {
      inputEl.type = "text";
      inputEl.value = formatThousandNumber(inputEl.value);
    };
  },
  beforeUnmount: el => {
    const inputEl = el.querySelector("input");
    if (!inputEl) return;
    inputEl.onfocus = null;
    inputEl.onpaste = null;
    inputEl.onblur = null;
  }
};

export default thousands;
