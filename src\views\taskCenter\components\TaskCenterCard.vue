<template>
  <div class="task-center-card">
    <div class="task-center-card-top">
      <div class="task-center-card-top-header">
        <div class="task-center-card-top-header-left">
          <span class="mask" @click="gotoDetail">
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ itemData.orderMark ? itemData.orderMark : "- -" }}
            </ReText>
            <i class="iconfont link-arrow" />
          </span>
        </div>
        <div class="task-center-card-top-header-right">
          <span
            class="plan-endtime"
            :style="{
              color: itemData.msPlannedEndDateShowRed ? '#FF0000' : '#8c8c8c'
            }"
          >
            {{ `${t("trade_common_planEnd")}:` }}
            {{
              itemData.msPlannedEndDate
                ? getTimeLineStampFormat(itemData.msPlannedEndDate, false)
                : "- -"
            }}
          </span>
        </div>
      </div>
      <div class="task-center-card-top-milestone-info">
        <div class="task-center-card-top-milestone-info-left">
          <span class="milestone-name">
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ itemData.msName }}
            </ReText>
          </span>
          <span v-if="itemData.msLabelList.length > 0" class="label-group">
            <span
              v-for="(labelItem, index) in itemData.msLabelList.slice(0, 2)"
              :key="index"
              class="label-item"
              :style="{ borderColor: labelItem.style, color: labelItem.style }"
            >
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                {{ labelItem.labelValue }}
              </ReText>
            </span>
            <el-popover placement="bottom" trigger="click">
              <template #reference>
                <span
                  v-if="itemData.msLabelList.length > 2"
                  class="more-label"
                  >{{ "+" + (itemData.msLabelList.length - 2) }}</span
                >
              </template>
              <template #default>
                <div class="label-list">
                  <span
                    v-for="(labelItem, index) in itemData.msLabelList.slice(2)"
                    :key="index"
                    class="label-item"
                    :style="{
                      borderColor: labelItem.style,
                      color: labelItem.style
                    }"
                    ><ReText type="info" :tippyProps="{ delay: 50000 }">
                      {{ labelItem.labelValue }}
                    </ReText></span
                  >
                </div>
              </template>
            </el-popover>
          </span>
        </div>
        <div class="task-center-card-top-milestone-info-right">
          <span
            class="task-center-card-top-milestone-name"
            @click="gotoSccsManage"
          >
            <ReText type="info" :tippyProps="{ delay: 50000 }">{{
              itemData.sccsName
            }}</ReText></span
          >
          <span class="line" />

          <span class="task-center-card-top-team-name">
            <svg
              v-if="!itemData.sccsTeamIsCurrentTeam"
              class="svg-icon svg-icon-coop"
              aria-hidden="true"
            >
              <use xlink:href="#link-coop" />
            </svg>
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ itemData.sccsTeamName }}
            </ReText>
          </span>
        </div>
      </div>
    </div>
    <div class="task-center-card-bottom">
      <div class="task-center-card-bottom-left">
        <div
          v-if="
            itemData.workOrderType === 'WORK_ORDER' ||
            itemData.workOrderType === 'WORK_ORDER_REPLY'
          "
          class="task-center-card-bottom-left-title"
        >
          <span
            class="task-center-card-item-worderName"
            @click="handleTrigger('workOrderDynamic', itemData)"
          >
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ itemData.workOrderName }}
            </ReText>
          </span>
          <span class="task-center-card-item-worderComplete">
            <span class="task-center-card-item-worderComplete-text">{{
              t("trade_work_order_completion_rate")
            }}</span>
            <el-progress
              class="task-center-card-item-worderComplete-progress"
              :text-inside="true"
              :stroke-width="14"
              :percentage="itemData.workOrderCompleteRate || 0"
            />
          </span>
        </div>
        <div
          v-else-if="itemData.workOrderType === 'MS_REPLY'"
          class="task-center-card-bottom-left-title"
        >
          <span
            class="task-center-card-item-milestoneName"
            @click="handleTrigger('milestoneDynamic', itemData)"
          >
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ itemData.msName }}
            </ReText>
          </span>
          <div class="task-center-card-bottom-left-title-content">
            <div class="task-center-card-item-milestoneComplete">
              <span class="task-center-card-item-milestoneComplete-text">{{
                t("trade_work_order_approval_completed")
              }}</span>
              <span class="task-center-card-item-milestoneComplete-count">{{
                `${itemData.completeReplyWorkOrderCount}/${itemData.workOrderCount}`
              }}</span>
            </div>
            <div class="task-center-card-item-milestoneComplete">
              <span class="task-center-card-item-milestoneComplete-text">{{
                t("trade_work_order_completed_label")
              }}</span>
              <span class="task-center-card-item-milestoneComplete-count">{{
                `${itemData.completeEditWorkOrderCount}/${itemData.workOrderCount}`
              }}</span>
            </div>
          </div>
        </div>
        <div class="task-center-card-bottom-left-content">
          <!-- 发起 -->
          <div class="task-center-card-bottom-left-content-col">
            <span class="task-center-card-col-tip">
              {{ t("trade_home_instigation") }}
            </span>
            <div class="task-center-card-col-avater">
              <LkAvater
                :teamInfo="{
                  avatar: itemData.createUserInfo?.avatar,
                  username: itemData.createUserInfo?.username,
                  email: itemData.createUserInfo?.email
                }"
                :size="22"
              />
              <span class="task-center-card-col-text">
                {{ getTimeLineStampFormat(itemData.createTime) }}
              </span>
            </div>
          </div>
          <!-- 采集 -->
          <div
            v-if="
              itemData.workOrderType === 'WORK_ORDER_REPLY' ||
              itemData.workOrderType === 'WORK_ORDER'
            "
            class="task-center-card-bottom-left-content-col"
          >
            <span class="task-center-card-col-tip">
              {{ t("trade_common_gather") }}
            </span>
            <div class="task-center-card-col-avater">
              <span @click.stop>
                <LkAvatarGroupNext
                  :size="22"
                  :avatarListGroup="itemData.editUserList"
                  :maxAvatar="4"
                />
              </span>
              <span
                v-if="itemData.editTime"
                class="task-center-card-col-text time"
              >
                {{ getTimeLineStampFormat(itemData.editTime) }}
              </span>
              <span
                v-else
                class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
              >
                {{ t("trade_order_notSubmit") }}
              </span>
            </div>
          </div>
          <!-- 里程碑批复 -->
          <div
            v-if="itemData.workOrderType === 'MS_REPLY'"
            class="task-center-card-bottom-left-content-col"
          >
            <span class="task-center-card-col-tip">
              {{ t("trade_template_replyBtnName") }}
            </span>
            <div class="task-center-card-col-avater">
              <span @click.stop>
                <LkAvatarGroupNext
                  :size="22"
                  :avatarListGroup="itemData.editUserList"
                  :maxAvatar="4"
                />
              </span>
              <span
                v-if="itemData.editTime"
                class="task-center-card-col-text time"
              >
                {{ getTimeLineStampFormat(itemData.editTime) }}
              </span>
              <span
                v-else
                class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
              >
                {{ t("trade_order_notSubmit") }}
              </span>
            </div>
          </div>
          <!-- 工单批复 -->
          <div
            v-if="
              itemData.workOrderType === 'WORK_ORDER_REPLY' ||
              (itemData.workOrderType === 'WORK_ORDER' &&
                itemData.workOrderReplyId)
            "
            class="task-center-card-bottom-left-content-col"
          >
            <span class="task-center-card-col-tip">
              {{ t("trade_order_taskReply") }}
            </span>
            <div class="task-center-card-col-avater">
              <span @click.stop>
                <LkAvatarGroupNext
                  :size="22"
                  :avatarListGroup="itemData.replyUserList"
                  :maxAvatar="4"
                />
              </span>
              <span
                v-if="itemData.replyTime"
                class="task-center-card-col-text time"
              >
                {{ getTimeLineStampFormat(itemData.replyTime) }}
              </span>
              <span
                v-else
                class="milestone-reply-form-span-text milestone-reply-form-span-red-text"
              >
                {{ t("trade_order_notSubmit") }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="task-center-card-bottom-right">
        <div class="task-center-card-bottom-right-type">
          <div
            v-if="itemData.workOrderType === 'WORK_ORDER'"
            class="type-block-workder-order"
          />
          <div
            v-if="itemData.workOrderType === 'WORK_ORDER_REPLY'"
            class="type-block-worder-order-reply"
          />
          <div
            v-if="itemData.workOrderType === 'MS_REPLY'"
            class="type-block-milstobe-reply"
          />
          <div
            v-if="itemData.replyWorkOrderTaskId"
            class="type-block-worder-order-reply"
          />
        </div>
        <div class="task-center-card-bottom-right-oprate">
          <div class="task-center-card-bottom-right-desc">
            <span v-if="itemData.assignInfo">{{ itemData.assignInfo }}</span>
            <span>
              <span v-if="itemData.completeReason" style="margin-right: 5px">{{
                itemData.completeReason
              }}</span>
              <span v-if="itemData.workOrderCompleteTime">{{
                getTimeLineStampFormat(itemData.workOrderCompleteTime)
              }}</span>
            </span>
          </div>
          <div class="task-center-card-bottom-right-btn">
            <!-- 工单采集指派 -->
            <span
              v-if="
                itemData.workOrderType === 'WORK_ORDER' &&
                itemData.permInfo?.reAssignable
              "
              class="task-center-card-bottom-right-btn-item"
              @click.stop="
                handleTrigger(
                  getWorkOrderReplyWhetherTeam(itemData, itemData.editUserList)
                    ? 'collectDistribute'
                    : 'collectorsPerson',
                  itemData
                )
              "
              ><i class="iconfont link-distribute" />{{
                t("trade_common_gatherAssign")
              }}</span
            >
            <!-- 工单批复指派 -->
            <span
              v-if="
                itemData.workOrderType === 'WORK_ORDER_REPLY' &&
                itemData.permInfo?.reAssignable
              "
              class="task-center-card-bottom-right-btn-item"
              @click.stop="
                handleTrigger(
                  getWorkOrderReplyWhetherTeam(itemData, itemData.replyUserList)
                    ? 'approvalDistribute'
                    : 'approvalAssignPerson',
                  itemData
                )
              "
              ><i class="iconfont link-distribute" />{{
                t("trade_home_approvalAssign")
              }}</span
            >
            <!-- 里程碑批复指派 -->
            <span
              v-if="
                itemData.workOrderType === 'MS_REPLY' &&
                itemData.permInfo?.reAssignable
              "
              class="task-center-card-bottom-right-btn-item"
              @click.stop="
                handleTrigger(
                  getWorkOrderReplyWhetherTeam(itemData, itemData.editUserList)
                    ? 'milestoneAssign'
                    : 'updateMilestoneCoopPersonnel',
                  itemData
                )
              "
              ><i class="iconfont link-distribute" />{{
                t("trade_home_approvalAssign")
              }}</span
            >
            <span
              v-if="
                itemData.workOrderType === 'WORK_ORDER' &&
                itemData.permInfo?.editable
              "
              class="task-center-card-bottom-right-btn-item"
              @click.stop="handleTrigger('workOrderCollect', itemData)"
              ><i class="iconfont link-collect" />{{
                t("trade_common_gather")
              }}</span
            >
            <span
              v-if="
                (itemData.workOrderType === 'MS_REPLY' ||
                  itemData.workOrderType === 'WORK_ORDER_REPLY') &&
                itemData.permInfo?.editable
              "
              class="task-center-card-bottom-right-btn-item"
              @click.stop="
                handleTrigger(
                  itemData.workOrderType === 'MS_REPLY'
                    ? 'milestoneReply'
                    : 'workOrderReply',
                  itemData
                )
              "
              ><i class="iconfont link-reply" />{{
                t("trade_home_approval")
              }}</span
            >
            <span
              v-if="itemData.completeReasonType"
              class="task-center-card-bottom-right-btn-item"
              @click.stop="
                handleTrigger(
                  ['WORK_ORDER', 'WORK_ORDER_REPLY'].includes(
                    itemData.workOrderType
                  )
                    ? 'workOrderDynamic'
                    : 'milestoneDynamic',
                  itemData
                )
              "
              ><i class="iconfont link-display" />{{
                t("trade_common_view")
              }}</span
            >
            <el-dropdown
              v-if="itemData.permInfo?.updateProcessor"
              placement="bottom"
              trigger="click"
              style="vertical-align: text-top"
              @command="handleTrigger"
            >
              <span class="task-center-card-bottom-right-btn-item"
                ><i class="iconfont link-more"
              /></span>
              <template #dropdown>
                <el-dropdown-menu class="order-region-popper">
                  <!-- 修改工单批复人 -->
                  <el-dropdown-item
                    v-if="itemData.workOrderType === 'WORK_ORDER_REPLY'"
                    :command="{
                      data: itemData,
                      type: 'modifyWorkOrderProcessor'
                    }"
                  >
                    <i class="iconfont link-modify-processor" />
                    <span>{{ t("trade_home_approvalAssignPerson") }}</span>
                  </el-dropdown-item>
                  <!-- 修改工单处理人 -->
                  <el-dropdown-item
                    v-if="itemData.workOrderType === 'WORK_ORDER'"
                    :command="{
                      data: itemData,
                      type: 'modifyProcessor'
                    }"
                  >
                    <i class="iconfont link-modify-processor" />
                    <span>{{ t("trade_order_updatePersonnel") }}</span>
                  </el-dropdown-item>
                  <!-- 修改里程碑批复人 -->
                  <el-dropdown-item
                    v-if="itemData.workOrderType === 'MS_REPLY'"
                    :command="{
                      data: itemData,
                      type: 'updateMilestonePersonnel'
                    }"
                  >
                    <i class="iconfont link-modify-processor" />
                    <span>{{ t("trade_order_updatePersonnel") }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <span
              class="task-center-card-bottom-right-btn-item-star"
              @click.stop="changeCollect(itemData, 'work-order')"
            >
              <i
                v-if="!itemData?.favorite"
                class="iconfont link-star colorNormal"
              />
              <i v-else class="iconfont link-star-filled colorOrange" />
            </span>
          </div>
        </div>
        <div
          v-if="itemData.replyWorkOrderTaskId"
          class="task-center-card-bottom-right-oprate"
          :class="['worder-order-reply']"
        >
          <div class="task-center-card-bottom-right-desc">
            <span v-if="itemData.replyInfo.assignInfo">{{
              itemData.replyInfo.assignInfo
            }}</span>
            <span>
              <span
                v-if="itemData.replyInfo.completeReason"
                style="margin-right: 5px"
                >{{ itemData.replyInfo.completeReason }}</span
              >
              <span v-if="itemData.replyInfo.completeTime">{{
                getTimeLineStampFormat(itemData.replyInfo.completeTime)
              }}</span>
            </span>
          </div>
          <div class="task-center-card-bottom-right-btn">
            <!-- 工单批复指派 -->
            <span
              v-if="itemData.replyInfo.permInfo.reAssignable"
              class="task-center-card-bottom-right-btn-item"
              @click.stop="
                handleTrigger(
                  getWorkOrderReplyWhetherTeam(itemData, itemData.replyUserList)
                    ? 'approvalDistribute'
                    : 'approvalAssignPerson',
                  itemData,
                  true
                )
              "
              ><i class="iconfont link-distribute" />{{
                t("trade_home_approvalAssign")
              }}</span
            >
            <span
              v-if="itemData.replyInfo.permInfo.editable"
              class="task-center-card-bottom-right-btn-item"
              @click.stop="handleTrigger('workOrderReply', itemData)"
              ><i class="iconfont link-reply" />{{
                t("trade_home_approval")
              }}</span
            >
            <el-dropdown
              v-if="itemData.replyInfo.permInfo.updateProcessor"
              placement="bottom"
              trigger="click"
              style="vertical-align: text-top"
              @command="handleTrigger"
            >
              <span class="task-center-card-bottom-right-btn-item"
                ><i class="iconfont link-more"
              /></span>
              <template #dropdown>
                <el-dropdown-menu class="order-region-popper">
                  <!-- 修改工单批复人 -->
                  <el-dropdown-item
                    :command="{
                      data: itemData,
                      isSecond: true,
                      type: getWorkOrderReplyWhetherTeam(
                        itemData,
                        itemData.replyUserList
                      )
                        ? 'modifyWorkOrderProcessor'
                        : 'approvalAssignPerson'
                    }"
                  >
                    <i class="iconfont link-modify-processor" />
                    <span>{{ t("trade_home_approvalAssignPerson") }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <span
              v-if="itemData.replyInfo.completeReasonType"
              class="task-center-card-bottom-right-btn-item"
              @click.stop="handleTrigger('workOrderDynamic', itemData)"
              ><i class="iconfont link-display" />{{
                t("trade_common_view")
              }}</span
            >
            <span
              class="task-center-card-bottom-right-btn-item-star"
              @click.stop="changeCollect(itemData, 'work-order-reply')"
            >
              <i
                v-if="!itemData.replyInfo?.favorite"
                class="iconfont link-star colorNormal"
              />
              <i v-else class="iconfont link-star-filled colorOrange" />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <WorkOrderReplyDialog
    ref="WorkOrderReplyDialogRef"
    @handleUpdateCollection="handleConfirmSuccess"
  />
  <WorkOrderMilestoneReplyDialog
    ref="WorkOrderMilestoneReplyDialogRef"
    @handleUpdateCollection="handleConfirmSuccess"
  />
  <WorkOrderCollectionDialog
    ref="WorkOrderCollectionDialogRef"
    @handleUpdateCollection="handleConfirmSuccess"
  />
  <!-- 修改处理人弹窗 -->
  <OrderChooseProcessorDialog
    ref="chooseProcessorRef"
    :personnelControlMode="orderChooseMode"
    :dialogTitle="orderChooseTitle"
    :orderChooseType="orderChooseType"
    @handleChooseConfirm="handleChooseConfirm"
  />
  <!-- 修改工单处理人弹窗 -->
  <EditWorkOrderDialog
    ref="EditWorkOrderDialogRef"
    @handleCreateWorkOrderSuccess="handleConfirmSuccess"
  />
</template>
<script lang="ts" setup>
import dayjs from "dayjs";
import LkAvater from "@/components/lkAvatar/index";
import { ReText } from "@/components/ReText";
import { updateTaskFavorite } from "@/api/order";
import WorkOrderReplyDialog from "@/views/orderDetail/components/WorkOrderReplyDialog.vue";
import WorkOrderMilestoneReplyDialog from "@/views/orderDetail/components/WorkOrderMilestoneReplyDialog.vue";
import WorkOrderCollectionDialog from "@/views/orderDetail/components/WorkOrderCollectionDialog.vue";
import OrderChooseProcessorDialog from "@/views/orderDetail/components/OrderChooseProcessor/OrderChooseProcessorDialog.vue";
import EditWorkOrderDialog from "@/views/orderDetail/components/EditWorkOrderDialog.vue";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";

import {
  getTradeSccsRolePermission,
  getTradeCoopSccsRolePermission,
  assignMilestoneReply,
  updateMilestoneReply,
  assignMilestoneWorkOrderCoopTeamReplier,
  updateCoolTeamReplier,
  updateCoolTeamCollector,
  assignMilestoneCollect,
  assignMilestoneWorkOrderReply,
  updateWorkOrderReply,
  getOrderDetail
} from "@/api/order";
import { storageLocal, storageSession } from "@pureadmin/utils";
import { ElMessage } from "element-plus";

import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { getSccsRolePermSet } from "@/api/common";
import { TransformSubmitDataStructure } from "@/utils/formDesignerUtils";

const { t } = useI18n();
const router = useRouter();

const props = defineProps({
  itemData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});
const emit = defineEmits(["handleConfirmSuccess"]);
const WorkOrderReplyDialogRef = ref<HTMLElement | any>(null);
const WorkOrderMilestoneReplyDialogRef = ref<HTMLElement | any>(null);
const WorkOrderCollectionDialogRef = ref<HTMLElement | any>(null);
const chooseProcessorRef = ref<HTMLElement | any>(null);
const EditWorkOrderDialogRef = ref<HTMLElement | any>(null);

const orderChooseMode = ref<string>("listMode");
const orderChooseTitle = ref<string>("");
const orderChooseType = ref<string>("");
const orderChooseRowData = ref<any>({});

const currentOrderId = ref<string>("");
const currentSccsId = ref<string>("");
const currentClickIsSecond = ref<boolean>(false);

/**
 * 工单批复的指派权限：当前登录的这个团队的，而不是这个团队的人
 */
const getWorkOrderReplyWhetherTeam = (workOrder: any, userList: any[]) => {
  if (workOrder.edited) {
    return false;
  }
  const { latestLoginTeamId } = storageLocal().getItem("user-info") as any;
  const msReplyTeamList = userList.filter(msReplyUsers => !msReplyUsers.user);
  const index = msReplyTeamList.findIndex(
    teamItem => teamItem.teamId === latestLoginTeamId
  );
  return msReplyTeamList && msReplyTeamList.length === 0 ? false : index !== -1;
};

const getTimeLineStampFormat = (dateTime: any, withSecond: boolean = true) => {
  if (dateTime && withSecond) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm");
  } else if (dateTime && !withSecond) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD");
  } else {
    return "";
  }
};
const changeCollect = async (itemData, type) => {
  const sourceId =
    type === "work-order" ? itemData.id : itemData.replyWorkOrderTaskId;
  const favorite =
    type === "work-order" ? itemData.favorite : itemData.replyInfo.favorite;
  const { code } = await updateTaskFavorite({
    favoriteType: "TASK", // 收藏类型
    sourceId: sourceId, // 收藏对象id
    favorite: !favorite // 是否收藏
  });
  if (code === 0) {
    type === "work-order"
      ? (itemData.favorite = !favorite)
      : (itemData.replyInfo.favorite = !favorite);
  }
};
const handleTrigger = async (type: any, itemData, isSecond?: boolean) => {
  if (typeof type === "string") {
    currentSccsId.value = itemData.sccsId;
    currentOrderId.value = itemData.orderId;
    currentClickIsSecond.value = isSecond || false;
  } else {
    currentSccsId.value = type.data.sccsId;
    currentOrderId.value = type.data.orderId;
    currentClickIsSecond.value = type.isSecond;
  }
  if (type === "milestoneReply" || type === "milestoneDynamic") {
    // 里程碑批复
    WorkOrderMilestoneReplyDialogRef.value.open(
      {
        msId: itemData.msId
      },
      {},
      type,
      {
        sccsId: itemData.sccsId,
        templateId: itemData.templateId,
        orderId: itemData.orderId
      }
    );
  } else if (type === "workOrderReply" || type === "workOrderDynamic") {
    // 工单批复
    WorkOrderReplyDialogRef.value.open(
      {
        msId: itemData.msId,
        workOrderId: itemData.fromWorkOrderId
      },
      {},
      type,
      itemData.orderId,
      {
        sccsId: itemData.sccsId,
        templateId: itemData.templateId
      }
    );
  } else if (type === "workOrderCollect") {
    // 工单采集
    WorkOrderCollectionDialogRef.value.open(
      {
        msId: itemData.msId,
        workOrderId: itemData.workOrderId
      },
      {},
      itemData.orderId,
      {
        sccsId: itemData.sccsId,
        templateId: itemData.templateId
      }
    );
  } else if (type === "approvalAssignPerson") {
    // 修改sccs协作团队工单批复人
    orderChooseTitle.value = "trade_order_updateWorkOrderApprover";
    orderChooseType.value = type;
    orderChooseMode.value = "tabMode";
    orderChooseRowData.value = itemData;
    let rowDataEditList = itemData.replyUserList?.map(replyUser => {
      return Object.assign(replyUser, {
        teamMemberId: replyUser.teamMemberId,
        avatar: replyUser.userAvatar,
        username: replyUser.userName,
        coop: replyUser.coopTeamUser
      });
    });
    chooseProcessorRef.value.open(rowDataEditList, "ASSIGN_PROCESSOR", {
      sccsId: itemData.sccsId
    });
  } else if (type.type === "modifyWorkOrderProcessor") {
    // 修改工单批复人
    orderChooseTitle.value = "trade_order_updateWorkOrderApprover";
    orderChooseType.value = type.type;
    orderChooseMode.value = "tabMode";
    orderChooseRowData.value = type.data;
    let rowDataEditList = type.data.replyUserList?.map(replyUser => {
      return Object.assign(replyUser, {
        teamMemberId: replyUser.teamMemberId,
        avatar: replyUser.userAvatar,
        username: replyUser.userName,
        coop: replyUser.coopTeamUser
      });
    });
    chooseProcessorRef.value.open(rowDataEditList, "ASSIGN_PROCESSOR", {
      sccsId: type.data.sccsId
    });
  } else if (type.type === "approvalAssignPerson") {
    // 修改sccs协作团队工单批复人
    orderChooseTitle.value = "trade_order_updateWorkOrderApprover";
    orderChooseType.value = type.type;
    orderChooseMode.value = "listMode";
    orderChooseRowData.value = type.data;
    let rowDataEditList = type.data.replyUserList.map(replyUser => {
      return Object.assign(replyUser, {
        teamMemberId: replyUser.teamMemberId,
        avatar: replyUser.userAvatar,
        username: replyUser.userName,
        coop: replyUser.coopTeamUser
      });
    });
    chooseProcessorRef.value.open(rowDataEditList, '"ASSIGN_PROCESSOR"', {
      sccsId: type.data.sccsId
    });
  } else if (type.type === "modifyProcessor") {
    // 内部sccs修改处理人
    const { data } = await getOrderDetail({
      sccsId: type.data.sccsId,
      orderId: type.data.orderId
    });
    const { widgetList, subWidgetMap } = data;
    let orderWidgetList = TransformSubmitDataStructure(
      widgetList,
      subWidgetMap
    );
    let workOrderData = data.milestoneGroupList
      .find(e => e.msId === type.data.msId)
      .workOrderGroupList.find(e => e.workOrderId === type.data.workOrderId);
    EditWorkOrderDialogRef.value.open(
      type.data.sccsId,
      workOrderData,
      orderWidgetList
    );
  } else if (type.type === "updateMilestonePersonnel") {
    // 修改里程碑批复人
    orderChooseTitle.value = "trade_order_updateMileStoneCollectors";
    orderChooseType.value = type.type;
    orderChooseMode.value = "tabMode";
    orderChooseRowData.value = type.data;
    let rowDataEditList = type.data.editUserList.map(replyUser => {
      return Object.assign(replyUser, {
        teamMemberId: replyUser.teamMemberId,
        avatar: replyUser.userAvatar,
        username: replyUser.userName,
        coop: replyUser.coopTeamUser
      });
    });
    chooseProcessorRef.value.open(rowDataEditList, "ASSIGN_PROCESSOR", {
      sccsId: type.data.sccsId
    });
  } else if (type === "collectDistribute") {
    // 工单采集指派
    orderChooseTitle.value = "trade_order_WorkOrderCollectionAssignment";
    orderChooseType.value = type;
    orderChooseMode.value = "listMode";
    orderChooseRowData.value = itemData;
    chooseProcessorRef.value.open([], "ASSIGN_PROCESSOR", {
      sccsId: itemData.sccsId
    });
  } else if (type === "collectorsPerson") {
    // 修改sccs协作团队工单采集人
    orderChooseTitle.value = "trade_order_updateWorkOrderCollectors";
    orderChooseType.value = type;
    orderChooseMode.value = "listMode";
    orderChooseRowData.value = itemData;
    const editUserList = itemData.editUserList.map(replyUser => {
      return Object.assign(replyUser, {
        teamMemberId: replyUser.teamMemberId,
        avatar: replyUser.userAvatar,
        username: replyUser.userName,
        coop: replyUser.coopTeamUser
      });
    });
    chooseProcessorRef.value.open(editUserList, "ASSIGN_PROCESSOR", {
      sccsId: itemData.sccsId
    });
  } else if (type === "approvalDistribute") {
    // 工单批复指派
    orderChooseTitle.value = "trade_order_AssignWorkOrderApproval";
    orderChooseType.value = type;
    orderChooseMode.value = "listMode";
    orderChooseRowData.value = itemData;
    chooseProcessorRef.value.open([], "ASSIGN_PROCESSOR", {
      sccsId: itemData.sccsId
    });
  } else if (type === "milestoneAssign") {
    // 修改里程碑批复人
    orderChooseTitle.value = "trade_order_updateMilestoneApprover";
    orderChooseType.value = type;
    orderChooseMode.value = "listMode";

    orderChooseRowData.value = itemData;
    chooseProcessorRef.value.open([], "ASSIGN_PROCESSOR", {
      sccsId: itemData.sccsId
    });
  } else if (type === "updateMilestoneCoopPersonnel") {
    // 修改sccs协作团队里程碑处理人
    orderChooseTitle.value = "trade_order_updateWorkOrderApprover";
    orderChooseType.value = type;
    orderChooseMode.value = "listMode";
    orderChooseRowData.value = itemData;
    let rowDataEditList = itemData.editUserList.map(replyUser => {
      return Object.assign(replyUser, {
        teamMemberId: replyUser.teamMemberId,
        avatar: replyUser.userAvatar,
        username: replyUser.userName,
        coop: replyUser.coopTeamUser
      });
    });
    chooseProcessorRef.value.open(rowDataEditList, "ASSIGN_PROCESSOR", {
      sccsId: itemData.sccsId
    });
  }
};
const handleConfirmSuccess = (): void => {
  emit("handleConfirmSuccess");
};

const handleChooseConfirm = async (
  chooseTeamMemberIds: string[],
  triggerType: string
): Promise<void> => {
  const sccsId = currentSccsId.value;
  const orderId = currentOrderId.value;
  //todo 代码简化
  if (triggerType === "milestoneAssign") {
    // 修改里程碑批复人
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: orderChooseRowData.value.msId,
      workOrderId: orderChooseRowData.value.workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "updateMilestonePersonnel") {
    // 修改里程碑批复人
    const { msId, workOrderId } = orderChooseRowData.value;
    const { code } = await updateMilestoneReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: workOrderId,
      milestoneReplyAssignedUserVOList: chooseTeamMemberIds
    });
    operateAfter(code);
  } else if (triggerType === "updateMilestoneCoopPersonnel") {
    //修改sccs协作团队里程碑处理人
    const { msId, workOrderId } = orderChooseRowData.value;
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneWorkOrderCoopTeamReplier({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "modifyWorkOrderProcessor") {
    //修改工单批复人
    const { msId, workOrderReplyId, workOrderId } = orderChooseRowData.value;
    const { code } = await updateWorkOrderReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: currentClickIsSecond.value ? workOrderReplyId : workOrderId,
      workOrderReplyAssignedUserVOList: chooseTeamMemberIds
    });
    operateAfter(code);
  } else if (triggerType === "approvalAssignPerson") {
    // 修改sccs协作团队工单批复人
    const { msId, workOrderReplyId, workOrderId } = orderChooseRowData.value;
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await updateCoolTeamReplier({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: currentClickIsSecond.value ? workOrderReplyId : workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "collectorsPerson") {
    // 修改sccs协作团队工单采集人
    const { msId, workOrderId } = orderChooseRowData.value;
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await updateCoolTeamCollector({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "collectDistribute") {
    // 工单采集指派
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneCollect({
      sccsId: sccsId,
      orderId: orderId,
      msId: orderChooseRowData.value.msId,
      workOrderId: orderChooseRowData.value.workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  } else if (triggerType === "approvalDistribute") {
    // 工单批复指派
    let chooseTeamMemberId = [];
    const { msId, workOrderReplyId, workOrderId } = orderChooseRowData.value;
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneWorkOrderReply({
      sccsId: sccsId,
      orderId: orderId,
      msId: msId,
      workOrderId: currentClickIsSecond.value ? workOrderReplyId : workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    operateAfter(code);
  }
};
const operateAfter = code => {
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    emit("handleConfirmSuccess");
  }
};
const gotoSccsManage = async () => {
  const { sccsId, templateId, sccsName, sccsTeamIsCurrentTeam } =
    props.itemData;
  const { data } = await getSccsRolePermSet({ sccsId: sccsId });
  storageSession().setItem("userSccsPerm", data);
  const parameter = {
    sccsId: sccsId,
    templateId: templateId,
    coopTeamMark: !sccsTeamIsCurrentTeam ? 1 : 2,
    sccsName: sccsName
  };
  router.push({ name: "orderManage", query: parameter });
};
const gotoDetail = () => {
  handleJumpDetail(props.itemData);
};
const handleJumpDetail = async (row): Promise<void> => {
  const { orderId, templateId, sccsName, sccsId } = row;
  const params = {
    sccsName: sccsName,
    sccsId: sccsId,
    templateId: templateId,
    orderId: orderId,
    orderMark: row.orderMark,
    coopTeamMark: row.sccsTeamIsCurrentTeam ? "2" : ""
  };
  let resp: any = {};
  if (params.coopTeamMark === "2") {
    resp = await getTradeSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  } else {
    resp = await getTradeCoopSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  }
  //@ts-ignore
  storageLocal().setItem(`${row.orderId}_userTeamRole`, resp.data[row.orderId]);
  router.push({ name: "orderDetail", query: params });
};
</script>
<style lang="scss" scoped>
.task-center-card {
  width: 100%;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 2px 13px 0 rgb(0 0 0 / 9%);

  &:last-child {
    margin-bottom: 0;
  }

  .task-center-card-top {
    height: 66px;
    padding: 0 12px;
    padding-top: 10px;
    border-radius: 8px 8px 0 0;

    .task-center-card-top-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 5px;

      .task-center-card-top-header-left {
        display: flex;
        align-items: center;

        .mask {
          display: flex;
          align-items: center;
          max-width: 728px;
          padding: 2px 6px;
          margin-right: 22px;
          cursor: pointer;
          background: #d9e1f8;
          border-radius: 4px;

          .link-arrow {
            margin-left: 4px;
            font-size: 12px;
            color: #96abeb;
          }

          .el-text.el-text--info {
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
            color: #595959;
          }

          &:hover {
            background: #dae3fe;

            .el-text.el-text--info {
              color: #262626;
            }
          }
        }
      }

      .task-center-card-top-header-right {
        font-size: 12px;
        line-height: 14px;
        color: #8c8c8c;
      }
    }

    .task-center-card-top-milestone-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .task-center-card-top-milestone-info-left,
      .task-center-card-top-milestone-info-right {
        display: flex;
        align-items: center;

        .milestone-name {
          display: flex;
          align-items: center;
          max-width: 548px;
          margin-right: 8px;

          .el-text.el-text--info {
            font-size: 14px;
            line-height: 20px;
            color: #262626;
          }
        }

        .label-group {
          display: flex;
          align-items: center;

          .label-item {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 120px;
            padding: 0 5px;
            margin-right: 5px;
            overflow: hidden;
            font-size: 12px;
            border-style: solid;
            border-width: 1px;
            border-radius: 12px;

            ::v-deep(.el-text.is-truncated) {
              font-size: 12px;
              color: inherit;
            }
          }

          .more-label {
            width: 24px;
            height: 20px;
            font-size: 12px;
            line-height: 20px;
            color: #595959;
            text-align: center;
            cursor: pointer;
            border: 1px solid #cfcfcf;
            border-radius: 11px;
          }
        }
      }

      .task-center-card-top-milestone-name {
        display: inline-flex;
        align-items: center;
        max-width: 255px;
        cursor: pointer;

        .el-text.el-text--info {
          font-size: 12px;
          line-height: 17px;
          color: #0070d2;
        }

        &:hover {
          .el-text.el-text--info {
            color: #3493e6;
          }
        }
      }

      .line {
        display: inline-block;
        width: 1px;
        height: 14px;
        margin: 0 12px;
        border: 1px solid #ddd;
      }

      .task-center-card-top-team-name {
        display: flex;
        align-items: center;
        max-width: 409px;

        .el-text.el-text--info {
          font-size: 12px;
          line-height: 14px;
          color: #8c8c8c;
        }

        .link-coop {
          margin-right: 3px;
          font-size: 12px;
        }
      }
    }
  }

  .task-center-card-bottom {
    display: flex;
    height: 104px;
    background: #fff;
    border-top: 1px solid #ebebeb;
    border-radius: 0 0 8px 8px;

    .task-center-card-bottom-left {
      flex: 1;
      padding: 12px;

      .task-center-card-bottom-left-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .task-center-card-item-worderName {
          display: flex;
          align-items: center;
          max-width: 340px;
          padding: 2px 6px;
          margin-right: 20px;
          cursor: pointer;
          background: #e6f1fb;
          border-radius: 4px;

          .el-text.el-text--info {
            font-size: 14px;
            color: #0070d2;
          }

          &:hover {
            color: #0070d2;
            background: #ccecff;
          }
        }

        .task-center-card-bottom-left-title-content {
          display: flex;
          align-items: center;
        }

        .task-center-card-item-milestoneName {
          display: flex;
          align-items: center;
          max-width: 340px;
          padding: 2px 6px;
          margin-right: 20px;
          cursor: pointer;
          background: #fdf7e2;
          border-radius: 4px;

          .el-text.el-text--info {
            font-size: 14px;
            color: #d7b20f;
          }

          &:hover {
            background: #fff0b4;
          }
        }

        .task-center-card-item-worderComplete,
        .task-center-card-item-milestoneComplete {
          display: flex;
          align-items: center;

          .task-center-card-item-worderComplete-progress {
            width: 100px;
            font-size: 10px;

            ::v-deep(.el-progress-bar__outer) {
              background: #cedbff;
            }
          }

          .task-center-card-item-worderComplete-text {
            margin-right: 5px;
            font-size: 12px;
            color: #8c8c8c;
          }

          .task-center-card-item-milestoneComplete-text {
            margin-right: 7px;
            font-size: 12px;
            color: #8c8c8c;
          }
        }

        .task-center-card-item-milestoneComplete {
          margin-right: 36px;

          &:last-child {
            margin-right: 0;
          }

          .task-center-card-item-milestoneComplete-count {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
          }
        }
      }

      .task-center-card-bottom-left-content {
        display: flex;
        align-items: center;
        margin-top: 16px;

        .task-center-card-bottom-left-content-col {
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-right: 32px;

          .task-center-card-col-tip {
            margin-right: 6px;
            font-size: 12px;
            color: #8c8c8c;
          }

          .task-center-card-col-avater {
            display: inline-flex;
            align-items: center;

            .lk-avater-container {
              position: relative;
              display: inline-flex;
              align-items: center;
            }

            .task-center-card-col-text {
              margin-left: 3px;
              font-size: 12px;
              color: #262626;

              &.time {
                margin-left: 10px;
              }
            }

            .milestone-reply-form-span-red-text {
              margin-left: 10px;
              font-size: 12px;
              font-weight: bolder;
              color: #e62412;
            }
          }
        }

        .task-center-card-bottom-left-content-col:last-child {
          margin-right: 0;
        }
      }
    }

    .task-center-card-bottom-right {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 453px;
      border-left: 1px solid #ebebeb;

      .task-center-card-bottom-right-type {
        position: absolute;
        left: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 9px;
        height: 100%;
        border-radius: 0 5px 5px 0;

        .type-block-workder-order {
          display: inline-block;
          width: 6px;
          height: 50%;
          background: #0070d2;
          border-radius: 0 3px 3px 0;
          // &.worder-order {
          //   background: #0070d2;
          // }

          // &.worder-order-reply {
          //   background: #f59348;
          // }

          // &.milstobe-reply {
          //   background: #edd057;
          // }
        }

        .type-block-milstobe-reply {
          display: inline-block;
          width: 6px;
          height: 50%;
          background: #edd057;
          border-radius: 0 3px 3px 0;
          // &.worder-order {
          //   background: #0070d2;
          // }

          // &.worder-order-reply {
          //   background: #f59348;
          // }

          // &.milstobe-reply {
          //   background: #edd057;
          // }
        }

        .type-block-worder-order-reply {
          display: inline-block;
          width: 6px;
          height: 50%;
          background: #f59348;
          border-radius: 0 3px 3px 0;
          // &.worder-order {
          //   background: #0070d2;
          // }

          // &.worder-order-reply {
          //   background: #f59348;
          // }

          // &.milstobe-reply {
          //   background: #edd057;
          // }
        }
      }

      .task-center-card-bottom-right-oprate {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50%;
        padding: 0 12px;

        &.worder-order-reply {
          border-top: 1px solid #ebebeb;
        }

        &.worder-order {
          background: #f0f7ff;
        }

        &.milestone-reply {
          border-top: 1px solid #ebebeb;
        }

        .task-center-card-bottom-right-desc {
          display: flex;
          flex-direction: column;
          font-size: 12px;
          line-height: 17px;
        }

        .task-center-card-bottom-right-btn {
          display: flex;
          align-items: center;

          .task-center-card-bottom-right-btn-item {
            padding: 6px 16px;
            margin-right: 10px;
            font-size: 14px;
            line-height: 20px;
            color: #595959;
            cursor: pointer;
            background: #efefef;
            border-radius: 4px;

            &:hover {
              color: #0070d2;
              background: #e1edff;

              .iconfont {
                color: #0070d2;
              }
            }

            .iconfont {
              margin-right: 5px;
              font-size: 12px;
              color: #595959;

              &.link-more {
                margin-right: 0;
              }
            }
          }

          .task-center-card-bottom-right-btn-item-star {
            font-size: 12px;
            color: #595959;
            cursor: pointer;

            .iconfont {
              font-size: 12px;

              &.colorNormal {
                color: #8c8c8c;
              }

              &.colorOrange {
                color: rgb(254 199 60);
              }
            }
          }
        }
      }
    }
  }
}

.label-list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 120px;

  .label-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    max-width: 120px;
    padding: 0 5px;
    margin-right: 5px;
    margin-bottom: 5px;
    overflow: hidden;
    font-size: 12px;
    border-style: solid;
    border-width: 1px;
    border-radius: 12px;

    ::v-deep(.el-text.is-truncated) {
      font-size: 12px;
      color: inherit;
    }
  }
}
</style>
