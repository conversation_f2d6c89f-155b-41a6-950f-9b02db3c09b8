import { http } from "@/utils/http";
import type { Result } from "./type";
interface taskParams {
  taskQueryDayType: string;
}
/**
 * 获取关注任务数量(近几天切换)
 * @param data
 * @returns
 */
export const getUserFavoriteTaskCount = (params: taskParams) => {
  return http.request<Result>(
    "get",
    "/trade/user-task/get-user-favorite-task-count",
    {
      params
    }
  );
};

/**
 * 工作台-邮件协作统计数
 * @param data
 * @returns
 */
export const getCoopEmailCount = () => {
  return http.request<Result>("get", "/trade/work-order-coop-email/statistics");
};

/**
 * 任务中心-我发起的邮件协作列表
 * @returns
 */
export const getCoopEmailPage = (data: any) => {
  return http.request<Result>("post", "/trade/work-order-coop-email/page", {
    data
  });
};

/**
 * 我发起的判断邮件是否可以采集
 * @param data
 * @returns
 */
export const getCoopEmailCollectable = params => {
  return http.request<Result>("get", "/trade/work-order/collectable", {
    params
  });
};

/**
 * 任务中心-我收到的邮件协作列表(我协作的)
 * @returns
 */
export const getMyReceiveCoopEmailPage = (data: any) => {
  return http.request<Result>(
    "post",
    "/trade/work-order-coop-email/my-receive-coop-email-page",
    {
      data
    }
  );
};

/**
 * 我协作的-待处理个数
 * @param data
 * @returns
 */
export const getCoopEmailPendingNumber = params => {
  return http.request<Result>(
    "get",
    "/trade/work-order-coop-email/pending-number",
    {
      params
    }
  );
};

/**
 * 工作台-团队待指派数量
 * @param data
 * @returns
 */
export const getTeamPendingTaskCount = () => {
  return http.request<Result>(
    "get",
    "/trade/user-task/get-team-pending-task-count"
  );
};

/**
 * 工作台-我的待办数量
 * @param data
 * @returns
 */
export const getUserPendingTaskCount = () => {
  return http.request<Result>(
    "get",
    "/trade/user-task/get-user-pending-task-count"
  );
};

/**
 * 工作台-我的关注数量
 * @param data
 * @returns
 */
export const getUserAllFavoriteTaskCount = () => {
  return http.request<Result>(
    "get",
    "/trade/user-task/get-user-all-favorite-task-count"
  );
};

/**
 * 工作台-今天截止数量
 * @param data
 * @returns
 */
export const getUserTodayStopCount = () => {
  return http.request<Result>(
    "get",
    "/trade/user-task/get-user-today-stop-count"
  );
};

/**
 * 用户任务中心-取创建用户列表
 * @param data
 * @returns
 */
export const getCreatorList = params => {
  return http.request<Result>("get", "/trade/user-task/get-creator-list", {
    params
  });
};

/**
 * 邮件协作-发起人列表
 * @param data
 * @returns
 */
export const getPromoter = params => {
  return http.request<Result>("get", "/trade/work-order-coop-email/promoter", {
    params
  });
};

/**
 * 获取任务分页数据
 * @returns
 */
export const getUserTaskPage = (data: any) => {
  return http.request<Result>("post", "/trade/user-task/page", { data });
};
