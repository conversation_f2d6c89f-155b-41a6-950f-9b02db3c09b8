.task-center-container {
  display: flex;
  justify-content: center;
  height: calc(100vh - 41px);
  margin: 0 !important;
  overflow: hidden;

  .task-center-container-left {
    width: 180px;
    height: 100%;
    padding-top: 16px;

    .el-menu {
      height: 100%;
      background-color: #f5f6fa !important;

      .el-menu-item {
        display: flex;
        align-items: center;
        padding: 0 16px;
        font-weight: 600;
        background-color: #f5f6fa !important;

        .el-menu-title {
          display: inline-flex;
          align-items: center;

          .el-text {
            color: #797979 !important;
          }
        }

        &:hover {
          background: #f0f0f0 !important;
        }

        &.is-active {
          font-size: 14px;
          color: #0070d2 !important;
          background: #e6f1ff !important;
          border-right: 2px solid #0070d2;

          .el-menu-title {
            .el-text {
              font-weight: bold;
              color: #0070d2 !important;
            }
          }
        }

        .svg-icon {
          flex-shrink: 0;
          width: 16px;
          height: 16px;
          margin-right: 10px;
        }

        .el-menu-badge {
          height: 18px;
          padding: 0 6px;
          margin-left: 10px;
          font-size: 12px;
          line-height: 18px;
          color: #fff;
          text-align: center;
          background-color: var(--el-color-danger);
          border-radius: 10px;
        }
      }

      .el-sub-menu {
        &.is-active {
          ::v-deep(.el-sub-menu__title) {
            color: #000 !important;
          }
        }

        ::v-deep(.el-sub-menu__title) {
          background: none !important;

          &:hover {
            font-size: 14px;
            background: none !important;
          }
        }
      }
    }
  }

  .task-center-container-right {
    flex: 1;
    min-width: 1210px;
    max-width: 1558px;
    height: 100%;
    padding-top: 16px;

    .task-center-container-right-header {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 0 20px;
    }

    .task-center-container-right-container {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: calc(100vh - 140px);
      padding-bottom: 16px;

      .task-card {
        width: 100%;
        padding-bottom: 20px;
        overflow: auto;
      }

      ::v-deep(.el-loading-mask) {
        background: #f5f6fa;
      }
    }
  }
}

/* 小屏模式 */
@media (width <= 1440px) {
  .task-center-container {
    justify-content: flex-start;
    overflow: auto hidden;

    .task-center-container-left {
      flex-shrink: 0;

      .el-menu-title {
        max-width: 105px !important;
      }
    }
  }
}

/* 小屏模式 */
@media (width >1440px) {
  .task-center-container {
    .task-center-container-left {
      width: 210px !important;
    }
  }
}
