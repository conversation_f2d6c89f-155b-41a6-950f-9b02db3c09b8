.create-work-order-dialog {
  height: 90%;

  .el-dialog__header {
    padding: 0 !important;
  }

  .el-dialog__footer {
    display: none !important;
  }

  .el-dialog__body {
    height: 100% !important;
  }

  .create-work-order-tabs {
    // height: 60vh;
    height: calc(100% - 50px);

    .el-tabs__header {
      justify-content: left;
      height: 54px;
      margin-bottom: 0 !important;

      .el-tabs__nav {
        display: inline-flex;
        align-items: center;
        height: 54px;
      }

      .el-tabs__nav-wrap {
        flex: none !important;
        padding-left: 18px !important;

        &.is-scrollable {
          padding: 0 34px !important;
        }

        .el-tabs__nav-prev,
        .el-tabs__nav-next {
          display: inline-block;
          width: 24px;
          height: 24px;
          margin: 8px 5px;
          line-height: 24px;
          text-align: center;
          background: #f2f2f2;
          border-radius: 50%;
        }

        .el-tabs__nav-scroll {
          display: inline-flex;
          width: auto !important;
          height: 54px;

          .el-tabs__item {
            padding: 0 8px 0 11px;

            .create-work-order-tabs-header {
              display: flex;
              align-items: center;

              .create-work-order-text {
                margin-right: 7px;
                font-size: 14px;
                font-weight: normal;
                line-height: 14px;
                color: #262626;
                text-align: left;
              }

              .create-work-order-btn {
                display: inline-flex;
                justify-content: center;
                width: 22px;
                height: 22px;
                margin-right: 5px;
                line-height: 22px;
                text-align: center;
                border-radius: 4px;

                .iconfont {
                  font-size: 12px;
                  color: #808080;
                }

                &:hover {
                  background: #e5e5e5;
                }
              }

              .create-work-order-error-icon {
                font-size: 12px;
                color: #e62412;
              }
            }

            &.is-active {
              .create-work-order-tabs-header {
                .create-work-order-text {
                  font-weight: bolder;
                  color: #0070d2;
                }
              }
            }

            &:hover {
              .create-work-order-tabs-header {
                .create-work-order-text {
                  font-weight: bolder;
                  color: #0070d2;
                }
              }
            }
          }
        }
      }

      .el-tabs__new-tab {
        position: relative;
        border: 0 none;
      }
    }

    .el-tabs__content {
      background: #f6f6f6;
      border-radius: 6px;

      .el-tab-pane {
        height: 100%;

        .create-work-order-container {
          display: flex;
          width: 100%;
          height: 100%;
          padding: 10px;
          background: #f6f6f6;

          .create-work-order-left {
            width: 358px;
            margin-right: 15px;
            background: #fff;
            border-radius: 6px;

            .create-work-order-form {
              padding: 20px 14px;

              .create-work-order-form-list {
                margin-top: 29px;

                .dialog-content-form-list-container {
                  padding: 10px 8px;
                  background: #f4f5fa;
                  border-radius: 4px;

                  .work-order-personnel-header {
                    margin-bottom: 10px;

                    .work-order-personnel-header-btn {
                      display: flex;
                      align-items: center;
                      cursor: pointer;

                      .iconfont {
                        font-size: 14px;
                        color: #0070d2;
                      }

                      .work-order-personnel-header-btn-text {
                        margin-left: 6px;
                        font-size: 13px;
                        font-weight: bold;
                        line-height: 18px;
                        color: #0070d2;
                        text-align: left;
                      }
                    }
                  }

                  .work-order-personnel-main-list {
                    .work-order-personnel-main-li {
                      padding: 10px;
                      margin-bottom: 8px;
                      background: linear-gradient(#f7faff, #fff);
                      border: 1px solid #e9eaed;
                      border-radius: 4px;

                      &:last-child {
                        margin-bottom: 0;
                      }

                      .work-order-personnel-header {
                        display: flex;
                        align-items: baseline;

                        .work-order-personnel-header-title {
                          font-size: 14px;
                          font-weight: bolder;
                          line-height: 14px;
                          color: #262626;
                          text-align: left;

                          &::before {
                            display: none !important;
                            content: "";
                          }
                        }

                        .work-order-personnel-header-control {
                          flex: 1;
                          text-align: right;
                          white-space: nowrap;

                          .el-switch__label--right {
                            margin-left: 5px;
                          }
                        }
                      }

                      .work-order-peronnel-container {
                        .work-order-peronnel-container-text {
                          display: inline-block;
                          height: 29px;
                          font-size: 13px;
                          font-weight: normal;
                          line-height: 29px;
                          color: #bfbfbf;
                          text-align: left;
                        }
                      }
                    }
                  }
                }
              }

              .create-work-order-title {
                display: flex;
                align-items: center;
                height: 16px;
                margin: 10px 0;
                font-size: 14px;
                font-weight: bolder;
                line-height: 16px;
                color: #262626;
                text-align: left;

                .work-order-personnel-header-control {
                  flex: 1;
                  text-align: right;
                  white-space: nowrap;
                }

                &::before {
                  margin-right: 6px;
                  font-weight: bolder;
                  color: #e61b1b;
                  content: "*";
                }
              }

              .work-order-personnel-header {
                display: flex;
                align-items: center;

                .work-order-personnel-header-title {
                  font-size: 14px;
                  font-weight: bolder;
                  line-height: 14px;
                  color: #262626;
                  text-align: left;

                  &::before {
                    margin-right: 6px;
                    font-weight: bolder;
                    color: #e61b1b;
                    content: "*";
                  }

                  .work-order-personnel-header-tip {
                    font-size: 12px;
                    line-height: 20px;
                    color: #797979;
                    text-align: justify;
                  }
                }

                .work-order-personnel-header-control {
                  flex: 1;
                  text-align: right;
                }
              }

              .work-order-reply-container,
              .work-order-ms-reply-container {
                margin-top: 29px;

                .work-order-reply-body {
                  display: flex;
                  align-items: center;
                  margin-top: 13px;
                }
              }
            }
          }

          .create-work-order-right {
            flex: 1;
            max-width: calc(100% - 378px);
          }
        }
      }
    }
  }

  .create-work-order-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 50px;
    padding: 0 30px;
    background: #fff;
    border-top: 1px solid #e4e4e4;

    .create-work-order-footer-text {
      margin-right: 50px;
      font-size: 14px;
      line-height: 14px;
      color: #8c8c8c;
      text-align: left;

      .create-work-order-tip {
        font-weight: bolder;
        color: #262626;
      }
    }

    .el-button {
      width: 68px !important;
    }
  }
}

.dialog-content-flex-right-header {
  margin: 7px 0 12px;
  font-size: 12px;
  font-weight: bolder;
  line-height: 17px;
  color: #262626;
  text-align: left;
}

.work-order-tooltip-body {
  .work-order-tooltip-text {
    margin-right: 3px;
    font-size: 12px;
    font-weight: normal;
    line-height: 17px;
    color: #f6974f;
    text-align: center;
  }

  .iconfont {
    font-size: 13px;
    color: #808080;
    cursor: pointer;
  }
}

.el-dialog__body {
  position: relative;

  .create-work-order-dialog-header {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    height: 50px;

    .create-work-order-dialog-header-left {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-around;
      height: 54px;

      .header-col {
        display: inline-flex;
        flex: 1;
        align-items: center;
        padding: 3px 10px;
        margin-right: 12px;
        cursor: pointer;
        border-radius: 4px;

        &.header-icon-active {
          color: #2082ed;
          background: #e1edff;

          .header-icon {
            .iconfont {
              color: #2082ed;
            }
          }
        }

        &:nth-child(2) {
          flex: none;
        }

        &:hover {
          background: #e5e5e5;
        }

        .header-text {
          align-items: center;
          cursor: pointer;
        }

        .header-icon {
          .iconfont {
            font-size: 13px;
            color: #595959;

            &.link-quote-third-party {
              margin-right: 6px;
            }
          }
        }
      }
    }

    .create-work-order-dialog-header-right {
      position: relative;
      place-items: center center;
      width: 55px;
      height: 50px;
      vertical-align: middle;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        display: inline-block;
        width: 1px;
        height: 16px;
        content: "";
        background: #e5e5e5;
        transform: translateY(-50%);
      }

      .create-work-order-dialog-header-right-col {
        position: absolute;
        top: 50%;
        left: 50%;
        display: inline-flex;
        align-items: center;
        text-align: center;
        cursor: pointer;
        border-radius: 4px;
        transform: translate(-50%, -50%);

        &:hover {
          background: rgb(0 0 0 / 6%);
        }
      }
    }
  }
}
