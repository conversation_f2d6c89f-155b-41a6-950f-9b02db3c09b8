<template>
  <span
    v-for="item in milestoneButtons"
    :key="item.type"
    class="order-detail-region-operating-btn"
  >
    <el-badge
      v-if="!item.dropdownList"
      :value="badgeNumber"
      color="#E62412"
      :show-zero="false"
      :badge-class="'milestone-badege-class'"
      @click="handleTrigger(item)"
    >
      <span
        class="milestone-btn"
        :data-ms-id="props.workOrderData.msId"
        :data-action="item.type"
        :data-work-order-id="props.workOrderData.workOrderId"
      >
        <i :class="['iconfont', item.icon]" />
        {{ t(item.label) }}
      </span>
    </el-badge>
    <el-dropdown
      v-if="
        item.dropdownList &&
        item.dropdownList.length > 0 &&
        item.customPermission
      "
      placement="bottom"
      trigger="click"
      style="vertical-align: text-top"
      @command="handleTrigger"
    >
      <el-badge
        :value="badgeNumber"
        color="#E62412"
        :show-zero="false"
        :badge-class="'milestone-badege-class'"
      >
        <span class="milestone-btn">
          <i :class="['iconfont', item.icon]" />
          {{ t(item.label) }}
        </span>
      </el-badge>
      <template #dropdown>
        <el-dropdown-menu class="order-region-popper">
          <el-dropdown-item
            v-for="child in item.dropdownList"
            :key="child.type"
            :class="[child.class]"
            :command="child.type"
          >
            <i :class="['iconfont', child.icon]" />
            <span>{{ t(child.label) }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </span>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";

const { t } = useI18n();
const route = useRoute();
const badgeNumber = ref<number>(0);

const props = defineProps({
  milestoneButtons: {
    type: Object as PropType<any>,
    default: () => {}
  },
  workOrderData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const emit = defineEmits<{
  (e: "handleTrigger", data): void;
}>();

const handleTrigger = (item: any) => {
  emit("handleTrigger", item);
};

onMounted(() => {
  // emitter.on("handleWorkOrderEmailCoopCount", data => {
  //   badgeNumber.value = data[`${props.workOrderData.workOrderId}:COOP_EMAIL`];
  // });
});
</script>
<style lang="scss" scoped>
@use "../style/index.scss";
</style>
