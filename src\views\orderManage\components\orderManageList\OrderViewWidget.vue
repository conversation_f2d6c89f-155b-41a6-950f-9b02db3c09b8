<template>
  <div v-if="presentViewVisible">
    <Transition
      leave-active-class="animate__animated animate__fadeOutRight"
      enter-active-class="animate__animated animate__fadeInRight"
    >
      <div v-if="viewVisible" class="order-view-body">
        <div class="order-view-header">
          <div class="order-view-title">{{ t("trade_view_updateed") }}</div>
          <el-tooltip
            effect="dark"
            :content="t('trade_view_retract')"
            placement="top"
            :show-after="500"
          >
            <i
              class="iconfont link-arrow-double-right"
              @click="handleViewVisible"
            />
          </el-tooltip>
        </div>
        <div class="order-view-container">
          <el-button type="primary" color="#0070D2" @click="handleAddView">
            {{ t("trade_common_saveAs") }}
          </el-button>
          <el-button
            v-if="!props.presentView.defaultView"
            type="primary"
            plain
            color="#0070D2"
            @click="handleUpdateView"
          >
            {{ t("trade_common_coverThis") }}
          </el-button>
        </div>
      </div>
    </Transition>
    <div
      v-if="!viewVisible"
      class="order-view-save-btn"
      @click="handleViewVisible"
    >
      <i class="iconfont link-save" />
    </div>
    <LkDialog
      ref="orderViewRef"
      :title="t('trade_order_saveViews')"
      @confirm="handleSaveOrderView"
    >
      <template #default>
        <div>{{ t("trade_common_addViewTip") }}</div>
        <el-input
          v-model="orderViewName"
          maxlength="20"
          cleariable
          show-word-limit
          placeholder="最多20字"
        />
      </template>
    </LkDialog>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import LkDialog from "@/components/lkDialog/index";
import { addOrderView, updateOrderView } from "@/api/order";
import { ElMessage } from "element-plus";
import { cloneDeep, isEqual } from "@pureadmin/utils";

const props = defineProps({
  presentView: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const emit = defineEmits(["handleAddOrderView"]);

const { t } = useI18n();
const route = useRoute();
const orderViewRef = ref<any | HTMLElement>(null);
const viewVisible = ref<boolean>(true);
const presentViewVisible = ref<boolean>(false); // 变更视图的组件显示状态
const orderViewName = ref<string>("");
const orderViewId = ref<string>("");

const orderViewProp = ref<any>({
  conditionList: [],
  sortList: [],
  fields: [],
  latestTableType: "DETAIL",
  colStatisticsItemList: [],
  pageSize: 10,
  defaultView: true,
  viewType: "",
  msId: "",
  keyWords: ""
});
const oldOrderViewProp = ref<any>(undefined);

const handleViewVisible = () => {
  viewVisible.value = !viewVisible.value;
};

const handleAddView = () => {
  orderViewRef.value.open();
};

// 覆盖当前视图
const handleUpdateView = async () => {
  const { sccsId } = route.query;
  const { code } = await updateOrderView({
    id: orderViewId.value,
    sccsId: sccsId,
    title: orderViewName.value,
    ...orderViewProp.value
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    presentViewVisible.value = false;
  }
};

const handleSaveOrderView = async () => {
  const { sccsId } = route.query;
  const { code, data } = await addOrderView({
    sccsId: sccsId,
    title: orderViewName.value,
    ...orderViewProp.value
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    presentViewVisible.value = false;
    orderViewRef.value.close();
    oldOrderViewProp.value = cloneDeep(orderViewProp.value);
    emit("handleAddOrderView");
  }
};

const handleUpdatePresentViewVisible = ({
  conditionList,
  colStatisticsItemList,
  fields,
  latestTableType,
  pageSize,
  keyWords,
  viewType,
  sortList,
  msId,
  defaultView,
  title,
  id
}) => {
  if (conditionList) {
    orderViewProp.value.conditionList = conditionList;
  }
  if (fields) {
    orderViewProp.value.fields = fields;
  }
  if (colStatisticsItemList) {
    orderViewProp.value.colStatisticsItemList = colStatisticsItemList;
  }
  if (latestTableType) {
    orderViewProp.value.latestTableType = latestTableType;
  }
  if (pageSize) {
    orderViewProp.value.pageSize = pageSize;
  }
  if (keyWords) {
    orderViewProp.value.keyWords = keyWords;
  }
  if (sortList) {
    orderViewProp.value.sortList = sortList;
  }
  orderViewProp.value.viewType = viewType;
  orderViewProp.value.msId = msId;
  orderViewProp.value.defaultView = defaultView;
  if (!defaultView) {
    orderViewName.value = title;
    orderViewId.value = id;
  } else {
    orderViewName.value = "";
  }

  if (!oldOrderViewProp.value) {
    oldOrderViewProp.value = cloneDeep(orderViewProp.value);
  } else {
    // 比较新旧配置，如果有变化才更新visible
    const configChanged = !isEqual(oldOrderViewProp.value, orderViewProp.value);

    if (configChanged) {
      presentViewVisible.value = true;
    } else {
      presentViewVisible.value = false;
    }
  }
};

defineExpose({
  handleUpdatePresentViewVisible
});
</script>
<style lang="scss" scoped>
.order-view-body {
  position: fixed;
  right: 0;
  bottom: 150px;
  z-index: 3000;
  width: 297px;
  padding: 13px 16px 18px 20px;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 17px 0 rgb(0 0 0 / 24%);

  .order-view-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 22px;

    .order-view-title {
      font-size: 14px;
      font-weight: bold;
      line-height: 20px;
      color: #262626;
    }

    .iconfont {
      font-size: 12px;
      cursor: pointer;
    }
  }
}

.order-view-save-btn {
  position: fixed;
  right: 0;
  bottom: 180px;
  z-index: 3000;
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  color: #595959;
  text-align: center;
  cursor: pointer;
  background: #fff;
  border-radius: 6px 0 0 6px;
  box-shadow: 0 2px 17px 0 rgb(0 0 0 / 24%);

  &:hover {
    background: #f5f7fa;
  }
}
</style>
