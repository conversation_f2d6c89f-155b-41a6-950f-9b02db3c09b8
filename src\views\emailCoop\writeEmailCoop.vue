<template>
  <div class="write-email-coop-body">
    <div class="write-email-coop-container">
      <div class="write-email-coop-header">
        <div class="write-email-coop-left">
          <div class="write-email-coop-title">{{ mainForm.subject }}</div>
          <div class="write-email-coop-tip">
            {{ mainForm.creatorName }}
            <span class="write-email-coop-email-tip">
              &lt;{{ mainForm.creatorEmail }}&gt;
            </span>
          </div>
        </div>
        <div class="write-email-coop-right">
          <el-image class="sign-image" :src="logoImage" />
        </div>
      </div>
      <div class="write-email-coop-detail-body">
        <el-scrollbar max-height="calc(100vh - 190px)">
          <div class="write-email-coop-detail-title">订单信息</div>
          <div class="write-email-coop-detail-form">
            <div class="write-email-coop-detail-system-message">
              <div class="write-email-coop-detail-system-message-title">
                {{ t("trade_common_remark") }}
              </div>
              <div v-if="mainRemark && mainRemark !== '<p><br></p>'">
                <Editor
                  v-model="mainRemark"
                  :defaultConfig="{ readOnly: true }"
                />
              </div>
              <div v-else>-</div>
              <div class="write-email-coop-detail-system-message-title mt30">
                {{ t("trade_email_files") }}
              </div>
              <div v-if="mainWidgetFiles.length > 0">
                <OrderFilePreview
                  v-for="item in mainWidgetFiles"
                  :key="item"
                  :filePreview="item"
                />
              </div>
              <div v-else>-</div>
            </div>
            <OrderDetailDescWidget
              v-if="mainFormData?.widgetJsonList"
              :widgetForm="mainFormData.widgetJsonList"
              :widgetData="mainWidgetData"
            />
          </div>
          <div class="write-email-coop-detail-title mt26">工单信息</div>
          <div class="write-email-coop-detail-form">
            <LkTrendsAggregationForm
              ref="TrendsAggregationFormRef"
              :sccsId="sccsIdRef"
              :orderId="mainForm.orderId"
              :workOrderId="mainForm.workOrderId"
              :formList="milestoneFormData"
              :formWidgetData="milestoneWidgetData"
              :trendsFormFlag="true"
              :hiddenFormIdList="hiddenFormIdList"
              :operationalFactorData="operationalFactorData"
              :defaultValueActuatorFlag="false"
              :workOrderAllowOperation="workOrderAllowOperation"
            />
          </div>
        </el-scrollbar>
      </div>
      <div class="write-email-coop-detail-bottom">
        <el-button
          type="primary"
          color="#0070D2"
          @click="handleSubmitEmailWidgetData"
        >
          {{ t("trade_common_sure") }}
        </el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import logoImage from "@/assets/images/email/linkincreate.png";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import OrderFilePreview from "@/views/orderManage/components/OrderFilePreview.vue";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import {
  handleAccordingToFormIdObtainData,
  TransformSubmitDataStructure,
  handleObtainDynamicDefaultValue,
  getEntireFormData
} from "@/utils/formDesignerUtils";
import { getSccsMemberList, getSccsCoopTeamList } from "@/api/sccs";

import { Editor } from "@wangeditor/editor-for-vue";
import {
  getEmailCoopReceiverStructure,
  getEmailCoopReceiverDetail,
  submitWorkOrderCoopEmail
} from "@/api/email";
import { ElMessage } from "element-plus";
import { getOrderDetail, getOrderTemplateDetail } from "@/api/order";
import { getSccsFieldVisibleConditions } from "@/api/common";
import { storageSession } from "@pureadmin/utils";

const { t } = useI18n();
const route = useRoute();
const TrendsAggregationFormRef = ref<any | HTMLElement>(null);
const receiverTemplateData = ref<any>([]);
const mainForm = ref<any>({});
const mainFormData = ref<any>({});
const mainWidgetData = ref<any>([]);
const milestoneFormData = ref<any>([]);
const mainRemark = ref<string>("");
const mainWidgetFiles = ref<any[]>([]);
const emailCollapseActive = ref<string[]>([]);
const milestoneWidgetData = ref<any>([]);
const hiddenFormIdList = ref<string[]>([]);
const operationalFactorData = ref<any>({});
const sccsIdRef = ref<string>(route.query.sccsId as string);
const orderIdRef = ref<string>(route.query.orderId as string);

const workOrderAllowOperation = computed(() => {
  return milestoneFormData.value.filter(ms => ms.editable).map(ms => ms.id);
});

const handleSubmitEmailWidgetData = async () => {
  const widgetItemDataList =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(true);
  const linkedReferenceSourceList =
    TrendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  if (widgetItemDataList === false) {
    return;
  }

  const emailCoopId = route.query.id as string;
  const { code } = await submitWorkOrderCoopEmail({
    id: emailCoopId,
    widgetItemDataList: widgetItemDataList,
    linkedReferenceSaveList: linkedReferenceSourceList
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
  }
};

onMounted(async () => {
  const emailCoopId = route.query.id as string;
  Promise.all([
    getEmailCoopReceiverStructure({ id: emailCoopId }),
    getEmailCoopReceiverDetail({ id: emailCoopId })
  ]).then(async data => {
    receiverTemplateData.value = data[0].data;
    mainFormData.value = data[0].data.find(
      mainForm => mainForm.formType === "MAIN_FORM"
    );
    milestoneFormData.value = data[0].data.filter(
      mainForm => mainForm.formType !== "MAIN_FORM"
    );
    const widgetJsonList = [];
    milestoneFormData.value.map(widget => {
      widgetJsonList.push(...widget.widgetJsonList);
    });

    emailCollapseActive.value = milestoneFormData.value.map(
      milestone => milestone.id
    );
    const {
      templateId,
      sccsId,
      orderId,
      milestoneId,
      widgetDataForOrderList,
      widgetDataSubForOrderMap,
      widgetDataList,
      widgetDataSubMap,
      formHiddenList,
      linkedReferenceMap,
      linkedReferenceForOrderMap,
      remark,
      fileList
    } = data[1].data;

    sccsIdRef.value = sccsId;
    orderIdRef.value = orderId;

    mainWidgetData.value = TransformSubmitDataStructure(
      widgetDataForOrderList,
      widgetDataSubForOrderMap,
      linkedReferenceForOrderMap
    );
    milestoneWidgetData.value = TransformSubmitDataStructure(
      widgetDataList,
      widgetDataSubMap,
      linkedReferenceMap
    );

    Object.assign(mainForm.value, data[1].data);
    mainRemark.value = remark;
    mainWidgetFiles.value = fileList;
    hiddenFormIdList.value = formHiddenList;

    if (templateId && orderId) {
      await getDefaultConfigWidgetData(orderId, {
        sccsId,
        templateId,
        from: "outSide"
      });
    }

    if (milestoneId && orderId) {
      operationalFactorData.value = getEntireFormData(milestoneId, orderId);
    }
  });
});

const getDefaultConfigWidgetData = async (
  orderId: string,
  customParams?: {
    sccsId: string;
    templateId: string;
    from?: string;
  }
) => {
  return new Promise(resolve => {
    const sccsId: string = customParams?.sccsId;
    const templateId: string = customParams?.templateId;
    Promise.all([
      getSccsMemberList({ sccsId: sccsId }),
      getSccsCoopTeamList({ sccsId: sccsId }),
      getOrderTemplateDetail({
        templateId: templateId,
        orderId: orderId
      }),
      getSccsFieldVisibleConditions({ sccsId: sccsId })
    ]).then(async resp => {
      let encapsulationData;
      let orderData;
      if (orderId && customParams?.from !== "outSide") {
        const { data } = await getOrderDetail({
          sccsId: sccsId,
          orderId: orderId
        });
        encapsulationData = await handleAccordingToFormIdObtainData(
          data,
          resp[2].data
        );
        orderData = data;
      } else {
        const { widgetJsonList, id } = resp[2].data.mainForm;
        let mainFormData = await handleObtainDynamicDefaultValue(
          widgetJsonList,
          TransformSubmitDataStructure([], [])
        );
        let mainFormObject = {};

        mainFormObject[id] = mainFormData;
        encapsulationData = {
          formulaObject: mainFormObject,
          formVisibleObject: { mainForm: mainFormObject },
          orderDetailData: {}
        };
      }

      storageSession().setItem("widgetData", {
        sccsMemberList: resp[0].data,
        sccsCoopTeamList: resp[1].data,
        entrieTemplateForm: resp[2].data,
        templateData: encapsulationData,
        formVisibleTemplateFormConfigure: resp[3].data
      });

      resolve({
        sccsMemberList: resp[0].data,
        orderDetailData: encapsulationData.orderDetailData,
        orderTemplateData: resp[2].data,
        orderData: orderData
      });
    });
  });
};
</script>

<style lang="scss" scoped>
.write-email-coop-body {
  width: 100%;
  max-height: 100vh;
  overflow: hidden;
  background: #f7f7f7;

  .write-email-coop-container {
    width: 1200px;
    height: 100vh;
    margin: 0 auto;

    .write-email-coop-header {
      display: flex;
      align-items: center;
      padding: 15px 0 40px;

      .write-email-coop-left {
        flex: 1;

        .write-email-coop-title {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: bolder;
          line-height: 16px;
          color: #262626;
          text-align: left;
        }

        .write-email-coop-tip {
          font-size: 14px;
          line-height: 16px;
          color: #262626;
          text-align: left;

          .write-email-coop-email-tip {
            color: #797979;
          }
        }
      }

      .write-email-coop-right {
        width: 179px;

        .sign-image {
          vertical-align: middle;
        }
      }
    }

    .write-email-coop-detail-body {
      .write-email-coop-detail-title {
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: bolder;
        line-height: 16px;
        color: #262626;

        &.mt26 {
          margin-top: 26px;
        }
      }

      .write-email-coop-detail-form {
        padding: 18px;
        background: #fff;
        border: 1px solid #e6eaf0;
        border-radius: 6px;

        .write-email-coop-detail-system-message {
          padding: 26px 22px;
          margin-bottom: 16px;
          background: linear-gradient(#f0f1f6, #f6f8fa);
          border-radius: 6px;

          .write-email-coop-detail-system-message-title {
            margin-bottom: 6px;
            font-size: 14px;
            line-height: 16px;
            color: #707177;
            text-align: left;

            &.mt30 {
              margin-top: 30px;
            }
          }

          ::v-deep(.w-e-text-container) {
            background: transparent !important;
            border: 1px solid #e3e3e3;
          }

          ::v-deep(.order-file-preview-body) {
            .svg-icon {
              width: 28px;
              height: 35px;
              margin-right: 10px;
            }
          }
        }

        .order-form-edit-collapse {
          .el-collapse-item.HIDDEN {
            display: none;
          }

          .order-state {
            display: inline-block;
            height: 20px;
            padding: 0 5px;
            margin-left: 5px;
            font-size: 12px;
            font-weight: normal;
            line-height: 20px;
            color: #808080;
            text-align: center;
            background: #e2e2e2;
            border-radius: 12px;
          }

          .order-state-editable {
            color: #2082ed;
            background: #d8eff9;
          }
        }
      }
    }

    .write-email-coop-detail-bottom {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 60px;
      padding: 0 24px;
      background: #fff;
      border-top: 1px solid #e6eaf0;
      border-radius: 0 0 6px 6px;
    }
  }
}
</style>
