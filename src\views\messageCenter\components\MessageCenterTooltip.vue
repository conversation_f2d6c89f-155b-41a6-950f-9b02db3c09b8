<template>
  <div v-if="tooltipVisible" class="message-center-home-main">
    <div class="message-center-home-top">
      <div class="message-center-home-top-stepper-body">
        <div class="message-center-home-top-stepper">
          <i class="iconfont link-arrow-left" @click="handleSwitch('prev')" />
          <div class="message-center-home-top-stepper-text">
            {{ tooltipCurrentIndex + 1 }}/{{ tooltipMessageList.length }}
          </div>
          <i class="iconfont link-arrow-right" @click="handleSwitch('next')" />
        </div>
      </div>
      <div class="message-center-home-top-close" @click="handleClose">
        <i class="iconfont link-close" />
      </div>
    </div>
    <div class="message-center-home-container">
      <div
        class="message-center-drawer-col"
        @click="handleClickMessage(tooltipMessageList[tooltipCurrentIndex])"
      >
        <div class="message-center-drawer-icon">
          <LKAvater
            v-if="
              !tooltipMessageList[tooltipCurrentIndex]?.fromUserAvatar ||
              tooltipMessageList[tooltipCurrentIndex]?.fromUserAvatar.includes(
                '/'
              )
            "
            :size="32"
            :teamInfo="{
              avatar: tooltipMessageList[tooltipCurrentIndex]?.fromUserAvatar,
              username: tooltipMessageList[tooltipCurrentIndex]?.fromUserName
            }"
          />
          <i
            v-else
            class="iconfont message-icon"
            :class="`link-${tooltipMessageList[tooltipCurrentIndex]?.fromUserAvatar}`"
          />
        </div>

        <div class="message-center-drawer-text-body">
          <div class="message-center-drawer-title">
            <span>{{ tooltipMessageList[tooltipCurrentIndex]?.title }}</span>

            <div class="message-center-drawer-time">
              {{
                dayjs(
                  new Date(tooltipMessageList[tooltipCurrentIndex].createTime)
                ).format("YYYY-MM-DD HH:mm:ss")
              }}
            </div>
          </div>
          <div class="message-center-drawer-tip">
            <ReText
              :key="tooltipMessageList[tooltipCurrentIndex]?.content"
              lineClamp="2"
              :tippyProps="{ delay: 50000 }"
            >
              <span
                v-html="
                  messageCenterContent(
                    tooltipMessageList[tooltipCurrentIndex]?.content
                  )
                "
              />
            </ReText>
          </div>
          <div class="message-center-drawer-footer">
            <div class="message-center-drawer-left">
              <i
                v-if="
                  ['news-email-colla', 'news-automation'].includes(
                    tooltipMessageList[tooltipCurrentIndex]?.fromTeamAvatar
                  )
                "
                class="iconfont"
                :class="`link-${tooltipMessageList[tooltipCurrentIndex]?.fromTeamAvatar}`"
              />
              <LKAvater
                v-else-if="
                  tooltipMessageList[tooltipCurrentIndex]?.fromTeamName
                "
                class="colorOrange"
                :size="18"
                :teamInfo="{
                  avatar:
                    tooltipMessageList[tooltipCurrentIndex]?.fromTeamAvatar,
                  username:
                    tooltipMessageList[tooltipCurrentIndex]?.fromTeamName
                }"
                shape="square"
              />
              <div class="message-center-drawer-left-title">
                {{ tooltipMessageList[tooltipCurrentIndex].fromTeamName }}
              </div>
            </div>
          </div>
        </div>
        <div class="message-center-drawer-next-icon">
          <i class="iconfont link-arrow-right" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from "vue";
import dayjs from "dayjs";
import { ReText } from "@/components/ReText";
import LKAvater from "@/components/lkAvatar/index";
import { useMessageCenterStoreHook } from "@/store/modules/messageCenter";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { switchTeam } from "@/utils/auth";
import { storageLocal } from "@pureadmin/utils";

// 使用消息中心 store
const messageCenterStore = useMessageCenterStoreHook();
const { tooltipMessageList, tooltipCurrentIndex, tooltipVisible } =
  storeToRefs(messageCenterStore);
const router = useRouter();
const userInfo: any = storageLocal().getItem("user-info");

// 计算属性
const messageCenterContent = computed(() => {
  return content => {
    return content?.replace(/<p>/g, "").replace(/<\/p>/g, "");
  };
});

const handleClose = (): void => {
  messageCenterStore.closeTooltip();
};

const handleSwitch = (mode: "prev" | "next") => {
  messageCenterStore.switchTooltipMessage(mode);
};

const handleClickMessage = (item: any): void => {
  messageCenterStore.markMessageAsRead(item.id);
  if (item.link) {
    const currentUrl = new URL(window.location.href);
    const targetUrl = new URL(item.link);

    const goto = (url: string) => {
      const urlObj = new URL(url);
      const teamId = urlObj.searchParams.get("toTeamId");
      if (teamId && userInfo?.latestLoginTeamId !== teamId) {
        switchTeam(teamId, true, urlObj.pathname + urlObj.search + urlObj.hash);
      } else {
        router.push(urlObj.pathname + urlObj.search + urlObj.hash);
      }
    };

    if (
      currentUrl.hostname === "localhost" ||
      currentUrl.hostname === targetUrl.hostname
    ) {
      goto(item.link);
    } else {
      window.open(item.link, "_blank");
    }
    handleClose();
  }
};
</script>
<style lang="scss" scoped>
@use "../index.scss";

::v-deep(.message-center-child-drawer) {
  .el-drawer__header {
    height: 56px;
    padding: 0 17px 0 21px !important;
    margin: 0 !important;

    .drawer-header-body {
      display: flex;

      .drawer-header-left {
        flex: 1;
        font-size: 18px;
        font-weight: bolder;
        line-height: 25px;
        color: #262626;
        text-align: left;
      }

      .drawer-header-right {
        .icon {
          font-size: 12px;
          color: #808080;
          cursor: pointer;
        }

        .icon1 {
          margin-left: 20px;
          font-size: 13px;
        }
      }
    }
  }

  .el-drawer__body {
    background: #f2f2f2 !important;

    .drawer-message-list-body {
      .drawer-message-list-row {
        background: #f2f2f2 !important;

        .message-center-drawer-col {
          display: flex;
          padding: 10px 23px 10px 20px;
          margin: 10px 14px 0;
          background: #fff;
          border-radius: 6px;

          .message-center-drawer-last-icon {
            align-items: baseline;
            margin-right: 10px;
          }

          .message-center-drawer-text-body {
            flex: 1;
            align-items: center;
            min-width: 0;
            margin-right: 58px;

            .message-center-drawer-title {
              font-size: 14px;
              line-height: 20px;
              color: #595959;
            }

            .message-center-drawer-tip {
              // height: 40px;
              margin: 8px 0 11px;
              font-size: 14px;
              line-height: 20px;
              color: #262626;
              word-break: break-all;
            }

            .message-center-drawer-avatar {
              display: flex;
              align-items: center;

              .message-center-drawer-text {
                align-items: center;
                margin-left: 2px;
                font-size: 12px;
                color: #8c8c8c;
                vertical-align: middle;
              }
            }
          }

          .message-center-drawer-next-icon {
            position: relative;
            align-content: center;

            .next-icon {
              color: #808080;
            }

            .iconRead {
              position: absolute;
              top: 0;
              right: 0;
              font-size: 10px;
              color: #808080;
            }
          }
        }
      }
    }

    .drawer-message-small-tip {
      margin: 23px auto;
      font-size: 14px;
      line-height: 20px;
      color: #a8abb2;
      text-align: center;
    }

    .iconRead {
      &:hover {
        color: #2082ed !important;
      }
    }
  }
}

.message-center-home-main {
  position: absolute;
  bottom: 25px;
  left: 75px;
  z-index: 2001;

  .message-center-home-top {
    display: flex;
    margin-bottom: 9px;

    .message-center-home-top-stepper-body {
      flex: 1;

      .message-center-home-top-stepper {
        display: flex;
        align-items: center;
        width: 150px;
        height: 32px;
        padding: 0 10px;
        background: #fff;
        border: 1px solid #efefef;
        border-radius: 72px;
        box-shadow: 0 4px 35px 0 rgb(0 0 0 / 11%);

        .iconfont {
          font-size: 12px;
          color: #808080;
          cursor: pointer;
        }

        .message-center-home-top-stepper-text {
          flex: 1;
          align-content: center;
          height: 32px;
          font-size: 14px;
          color: #262626;
          text-align: center;
        }
      }
    }

    .message-center-home-top-close {
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
      background: #fff;
      border-radius: 50%;
      box-shadow: 0 2px 17px 0 rgb(0 0 0 / 14%);

      .iconfont {
        font-size: 10px;
        color: #808080;
      }
    }
  }

  .message-center-home-container {
    box-sizing: border-box;
    width: 390px;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 18px 0 rgb(0 0 0 / 15%);

    .message-center-drawer-col {
      display: flex;
      cursor: pointer;

      .message-center-drawer-icon {
        width: 32px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        background: #3b8fea;
        border-radius: 50%;

        .iconfont {
          font-size: 14px;
          color: #fff;
        }

        .message-icon {
          display: inline-block;
          width: 32px;
          height: 32px;
          line-height: 32px;
          color: #fff;
          text-align: center;
          background: #3db0e1;
          border-radius: 50%;

          &.link-news-inform {
            background: #3be8ab;
          }
        }
      }

      .message-center-drawer-last-icon {
        align-items: baseline;
        margin-right: 10px;
      }

      .message-center-drawer-text-body {
        flex: 1;
        align-items: center;
        min-width: 0;
        max-width: 308px;
        margin: 0 10px;

        .message-center-drawer-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          line-height: 20px;

          span {
            flex: 1;
            flex-shrink: 0;
            min-width: 0;
            overflow: hidden;
            color: #595959;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .message-center-drawer-time {
            flex-shrink: 0;
            margin-left: 10px;
            font-size: 12px;
            line-height: 20px;
            color: #808080;
          }
        }

        .message-center-drawer-tip {
          // height: 40px;
          margin: 8px 0 11px;
          font-size: 14px;
          line-height: 20px;
          color: #262626;
          word-break: break-all;

          ::v-deep(.el-text) {
            display: -webkit-box;
            height: 100%;
            overflow: hidden;
            color: #262626;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2; /* 限制文本为2行 */
            white-space: normal;
            -webkit-box-orient: vertical;
          }
        }

        .message-center-drawer-footer {
          display: flex;
          align-items: center;

          .message-center-drawer-left {
            display: flex;
            flex: 1;
            align-items: center;

            .message-center-drawer-left-title {
              margin-left: 2px;
              font-size: 12px;
              line-height: 17px;
              color: #8c8c8c;
              text-align: left;
            }
          }
        }

        .message-center-drawer-avatar {
          display: flex;
          align-items: center;

          .message-center-drawer-text {
            align-items: center;
            margin-left: 2px;
            font-size: 12px;
            color: #8c8c8c;
            vertical-align: middle;
          }
        }
      }

      .message-center-drawer-next-icon {
        position: relative;
        align-content: center;

        .next-icon {
          color: #808080;
        }

        .link-arrow-right {
          font-size: 12px;
          color: #808080;
        }

        .iconRead {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 10px;
          color: #808080;
        }
      }
    }
  }
}
</style>
