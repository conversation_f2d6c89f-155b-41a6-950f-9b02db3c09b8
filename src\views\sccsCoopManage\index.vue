<template>
  <el-drawer
    v-model="drawer"
    class="team-manage-drawer-body"
    size="94%"
    destroy-on-close
  >
    <template #header="{ titleId, titleClass }">
      <el-button
        disabled
        style="flex: none; height: 24px; padding: 2px 4px 2px 7px"
      >
        {{ t("trade_common_sccsCoopSetting") }}
      </el-button>
      <div :id="titleId" :class="titleClass" class="draw-header-title">
        {{ sccsBasicInfo.sccsName }}
      </div>
    </template>
    <el-tabs
      v-model="activeName"
      class="left-tabs-body w210"
      tab-position="left"
      style="height: 100%"
      @tab-click="handleClick"
      @tab-change="handleClick"
    >
      <el-tab-pane
        v-for="(item, index) in tabPaneList"
        :key="index"
        :name="item.name"
        :lazy="true"
      >
        <template #label>
          <span class="custom-tabs-label">
            <FontIcon :icon="item.iconfont" />
            <span class="custom-tabs-text">{{ t(item.title) }}</span>
          </span>
        </template>
        <component
          :is="item.component"
          v-if="activeName === item.name"
          :basicInfo="sccsBasicInfo"
          :groupId="groupId"
          lazy
          @updateSccsSuccess="updateSccsSuccess"
        />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import SccsCoopRoleManageInfo from "./components/SccsCoopRoleManageInfo.vue";
import SccsCoopMembershipInfo from "./components/SccsCoopMembershipInfo.vue";
import { getSccsDetail } from "@/api/sccs";
import type { TabsPaneContext } from "element-plus";

const { t } = useI18n();
const route = useRoute();
const drawer = ref<boolean>(false);
const drawerTeamName = ref<string>("");
const activeName = ref("membersInfo");
const sccsBasicInfo = ref<any>({});
const groupId = ref<string>("");
const tabPaneList = ref<any[]>([
  {
    title: "trade_sccs_permissionMag",
    iconfont: "link-rights-manage",
    component: SccsCoopRoleManageInfo,
    name: "membersInfo"
  },
  {
    title: "trade_team_membersManage",
    iconfont: "link-team-members",
    component: SccsCoopMembershipInfo,
    name: "membershipInfo"
  }
]);

const props = defineProps({
  teamName: {
    type: String as PropType<string>,
    default: ""
  }
});

const handleClick = (tab: TabsPaneContext) => {
  //@ts-ignore
  if (tab && tab.name) {
    //@ts-ignore
    activeName.value = tab.name;
  }
};

const open = async (id: string, sccsGroupId: string): Promise<void> => {
  drawer.value = true;
  drawerTeamName.value = props.teamName;
  groupId.value = sccsGroupId;
  const res = await getSccsDetail({ id: id });
  if (res.code === 0) {
    sccsBasicInfo.value = res.data;
  }
};

const updateSccsSuccess = async (id): Promise<void> => {
  const res = await getSccsDetail({ id: id });
  if (res.code === 0) {
    sccsBasicInfo.value = res.data;
  }
};

onMounted(() => {
  const { action } = route.query;
  const targetTab = tabPaneList.value.find(item => item.name === action);
  if (targetTab) {
    drawer.value = true;
    activeName.value = targetTab.name;
  }
});

defineExpose({
  open
});
</script>
<style lang="scss">
.team-manage-drawer-body {
  max-width: 1200px;

  .el-drawer__header {
    height: 42px;
    padding: 0 16px 0 13px;
    margin: 0;
    line-height: 42px;
    border-bottom: 1px solid #ebebeb;

    .el-drawer__close-btn {
      i {
        color: #8c8c8c;
      }
    }
  }

  .el-drawer__body {
    padding: 0;

    .left-tabs-body {
      .el-tabs__header {
        width: 210px;

        .el-tabs__nav-wrap {
          width: 100%;
          padding-top: 12px;

          .el-tabs__nav {
            width: 100%;

            .el-tabs__item {
              .custom-tabs-label {
                display: inline-block;
                width: 100%;
                text-align: left;

                .iconfont {
                  margin-right: 3px;
                  font-size: 14px;
                }

                .custom-tabs-text {
                  font-size: 14px;
                  color: #262626;
                }

                .custom-tabs-number {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  margin-left: 6px;
                  font-size: 12px;
                  line-height: 16px;
                  color: #fff;
                  text-align: center;
                  background: #e62412;
                  border-radius: 50%;
                }
              }

              &:hover {
                .custom-tabs-label {
                  .iconfont {
                    color: #262626;
                  }
                }
              }
            }

            .el-tabs__item.is-active {
              background: #e6f1ff;

              .custom-tabs-label {
                .custom-tabs-text {
                  color: #0070d2;
                }
              }

              &:hover {
                .custom-tabs-label {
                  .iconfont {
                    color: #0070d2;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
