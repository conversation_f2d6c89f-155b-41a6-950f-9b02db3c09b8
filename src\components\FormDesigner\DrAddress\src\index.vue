<template>
  <el-cascader
    ref="AddressCascaderRef"
    v-model="widgetFormData[widgetConfigure._fc_id].areaCodeList"
    style="width: 100%; margin-bottom: 10px"
    clearable
    :options="regionData"
    :placeholder="areaPlaceholderTitle"
    :props="{
      label: 'name',
      value: 'code',
      children: 'children'
    }"
  />
  <el-input
    v-if="widgetConfigure.props.formatType === 1"
    v-model="widgetFormData[widgetConfigure._fc_id].address"
    :rows="3"
    type="textarea"
    :placeholder="addressPlaceholderTitle"
  />
</template>
<script lang="ts" setup>
import { ref, inject, watch, computed } from "vue";
import { ElCascader, ElInput } from "element-plus";
import { cloneDeep, isEqual, storageLocal } from "@pureadmin/utils";
import p from "./p-code.json";
import pc from "./pc-code.json";
import pca from "./pca-code.json";
import pcas from "./pcas-code.json";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const areaPlaceholderTitle = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.areaPlaceholderEn
    : props.widgetConfigure.props?.areaPlaceholder;
});

const addressPlaceholderTitle = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.addressPlaceholderEn
    : props.widgetConfigure.props?.addressPlaceholder;
});

const widgetFormData = ref<any>({});
const AddressCascaderRef = ref<HTMLElement | any>(null);
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const emit = defineEmits(["handleUpdateWidgetData"]);

const regionData = computed(() => {
  if (props.widgetConfigure.props.formatType === 1) {
    return pcas;
  } else if (props.widgetConfigure.props.formatType === 2) {
    return pcas;
  } else if (props.widgetConfigure.props.formatType === 3) {
    return pca;
  } else if (props.widgetConfigure.props.formatType === 4) {
    return pc;
  } else if (props.widgetConfigure.props.formatType === 5) {
    return p;
  } else {
    return pca;
  }
});

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    const trendsFormData = newVal;

    if (
      typeof trendsFormData === "object" &&
      trendsFormData.hasOwnProperty("areaCodeList")
    ) {
      widgetFormData.value = cloneDeep(props.trendsForm);
    } else {
      widgetFormData.value[props.widgetConfigure._fc_id] = {
        areaCodeList: [],
        address: ""
      };
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    const widgetTrendsFormValue =
      props.trendsForm[props.widgetConfigure._fc_id];

    if (
      (!(widgetTrendsFormValue instanceof Object) &&
        newVal.areaCodeList.length > 0 &&
        !!newVal.address) ||
      (widgetTrendsFormValue instanceof Object &&
        !isEqual(newVal, widgetTrendsFormValue))
    ) {
      const presentTextList = AddressCascaderRef.value.presentText.split("/");
      const presentText = `${presentTextList.map(presentText => presentText.trim()).join("")}${newVal.address}`;
      let addressObject = {
        areaCodeList: newVal.areaCodeList,
        address: newVal.address
      };
      const widgetId = props.widgetConfigure._fc_id;
      if (props.widgetRowIndex !== -1) {
        emit("handleUpdateWidgetData", "DrAddress", {
          obj: addressObject,
          label: presentText,
          widgetId: widgetId,
          $rowIndex: props.widgetRowIndex
        });
      } else {
        handleWidgetFormsValue(
          widgetId,
          {
            obj: addressObject,
            label: presentText,
            widgetId: widgetId,
            $rowIndex: props.widgetRowIndex
          },
          addressObject
        );
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
