.linkIncrease-container {
  position: relative;
  display: flex;
  justify-content: flex-start;
  height: 100%;
  padding: 16px;
  overflow-x: auto;
  background: #f5f6fa;

  &.flex-center {
    justify-content: center;
  }

  .linkIncrease-container-left {
    position: relative;
    flex: 1;
    min-width: 382px;
    max-width: 584px;
    max-height: calc(100vh - 73px);
    margin-right: 20px;
    transition: all 0.3s ease;

    .anchor-icon-left {
      position: absolute;
      top: 8px;
      right: -14px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 34px;
      height: 34px;
      padding: 4px;
      text-align: center;
      cursor: pointer;
      background: #fff;
      border: 1px solid rgb(1 6 62 / 9%);
      border-radius: 50%;

      .link-pack-up {
        font-size: 12px;
        color: #8c8c8c;
      }
    }

    &.hideLeft {
      display: none;
    }
  }

  .linkIncrease-container-right {
    position: relative;
    flex: 2;
    min-width: 1028px;
    max-width: 1224px;
    max-height: calc(100vh - 73px);
    transition: all 0.3s ease;

    .anchor-icon-right {
      position: absolute;
      top: 8px;
      left: -43px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 34px;
      height: 34px;
      padding: 4px;
      text-align: center;
      cursor: pointer;
      background: #fff;
      border: 1px solid rgb(1 6 62 / 9%);
      border-radius: 50%;

      .svg-icon-order-schedule,
      .svg-icon-order-schedule-white {
        width: 100%;
        height: 100%;
      }

      &.orange {
        background: #ff721b;
      }
    }
  }

  .linkIncrease-work-module {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 46px;
    margin-bottom: 16px;
    background: linear-gradient(90deg, #ffdfd0 0%, #ffd8c0 72%, #ffeac4);
    border-radius: 8px;
    box-shadow: 0 0 3px 0 rgb(1 6 62 / 11%);

    .svg-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      margin-right: 6px;
      vertical-align: middle;
    }

    .link-coordinator {
      width: 24px;
      height: 24px;
      color: #ff4451;
      text-align: center;
      background: #faa;
      border-radius: 50%;
    }

    .team-task-text {
      margin-left: 8px;
      font-size: 16px;
      line-height: 22px;
      color: #595959;
    }

    .team-task-number {
      margin: 0 18px;
      font-size: 20px;
      font-weight: 600;
      line-height: 30px;
      color: #262626;
      cursor: pointer;
    }

    .team-task-link {
      font-size: 12px;
      line-height: 17px;
      color: #358ff0;
      cursor: pointer;

      .link-arrow-right {
        margin-left: 8px;
        font-size: 10px;
        color: #358ff0;
      }
    }
  }

  .linkIncrease-work-card {
    display: flex;
    flex: 1;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 0 3px 0 rgb(1 6 62 / 9%);

    &.mr20 {
      margin-right: 20px;
    }

    &.flex2 {
      flex: 2;
    }

    ::v-deep(.el-calendar) {
      padding: 0 16px;
      border-radius: 8px;
      transition: height 0.3s ease;

      // 收起
      &.is-retract {
        height: 220px;
        overflow: hidden;

        .el-calendar__body {
          height: 145px;
        }
      }

      // 展开
      &.is-expand {
        max-height: 400px;
      }

      .el-calendar__body {
        padding: 0;
        overflow: hidden;
        background: linear-gradient(180deg, #ebf5ff, #fff);
        border: 0 none;
        border-radius: 20px;
        transition: height 0.3s ease;
      }

      .el-calendar-table td {
        border: none;

        &.is-today {
          .el-calendar-day {
            color: #0070d2;
            background: #ddedff;
            border-radius: 4px;

            .el-calendar-day-text {
              display: inline-flex;
              align-items: center;
              font-size: 14px;
              font-weight: bold;
              line-height: 20px;
              color: #0070d2;
            }
          }
        }
      }

      .el-calendar-table thead th {
        font-size: 12px;
      }

      .el-calendar-table .el-calendar-day {
        width: 45px;
        height: 45px;
        margin: 0 auto;
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        color: #595959;

        .el-calendar-day-default {
          display: flex;
          flex-direction: column;
          align-items: center;

          .el-calendar-day-text {
            display: inline-flex;
            align-items: center;
            font-size: 14px;
            font-weight: bold;
            line-height: 20px;
            color: #262626;
          }
        }

        .el-calendar-day-decs {
          display: inline-flex;
          align-items: center;
          justify-content: center;

          .calendar-day-col-item-point-blue,
          .calendar-day-col-item-point-red {
            display: inline-block;
            margin: 0 2px;
          }
        }
      }

      .el-calendar-table tr:first-child td {
        border-top: none !important;
      }

      .el-calendar-table tr td:first-child {
        border-left: none !important;
      }

      .current {
        text-align: center;
        border: 0 none;

        .el-calendar-day {
          margin: 0 auto;
          font-size: 14px;
          line-height: 27px;
          color: #595959;
        }
      }

      .next {
        text-align: center;
        border: 0 none;

        .el-calendar-day {
          margin: 0 auto;
          font-size: 14px;
          line-height: 27px;
          color: #595959;
        }
      }

      .is-selected {
        display: flex;
        flex-direction: column;
        align-items: center;
        background: transparent !important;

        &.is-today {
          .el-calendar-day {
            color: #0070d2;
            background: #ddedff;
            border-radius: 4px;

            .el-calendar-day-text {
              display: inline-flex;
              align-items: center;
              font-size: 14px;
              font-weight: bold;
              line-height: 20px;
              color: #0070d2;
            }
          }
        }

        .el-calendar-day-text {
          display: inline-flex;
          align-items: center;
          font-size: 14px;
          font-weight: bold;
          line-height: 20px;
          color: #0070d2;
        }

        .el-calendar-day {
          background: #e4f1ff;
          border: 1px solid #358ff0;
          border-radius: 4px;
        }
      }

      .el-calendar__header {
        display: block;
        padding: 12px 0;
        border-bottom: none;

        .calendar-btn-body {
          display: flex;
          align-items: center;
          width: 100%;
          margin-bottom: 7px;
          text-align: right;

          .linkIncrease-work-header-name {
            display: flex;
            flex: 1;
            align-items: center;
            font-size: 16px;
            font-weight: bolder;
            line-height: 23px;
            color: #262626;
            text-align: left;

            .svg-icon-order-schedule {
              width: 22px;
              height: 22px;
              margin-right: 8px;
            }
          }

          .linkIncrease-work-header-oprate {
            display: inline-flex;
            align-items: center;
          }

          .el-button {
            height: 20px;
            background: #fff;
            border: 1px solid #c7c7c7;
            border-radius: 11px;

            &:hover {
              color: #358ff0;
              border: 1px solid #358ff0;
            }

            &:active {
              color: #358ff0;
              border: 1px solid #358ff0;
            }
          }

          .el-input-number {
            border: none;
          }

          .el-input-number__increase {
            background: transparent;
            border-left: 0 none;

            i {
              font-size: 10px;
            }
          }

          .el-input-number__decrease {
            background: transparent;
            border-right: 0 none;

            i {
              font-size: 10px;
            }
          }

          .el-input__wrapper {
            box-shadow: none !important;
          }
        }

        .calendar-year-body {
          display: flex;
          justify-content: space-between;
          width: 100%;

          .calendar-day-col {
            display: flex;
            align-items: center;
            padding: 4px;
            font-size: 14px;
            line-height: 20px;
            color: #595959;
            text-align: center;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
              background: #e4f1ff;
            }

            &.is-active {
              background: #e4f1ff;
            }

            .calendar-day-col-item {
              display: inline-flex;
              align-items: center;

              .calendar-day-col-flex {
                display: inline-flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-around;
                width: 8px;
                height: 12px;
              }
            }
          }
        }
      }

      .calendar-day-col-item-point-blue {
        width: 4px;
        height: 4px;
        background: #358ff0;
        border-radius: 50%;

        &.large {
          width: 6px;
          height: 6px;
        }
      }

      .calendar-day-col-item-point-red {
        width: 4px;
        height: 4px;
        background: #e62412;
        border-radius: 50%;

        &.large {
          width: 6px;
          height: 6px;
        }
      }
    }

    .expand-btn {
      display: flex;
      justify-content: center;
      width: 100%;
      font-size: 12px;
      font-weight: normal;
      color: #358ff0;
      text-align: center;
      cursor: pointer;

      .link-arrow-down,
      .link-arrow-up {
        font-size: 10px;
      }
    }

    .home-current-date-list {
      overflow: hidden;

      &.is-retract {
        max-height: calc(100vh - 327px);
      }

      &.is-expand {
        max-height: calc(100vh - 507px);
      }

      .home-current-date-list-title {
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        color: #262626;
      }

      .home-current-date-list-collapse {
        --el-collapse-border-color: none;
        --el-collapse-header-height: 36px;
        --el-collapse-header-bg-color: #b7c5f6;
        --el-collapse-header-text-color: #fff;
        --el-collapse-header-font-size: 14px;
        --el-collapse-content-bg-color: #f5f5f9;
        --el-collapse-content-font-size: 14px;

        border: none;
        border-radius: 4px;

        ::v-deep(.el-collapse-item__header) {
          border-radius: 4px 4px 0 0;
        }

        ::v-deep(.el-collapse-item__wrap) {
          border-radius: 0 0 4px 4px;

          .el-collapse-item__content {
            padding-bottom: 16px;
          }
        }

        .el-collapse-item-name {
          margin-left: 14px;
          font-size: 14px;
          color: #fff;
        }

        .el-collapse-item {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .el-collapse-item-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 14px;
            margin: 16px 0 0;
            font-size: 16px;
            color: #e62412;

            .link-explain {
              font-size: 12px;
              color: #8c8c8c;
            }

            &.blue {
              color: #358ff0;
            }

            .el-collapse-item-title-badge {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 16px;
              height: 16px;
              margin-left: 8px;
              font-size: 12px;
              line-height: 16px;
              color: #fff;
              background: #e62412;
              border-radius: 50%;

              &.blue {
                background: #358ff0;
              }
            }
          }

          .el-collapse-item-content-item {
            padding: 10px 0;
            padding-bottom: 0;
            cursor: pointer;

            &:hover {
              background: #f0f0f0;
            }
          }

          .el-row {
            padding: 0 14px;
            margin-bottom: 4px;

            .el-col {
              display: flex;
              align-items: center;

              &.align-right {
                justify-content: right;
              }
            }

            &:last-child {
              margin-bottom: 0;
            }

            .el-collapse-item-content-item-title {
              padding: 4px 8px;
              font-size: 14px;
              font-weight: bold;
              color: #595959;
              background: #d9e1f8;
              border-radius: 4px;
            }

            .el-collapse-item-content-item-reply {
              font-size: 14px;
              color: #8c8c8c;
              white-space: nowrap;
            }

            .el-collapse-item-content-item-complete {
              font-size: 14px;
              color: #8c8c8c;
            }

            .el-collapse-item-content-item-msName {
              font-size: 14px;
              color: #8c8c8c;
              white-space: nowrap;
            }

            .el-collapse-item-content-item-replyValue,
            .el-collapse-item-content-item-completeValue {
              font-size: 16px;
              font-weight: 600;
              color: #262626;

              .complete-count {
                color: #67c23a;
              }
            }
          }

          .el-collapse-item-content-pagination {
            display: flex;
            justify-content: center;

            ::v-deep(.el-pagination) {
              --el-pagination-bg-color: none;
              --el-pagination-button-disabled-bg-color: none;
            }
          }
        }

        .el-collapse-item-line {
          height: 2px;
          margin: 0 14px;
          margin-top: 16px;
          background: #eaeaea;
        }
      }
    }
  }
}
