<template>
  <div class="table-statistics-container" style="position: relative">
    <ListTable
      ref="tableInstanceRef"
      :options="option"
      :style="{ height: `50px`, 'z-index': 1000 }"
    />
    <!-- 统计列需要显示tooltip，因为特点的交互模式，如果直接写在table的配置里面会导致无法控制tooltip的显示隐藏，所以模拟一个悬浮的元素来挂载tooltip -->
    <el-tooltip
      v-if="isShowTooltip"
      ref="tooltipRef"
      :content="t('trade_statistics_tip')"
      placement="bottom-start"
      effect="dark"
      :visible="tooltipVisible"
      trigger="hover"
    >
      <div
        class="tooltip-trigger-area"
        :style="{
          position: 'absolute',
          top: '0px',
          left: '0px',
          width: '60px',
          height: '40px',
          backgroundColor: 'transparent',
          cursor: 'pointer',
          zIndex: 1001
        }"
        @mouseenter="handleTooltipMouseenter"
        @mouseleave="handleTooltipMouseleave"
      />
    </el-tooltip>
  </div>
</template>
<script lang="tsx" setup>
import {
  computed,
  h,
  markRaw,
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  watch
} from "vue";
import { VTable } from "@visactor/vue-vtable";
import { ElTooltip } from "element-plus";
import TableStatWidget from "../../components/TableStatWidget";
import { useI18n } from "vue-i18n";

const props = defineProps({
  column: {
    type: Array as PropType<any>,
    default: () => []
  },
  statisticsResultList: {
    type: Array as PropType<any>,
    default: () => []
  },
  selectedRow: {
    type: Array as PropType<any>,
    default: () => []
  },
  presentView: {
    type: Object as PropType<any>,
    default: () => {}
  },
  colStatisticsSearchCondition: {
    type: Object as PropType<any>,
    default: () => {}
  },
  isOperate: {
    type: Boolean as PropType<boolean>,
    default: true
  },
  isShowTooltip: {
    type: Boolean as PropType<boolean>,
    default: true
  },
  customScrollStyle: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();

const tableInstanceRef = ref<HTMLElement | any>(null);
const tooltipRef = ref<any>(null);
const tooltipVisible = ref(false);
const tooltipTimer = ref<NodeJS.Timeout | null>(null);
let fixedNumber = markRaw<any>(0);
const option = markRaw({
  records: [{}],
  columns: [],
  defaultHeaderRowHeight: 36,
  defaultHeaderColWidth: props.isOperate ? undefined : [60, "auto"],
  defaultRowHeight: 40,
  columnWidthComputeMode: "only-body",
  limitMaxAutoWidth: 300,
  showHeader: false,
  rightFrozenColCount: props.isOperate ? 1 : 0,
  hover: {
    highlightMode: "row",
    disableHover: true
  },
  select: {
    highlightMode: "row",
    disableSelect: true
  },
  customConfig: {
    createReactContainer: true
  },
  resize: {
    columnResizeMode: "header"
  },
  tooltip: {
    isShowOverflowTextTooltip: true,
    overflowTextTooltipDisappearDelay: 500
  },
  theme: {
    headerStyle: {
      color: "#797979",
      fontSize: "13",
      fontWeight: "bolder",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    bodyStyle: {
      color: "#262626",
      fontSize: "13",
      textAlign: "center",
      borderColor: "#e8e8e8",
      padding: [10, 4]
    },
    scrollStyle: {
      visible: "always",
      hoverOn: false,
      width: 10,
      scrollRailColor: "#fbfbfb",
      scrollSliderColor: "#d3d3d3",
      ...props.customScrollStyle
    },
    buttonStyle: {
      buttonColor: "transparent",
      buttonBorderColor: "transparent",
      buttonHoverColor: "transparent",
      buttonHoverBorderColor: "transparent"
    }
  }
});

const emit = defineEmits(["handleScrollChange", "handleStatisticalResult"]);

const tableInstance: any = computed(() => {
  return tableInstanceRef.value.vTableInstance;
});

const handleScrollChange = e => {
  tableInstance.value.setScrollLeft(e.scrollLeft);
};

// 处理 tooltip 鼠标进入事件
const handleTooltipMouseenter = () => {
  tooltipVisible.value = true;
};

// 处理 tooltip 鼠标离开事件
const handleTooltipMouseleave = () => {
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value);
    tooltipTimer.value = null;
  }
  tooltipVisible.value = false;
};

// 监听 selectedRow 变化，控制 tooltip 显示
watch(
  () => props.selectedRow,
  (newVal, oldVal) => {
    // 清除之前的定时器
    if (tooltipTimer.value) {
      clearTimeout(tooltipTimer.value);
      tooltipTimer.value = null;
    }

    if (newVal && newVal?.length > 0 && oldVal?.length === 0) {
      // 有勾选记录时，主动展示 tooltip
      tooltipVisible.value = true;

      // 3秒后自动关闭
      tooltipTimer.value = setTimeout(() => {
        tooltipVisible.value = false;
        tooltipTimer.value = null;
      }, 3000);
    } else {
      // 没有勾选记录时，关闭 tooltip
      tooltipVisible.value = false;
    }
  },
  { immediate: true }
);

watch(
  [
    () => props.column,
    () => props.statisticsResultList,
    () => props.selectedRow,
    () => props.colStatisticsSearchCondition
  ],
  () => {
    if (props.column && props.column.length > 0) {
      let tableColumns: any = [];
      tableColumns = props.column.map(columnConfig => {
        let columnWidthCompareValue = {
          orderCreateInfo: 190,
          orderUpdateInfo: 190,
          workOrderCreateInfo: 190,
          workOrderReplyInfo: 240,
          workOrderCollectInfo: 240,
          planTime: 190,
          actualTime: 190
        };
        return {
          field: columnConfig.name,
          title: columnConfig.label,
          width: columnWidthCompareValue.hasOwnProperty(columnConfig.name)
            ? columnWidthCompareValue[columnConfig.name]
            : columnConfig.name.indexOf("replyInfo") > -1
              ? 240
              : columnConfig.msId
                ? 300
                : columnConfig.width,
          hide: !columnConfig.show,
          customLayout: args => {
            const { table, row, col, rect, value } = args;
            const { height, width } = rect ?? table.getCellRect(col, row);

            const fieldName = tableInstance.value.getCellHeaderPaths(
              args.col,
              args.row
            ).colHeaderPaths[0].field;

            let container;
            if (
              !(
                columnConfig.type === "ORDER_WIDGET" &&
                columnConfig.widgetType === "TABLE_FORM"
              ) &&
              !["planTime", "actualTime"].includes(columnConfig.shortName) &&
              !["planTime", "actualTime"].includes(columnConfig.name)
            ) {
              container = new VTable.CustomLayout.Group({
                height,
                width,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                //@ts-ignore
                vue: {
                  element: h(TableStatWidget, {
                    fieldName: fieldName,
                    columnConfig: columnConfig,
                    presentView: props.presentView,
                    selectedRow: props.selectedRow,
                    statisticsResultList: props.statisticsResultList,
                    colStatisticsSearchCondition:
                      props.colStatisticsSearchCondition,
                    onHandleStatisticalResult: data => {
                      emit("handleStatisticalResult", data);
                    }
                  }),
                  container: table.bodyDomContainer
                }
              });
            }

            return {
              rootContainer: container,
              renderDefault: false
            };
          }
        };
      });
      tableColumns.unshift({
        field: "",
        title: "",
        width: 60,
        icon: [
          {
            name: "edit",
            type: "svg",
            width: 15,
            height: 15,
            positionType: VTable.TYPES.IconPosition.inlineFront,
            svg: '<svg t="1747030129351" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9790" width="200" height="200"><path d="M936.228571 987.428571H87.771429c-29.257143 0-51.2-21.942857-51.2-51.2V87.771429c0-29.257143 21.942857-51.2 51.2-51.2s58.514286 21.942857 58.514285 51.2V877.714286h789.942857c29.257143 0 51.2 21.942857 51.2 51.2 0 36.571429-21.942857 58.514286-51.2 58.514285m-87.771428-160.914285h-51.2c-29.257143 0-51.2-21.942857-51.2-51.2V351.085714c0-29.257143 21.942857-51.2 51.2-51.2h51.2c29.257143 0 51.2 21.942857 51.2 51.2v424.228572c0 29.257143-21.942857 51.2-51.2 51.2m-263.314286 0h-51.2c-29.257143 0-51.2-21.942857-51.2-51.2V168.228571c0-29.257143 21.942857-51.2 51.2-51.2H585.142857c29.257143 0 51.2 21.942857 51.2 51.2v607.085715c0 29.257143-21.942857 51.2-51.2 51.2m-256 0h-51.2c-29.257143 0-51.2-21.942857-51.2-51.2V504.685714c0-29.257143 21.942857-51.2 51.2-51.2h51.2c29.257143 0 51.2 21.942857 51.2 51.2v270.628572c0 29.257143-29.257143 51.2-51.2 51.2" fill="#A8ABB2" p-id="9791"></path></svg>'
          }
        ]
      });
      if (props.isOperate) {
        tableColumns.push({
          field: "operate",
          title: "",
          width: 68,
          cellType: "text"
        });
      }

      nextTick(() => {
        tableInstance.value.updateColumns(tableColumns);
        fixedNumber = props.column.filter(column => column.fixed).length + 1;
        tableInstance.value.frozenColCount = fixedNumber;
        tableInstance.value.renderWithRecreateCells();
      });
    }
  },
  {
    deep: true,
    immediate: true
  }
);

onMounted(() => {
  // SCROLL
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.SCROLL, e => {
    emit("handleScrollChange", e);
  });
});

onUnmounted(() => {
  // 清理定时器
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value);
    tooltipTimer.value = null;
  }
});

defineExpose({
  handleScrollChange
});
</script>
