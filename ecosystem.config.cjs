module.exports = {
  apps: [
    {
      name: "formulas-api-server", // 应用名称
      script: 'tsx watch ./server/api-server.js',
      instances: 1, // 实例数量
      autorestart: true, // 自动重启
      watch: false, // 不监听文件变化
      max_memory_restart: "500M", // 内存限制
      env: {
        NODE_ENV: "production",
        PORT: 38090 // 服务端口
      },
      // 构建后部署的钩子
      deploy: {
        production: {
          "post-deploy": "npm install && npm run build"
        }
      }
    }
  ]
};
