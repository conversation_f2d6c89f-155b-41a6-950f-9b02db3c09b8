import { useWebSocket } from "@vueuse/core";
import { watchEffect } from "vue";
import { getToken } from "@/utils/auth";
import { router } from "@/router";
import { emitter } from "@/utils/mitt";
import { useMessageCenterStoreHook } from "@/store/modules/messageCenter";

class WebsocketProxy {
  public server;
  constructor() {
    this.server = {};
  }

  connectWebsocket() {
    if (!(getToken() && getToken().accessToken)) {
      return;
    }

    const server: string =
      (import.meta.env.VITE_BASE_URL + "infra/ws").replace("http", "ws") +
      "?token=" +
      getToken().accessToken; // WebSocket 服务地址

    const WebSocketServer = useWebSocket(server, {
      autoReconnect: true,
      heartbeat: true,
      onConnected() {
        console.info("%cWebsocket 连接成功", "color: #67C23A;");
      },
      onDisconnected() {
        console.info("%cWebsocket 断开连接", "color: #F56C6C;");
      }
    });
    const { data } = WebSocketServer;
    this.server = WebSocketServer;

    watchEffect(() => {
      const response = data.value;
      if (!response || response === "pong") {
        return;
      }
      const jsonMessage = JSON.parse(data.value);
      const type = jsonMessage.notifyType;
      // 团队成员移除后没有团队了
      if (type === "teamMemberRemovedNotHaveTeam") {
        router.push({ path: "/createTeam" });
        return;
      }
      // 团队成员移除后还有团队
      if (type === "teamMemberRemovedHaveTeam") {
        router.push({ path: "/selectTeam" });
        return;
      }
      if (type === "NOTIFY") {
        const messageCenterStore = useMessageCenterStoreHook();

        messageCenterStore.addTooltipMessage(jsonMessage);
        messageCenterStore.fetchMessageCenterData();

        return;
      }
      // 订单待刷新，订单信息已被其余用户更新过
      if (type === "ORDER_PENDING_REFRESH") {
        emitter.emit(
          "handleUpdateOrderInfo",
          JSON.parse(jsonMessage.jsonObject)
        );
        return;
      }
    });

    return {
      WebSocketServer
    };
  }

  send(type: string, data: any) {
    const sendData = {
      type: type,
      content: JSON.stringify(data)
    };
    if (Object.keys(this.server).length > 0) {
      this.server.send(JSON.stringify(sendData));
    } else {
      const server = this.connectWebsocket();
      server.send(JSON.stringify(sendData));
    }
  }
}

export const websocket = new WebsocketProxy();
