export type Result = {
  data: Array<any> | any;
  code: number;
  msg: string;
  node: string;
};

export interface LoginFormState {
  username: string | any;
  password: string;
  userOs: string;
  userAgent: string;
  userDevice: string;
}

export interface ResetPwdByEmailProp {
  email: string;
  password: string;
}

export interface MailLoginProp {
  username: string;
  mailCode: string;
  userOs: string;
  userAgent: string;
  userDevice: string;
}

export interface VerifyCodeProp {
  email: string;
  mailCodeType?: string | null;
}

export enum mailCodeTypeEnum {
  TRADE_USER_LOGIN,
  TRADE_USER_REGISTER,
  TRADE_FORGET_PASSWORD
}

export interface VerifyCodeEmailProp {
  email: string;
  mailCode: string;
  mailCodeType?: mailCodeTypeEnum | null;
}

export interface RegisterProp {
  email: string;
  username: string;
  sex?: number | null;
  password: string;
}

export interface RefreshTokenResult {
  code: number;
  data: {
    userId: string;
    accessToken: string;
    refreshToken: string;
    expiresTime: number;
  };
}
