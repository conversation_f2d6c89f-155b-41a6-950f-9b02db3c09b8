<template>
  <LkDialog
    ref="LkDialogRef"
    :title="t('trade_batch_modify_order_status')"
    @confirm="handlemodifyOrderStatusConfirm"
    @close="handleClosemodifyOrderStatusDialog"
  >
    <template #default>
      <div
        class="modifyOrderStatus-title"
        v-html="batchModifyMsStatusSelectedTip"
      />
      <el-form
        ref="modifyOrderRef"
        :model="modifyOrder"
        :rules="modifyMilestoneRules"
        label-position="top"
      >
        <el-form-item :label="t('trade_operation_type')" prop="operationType">
          <el-select
            v-model="modifyOrder.operationType"
            filterable
            :placeholder="t('trade_select_operation_type_placeholder')"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in operationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div
        v-loading="modifyOrderStatusLoading"
        class="modifyOrderStatus-executable-situation"
      >
        <div
          v-if="Object.keys(updateOrderStatusOrder).length > 0"
          class="have-update-order-status-order-info"
        >
          <div class="order-info-title">
            {{ t("trade_only_execute_modifiable_orders") }}
          </div>
          <el-scrollbar height="104px">
            <div class="order-info-list">
              <div class="order-info-item">
                <div class="order-info-item-title">
                  {{
                    `${t("trade_modifiable_order")}(${updateOrderStatusOrder.operableOrder.length})：`
                  }}
                </div>
                <div class="order-info-item-orderMarks">
                  <span>{{
                    updateOrderStatusOrder.operableOrder.length === 0
                      ? t("trade_nothing")
                      : operableOrderStr
                  }}</span>
                </div>
              </div>
              <div class="order-info-item">
                <div class="order-info-item-title">
                  {{
                    `${t("trade_cannot_modify_order")}(${updateOrderStatusOrder.notOperableOrder.length})：`
                  }}
                </div>
                <div class="order-info-item-orderMarks">
                  <span>{{
                    updateOrderStatusOrder.notOperableOrder.length === 0
                      ? t("trade_nothing")
                      : notOperableOrderStr
                  }}</span>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <span v-else class="modifyOrderStatus-placeholder-text">{{
          t("trade_select_the_type_of_operation_first_placeholder")
        }}</span>
      </div>
    </template>
  </LkDialog>
  <OrderCancelDialog
    ref="OrderCancelDialogRef"
    @handleCancelOrder="handleCreateWorkOrderSuccess"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, watch, markRaw, PropType, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox, type FormRules } from "element-plus";
import LkDialog from "@/components/lkDialog/index";
import OrderCancelDialog from "@/views/orderDetail/components/OrderCancelDialog.vue";

import { WarningFilled } from "@element-plus/icons-vue";

import {
  getHaveUpdateOrderStatusOrderInfo,
  batchUpdateOrderStatus
} from "@/api/order";

const props = defineProps({
  currentSccsId: {
    type: String as PropType<any>,
    default: ""
  },
  selectedNumber: {
    type: Number as PropType<any>,
    default: 0
  },
  tableSelectedList: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const { t } = useI18n();

// 批量修改里程碑状态
const LkDialogRef = ref<HTMLElement | any>(null);
const modifyOrderRef = ref<HTMLElement | any>(null);
const OrderCancelDialogRef = ref<HTMLElement | any>(null);
const modifyOrder = ref<any>({
  operationType: "" // 操作类型 1.RESTART 2.CANCEL 3.COMPLETE
});
const modifyMilestoneRules = reactive<FormRules<any>>({
  operationType: [
    {
      required: true,
      message: t("trade_select_operation_type_placeholder"),
      trigger: "change"
    }
  ]
});
const operationTypeOptions = ref<any>([
  {
    label: t("trade_order_restart"),
    value: "RESTART"
  },
  {
    label: t("trade_order_completion"),
    value: "COMPLETE"
  },
  {
    label: t("trade_order_cancel"),
    value: "CANCEL"
  }
]);
const updateOrderStatusOrder = ref<any>({});
const notOperableOrderStr = ref<string>("");
const operableOrderStr = ref<string>("");
// 可操作的订单id列表
const operationOrderIdList = ref<any>([]);
const modifyOrderStatusLoading = ref<boolean>(false);
const batchModifyMsStatusSelectedTip = computed(() => {
  return useI18n().t("trade_batchModifyMsStatusSelectedTip", {
    params0: `<span>${props.selectedNumber}</span>`
  });
});

// 批量修改里程碑状态 确认提交
const handlemodifyOrderStatusConfirm = () => {
  // 校验表单
  modifyOrderRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return;
    }
    // 校验通过，发送请求
    // 判断当前操作类型给出不同提示
    const operationTypeMap = {
      RESTART: {
        title: t("trade_order_restart"),
        message: t("trade_order_restartTip")
      },
      CANCEL: {
        title: t("trade_order_cancel")
      },
      COMPLETE: {
        title: t("trade_order_completion"),
        message: t("trade_order_completionTip")
      }
    };
    if (operationOrderIdList.value && operationOrderIdList.value.length === 0) {
      ElMessage({
        message: t("trade_no_operated_order_tip"),
        type: "warning"
      });
    } else if (
      ["RESTART", "COMPLETE"].includes(modifyOrder.value.operationType) &&
      operationOrderIdList.value.length > 0
    ) {
      ElMessageBox.confirm(
        operationTypeMap[modifyOrder.value.operationType].message,
        operationTypeMap[modifyOrder.value.operationType].title,
        {
          confirmButtonText: t("trade_common_confirm"),
          cancelButtonText: t("trade_common_cancel"),
          confirmButtonClass: "confrim-message-btn-warn-class",
          customClass: "order_confirm_message_box",
          type: "warning",
          icon: markRaw(WarningFilled),
          center: true
        }
      )
        .then(async () => {
          const { code } = await batchUpdateOrderStatus({
            sccsId: props.currentSccsId,
            orderIdList: operationOrderIdList.value,
            operationType: modifyOrder.value.operationType,
            cancelReason: "", // 取消原因
            cancelRemark: "" // 取消备注
          });
          operateAfter(code);
        })
        .catch(e => {
          console.log(e);
        });
    } else if (
      modifyOrder.value.operationType === "CANCEL" &&
      operationOrderIdList.value.length > 0
    ) {
      // 取消订单
      // ElMessageBox.confirm(
      //   t("trade_order_cancelOrderTip"),
      //   t("trade_order_cancel"),
      //   {
      //     confirmButtonText: t("trade_common_confirm"),
      //     cancelButtonText: t("trade_common_cancel"),
      //     confirmButtonClass: "confrim-message-btn-warn-class",
      //     customClass: "order_confirm_message_box",
      //     type: "warning",
      //     icon: markRaw(WarningFilled),
      //     center: true
      //   }
      // )
      //   .then(() => {
      OrderCancelDialogRef.value.open(true, operationOrderIdList.value);
      // })
      // .catch(() => {
      //   ElMessage({
      //     message: t("trade_common_cancel"),
      //     type: "info"
      //   });
      // });
    }
  });
};
const operateAfter = code => {
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    modifyOrderRef.value.resetFields();
    modifyOrderRef.value.clearValidate();
    // 更新表格数据
    handleReloadTableData();
    LkDialogRef.value.close();
  }
};
// 批量修改里程碑状态 关闭弹窗
const handleClosemodifyOrderStatusDialog = () => {
  modifyOrderRef.value.resetFields();
  modifyOrderRef.value.clearValidate();
  LkDialogRef.value.close();
};
// 初始化里程碑列表
const open = async () => {
  LkDialogRef.value.open();
};

const handleCreateWorkOrderSuccess = (): void => {
  handleReloadTableData();
  modifyOrderRef.value.resetFields();
  modifyOrderRef.value.clearValidate();
  LkDialogRef.value.close();
};

defineExpose({
  open
});

const emit = defineEmits(["handleReloadTableData"]);

const handleReloadTableData = () => {
  emit("handleReloadTableData");
};

watch(
  () => modifyOrder.value,
  () => {
    if (modifyOrder.value.operationType) {
      let params = {
        sccsId: props.currentSccsId,
        operationType: modifyOrder.value.operationType,
        orderIdList: props.tableSelectedList.map(item => item.orderId)
      };
      modifyOrderStatusLoading.value = true;
      getHaveUpdateOrderStatusOrderInfo(params).then(res => {
        updateOrderStatusOrder.value = res.data;
        let notArray = [] as any;
        let operableArray = [] as any;
        operationOrderIdList.value = [];
        notOperableOrderStr.value = "";
        operableOrderStr.value = "";
        if (res.data.notOperableOrder.length > 0) {
          updateOrderStatusOrder.value.notOperableOrder.forEach(item => {
            notArray.push(item.orderMark);
          });
          notOperableOrderStr.value = notArray.join("、");
        }
        if (res.data.operableOrder.length > 0) {
          updateOrderStatusOrder.value.operableOrder.forEach(item => {
            operableArray.push(item.orderMark);
            operationOrderIdList.value.push(item.id);
          });
          operableOrderStr.value = operableArray.join("、");
        }
        modifyOrderStatusLoading.value = false;
      });
    } else {
      updateOrderStatusOrder.value = {};
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss">
@use "../index.scss";
</style>
