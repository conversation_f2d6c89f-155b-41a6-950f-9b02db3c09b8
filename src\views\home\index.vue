<template>
  <div
    class="linkIncrease-container"
    :class="[screenWidth > 1440 ? 'flex-center' : '']"
  >
    <div class="linkIncrease-container-left" :class="[hide ? 'hideLeft' : '']">
      <orderSchedule />
      <div class="anchor-icon-left" @click="hideLeft">
        <i class="iconfont link-pack-up" />
      </div>
    </div>

    <div class="linkIncrease-container-right">
      <TaskCenterCard ref="TaskCenterCardRef" />
      <div
        class="linkIncrease-work-card"
        :style="{
          height: hideTeamCard ? 'calc(100% - 222px)' : 'calc(100% - 282px)'
        }"
      >
        <SccsPanelManagement :hideTeamCard="hideTeamCard" />
      </div>
      <div
        v-if="hide"
        class="anchor-icon-right"
        :class="[showWhite ? 'orange' : '']"
        @click="hideLeft"
        @mouseenter="changeIcon"
        @mouseleave="changeIcon"
      >
        <svg v-show="!showWhite" class="svg-icon svg-icon-order-schedule">
          <use xlink:href="#link-order-schedule" />
        </svg>
        <svg v-show="showWhite" class="svg-icon svg-icon-order-schedule-white">
          <use xlink:href="#link-schedule-white" />
        </svg>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import SccsPanelManagement from "@/views/sccs/index.vue";
import TaskCenterCard from "@/views/home/<USER>/TaskCenterCard.vue";
import OrderSchedule from "@/views/home/<USER>/OrderSchedule.vue";
import { getTeamHome } from "@/api/common";
import { switchTeam } from "@/utils/auth";
import { teamCooperationCache } from "@/utils/cooperatioTeam";

const route = useRoute();
const currentTeam = ref<any>();
const hide = ref(false);
const showWhite = ref(false);
const TaskCenterCardRef = ref<any>();
const hideTeamCard = computed(() => {
  if (TaskCenterCardRef.value) {
    return TaskCenterCardRef.value?.myTaskInfo.allTeamTaskCount <= 0;
  } else {
    return false;
  }
});

const hideLeft = () => {
  hide.value = !hide.value;
  showWhite.value = false;
};

const changeIcon = () => {
  showWhite.value = !showWhite.value;
};
const screenWidth = ref(window.innerWidth);
const updateScreenSize = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(async () => {
  window.addEventListener("resize", updateScreenSize);
});

onMounted(async () => {
  const { teamId } = route.query;
  if (teamId) {
    switchTeam(teamId as string, true, "/");
  } else {
    currentTeam.value = await teamCooperationCache.currentlyUsedTeam();
    getTeamHome({ teamId: currentTeam.value.id });
  }
});
</script>
<style lang="scss" scoped>
@use "./components/index.scss";
</style>
