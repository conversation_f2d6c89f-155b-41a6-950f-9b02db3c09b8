<template>
  <div class="create-team-container">
    <div class="create-team-header">
      <LKSwitchLanuage />
      <div class="create-team-logout">
        <FontIcon icon="link-exit" />
        <span class="overall-type-button" @click="handleLogout">
          {{ t("trade_common_logout") }}
        </span>
      </div>
    </div>
    <div class="create-team-body">
      <div class="create-team-body-header" />
      <div class="create-team-body-main">
        <LkAvater />
        <div class="create-team-body-title">
          {{ t("trade_team_welcomeToJoin") }} Linkincrease
        </div>
        <div class="create-team-body-tip">
          {{ t("trade_common_startBusiness") }}
        </div>
        <div class="create-team-body-image">
          <el-image :src="joinTeamPic" fit="cover" style="width: 285px" />
        </div>

        <el-button
          type="primary"
          color="#0070D2"
          class="create-team-button"
          @click="addTeam"
          ><FontIcon icon="link-add" />{{
            t("trade_team_createTeam")
          }}</el-button
        >
        <div class="create-team-body-join-tip">
          {{ t("trade_welcome_tipUserMessage") }}
        </div>
        <div class="create-team-body-join-email">
          {{ joinTeamEmail }}
          <span @click="handleCopy(joinTeamEmail)">
            <i class="iconfont link-copy" />
          </span>
        </div>
        <div class="create-team-body-help-body">
          {{ t("trade_team_orClick") }}
          <el-link
            type="primary"
            :underline="false"
            href="https://help.linkincrease.com/zh/"
            target="_blank"
            color="#0070D2"
            >{{ t("trade_team_gotoHelpCenter") }}</el-link
          >
          {{ t("trade_team_findOutMore") }}
        </div>
        <div class="create-team-body-help-tip">
          {{ t("trade_team_teamProblemTip") }}
          <span><EMAIL></span>
        </div>
      </div>
      <div class="create-team-body-footer" />
    </div>
    <CreateTeamDialog
      ref="createTeamDialogRef"
      @createSuccess="createSuccess"
    />
    <CreateTeamGuideDialog
      ref="createTeamGuideDialogRef"
      @switchCardMode="switchCardMode"
      @inviteSuccess="inviteSuccess"
    />
    <InviteMembersDialog
      ref="inviteMembersDialogRef"
      @createSuccess="handleInviteMembersDialog"
    />
    <InviteTeamDialog
      ref="inviteTeamDialogRef"
      @inviteSuccess="inviteTeamSuccess"
    />
  </div>
</template>
<script lang="ts" setup>
import { markRaw, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import joinTeamPic from "@/assets/images/team/create-item.png";
import LKSwitchLanuage from "@/components/lKSwitchLanguage/index";
import LkAvater from "@/components/lkAvatar/index";
import CreateTeamDialog from "./component/createTeamDialog.vue";
import CreateTeamGuideDialog from "./component/createTeamGuideDialog.vue";
import InviteMembersDialog from "./component/inviteMembersDialog.vue";
import InviteTeamDialog from "./component/inviteTeamDialog.vue";
import { useNav } from "@/layout/hooks/useNav";
import { storageLocal } from "@pureadmin/utils";
import { useClipboard } from "@vueuse/core";
import { ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { switchTeam } from "@/utils/auth";

const { t } = useI18n();
const joinTeamEmail = ref<string>();
const createTeamDialogRef = ref<any>(null);
const createTeamGuideDialogRef = ref<any>(null);
const inviteMembersDialogRef = ref<any>(null);
const inviteTeamDialogRef = ref<any>(null);
const currentTeamId = ref<string>("");
const { logout } = useNav();
const { copy } = useClipboard();
const router = useRouter();

const addTeam = (): void => {
  createTeamDialogRef.value.open();
};

const handleLogout = () => {
  ElMessageBox.confirm(t("trade_login_exitSystem"), t("trade_logou_tip"), {
    confirmButtonText: t("trade_common_confirm"),
    cancelButtonText: t("trade_common_cancel"),
    confirmButtonClass: "confrim-message-btn-warn-class",
    customClass: "order_confirm_message_box",
    type: "warning",
    icon: markRaw(WarningFilled),
    center: true
  })
    .then(() => {
      logout();
    })
    .catch(() => {});
};
const handleCopy = (code: string): void => {
  copy(code);
  ElMessage({
    message: t("trade_common_copySuccess"),
    type: "success"
  });
};

const createSuccess = (data: any): void => {
  currentTeamId.value = data;
  createTeamGuideDialogRef.value.open(data);
};

const switchCardMode = (mode: string): void => {
  mode === "person"
    ? inviteMembersDialogRef.value.open(currentTeamId.value)
    : inviteTeamDialogRef.value.open(currentTeamId.value);
};

const inviteSuccess = () => {
  switchTeam(currentTeamId.value, false);
  router.push("/");
};

const inviteTeamSuccess = async (id: string) => {
  await switchTeam(id, false);
  router.push("/");
};

const handleInviteMembersDialog = (id: string) => {
  switchTeam(id, true);
};

onMounted(() => {
  const userInfo = storageLocal().getItem("user-info");
  //@ts-ignore
  joinTeamEmail.value = userInfo.email;
});
</script>
<style lang="scss" scoped>
.create-team-container {
  height: 100%;
  overflow: hidden;

  .create-team-header {
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    height: 250px;
    padding: 24px 33px;
    background: url("@/assets/images/team/welcomeBg.png") no-repeat top center;
    background-size: 100% 100%;

    .linkIncrease-login-language {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-end;

      ::v-deep(.iconfont) {
        color: #fff !important;
      }

      ::v-deep(.el-select) {
        .el-select__wrapper {
          .el-select__selected-item {
            color: #fff;
          }

          .el-select__suffix {
            i {
              color: #fff;
            }
          }
        }
      }
    }

    .create-team-logout {
      display: inline-block;
      width: 78px;
      height: 32px;
      margin-left: 35px;
      line-height: 32px;
      color: #fff;

      ::v-deep(.iconfont) {
        font-size: 14px;
      }

      .overall-type-button {
        padding: 4px 0;
        margin-left: 6px;
        font-size: 14px;
        color: #fff;
        cursor: pointer;
      }
    }
  }

  .create-team-body {
    width: calc(900 / 1440 * 100%);
    height: calc(100% - 100px);
    margin: 0 auto;
    margin-top: -180px;
    text-align: center;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 0 8px 0 rgb(0 0 0 / 9%);

    .create-team-body-header {
      height: 16px;
      background: #ffd469;
      border-radius: 12px 12px 0 0;
    }

    .create-team-body-main {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(100% - 114px);

      ::v-deep(.lk-avater-container) {
        position: relative;
        display: inline-block;
        margin-top: 80px;
        margin-bottom: 20px;

        .el-avatar--circle {
          font-size: 16px !important;
        }
      }

      .create-team-body-title {
        font-size: 30px;
        font-weight: bolder;
        line-height: 42px;
        color: #262626;
        cursor: pointer;
      }
    }

    .create-team-body-tip {
      margin-top: 4px;
      font-size: 14px;
      line-height: 20px;
      color: #595959;
      cursor: pointer;
    }

    .create-team-body-image {
      margin: 30px auto;
      cursor: pointer;
    }

    .create-team-button {
      width: 320px;
      height: 36px;
      margin-bottom: 18px;
      cursor: pointer;

      .iconfont {
        margin-right: 8px;
        font-size: 12px;
      }
    }

    .create-team-body-join-tip {
      padding: 0 50px;
      font-size: 14px;
      line-height: 22px;
      color: #8c8c8c;
      cursor: pointer;
    }

    .create-team-body-join-email {
      margin-top: 3px;
      font-size: 14px;
      font-weight: bolder;
      line-height: 16px;
      color: #262626;

      .iconfont {
        font-size: 12px;
        color: #8c8c8c;
        cursor: pointer;
      }
    }

    .create-team-body-help-body {
      margin-top: 6px;
      margin-bottom: 16px;
      font-size: 14px;
      line-height: 20px;
      color: #595959;
      cursor: pointer;

      ::v-deep(.el-link) {
        vertical-align: baseline;

        .el-link__inner {
          color: #0070d2;
        }
      }
    }

    .create-team-body-help-tip {
      font-size: 14px;
      line-height: 20px;
      color: #8c8c8c;
      cursor: pointer;

      span {
        margin-left: 8px;
        color: #262626;
      }
    }
  }

  .create-team-body-footer {
    height: 98px;
    background: url("@/assets/images/team/welcome_ft.png") no-repeat top center;
    background-size: 100% 100%;
  }
}
</style>
