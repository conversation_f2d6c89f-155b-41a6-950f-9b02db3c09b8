<template>
  <div
    v-if="
      !['DrTableForm', 'DrDivider', 'DrRelateCard'].includes(widgetConfig.type)
    "
  >
    <div
      v-if="
        widgetData !== undefined && widgetData !== null && widgetData !== ''
      "
    >
      <div
        v-if="
          [
            'DrInput',
            'DrInputNumber',
            'DrA<PERSON>ress',
            'DrDatePicker',
            'DrPercentage',
            'DrLocation',
            'DrFormulas'
          ].includes(widgetConfig.type)
        "
        class="widget-config-text"
      >
        <span
          v-if="
            widgetConfig.type === 'DrInputNumber' &&
            widgetConfig.props &&
            widgetConfig.props.unitPosition === 'before'
          "
          class="widget-config-span-unit"
        >
          {{ widgetConfigureUnit(widgetConfig) }}
        </span>
        <span v-if="isNumber(widgetData)" class="widget-config-span-text">
          {{
            widgetConfig.props.useThousandSeparator
              ? formatThousandNumber(widgetData)
              : formatNumberValueOf(widgetData)
          }}
        </span>
        <span
          v-else-if="
            widgetConfig.type === 'DrDatePicker' &&
            widgetConfig.props?.defaultValueConfig?.type === 'formula'
          "
          class="widget-config-span-text"
        >
          {{ dayjs(widgetData).format("YYYY-MM-DD") }}
        </span>
        <span v-else class="widget-config-span-text">
          {{ widgetData }}
        </span>
        <span
          v-if="
            !widgetInTable &&
            widgetConfig.type === 'DrInputNumber' &&
            widgetConfig.props &&
            widgetConfig.props.unitPosition === 'after'
          "
          class="widget-config-span-unit"
        >
          {{ widgetConfigureUnit(widgetConfig) }}
        </span>
        <span
          v-if="widgetConfig.type === 'DrPercentage'"
          class="widget-config-span-unit"
        >
          %
        </span>
        <!-- </ReText> -->
      </div>
      <div v-else-if="widgetConfig.type === 'DrTextarea'">
        <el-input
          class="readonly-textarea"
          :model-value="widgetData"
          autosize
          type="textarea"
          resize="none"
          readonly
        />
      </div>
      <div v-else-if="widgetConfig.type === 'DrRate'">
        <el-rate
          :model-value="Number(widgetData)"
          disabled
          :max="Number(widgetData)"
        />
      </div>
      <div v-else-if="widgetConfig.type === 'DrExchangeRates'">
        <lkExchangeRate :widgetConfig="widgetConfig" :widgetData="widgetData" />
      </div>
      <div v-else-if="widgetConfig.type === 'DrImagesUpload'">
        <el-image
          v-for="(item, index) in widgetData"
          :key="item"
          :src="item.url"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="getWidgetImageList(widgetData)"
          :initial-index="index"
          style="width: 116px; height: 116px; margin-right: 5px"
          fit="contain"
        />
      </div>
      <div
        v-else-if="widgetConfig.type === 'DrEditor'"
        style="border: 1px solid #e6e6e6; border-radius: 4px"
      >
        <Editor
          v-model="valueHtml"
          :defaultConfig="{ readOnly: true }"
          @onCreated="handleCreated"
        />
      </div>
      <div v-else-if="widgetConfig.type === 'DrSCCSMemberSelect'">
        <LkAvatarGroupNext
          :size="30"
          :avatarListGroup="getMemberOptions(widgetDataRow)"
          maxAvatar="4"
        />
      </div>
      <div v-else-if="widgetConfig.type === 'DrSCCSGroupMemberSelect'">
        <LkAvatarGroupNext
          class="colorOrange"
          :size="30"
          :avatarListGroup="getTeamOptions(widgetDataRow)"
          maxAvatar="4"
        />
      </div>
      <div v-else-if="widgetConfig.type === 'DrRadio'">
        <ReText>
          <template #default>
            <div v-html="widgetData" />
          </template>
          <template #content>
            {{ widgetData?.replace(/<[^>]*>?/g, "") }}
          </template>
        </ReText>
      </div>
      <div v-else-if="widgetConfig.type === 'DrSignature'">
        <el-image
          :src="widgetData"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="[widgetData]"
          :initial-index="0"
          style="width: 116px; margin-right: 5px"
          fit="contain"
        />
      </div>
      <div v-else-if="widgetConfig.type === 'DrFilesUpload'">
        <OrderFilePreview
          v-for="item in widgetData"
          :key="item"
          :filePreview="item"
          :fileList="widgetData"
        />
      </div>
      <div v-else-if="widgetConfig.type === 'DrCheckbox'">
        <div
          v-for="item in widgetData"
          :key="item"
          class="radio-span"
          :style="{
            background: getCheckboxOptions(item)?.color
          }"
        >
          <ReText>
            {{ getCheckboxOptions(item)?.label }}
          </ReText>
        </div>
      </div>
    </div>
    <div v-else>-</div>
  </div>
  <div
    v-else-if="widgetConfig.type === 'DrTableForm'"
    class="dr-table-form-body"
    :class="{
      'dr-table-not-data-form-body': !(
        widgetDataRow.childrenList && widgetDataRow.childrenList.length > 0
      )
    }"
  >
    <lkSubTable
      :widgetConfig="widgetConfig"
      :widgetData="widgetTableData.childrenList"
      :widgetDataRow="widgetDataRow"
    />
  </div>
  <div v-else-if="widgetConfig.type === 'DrDivider'">
    <el-divider :content-position="widgetConfig.props.contentPosition">
      <span v-if="widgetConfig.props.title">
        {{ widgetConfigureTitle(widgetConfig) }}
      </span>
    </el-divider>
  </div>
  <div
    v-else-if="widgetConfig.type === 'DrRelateCard'"
    class="dr-relateCard-bottom-container"
  >
    <div
      v-if="widgetTableData && widgetTableData.obj.length > 0"
      class="dr-relateCard-container"
    >
      <el-row :gutter="20">
        <el-col
          v-for="widgetChild in widgetConfig.props.relatedValue.rules"
          :key="widgetChild['_fc_id']"
          :span="widgetConfig.props.colSpan"
          class="dr-relateCard-item"
        >
          <OrderDetailDescWidget
            :widgetForm="[widgetChild]"
            :widgetData="widgetTableData?.obj"
          />
        </el-col>
      </el-row>
    </div>
    <div v-else>-</div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import { Editor } from "@wangeditor/editor-for-vue";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext/index";
import OrderFilePreview from "@/views/orderManage/components/OrderFilePreview.vue";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import lkExchangeRate from "./lkExchangeRate.vue";
import lkSubTable from "./lkSubTable.vue";
import { ReText } from "@/components/ReText";
import { isNumber, storageLocal } from "@pureadmin/utils";
import { formatThousandNumber, formatNumberValueOf } from "@/utils/common";

const props = defineProps({
  widgetConfig: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetDataRow: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetTableData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetInTable: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  visibleFormExpression: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();
const valueHtml = ref<string>("");
const translationLang = storageLocal().getItem("translationLang");

const widgetConfigureTitle = computed(() => {
  return widgetConfig => {
    return translationLang === "en"
      ? widgetConfig.props?.titleEn || widgetConfig.props?.title
      : widgetConfig.props?.title;
  };
});

const widgetConfigureUnit = computed(() => {
  return widgetConfig => {
    return translationLang === "en"
      ? widgetConfig.props?.unitEn || widgetConfig.props?.unit
      : widgetConfig.props?.unit;
  };
});

const getTableColumn = computed(() => {
  return props.widgetConfig.children.slice(1).map(column => {
    return column.children[0];
  });
});

const getOptions = labelName => {
  const widgetItem = props.widgetConfig.props.options.find(
    option => option.label === labelName
  );
  return widgetItem ? widgetItem : "#EFEFEF";
};

const getCheckboxOptions = field => {
  const widgetItem = props.widgetConfig.props.options.find(
    option => option.value === field
  );
  return widgetItem ? widgetItem : null;
};

const handleCreated = () => {
  valueHtml.value = props.widgetData;
};

const getWidgetImageList = widgetData => {
  let widgetDataList = [];
  widgetData.forEach(widgetItem => widgetDataList.push(widgetItem.url));
  return widgetDataList;
};

const getMemberOptions = widgetShowObj => {
  let memberList = [];
  if (
    widgetShowObj &&
    widgetShowObj.showObj &&
    widgetShowObj.showObj instanceof Array
  ) {
    memberList = widgetShowObj.showObj.map(member => {
      return {
        activate: member.activate,
        user: true,
        coop: false,
        edited: false,
        userAvatar: member.avatar,
        userName: member.name,
        email: member.email
      };
    });
  }
  return memberList;
};

const getTeamOptions = widgetShowObj => {
  let memberList = [];
  if (
    widgetShowObj &&
    widgetShowObj.showObj &&
    widgetShowObj.showObj instanceof Array
  ) {
    widgetShowObj.showObj.forEach(member => {
      memberList.push({
        user: false,
        coop: false,
        edited: false,
        activate: true,
        teamAvatar: member.avatar,
        teamName: member.name,
        shortName: member.shortName
      });
    });
  }
  return memberList;
};

watch(
  () => props.widgetData,
  () => {
    if (props.widgetConfig.type === "DrEditor") {
      valueHtml.value = props.widgetData;
    }
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped>
::v-deep(.table-header-row-class) {
  .el-table__cell {
    color: #797979;
    background: #f7f7f7;
  }
}

::v-deep(.readonly-textarea) {
  .el-textarea__inner {
    padding: 0;
    font-size: 14px;
    font-weight: bolder;
    color: #262626;
    box-shadow: none;
  }
}

/* 解决方案1 */
::v-deep(.w-e-text-container) {
  word-break: break-word;
  word-wrap: break-word;
}

::v-deep(.select-v2-text) {
  display: inline-block;
  max-width: 100%;
  padding: 5px;
  margin-right: 10px;
  margin-bottom: 5px;
  overflow: hidden;
  font-size: 12px;
  line-height: 12px;
  color: #262626;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #f2f2f2;
  border-radius: 3px;
}

.radio-span {
  display: inline-block;
  max-width: 100%;
  padding: 3px 5px;
  margin-right: 10px;
  margin-bottom: 5px;
  background: #f2f2f2;
  border-radius: 3px;

  ::v-deep(.el-text) {
    font-size: 12px;
    color: #262626;
  }
}

.dr-table-not-data-form-body {
  border: 1px solid #e8e8e8;
  border-top: 0 none;
}

.widget-config-text {
  display: inline-flex;
  align-items: center;
  max-width: 100%;

  .widget-config-span-text {
    display: inline-flex;
    // overflow: hidden;
    font-size: 14px;
    // font-weight: medium;
    line-height: 16px;
    color: #262626;
    text-align: left;
    // text-overflow: ellipsis;
    // white-space: nowrap;
  }

  .widget-config-span-unit {
    display: inline-flex;
    padding-left: 2px;
    font-size: 12px;
    line-height: 12px;
    color: #797979;
    text-align: left;

    &:first-child {
      margin-right: 4px;
    }

    &:last-child {
      margin-left: 4px;
    }
  }
}

.dr-relateCard-bottom-container {
  .dr-relateCard-container {
    padding: 22px 14px;
    background: #f6f8fa;
    border-radius: 4px;

    .dr-relateCard-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ::v-deep(.readonly-textarea) {
      .el-textarea__inner {
        background: transparent !important;
      }
    }
  }
}
</style>
