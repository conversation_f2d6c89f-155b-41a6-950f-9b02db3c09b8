.user-login-container {
  width: 100%;
  height: auto;

  .user-login-title {
    margin-top: 37px;
    font-size: 26px;
    font-weight: bolder;
    line-height: 36px;
    color: #262626;
  }

  .user-login-tip {
    margin-top: 5px;
    font-size: 14px;
    line-height: 20px;

    span {
      color: #0070d2;
      cursor: pointer;
    }
  }

  .user-login-form-item {
    margin-top: 32px;

    .user-login-btn-container {
      position: relative;

      .message-tip {
        width: 100%;
        font-size: 12px;
        line-height: 1.5;
        color: #e62412;
        text-align: center;
      }

      .user-login-btn-msg-container {
        position: absolute;
        top: -5px;
        left: 50%;
        transform: translateX(-50%);

        .el-text {
          width: 100%;
          font-size: 12px;
          line-height: 20px;
          color: #e62412;
          text-align: center;
        }

        .user-login-tip {
          margin-left: 2px;
          font-size: 12px;
          line-height: 20px;
          color: #0070d2;
          cursor: pointer;
        }
      }
    }

    .user-login-button {
      width: 100%;
      height: 40px;
      margin-top: 10px;
      background: #0070d2;
      border-radius: 8px;
    }

    .user-login-code-container {
      flex: 1;
      margin-bottom: 0 !important;

      &.is-error {
        margin-bottom: 22px !important;
      }

      ::v-deep(.el-form-item__content) {
        align-items: baseline;
      }

      .user-login-code-mailCode-container {
        flex: 1;
      }

      .el-button {
        // width: 127px;
        margin-left: 12px;
      }

      .count-down-body {
        color: #87c9ff;
        border: 1px solid #e0dfe4;
        border-radius: 4px;

        ::v-deep(.el-statistic) {
          .el-statistic__number {
            font-size: 14px;
            color: #87c9ff;
          }

          .el-statistic__suffix span {
            font-size: 14px;
            color: #87c9ff;
          }
        }
      }
    }

    .user-login-code-mailCode-text {
      display: inline-block;
      margin-bottom: 30px;
      font-size: 12px;
      line-height: 17px;
      color: #8c8c8c;
    }
  }

  .user-login-form-container-bottom {
    .user-login-form-top-left {
      display: flex;
      align-items: center;
      width: 100%;

      .el-checkbox {
        flex: 1;
      }
    }

    .user-login-form-middle {
      margin-top: 28px;
      text-align: center;

      .user-login-form-middle-title {
        height: 24px;
        margin: 28px 0;
        text-align: center;
        border-top: 1px solid #000;

        .user-login-form-middle-line {
          position: relative;
          top: -14px;
          background-color: #fff;

          &::before {
            z-index: -1;

            // position: absolute;
            // left: 50%;
            // transform: translateX(-50%);
            // top: 50%;
            display: inline-block;
            width: 100%;
            height: 1px;
            content: "";
            background: #ebebeb;
          }

          .line-with-text {
            position: absolute;
            top: 6px;
            left: 50%;
            width: 60px;
            font-size: 14px;
            color: #808080;
            text-align: center;
            background: #fff;
            transform: translateX(-50%);
          }

          // .line-with-text::after {
          //   content: attr(data-content);
          //   position: absolute;
          //   color: black;
          //   left: 50%;
          //   transform: translate(-50%, -50%);
          //   top: 50%;
          //   white-space: nowrap;
          // }
        }
      }

      .user-login-form-middle-box {
        display: inline-block;
        width: auto;

        .middle-button {
          display: inline-block;
          width: auto;

          &:first-child {
            margin-right: 54px;
          }

          .middle-box-text {
            display: block;
          }
        }
      }
    }

    .user-login-form-bottom {
      margin-top: 40px;
      text-align: center;

      .el-button-custom-row {
        text-align: center;
      }
    }

    .el-button-custom-text {
      padding: 0;
      font-size: 14px;
      color: #0070d2;
      cursor: pointer;
    }
  }

  .send-verify-code-btn {
    padding: 0 13px;
    color: #0070d2;
    background: #eef8ff;
    border: 1px solid #ccecff;
    border-radius: 4px;

    &:hover {
      color: #fff;
      background: #0070d2;
      border: 1px solid #0071d2;
    }
  }
}
