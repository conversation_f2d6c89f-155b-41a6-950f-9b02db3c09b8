<template>
  <div class="lk-avater-group-container">
    <el-popover placement="bottom" :width="235" trigger="click">
      <template #reference>
        <div class="lk-avater-col">
          <LkAvatar
            v-for="(item, index) in avatarGroup"
            :key="item.id"
            class="avatar-col"
            :shape="item.user ? 'circle' : 'square'"
            :class="[item.user ? '' : 'colorOrange']"
            :size="size"
            v-bind="$attrs"
            :style="{
              'z-index': 999 - index
            }"
            :teamInfo="{
              avatar: item.user ? item.userAvatar : item.teamAvatar,
              username: item.user ? item.userName : item.teamName,
              status: item.edited,
              coop: item.coopTeamUser
            }"
          />
          <el-avatar
            v-if="props.avatarList.length >= props.maxAvatar"
            class="avatar-col-main"
            :size="size"
            fit="cover"
          >
            +{{ remainingAvatarLength }}
          </el-avatar>
        </div>
      </template>
      <div class="lk-avater-group-popover-container">
        <div
          v-for="item in avatarList"
          :key="item.id"
          class="lk-avater-group-popover-col"
        >
          <LkAvatar
            :size="32"
            :shape="item.user ? 'circle' : 'square'"
            :class="[item.user ? '' : 'colorOrange']"
            :teamInfo="{
              avatar: item.user ? item.userAvatar : item.teamAvatar,
              username: item.user ? item.userName : item.teamName,
              status: item.edited,
              coop: item.coopTeamUser
            }"
          />
          <div v-if="!item.coopTeamUser" class="lk-avater-group-popover-text">
            <ReText
              v-if="item.user"
              type="info"
              class="lk-avater-group-popover-user-text"
              :tippyProps="{ delay: 50000 }"
            >
              <span v-if="!item.activate" class="tag-register-tip">
                ({{ t("trade_common_unregistered") }})
              </span>
              {{ item.userName }}
              <span v-if="item.activate">({{ item.email }})</span>
            </ReText>
            <ReText
              v-else
              type="info"
              class="lk-avater-group-popover-team-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.teamName }}({{ item.shortName }})
            </ReText>
          </div>
          <div v-else class="lk-avater-group-popover-text">
            <ReText
              type="info"
              class="lk-avater-group-popover-user-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.userName }}({{ item.email }})
            </ReText>
            <ReText
              type="info"
              class="lk-avater-group-popover-team-text"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.teamName }}({{ item.shortName }})
            </ReText>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { ReText } from "@/components/ReText";
import LkAvatar from "@/components/lkAvatar/index";

interface userAvaterInfo {
  id: string | null;
  avatar: string | null;
  name: string | null;
}

interface TeamInfo {
  id?: string;
  userAvatar: string;
  userName: string;
  teamAvatar: string;
  teamName: string;
  edited: string;
  user: boolean;
  coopTeamUser: boolean;
}

const props = defineProps({
  size: {
    type: Number as PropType<number>,
    default: 24
  },
  avatarList: {
    type: Object as PropType<TeamInfo[]>
  },
  maxAvatar: {
    type: Number as PropType<number>,
    default: 4
  },
  fontSize: {
    type: Number as PropType<number>,
    default: 16
  }
});

const { t } = useI18n();

const avatarGroup = computed<TeamInfo[]>(() => {
  return props.avatarList.slice(0, props.maxAvatar - 1);
});

const remainingAvatarLength = computed(() => {
  return props.avatarList.length - avatarGroup.value.length;
});
</script>
<style lang="scss" scoped>
.lk-avater-group-container {
  display: inline-block;
  vertical-align: bottom;

  .lk-avater-col {
    display: flex;
    width: 100%;

    .lk-avater-container {
      position: relative;
      display: inline-flex;
      margin-right: -10px;

      ::v-deep(.el-avatar) {
        font-size: 10px !important;
        border: 2px solid #fff;
      }
    }
  }

  .avatar-col-main {
    // z-index: -1 !important;
    font-size: 10px !important;
    border: 2px solid #fff;
  }
}

.orange-avatar-group {
  ::v-deep(.el-avatar) {
    border-color: #ffb780 !important;
  }
}

.lk-avater-group-popover-container {
  .lk-avater-group-popover-col {
    display: flex;
    align-items: center;
    padding: 8px 0;

    ::v-deep(.el-avatar) {
      font-size: 14px !important;
    }

    .lk-avater-group-popover-text {
      flex: 1;
      max-width: calc(100% - 40px);
      margin-left: 8px;

      .lk-avater-group-popover-user-text {
        overflow: hidden;
        font-size: 12px;
        color: #262626;
        text-overflow: ellipsis;
        white-space: nowrap;

        .tag-register-tip {
          margin-right: 5px;
          color: #fa8d0a !important;
        }
      }

      .lk-avater-group-popover-team-text {
        font-size: 12px;
        color: #262626;
      }
    }
  }
}
</style>
