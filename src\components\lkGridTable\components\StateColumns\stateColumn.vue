<template>
  <el-tag :color="getStateBackground" effect="light" round>
    <i :class="['iconfont', getStateIcon]" :style="`color: ${getStateColor}`" />
    <span class="order-text" :style="`color: ${getStateColor}`">
      {{ orderText }}
    </span>
  </el-tag>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { ElTag } from "element-plus";

const props = defineProps({
  content: {
    type: String as PropType<string>,
    default: ""
  },
  orderText: {
    type: String as PropType<string>,
    default: ""
  }
});

const getStateBackground = computed(() => {
  const STATE_BACKGROUNG = {
    ON_GOING: "#DDEFFF",
    COMPLETE: "#D9F4F0",
    CANCEL: "#E5E5E5",
    NOT_START: "#DDF2FF"
  };
  return STATE_BACKGROUNG[props.content];
});

const getStateColor = computed(() => {
  const STATE_COLOR = {
    ON_GOING: "#0070D2",
    COMPLETE: "#32A645",
    CANCEL: "#262626",
    NOT_START: "#3FC6FE"
  };
  return STATE_COLOR[props.content];
});

const getStateIcon = computed(() => {
  return props.content === "ON_GOING"
    ? "link-in-progress"
    : props.content === "COMPLETE"
      ? "link-completed"
      : props.content === "NOT_START"
        ? "link-not-started"
        : "link-cancelled";
});
</script>
<style lang="scss" scoped>
.el-tag {
  margin: 0 auto;

  .order-text {
    margin-left: 2px;
    font-size: 12px;
    font-weight: bolder;
    vertical-align: text-top;
  }
}
</style>
