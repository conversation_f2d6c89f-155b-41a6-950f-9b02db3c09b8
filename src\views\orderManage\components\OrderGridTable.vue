<template>
  <div class="order-manage-right-table-header">
    <div class="order-manage-right-table-left">
      <!-- eslint-disable-next-line vue/no-lone-template -->
      <div v-if="!relationType" class="order-manage-default">
        <el-radio-group
          v-model="tableViewMode"
          class="order-manage-right-radio"
          size="default"
          @change="handleChangeTableViewMode"
        >
          <el-radio-button
            v-for="(item, index) in radioGroup"
            :key="index"
            :label="t(item.label)"
            :value="item.value"
          />
        </el-radio-group>
        <div
          v-if="tableViewMode === 'SUBFORM' && subTableOptions.length > 1"
          class="order-manage-subtable-select-body"
        >
          <el-select-v2
            v-model="subTableWidgetId"
            :options="subTableOptions"
            placeholder="请选择展开的子表"
            @change="handleSwitchSubTable"
          />
        </div>
        <div
          class="order-manage-switch-body"
          :class="[isIpadScreen ? 'Screen_1366' : '']"
        >
          <el-switch
            v-if="!isIpadScreen && tableSelectedNumber > 0"
            v-model="orderSwitch"
            size="small"
          />
          <span
            v-if="!isIpadScreen && tableSelectedNumber <= 0"
            class="order-manage-text"
            :class="[isIpadScreen ? 'Screen_1366' : '']"
          >
            {{ t("trade_common_tableCheckTip") }}
          </span>
          <span
            v-if="!isIpadScreen && tableSelectedNumber > 0"
            class="order-manage-text"
            :class="[!isIpadScreen ? 'Screen_1366' : '']"
            v-html="tableSelectedNumberTip"
          />
        </div>
        <slot
          name="batchOperate"
          :tableSelectedNumber="tableSelectedNumber"
          :tableSelectedList="tableSelectedList"
        />
        <el-tooltip
          :content="t('trade_common_cancel')"
          :disabled="!!buttonTextVisible"
        >
          <span
            class="order-manage-btn"
            :class="[
              tableSelectedNumber <= 0 ? 'order-manage-disabled-btn' : '',
              currentScreenWidth <= 1366 ? 'Screen_1366' : ''
            ]"
            @click="handleCancelTableSelected"
          >
            <i class="iconfont link-delete" />
            {{ buttonTextVisible ? t("trade_common_cancel") : "" }}
          </span>
        </el-tooltip>
      </div>
      <div v-else class="order-manage-default">
        <OrderGridTableHeaderLeft
          :relationType="relationType"
          :tableSelectedNumber="tableSelectedNumber"
          :tableSelectedList="tableSelectedList"
          @clearSelectNumber="handleCancelTableSelected"
        />
      </div>
    </div>
    <div class="order-manage-right-table-right">
      <OrderGridTableSearchInput
        v-model="keyword"
        :searchResult="searchResult"
        @search="handleKeyWordSearch"
        @clear="handleJumpTableColumn('clear')"
        @jumpTableColumn="handleJumpTableColumn"
      />
      <div v-if="!relationType" style="display: inline-block">
        <!-- 导入/导出 -->
        <slot name="orderOperate" />
        <!-- 排序  -->
        <OrderSortWidget
          :orderFields="orderWidgetOperateFields"
          :currentScreenWidth="currentScreenWidth"
          :sortWidgetList="presentViewInfo.sortList"
          @handleTableSort="handleTableSort"
        />
        <!-- 字段设置 -->
        <OrderFixedWidget
          :orderFields="orderWidgetOperateFields"
          :tableRef="tableOrderRef"
          :currentScreenWidth="currentScreenWidth"
          @handleChangeColumnVisible="handleChangeColumnVisible"
        />
      </div>
      <OrderListFilterWidget
        :presentViewType="presentView.viewType"
        :orderFilterList="presentViewInfo.conditionList"
        :presentConditions="presentConditions"
        :currentScreenWidth="currentScreenWidth"
        @handleSearchTableByConditions="handleSearchTableByConditions"
      />
    </div>
  </div>
  <div v-show="tableViewMode === 'DETAIL'" class="order-manage-table-container">
    <LkGrid
      ref="orderTableRef"
      :key="presentView.id"
      :data="tableData"
      :height="height"
      :tableOpeateLists="tableOpeateLists"
      :orderPermissionList="orderPermissionList"
      :tablePresentConditionList="presentViewInfo.conditionList"
      :colStatisticsItemList="colStatisticsItemList"
      :colStatisticsSearchCondition="colStatisticsSearchCondition"
      :detailTable="false"
      :relationTodo="relationTodo"
      :tableTotal="tableTotal"
      :tableConfig="tableConfig"
      :presentView="presentView"
      :presentInfo="presentInfo"
      :milestoneIdList="milestoneIdList"
      :presentConditions="presentConditions"
      @handleOrderViewUndergo="handleOrderViewUndergo"
      @handleOperate="handleOperate"
      @handleCheckbox="handleGetGridChecked"
      @handleTableSort="handleTableSort"
      @handleUpdateTableConfig="handleUpdateTableConfig"
      @handleUpdateTable="handleReloadTableData"
      @handleDblClick="handleDblClick"
      @handleVisibleChange="handleVisibleChange"
      @handleGetCheckboxRecords="handleGetCheckboxRecords"
      @handleGetRadioRecord="handleGetRadioRecord"
    >
      <template #vxeTableFooterLeft>
        <OrderTableTip />
      </template>
      <template #subscript="{ rowData }">
        <Subscript
          v-if="rowData.relationIconType"
          :rowData="rowData"
          :presentViewInfo="presentViewInfo"
        />
      </template>
    </LkGrid>
  </div>
  <div
    v-show="tableViewMode === 'SUBFORM'"
    class="order-manage-table-container"
  >
    <LkSubTableGrid
      ref="orderSubTableRef"
      :data="tableData"
      :height="height"
      :tableOpeateLists="tableOpeateLists"
      :tablePresentConditionList="presentViewInfo.conditionList"
      :colStatisticsItemList="colStatisticsItemList"
      :colStatisticsSearchCondition="colStatisticsSearchCondition"
      :detailTable="false"
      :tableTotal="tableTotal"
      :tableConfig="tableConfig"
      :presentView="presentView"
      :presentInfo="presentInfo"
      :milestoneIdList="milestoneIdList"
      :presentConditions="presentConditions"
      @handleOperate="handleOperate"
      @handleCheckbox="handleGetGridChecked"
      @handleUpdateTableConfig="handleUpdateTableConfig"
      @handleUpdateTable="handleReloadTableData"
      @handleDblClick="handleDblClick"
      @handleGetCheckboxRecords="handleGetCheckboxRecords"
    >
      <template #vxeTableFooterLeft>
        <OrderTableTip />
      </template>
    </LkSubTableGrid>
  </div>
  <div
    v-if="tableViewMode !== 'SUBFORM' && !orderSwitch"
    class="order-manage-footer"
  >
    <div class="order-manage-footer-left">
      <slot name="vxeTableFooterLeft" />
    </div>
    <div class="order-manage-footer-right">
      <el-pagination
        v-model:current-page="gridTablePage.pageNo"
        v-model:page-size="gridTablePage.pageSize"
        :page-sizes="[10, 50, 100, 300, 500]"
        size="default"
        :pager-count="6"
        layout="total, sizes, prev, pager, next "
        :total="tableTotal"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <OrderViewWidget
    ref="orderViewRef"
    :presentView="presentView"
    @handleAddOrderView="handleAddOrderView"
  />
  <WorkOrderCollectionDialog
    ref="WorkOrderCollectionDialogRef"
    @handleUpdateCollection="handleReloadTableData"
  />
  <WorkOrderReplyDialog
    ref="WorkOrderReplyDialogRef"
    @handleUpdateCollection="handleReloadTableData"
  />
  <OrderChooseProcessorDialog
    ref="chooseProcessorRef"
    :personnelControlMode="orderChooseMode"
    :dialogTitle="orderChooseTitle"
    :orderChooseType="orderChooseType"
    @handleChooseConfirm="handleChooseConfirm"
  />
</template>
<script lang="ts" setup>
import {
  ref,
  reactive,
  watch,
  nextTick,
  computed,
  markRaw,
  onMounted
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import { dict } from "@/utils/dict";
import { storageLocal, cloneDeep } from "@pureadmin/utils";
import LkGrid from "@/components/lkGridTable/lkGrid";
import LkSubTableGrid from "@/components/lkGridTable/lkSubTableGrid";
import OrderTableTip from "./OrderTableTip.vue";
import OrderListFilterWidget from "./orderManageList/OrderListFilterWidget.vue";
import OrderFixedWidget from "./orderManageList/OrderFixedWidget.vue";
import OrderSortWidget from "./orderManageList/OrderSortWidget.vue";
import OrderViewWidget from "./orderManageList/OrderViewWidget.vue";
import OrderGridTableHeaderLeft from "./OrderGridTableHeaderLeft.vue";
import OrderGridTableSearchInput from "./OrderGridTableSearchInput.vue";
import WorkOrderCollectionDialog from "@/views/orderDetail/components/WorkOrderCollectionDialog.vue";
import WorkOrderReplyDialog from "@/views/orderDetail/components/WorkOrderReplyDialog.vue";
import OrderChooseProcessorDialog from "@/views/orderDetail/components/OrderChooseProcessor/OrderChooseProcessorDialog.vue";
import Subscript from "./Subscript.vue";
import { getSccsLableAllFields } from "@/api/sccs";
import {
  deleteTradeOrder,
  getTradeSccsRolePermission,
  getTradeCoopSccsRolePermission,
  getWorkOrderOperationList,
  deleteWorkOrder,
  assignMilestoneCollect,
  assignMilestoneWorkOrderReply,
  getOrderSearchFileds
} from "@/api/order";
import { TableOperateProp } from "@/types/type.d";
import { useOnceWhen } from "@/hooks";
import { inject } from "vue";

const props = defineProps({
  presentView: {
    type: Array as PropType<any>,
    default: () => []
  },
  presentViewInfo: {
    type: Array as PropType<any>,
    default: () => []
  },
  tableConfig: {
    type: Array as PropType<any>,
    default: () => []
  },
  tableTotal: {
    type: Number as PropType<any>,
    default: 0
  },
  milestoneIdList: {
    type: Array as PropType<any>,
    default: () => []
  },
  orderPermissionList: {
    type: Array as PropType<any>,
    default: () => []
  },
  relationType: {
    type: String as PropType<any>,
    default: ""
  },
  currentScreenWidth: {
    type: Number as PropType<any>,
    default: 0
  },
  colStatisticsItemList: {
    type: Array as PropType<any>,
    default: () => []
  },
  colStatisticsSearchCondition: {
    type: Object as PropType<any>,
    default: () => {}
  },
  relationTodo: {
    type: Boolean as PropType<boolean>,
    default: true
  }
});

const emit = defineEmits([
  "handleKeyWordSearch",
  "handleRemoveSuccess",
  "handleReloadTableData",
  "handleGetCheckNumber",
  "handleUpdateTableConfig",
  "handleEditOrderDetail",
  "handlePageSizeChanged",
  "handleSearchTableByConditions",
  "handleAddOrderView",
  "handleSortTable",
  "handleChangeColumnVisible"
]);

interface PresentProp {
  conditionList?: any[];
  colStatisticsItemList?: any[];
  fields?: any[];
  latestTableType?: string;
  pageSize?: number;
  keyWords?: string;
  sortList?: any[];
}

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const presentInfo = ref<PresentProp>({});
const presentConditions = ref<any>({});
const height = ref<number>(0);
const subTableWidgetId = ref<string>("");
const orderTableRef = ref<HTMLElement | any>(null);
const orderSubTableRef = ref<HTMLElement | any>(null);
const orderViewRef = ref<HTMLElement | any>(null);
const WorkOrderCollectionDialogRef = ref<HTMLElement | any>(null);
const WorkOrderReplyDialogRef = ref<HTMLElement | any>(null);
const chooseProcessorRef = ref<HTMLElement | any>(null);
const keyword = ref<string>(""); // 搜索关键字
const tableViewMode = ref<string>("DETAIL"); // 表格视图模式
const orderSwitch = ref<boolean>(false); // 过滤表格选中的行数
const orderWidgetOperateFields = ref<any>([]); // 字段排序字段列表
const tableData = ref<any>([]);
const orderChooseMode = ref<string>("listMode");
const orderChooseTitle = ref<string>("");
const orderChooseType = ref<string>("");
const presentHiddenWidgetIdList = ref<string[]>([]);
const orderChooseRowData = ref<any>({});
const tableSelectedNumber = ref<number>(0);
const tableSelectedNumberTip = computed(() => {
  return t("trade_common_selected", {
    params0: `<span class="order-manage-text-name">${tableSelectedNumber.value}</span>`
  });
});
const isIpadScreen = computed(() => {
  return props.currentScreenWidth <= 1366;
});
const tableSelectedList = ref<any[]>([]);
const searchResult = ref<any>({
  index: 0,
  results: []
});
const gridTablePage = reactive({
  pageSize: 10,
  pageNo: 1
});

const buttonTextVisible = inject("buttonTextVisible");

const radioGroup = computed(() => {
  if (props.presentView.viewType === "ORDER") {
    return [
      {
        label: "trade_common_overview",
        value: "ALL_VIEW"
      },
      {
        label: "trade_common_details",
        value: "DETAIL"
      },
      {
        label: "trade_common_Subtable",
        value: "SUBFORM"
      }
    ];
  } else {
    return [
      {
        label: "trade_common_details",
        value: "DETAIL"
      },
      {
        label: "trade_common_Subtable",
        value: "SUBFORM"
      }
    ];
  }
});

const TABLE_OPERATE_LISTS = [
  {
    icon: "link-display",
    title: t("trade_common_details"),
    slotName: "details",
    permission: ["view_main_form"]
  },
  {
    icon: "link-edit",
    title: t("trade_common_edit"),
    slotName: "edit",
    permission: ["edit_order"]
  },
  {
    icon: "link-repeat",
    title: t("trade_common_repeat_order"),
    slotName: "edit",
    permission: ["clone_order"]
  },
  {
    icon: "link-order-association",
    title: t("trade_common_associationOrder"),
    slotName: "edit",
    permission: ["relate_order"]
  },
  {
    icon: "link-sccs-association",
    title: t("trade_common_associationSccs"),
    slotName: "edit",
    permission: ["relate_sccs_order"]
  },
  {
    icon: "link-ashbin",
    title: t("trade_common_delete"),
    slotName: "delete",
    iconClassName: "link-delete-icon",
    permission: ["delete_order"]
  }
];
const tableOpeateLists = ref<TableOperateProp[]>(TABLE_OPERATE_LISTS);

const tableOrderRef = computed(() => {
  if (tableViewMode.value === "DETAIL") {
    return orderTableRef.value;
  } else {
    return orderSubTableRef.value;
  }
});

const tableStatisticsItemList = computed(() => {
  return props.presentView.defaultView
    ? []
    : props.presentViewInfo.colStatisticsItemList;
});

const handleGetCheckboxRecords = (data: any) => {
  orderTableRef.value.setCheckboxRecords(data);
  tableSelectedNumber.value = data.length;
  tableSelectedList.value = data;
};

const handleGetRadioRecord = (data: any) => {
  tableSelectedNumber.value = 1;
  tableSelectedList.value = [data];
};

const subTableOptions = computed(() => {
  const subTableList = props.tableConfig.filter(
    widget => widget.widgetType === "TABLE_FORM"
  );
  return subTableList.map(subTable => {
    return {
      label: subTable.label,
      value: subTable.name
    };
  });
});

const handleCancelTableSelected = (): void => {
  tableOrderRef.value.handleClearCheckboxState();
  // FIXME: 表格组件问题,通过状态来取消复选框失效,但是刷新数据可以暂时解决这个问题.等表格组件修复后,再优化.
  tableOrderRef.value.handleReloadRecord();
  orderTableRef.value?.executeStatisticalResult?.();
};

const handleAddOrderView = () => {
  emit("handleAddOrderView");
};

const handleJumpTableColumn = (type: string) => {
  const searchResultData = orderTableRef.value.handleJumpTableColumn(type);
  searchResult.value = searchResultData;
};

// 关键字搜索
const handleKeyWordSearch = () => {
  const searchResultData = orderTableRef.value.handleListTableSearch(
    keyword.value
  );
  searchResult.value = searchResultData;
  emit("handleKeyWordSearch", keyword.value, orderTableRef.value);
  presentInfo.value.keyWords = keyword.value;
  orderViewRef.value.handleUpdatePresentViewVisible({
    ...presentInfo.value,
    viewType: props.presentView.viewType,
    msId: props.presentView.msId,
    defaultView: props.presentView.defaultView,
    title: props.presentView.title,
    id: props.presentView.id
  });
};

// 搜索条件变更
const handleSearchTableByConditions = (conditions: any) => {
  orderSwitch.value = false;
  emit("handleSearchTableByConditions", conditions);
  presentInfo.value.conditionList = conditions;
  orderViewRef.value.handleUpdatePresentViewVisible({
    ...presentInfo.value,
    viewType: props.presentView.viewType,
    msId: props.presentView.msId,
    defaultView: props.presentView.defaultView,
    title: props.presentView.title,
    id: props.presentView.id
  });
};

// 列宽拖拽变更
const handleOrderViewUndergo = ({ colStatistics, column }) => {
  presentInfo.value.colStatisticsItemList = colStatistics.filter(
    item => !!item.colStatisticType
  );
  presentInfo.value.fields = column;
  orderViewRef.value.handleUpdatePresentViewVisible({
    ...presentInfo.value,
    viewType: props.presentView.viewType,
    msId: props.presentView.msId,
    defaultView: props.presentView.defaultView,
    title: props.presentView.title,
    id: props.presentView.id
  });
};

// 切换订单列表表格类型
const handleChangeTableViewMode = (): void => {
  presentInfo.value.latestTableType = tableViewMode.value;

  if (tableViewMode.value === "SUBFORM") {
    subTableWidgetId.value = subTableOptions.value[0].value;
    handleSwitchSubTable();
  }

  orderViewRef.value.handleUpdatePresentViewVisible({
    ...presentInfo.value,
    viewType: props.presentView.viewType,
    msId: props.presentView.msId,
    defaultView: props.presentView.defaultView,
    title: props.presentView.title,
    id: props.presentView.id
  });
};

// 分页组件：一页的页数变更
const handleSizeChange = () => {
  gridTablePage.pageNo = 1;
  emit("handlePageSizeChanged", gridTablePage);
  presentInfo.value.pageSize = gridTablePage.pageSize;
  orderViewRef.value.handleUpdatePresentViewVisible({
    ...presentInfo.value,
    viewType: props.presentView.viewType,
    msId: props.presentView.msId,
    defaultView: props.presentView.defaultView,
    title: props.presentView.title,
    id: props.presentView.id
  });
};

// 表格排序
const handleTableSort = (sortList: any) => {
  orderSwitch.value = false;
  emit("handleSortTable", sortList);
  presentInfo.value.sortList = sortList;
  orderViewRef.value.handleUpdatePresentViewVisible({
    ...presentInfo.value,
    viewType: props.presentView.viewType,
    msId: props.presentView.msId,
    defaultView: props.presentView.defaultView,
    title: props.presentView.title,
    id: props.presentView.id
  });
};

// 切换表格隐藏列
const handleChangeColumnVisible = (columnList: any) => {
  const presentHiddenWidgetNames = columnList
    .filter(column => !column.visible)
    .map(column => column.field);

  presentHiddenWidgetIdList.value = presentHiddenWidgetNames;

  orderSwitch.value = false;
  emit("handleChangeColumnVisible", presentHiddenWidgetNames);
  presentInfo.value.fields = columnList;
  orderViewRef.value.handleUpdatePresentViewVisible({
    ...presentInfo.value,
    viewType: props.presentView.viewType,
    msId: props.presentView.msId,
    defaultView: props.presentView.defaultView,
    title: props.presentView.title,
    id: props.presentView.id
  });
};

const handleSwitchSubTable = () => {
  const tableConfigList = cloneDeep(presentInfo.value.fields);
  const detailTableConfigList = tableConfigList.filter(
    tableConfigItem =>
      tableConfigItem.type !== "ALL_VIEW" && !tableConfigItem.parentsName
  );
  const subTableIndex = detailTableConfigList.findIndex(
    tableWidget => tableWidget.name === subTableWidgetId.value
  );
  const subTableColumns = tableConfigList.filter(
    tableWidget => tableWidget.parentsName === subTableWidgetId.value
  );
  detailTableConfigList.splice(subTableIndex, 1, ...subTableColumns);
  const widgetList = detailTableConfigList.filter(
    widget => widget.widgetType !== "TABLE_FORM"
  );

  nextTick(() => {
    const { latestLoginTeamMemberId, latestLoginTeamId } =
      storageLocal().getItem("user-info") as any;
    orderSubTableRef.value.handleRenderColumnsInfo(widgetList);
    orderSubTableRef.value.handleReloadTableData(
      {
        sccsId: route.query.sccsId,
        keyword: keyword.value,
        msId: props.presentView.msId,
        sccsConditionList: presentInfo.value.conditionList,
        sortList: presentInfo.value.sortList,
        hiddenColumnList: presentHiddenWidgetIdList.value,
        teamMemberId: latestLoginTeamMemberId,
        currentTeamId: latestLoginTeamId,
        sourceWorkOrderId: route.query.workOrderId || "",
        sourceOrderId: route.query.orderId || "",
        sourceMsId: route.query.msId || "",
        sourceSccsId: route.query.sccsId || "",
        relationOperationType: route.query.typeValue || null,
        todo: route.query.todo === "1",
        ...gridTablePage
      },
      subTableWidgetId.value,
      subTableColumns[0]
    );
  });
};

const handleCurrentChange = () => {
  emit("handlePageSizeChanged", gridTablePage);
};

const handleRenderTableData = async (orderTableData: any, viewType: string) => {
  if (viewType !== "MILESTONE") {
    // 订单视图获取数据
    let tableData = [];
    const dateFormat = "YYYY-MM-DD";
    orderTableData.map(orderList => {
      let orderWidget = {};
      const orderStateText = dict
        .getDictByCode("order_state")
        .find(dictItem => dictItem.value === orderList.orderState)?.label;
      orderWidget["orderStateText"] = orderStateText;

      // 处理主表单控件字段
      orderList.widgetList.map(widgetItem => {
        if (
          ["IMAGE_UPLOAD", "FILE_UPLOAD", "EXCHANGE_RATE"].includes(
            widgetItem.widgetType
          )
        ) {
          orderWidget[widgetItem["widgetId"]] = widgetItem["obj"];
        } else if (widgetItem.widgetType === "TABLE_FORM") {
          orderWidget[widgetItem["widgetId"]] =
            !!orderList.subWidgetMap[widgetItem["widgetId"]];
        } else {
          orderWidget[widgetItem["widgetId"]] = widgetItem["label"];
        }
      });

      const tableFormWidgetList = props.tableConfig.filter(
        widget => widget.widgetType === "TABLE_FORM"
      );
      let tableFormObject = {};
      for (let tableForm of tableFormWidgetList) {
        tableFormObject[tableForm.name] =
          orderList.subWidgetMap[tableForm.name];
      }

      // 处理里程碑系统字段
      const orderMilestoneFields = props.tableConfig.filter(
        orderWidget =>
          orderWidget.type === "MILESTONE" || orderWidget.type === "MS_REPLY"
      );
      let milestoneOrderObject = {};
      orderMilestoneFields.map(orderMilestoneField => {
        const orderMilestoneList = orderMilestoneField.name.split("_");
        const orderMilestoneMsId = orderMilestoneList[0];
        const orderMilestoneOrderItem = orderList.milestoneList.find(
          milestoneItem => milestoneItem.msId === orderMilestoneMsId
        );
        if (orderMilestoneOrderItem) {
          if (["planTime", "actualTime"].includes(orderMilestoneList[1])) {
            let milestoneFields = "";
            let startFieldName =
              orderMilestoneList[1] === "planTime"
                ? "plannedStartDate"
                : "actualStartDate";
            let endFieldName =
              orderMilestoneList[1] === "planTime"
                ? "plannedEndDate"
                : "actualEndDate";
            if (orderMilestoneOrderItem[startFieldName]) {
              milestoneFields += `${dayjs(new Date(orderMilestoneOrderItem[startFieldName])).format(dateFormat)}`;
            }
            if (
              orderMilestoneOrderItem[startFieldName] &&
              orderMilestoneOrderItem[endFieldName]
            ) {
              milestoneFields += `~`;
            }
            if (orderMilestoneOrderItem[endFieldName]) {
              milestoneFields += `${dayjs(new Date(orderMilestoneOrderItem[endFieldName])).format(dateFormat)}`;
            }
            milestoneOrderObject[orderMilestoneField.name] = milestoneFields;
          } else if (
            ["status", "plannedDay", "actualDay"].includes(
              orderMilestoneList[1]
            )
          ) {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem[orderMilestoneList[1]];
          } else if (orderMilestoneList[1] === "manager") {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem?.managerUserEmail
                ? {
                    avatar: orderMilestoneOrderItem.managerUserAvatar,
                    username: orderMilestoneOrderItem.managerUserName,
                    email: orderMilestoneOrderItem.managerUserEmail
                  }
                : {};
          } else if (orderMilestoneList[1] === "replyInfo") {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem["msReplyUser"];
          } else if (orderMilestoneList[1] === "workOrderNumber") {
            milestoneOrderObject[orderMilestoneField.name] =
              orderMilestoneOrderItem["workOrderUserInfoList"].length;
          }
        }
      });

      // 处理自定义标签字段
      let labelOrderObject = {};
      if (orderList.labelList && orderList.labelList.length > 0) {
        const orderLabelFields = props.tableConfig.filter(
          orderWidget =>
            orderWidget.type === "ORDER_LABEL" ||
            orderWidget.type === "MS_LABEL"
        );

        orderLabelFields.map(labelField => {
          const labelConfigItem = orderList.labelList.find(
            labelItem => labelItem.labelId === labelField.name
          );
          if (labelConfigItem) {
            labelOrderObject[labelField.name] = labelConfigItem;
          }
        });
      }

      tableData.push(
        Object.assign(
          orderList,
          orderWidget,
          tableFormObject,
          milestoneOrderObject,
          labelOrderObject
        )
      );
    });
    orderTableRef.value.handleReloadTableData(tableData);
  } else {
    let tableData = [];
    const dateFormat = "YYYY-MM-DD";
    orderTableData.map(orderList => {
      let orderWidget = {};
      const orderStateText = dict
        .getDictByCode("order_state")
        .find(dictItem => dictItem.value === orderList.orderState)?.label;
      orderWidget["orderStateText"] = orderStateText;

      // 处理主表单控件字段
      [...orderList?.widgetList, ...orderList?.orderWidgetList].map(
        widgetItem => {
          ["IMAGE_UPLOAD", "FILE_UPLOAD"].includes(widgetItem.widgetType);
          orderWidget[widgetItem["widgetId"]] = [
            "IMAGE_UPLOAD",
            "FILE_UPLOAD"
          ].includes(widgetItem.widgetType)
            ? widgetItem["obj"]
            : widgetItem["label"];
        }
      );

      const tableFormWidgetList = props.tableConfig.filter(
        widget => widget.widgetType === "TABLE_FORM"
      );
      let tableFormObject = {};
      for (let tableForm of tableFormWidgetList) {
        tableFormObject[tableForm.name] =
          orderList.subWidgetMap[tableForm.name];
      }

      // workOrderCreateInfo workOrderCollectInfo workOrderReplyInfo;
      const workOrderFields = props.tableConfig.filter(
        widget =>
          widget.type === "WORK_ORDER_SPECIAL" ||
          (widget.type === "MILESTONE" &&
            [
              "manager",
              "workOrderCreateInfo",
              "planTime",
              "plannedDay",
              "actualTime",
              "actualDay"
            ].includes(widget.name))
      );
      let workOrderItemObject = {};
      workOrderFields.forEach(workOrderField => {
        if (workOrderField.name === "manager") {
          workOrderItemObject[workOrderField.name] = {
            avatar: orderList.managerUserAvatar,
            username: orderList.managerUserName,
            email: orderList.managerUserEmail
          };
        } else if (["planTime", "actualTime"].includes(workOrderField.name)) {
          let milestoneFields = "";
          let startFieldName =
            workOrderField.name === "planTime"
              ? "plannedStartDate"
              : "actualStartDate";
          let endFieldName =
            workOrderField.name === "planTime"
              ? "plannedEndDate"
              : "actualEndDate";
          if (orderList[startFieldName]) {
            milestoneFields += `${dayjs(new Date(orderList[startFieldName])).format(dateFormat)}`;
          }
          if (orderList[startFieldName] && orderList[endFieldName]) {
            milestoneFields += `~`;
          }
          if (orderList[endFieldName]) {
            milestoneFields += `${dayjs(new Date(orderList[endFieldName])).format(dateFormat)}`;
          }
          workOrderItemObject[workOrderField.name] = milestoneFields;
        } else if (["plannedDay", "actualDay"].includes(workOrderField.name)) {
          workOrderItemObject[workOrderField.name] =
            orderList[workOrderField.name];
        }
      });

      // 处理自定义标签字段
      let labelOrderObject = {};
      if (orderList.labelList && orderList.labelList.length > 0) {
        const orderLabelFields = props.tableConfig.filter(
          orderWidget =>
            orderWidget.type === "ORDER_LABEL" ||
            orderWidget.type === "MS_LABEL"
        );

        orderLabelFields.map(labelField => {
          const labelConfigItem = orderList.labelList.find(
            labelItem => labelItem.labelId === labelField.name
          );
          if (labelConfigItem) {
            labelOrderObject[labelField.name] = labelConfigItem;
          }
        });
      }

      tableData.push(
        Object.assign(
          orderList,
          orderWidget,
          tableFormObject,
          workOrderItemObject,
          labelOrderObject
        )
      );
    });
    // 工单视图获取数据
    orderTableRef.value.handleReloadTableData(tableData);
  }
  handleInitRenderFilterFields();
};

const handleInitRenderFilterFields = async () => {
  const sccsId = route.query.sccsId as string;
  const { data } =
    props.presentView.viewType === "ORDER"
      ? await getOrderSearchFileds({
          sccsId: sccsId
        })
      : await getSccsLableAllFields({
          sccsId: sccsId,
          milestoneId: props.presentView.msId,
          workOrder: true
        });
  let conditionMap = new Map();
  if (data) {
    data.forEach(condition => {
      if (condition.children instanceof Array) {
        condition.children.forEach(childCondition => {
          if (childCondition.type === "labels") {
            let childConditionList = [];
            childCondition.children.forEach(childItem => {
              const deepChildItem = cloneDeep(childItem);
              conditionMap.set(deepChildItem.value, deepChildItem["children"]);
              delete deepChildItem["children"];
              childConditionList.push(deepChildItem);
            });
            childCondition.children = childConditionList;
          }
        });
      }
    });
    presentConditions.value = {
      labelConditionMap: conditionMap,
      conditionList: data
    };
  }
};

const handleOperate = async (data: any): Promise<void> => {
  const { row, slot } = data;
  const { sccsId } = route.query;
  let widgetData = {};
  row.orderWidgetList?.forEach(widget => {
    widgetData[widget.widgetId] = widget["label"];
  });

  if (slot === "details") {
    handleJumpDetail(row);
  } else if (slot === "edit") {
    emit("handleEditOrderDetail", row.orderId);
  } else if (slot === "delete") {
    ElMessageBox.confirm(
      t("trade_order_deleteOrderTip"),
      t("trade_order_delete"),
      {
        confirmButtonText: t("trade_common_confirm"),
        cancelButtonText: t("trade_common_cancel"),
        confirmButtonClass: "confrim-message-btn-class",
        cancelButtonClass: "cancel-message-btn-class",
        type: "error",
        icon: markRaw(WarningFilled),
        center: true
      }
    ).then(async () => {
      const { code } = await deleteTradeOrder({
        id: row.orderId,
        sccsId: sccsId as string
      });
      if (code === 0) {
        ElMessage({
          message: t("trade_common_updateSuccess"),
          type: "success"
        });
        handleReloadTableData();
      }
    });
  } else if (slot === "work_order_collect") {
    // 工单采集
    WorkOrderCollectionDialogRef.value.open(row, row.orderId);
  } else if (slot === "work_order_approval") {
    // 工单批复
    WorkOrderReplyDialogRef.value.open(
      row,
      widgetData,
      "workOrderReply",
      row.orderId
    );
  } else if (slot === "work_order_view") {
    // 查看工单
    WorkOrderReplyDialogRef.value.open(
      row,
      widgetData,
      "workOrderDynamic",
      row.orderId
    );
  } else if (slot === "work_order_collect_assign") {
    // 工单采集指派
    orderChooseTitle.value = "trade_order_WorkOrderCollectionAssignment";
    orderChooseType.value = "collectDistribute";
    orderChooseMode.value = "listMode";
    orderChooseRowData.value = row;
    let rowDataEditList = [];
    row.workOrderUserInfo.editUserList.map(editUser => {
      let rowDataEdit = {};
      if (editUser.user) {
        rowDataEdit["assignedTeamMemberId"] = editUser.teamMemberId;
      } else {
        rowDataEdit["assignedTeamId"] = editUser.teamMemberId;
      }
      rowDataEditList.push(rowDataEdit);
    });
    chooseProcessorRef.value.open(rowDataEditList);
  } else if (slot === "work_order_approval_assign") {
    // 工单批复指派
    orderChooseTitle.value = "trade_order_AssignWorkOrderApproval";
    orderChooseType.value = "approvalDistribute";
    orderChooseMode.value = "listMode";
    orderChooseRowData.value = row;
    let rowDataEditList = [];
    chooseProcessorRef.value.open(rowDataEditList);
  } else if (slot === "work_order_delete") {
    // 删除工单
    const removeTip = `${t("trade_order_removeWorkOrderTip")}「${row.workOrderName}」？`;
    ElMessageBox.confirm(removeTip, t("trade_order_removeWorkOrder"), {
      confirmButtonText: t("trade_common_confirm"),
      cancelButtonText: t("trade_common_cancel"),
      confirmButtonClass: "confrim-message-btn-class",
      cancelButtonClass: "cancel-message-btn-class",
      customClass: "order_confirm_message_box",
      type: "error",
      icon: markRaw(WarningFilled),
      center: true
    }).then(async () => {
      const { code } = await deleteWorkOrder({
        id: row.workOrderId,
        sccsId: sccsId,
        orderId: row.orderId,
        msId: row.msId
      });
      if (code === 0) {
        ElMessage({
          message: t("trade_common_updateSuccess"),
          type: "success"
        });
        handleReloadTableData();
      }
    });
  }
};

const handleChooseConfirm = async (
  chooseTeamMemberIds: string[],
  triggerType: string
): Promise<void> => {
  const { sccsId } = route.query;
  if (triggerType === "collectDistribute") {
    // 工单采集指派
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneCollect({
      sccsId: sccsId,
      orderId: orderChooseRowData.value.orderId,
      msId: orderChooseRowData.value.msId,
      workOrderId: orderChooseRowData.value.workOrderId,
      teamMemberIdList: chooseTeamMemberId
    });
    if (code === 0) {
      ElMessage({
        message: t("trade_common_updateSuccess"),
        type: "success"
      });
      handleReloadTableData();
    }
  } else if (triggerType === "approvalDistribute") {
    // 工单批复指派
    let chooseTeamMemberId = [];
    chooseTeamMemberIds.every((choose: any) =>
      chooseTeamMemberId.push(
        choose.assignedTeamMemberId || choose.assignedTeamId
      )
    );
    const { code } = await assignMilestoneWorkOrderReply({
      sccsId: sccsId,
      orderId: orderChooseRowData.value.orderId,
      msId: orderChooseRowData.value.msId,
      workOrderId: orderChooseRowData.value.workOrderReplyId,
      teamMemberIdList: chooseTeamMemberId
    });
    if (code === 0) {
      ElMessage({
        message: t("trade_common_updateSuccess"),
        type: "success"
      });
      handleReloadTableData();
    }
  }
};

const handleReloadTableData = () => {
  emit("handleReloadTableData");
};

const handleGetGridChecked = (data: any) => {
  emit("handleGetCheckNumber", data.length);
};

const handleJumpDetail = async (row): Promise<void> => {
  const { sccsName, sccsId, templateId, coopTeamMark } = route.query;
  const params = {
    sccsName: sccsName,
    sccsId: sccsId,
    templateId: templateId,
    orderId: row.orderId,
    orderMark: row.orderMark ? row.orderMark : row.serialNumber,
    coopTeamMark: coopTeamMark
  };
  let resp: any = {};
  if (coopTeamMark === "2") {
    resp = await getTradeSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  } else {
    resp = await getTradeCoopSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  }
  //@ts-ignore
  storageLocal().setItem(`${row.orderId}_userTeamRole`, resp.data[row.orderId]);
  router.push({ name: "orderDetail", query: params });
};

const handleVisibleChange = async (bool: boolean, visibleData: any) => {
  if (bool && visibleData.row.workOrderId) {
    const { sccsId } = route.query;
    const { msId, workOrderId } = visibleData.row;
    const { data } = await getWorkOrderOperationList({
      sccsId: sccsId,
      milestoneId: msId,
      workOrderId: workOrderId
    });

    const workOrderOperations = dict.getDictByCode("work_order_operation");
    const translationLang = storageLocal().getItem("translationLang");
    let workOrderOperationObject: any = {};
    let workOrderOperationIcon: any = {
      VIEW: "link-display",
      DELETE: "link-ashbin",
      APPROVAL: "link-reply",
      APPROVAL_ASSIGN: "link-distribute",
      COLLECT: "link-collect",
      COLLECT_ASSIGN: "link-distribute"
    };
    for (let workOrderOperation of workOrderOperations) {
      workOrderOperationObject[workOrderOperation.value] = {
        title:
          translationLang === "zh"
            ? workOrderOperation.label
            : workOrderOperation.labelEn,
        slotName: `work_order_${workOrderOperation.value.toLocaleLowerCase()}`,
        icon: workOrderOperationIcon[workOrderOperation.value],
        iconClassName:
          workOrderOperation.value === "DELETE" ? "link-delete-icon" : ""
      };
    }

    const tableOperationList = data.map(btnValue => {
      return {
        icon: workOrderOperationObject[btnValue].icon,
        title: workOrderOperationObject[btnValue].title,
        slotName: workOrderOperationObject[btnValue].slotName,
        iconClassName: workOrderOperationObject[btnValue].iconClassName
      };
    });
    tableOpeateLists.value = tableOperationList;
  }
};

/**
 * 双击行
 */
const handleDblClick = (row): void => {
  handleJumpDetail(row);
};

const handleUpdateTableConfig = (type: string, data): void => {
  emit("handleUpdateTableConfig", data);
};

watch(
  () => [props.tableConfig, props.orderPermissionList],
  () => {
    if (!Object.keys(props.tableConfig)?.length) {
      return;
    }

    orderWidgetOperateFields.value = cloneDeep(props.tableConfig).filter(
      field => field.type !== "ALL_VIEW"
    );
    if (tableViewMode.value === "DETAIL" && props.tableConfig.length > 0) {
      const tableConfigList = cloneDeep(props.tableConfig);
      const detailTableConfigList = tableConfigList.filter(
        tableConfigItem =>
          tableConfigItem.type !== "ALL_VIEW" && !tableConfigItem.parentsName
      );

      const tableFormWidgetList: any = detailTableConfigList.filter(
        detailColumn => detailColumn.widgetType === "TABLE_FORM"
      );

      tableFormWidgetList.forEach(tableFormItem => {
        const index = detailTableConfigList.findIndex(
          detailWidget => detailWidget.name === tableFormItem.name
        );
        const childrenList = tableConfigList.filter(
          tableFormChild => tableFormChild.parentsName === tableFormItem.name
        );
        detailTableConfigList.splice(
          index,
          1,
          Object.assign(tableFormItem, {
            childrenList: childrenList
          })
        );
      });
      nextTick(() => {
        orderTableRef.value.handleRenderColumnsInfo(detailTableConfigList);
      });
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.presentViewInfo,
  () => {
    if (props.presentViewInfo) {
      gridTablePage.pageSize = props.presentViewInfo.pageSize;
      keyword.value = props.presentViewInfo.keyWords;
      tableViewMode.value = props.presentViewInfo.latestTableType || "DETAIL";
      presentInfo.value = props.presentViewInfo;
    }
  },
  { deep: true, immediate: true }
);

// 满足条件时，初始化视图配置
useOnceWhen(
  () => presentInfo.value,
  () =>
    Object.keys(presentInfo.value).length > 0 &&
    props.presentView &&
    orderViewRef.value,
  () => {
    orderViewRef.value.handleUpdatePresentViewVisible({
      ...presentInfo.value,
      viewType: props.presentView.viewType,
      msId: props.presentView.msId,
      defaultView: props.presentView.defaultView,
      title: props.presentView.title,
      id: props.presentView.id
    });
  },
  { deep: true }
);

watch(
  [() => props.presentView.viewType, () => props.orderPermissionList],
  () => {
    if (props.presentView.viewType === "ORDER") {
      tableOpeateLists.value = TABLE_OPERATE_LISTS;
    } else {
      tableOpeateLists.value = [
        {
          icon: "link-display",
          title: "trade_common_details",
          slotName: "details"
        }
      ];
    }
  }
);

const refreshTableData = (data: any) => {
  orderTableRef.value.handleReloadTableData(data);
  orderTableRef.value?.patchTableCheckboxStatus(tableSelectedList.value);
};

watch(
  () => orderSwitch.value,
  () => {
    if (orderSwitch.value) {
      tableData.value = orderTableRef.value?.handleGetTableRecords(); // 备份数据
      refreshTableData(tableSelectedList.value);
    } else {
      refreshTableData(tableData.value);
    }
  }
);

watch(
  () => tableSelectedList.value,
  () => {
    if (tableSelectedList.value.length > 0) {
      if (orderSwitch.value) {
        refreshTableData(tableSelectedList.value);
      }
    } else {
      orderSwitch.value = false;
    }
  }
);

onMounted(() => {
  const parentEl: HTMLElement = document.querySelector(
    "#order-manage-table-container"
  );
  const headerEl: HTMLElement = document.querySelector(
    "#order-manage-table-container .order-manage-right-table-header"
  );
  const footerEl: HTMLElement = document.querySelector(
    "#order-manage-table-container .order-manage-footer"
  );
  let gridTableHeight = parentEl.offsetHeight - headerEl.offsetHeight;
  if (footerEl) {
    gridTableHeight = gridTableHeight - footerEl?.offsetHeight;
  }
  height.value = gridTableHeight;
});

defineExpose({
  tableSelectedList,
  executeStatisticalResult: () =>
    orderTableRef.value?.executeStatisticalResult?.(),
  handleGetCheckboxRecords,
  handleRenderTableData,
  patchTableCheckboxStatus: rows => {
    orderTableRef.value?.patchTableCheckboxStatus(rows);
  }
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
