<script lang="ts" setup>
import { h, onMounted, onUnmounted, ref, useSlots } from "vue";
import { type TippyOptions } from "vue-tippy";
import { ElText, ElTooltip } from "element-plus";
// import { watch } from "vue";

defineOptions({
  name: "ReText"
});

const props = defineProps({
  class: {
    type: [String, Object]
  },
  style: {
    type: Object
  },
  lineClamp: {
    type: [String, Number]
  },
  tippyProps: {
    type: Object as PropType<TippyOptions>,
    default: () => ({})
  }
});

const $slots = useSlots();

const textRef = ref();
// const tippyFunc = ref();
const disabled = ref(false);

const getTippyProps = () => ({
  content: h($slots.content || $slots.default),
  ...props.tippyProps,
  delay: 500,
  duration: 500,
  maxWidth: "60vw"
});

const isTextEllipsis = (el: HTMLElement) => {
  if (!props.lineClamp) {
    // 单行省略判断
    return el.scrollWidth > el.clientWidth;
  } else {
    // 多行省略判断
    return el.scrollHeight > el.clientHeight;
  }
};

function handleHover(event: MouseEvent) {
  if ((event.target as HTMLElement).hasAttribute("title")) {
    (event.target as HTMLElement).removeAttribute("title");
  }
  if (isTextEllipsis(event.target as HTMLElement)) {
    // tippyFunc.value.enable();
    disabled.value = false;
  } else {
    // tippyFunc.value.disable();
    disabled.value = true;
  }
}

// el-text 在出现溢出情况下会被强制添加 title 属性
// 使用 MutationObserver 监听 textRef 的 title 属性变化，如果出现就去掉.
let observer: MutationObserver | null = null;
onMounted(() => {
  const el = textRef.value?.$el;
  if (el) {
    observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "title"
        ) {
          (mutation.target as HTMLElement).removeAttribute("title");
        }
      });
    });

    observer.observe(el, {
      attributes: true,
      attributeFilter: ["title"]
    });
  }
});

// 清理观察器
onUnmounted(() => {
  observer?.disconnect();
});

// tippyFunc 的问题很多，暂时使用el-tooltip来替换
// watch(
//   () => [props.tippyProps, $slots.default],
//   () => {
//     tippyFunc.value = useTippy(textRef.value?.$el, getTippyProps());
//   },
//   {
//     immediate: true,
//     deep: true
//   }
// );
</script>

<template>
  <el-tooltip
    :raw-content="true"
    :disabled="disabled"
    placement="top"
    popper-class="re-text-popper"
    v-bind="{ ...$attrs, ...props.tippyProps?.popperOptions }"
    :show-after="props.tippyProps ? props.tippyProps.delay / 100 : 500"
    :effect="props.tippyProps?.theme ?? 'dark'"
  >
    <template #content>
      <div class="re-text-popper-content">
        <slot name="content">
          <slot />
        </slot>
      </div>
    </template>
    <el-text
      v-bind="{
        truncated: !lineClamp,
        lineClamp,
        title: undefined
      }"
      ref="textRef"
      :class="props.class"
      :style="props.style"
      @mouseenter.self="handleHover"
    >
      <slot />
    </el-text>
  </el-tooltip>
</template>

<style lang="scss" scoped>
.re-text-popper-content {
  max-width: 60vw;
  word-break: break-all;
}
</style>
