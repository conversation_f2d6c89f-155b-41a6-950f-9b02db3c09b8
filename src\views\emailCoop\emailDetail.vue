<template>
  <div class="write-email-coop-detail-system-message">
    <div class="write-email-coop-detail-system-message-title">
      {{ t("trade_common_remark") }}
    </div>
    <div
      v-if="mainWidgetData.remark && mainWidgetData.remark !== '<p><br></p>'"
    >
      <Editor
        v-model="mainWidgetData.remark"
        :defaultConfig="{ readOnly: true }"
      />
    </div>
    <div v-else>-</div>
    <div class="write-email-coop-detail-system-message-title mt30">附件</div>
    <div
      v-if="
        mainWidgetData.fileList &&
        mainWidgetData.fileList instanceof Array &&
        mainWidgetData.fileList.length > 0
      "
    >
      <OrderFilePreview
        v-for="item in mainWidgetData.fileList"
        :key="item"
        :filePreview="item"
      />
    </div>
    <div v-else>-</div>
  </div>
  <el-collapse
    v-model="emailCollapseActive"
    class="order-form-edit-collapse"
    :class="{ 'order-form-edit-collapse-width': receiveClass }"
  >
    <el-collapse-item
      v-for="emailItem in receiverStructureList"
      :key="emailItem.id"
      :name="emailItem.id"
    >
      <template #title>
        <div class="order-form-edit-title-header">
          <span class="order-form-edit-title">
            {{ emailItem.name }}
          </span>
        </div>
      </template>
      <OrderDetailDescWidget
        :widgetForm="emailItem.widgetJsonList"
        :widgetData="handleRenderWidgetData(emailItem)"
      />
    </el-collapse-item>
  </el-collapse>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import { TransformSubmitDataStructure } from "@/utils/formDesignerUtils";
import { ElMessage } from "element-plus";
import { Editor } from "@wangeditor/editor-for-vue";
import OrderFilePreview from "@/views/orderManage/components/OrderFilePreview.vue";
import {
  getEmailCoopReceiverStructure,
  getReceiveCoopDetail
} from "@/api/email";

const { t } = useI18n();
const route = useRoute();
const receiveClass = ref<boolean>(false);
const receiverStructureList = ref<any>([]);
const emailCollapseActive = ref<string[]>([]);
const mainWidgetData = ref<any>({});

const handleRenderWidgetData = (data: any) => {
  const {
    widgetDataForOrderList,
    widgetDataSubForOrderMap,
    widgetDataList,
    widgetDataSubMap,
    linkedReferenceMap
  } = mainWidgetData.value;
  if (data.formType === "MAIN_FORM") {
    return TransformSubmitDataStructure(
      widgetDataForOrderList,
      widgetDataSubForOrderMap,
      linkedReferenceMap
    );
  } else {
    return TransformSubmitDataStructure(
      widgetDataList,
      widgetDataSubMap,
      linkedReferenceMap
    );
  }
};

const initRenderTemplate = (id: string, row: any, model: string) => {
  Promise.all([
    getEmailCoopReceiverStructure({ id: id }),
    model === "table" ? getReceiveCoopDetail({ id: id }) : ""
  ]).then(data => {
    receiverStructureList.value = data[0].data;
    emailCollapseActive.value = data[0].data.map(milestone => milestone.id);
    mainWidgetData.value = data[1].data || row.workOrderCoopEmailData;
  });
};

onMounted(async () => {
  const emailCoopId = route.query.id as string;
  const receiveFlag = !!route.query.receiveFlag as boolean;
  receiveClass.value = receiveFlag;
  if (receiveFlag) {
    ElMessage.error(t("trade_email_workOrderCompleted"));
    Promise.all([
      getEmailCoopReceiverStructure({ id: emailCoopId }),
      getReceiveCoopDetail({ id: emailCoopId })
    ]).then(data => {
      receiverStructureList.value = data[0].data;
      emailCollapseActive.value = data[0].data.map(milestone => milestone.id);
      mainWidgetData.value = data[1].data;
    });
  }
});

defineExpose({
  initRenderTemplate
});
</script>
<style lang="scss" scoped>
@use "./components/index.scss";

.order-form-edit-collapse-width {
  width: 1200px !important;
  padding: 20px 0;
  margin: 0 auto;
}

.write-email-coop-detail-system-message {
  padding: 26px 22px;
  margin-bottom: 16px;
  background: linear-gradient(#f0f1f6, #f6f8fa);
  border-radius: 6px;

  .write-email-coop-detail-system-message-title {
    margin-bottom: 6px;
    font-size: 14px;
    line-height: 16px;
    color: #707177;
    text-align: left;

    &.mt30 {
      margin-top: 30px;
    }
  }

  ::v-deep(.w-e-text-container) {
    background: transparent !important;
    border: 1px solid #e3e3e3;
  }

  ::v-deep(.order-file-preview-body) {
    .svg-icon {
      width: 28px;
      height: 35px;
      margin-right: 10px;
    }
  }
}
</style>
