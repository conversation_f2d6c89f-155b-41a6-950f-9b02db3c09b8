<template>
  <div class="login-form-container">
    <el-form
      ref="emailFormRef"
      :model="emailForm"
      :rules="emailRules"
      label-position="top"
      size="large"
      @validate="handleValidate"
    >
      <!-- 邮箱输入 -->
      <div class="login-form-item">
        <el-form-item :label="t('trade_login_email')" prop="username">
          <el-input
            v-model="emailForm.username"
            :placeholder="t('trade_login_mailText')"
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>
      </div>

      <!-- 验证码输入 -->
      <div class="login-form-item">
        <el-form-item
          ref="mailCodeFormItemRef"
          class="captcha-form-item"
          :label="t('trade_common_captcha')"
          prop="mailCode"
        >
          <div class="captcha-input-container">
            <el-input
              v-model="emailForm.mailCode"
              :placeholder="t('trade_common_inputCaptcha')"
              maxlength="6"
              clearable
              @keyup.enter="handleSubmit"
            />
          </div>
          <el-button
            v-if="!beginCountDown"
            type="primary"
            class="send-verify-code-btn"
            :loading="sendingCaptcha"
            @click="sendVerifyCode"
          >
            {{ t("trade_common_getCaptcha") }}
          </el-button>
          <el-button
            v-else
            class="count-down-body"
            type="primary"
            disabled
            color="#edf1fd"
          >
            <el-countdown
              :value="timeCode"
              format="(ss)"
              :prefix="t('trade_common_reacquireCaptcha')"
              @finish="countDownFinish"
            />
          </el-button>
        </el-form-item>
      </div>
      <el-text
        class="captcha-tip-text"
        :style="{
          marginTop: mailCodeValidateStatus ? '-16px' : '0px',
          marginBottom: mailCodeValidateStatus ? '16px' : '0px'
        }"
      >
        {{ t("trade_login_emailCodeTip") }}
      </el-text>

      <!-- 错误信息显示 -->
      <UserError
        :respMessage="respMessage"
        :formData="emailForm"
        :style="errorStyle"
      />

      <!-- 用户服务协议（仅登录显示） -->
      <UserAgreement v-if="mode === 'login'" />

      <!-- 提交按钮 -->
      <el-button
        type="primary"
        class="login-button"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        {{ submitButtonText }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch, onMounted, type PropType } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { isEmail } from "@pureadmin/utils";
import type { FormInstance, FormRules } from "element-plus";
import { getSendVerifyCode, mailLogin, checkMailCode } from "@/api/login";
import type {
  MailLoginProp,
  VerifyCodeProp,
  VerifyCodeEmailProp
} from "@/api/type.d";
import UserError from "./UserError.vue";
import UserAgreement from "./UserAgreement.vue";
import UAParser from "ua-parser-js";
import { cacheUsername, getCacheUsername } from "../utils";

const { t } = useI18n();
const route = useRoute();

// Props 定义
const props = defineProps({
  mode: {
    type: String as PropType<"login" | "register" | "reset">,
    default: "login"
  },
  errorStyle: {
    type: String as PropType<string>,
    default: ""
  }
});

// 表单引用
const emailFormRef = ref<FormInstance>();

// 状态管理
const submitLoading = ref<boolean>(false);
const sendingCaptcha = ref<boolean>(false);
const beginCountDown = ref<boolean>(false);
const timeCode = ref<number>(Date.now() + 1000 * 60);
const respMessage = ref<string>("");

// 表单数据
const emailForm = reactive<MailLoginProp>({
  username:
    (route.query.username as string) ||
    (route.query.email as string) ||
    "" ||
    getCacheUsername(),
  mailCode: "",
  userOs: "",
  userAgent: "",
  userDevice: ""
});

// 计算属性
const mailCodeType = computed(() => {
  if (props.mode === "login") return "TRADE_USER_LOGIN";
  if (props.mode === "register") return "TRADE_USER_REGISTER";
  if (props.mode === "reset") return "TRADE_FORGET_PASSWORD";
  return "TRADE_USER_LOGIN";
});

const submitButtonText = computed(() => {
  if (props.mode === "login") return t("trade_common_login");
  if (props.mode === "register") return t("trade_login_verifyEmail");
  if (props.mode === "reset") return t("trade_login_verifyEmail");
  return t("trade_common_login");
});

// 表单验证规则
const emailRules = computed<FormRules<MailLoginProp>>(() => ({
  username: [
    {
      required: true,
      message: t("trade_login_mailText"),
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        if (!isEmail(value)) {
          callback(new Error(t("trade_login_truemail")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  mailCode: [
    {
      required: true,
      message: t("trade_common_inputCaptcha"),
      trigger: "blur"
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && value.length === 6) {
          callback();
        } else {
          callback(new Error(t("trade_users_mail_code_is_not_correct")));
        }
      },
      trigger: "blur"
    }
  ]
}));

const mailCodeValidateStatus = ref<boolean>(true);
const handleValidate = (prop: string, isValid: boolean) => {
  if (prop === "mailCode") {
    mailCodeValidateStatus.value = isValid;
  }
};

// 事件定义
const emit = defineEmits<{
  handleLoginSuccess: [data: any];
  handleEmailVerified: [email: string];
}>();

// 发送验证码
const sendVerifyCode = async () => {
  try {
    await emailFormRef.value?.validateField("username");
  } catch (error) {
    return;
  }

  sendingCaptcha.value = true;
  respMessage.value = "";

  try {
    const sendVerify: VerifyCodeProp = {
      email: emailForm.username,
      mailCodeType: mailCodeType.value
    };

    const response = await getSendVerifyCode(sendVerify);

    if (response.code !== 0) {
      respMessage.value = response.node || response.msg;
      ElMessage.error(response.msg);
    } else {
      beginCountDown.value = true;
      timeCode.value = Date.now() + 1000 * 60;
    }
  } catch (error: any) {
    console.error("发送验证码失败:", error);
    const errorMsg = error?.response?.data?.msg || "发送验证码失败";
    respMessage.value = errorMsg;
    ElMessage.error(errorMsg);
  } finally {
    sendingCaptcha.value = false;
  }
};

// 倒计时结束
const countDownFinish = () => {
  beginCountDown.value = false;
};

// 处理提交
const handleSubmit = async () => {
  if (!emailFormRef.value) return;

  try {
    await emailFormRef.value.validate();
  } catch (error) {
    return;
  }

  submitLoading.value = true;
  respMessage.value = "";

  try {
    if (props.mode === "login") {
      // 登录逻辑
      const parser = new UAParser();
      emailForm.userDevice = parser.getDevice().model as string;
      emailForm.userOs = `${parser.getOS().name} ${parser.getOS().version}`;
      emailForm.userAgent = parser.getUA();

      const response = await mailLogin(emailForm);

      if (response.code !== 0) {
        respMessage.value = response.node || response.msg;
        ElMessage.error(response.msg);
      } else {
        emit("handleLoginSuccess", response.data);
      }
    } else {
      // 注册验证逻辑
      const checkMailCodeForm: VerifyCodeEmailProp = {
        email: emailForm.username,
        mailCode: emailForm.mailCode,
        //@ts-ignore
        mailCodeType: mailCodeType.value
      };

      const response = await checkMailCode(checkMailCodeForm);

      if (response.code !== 0) {
        respMessage.value = response.node || response.msg;
        ElMessage.error(response.msg);
      } else {
        emit("handleEmailVerified", emailForm.username);
      }
    }
  } catch (error: any) {
    console.error("提交失败:", error);
    const errorMsg = error?.response?.data?.msg || "操作失败，请重试";
    respMessage.value = errorMsg;
    ElMessage.error(errorMsg);
  } finally {
    submitLoading.value = false;
  }
};

// 暴露清除错误信息的方法
const clearErrorMessage = () => {
  respMessage.value = "";
};

const refreshUsername = () => {
  const savedUsername = sessionStorage.getItem("username");
  emailForm.username = savedUsername;
};

// 组件挂载时初始化
onMounted(() => {
  refreshUsername();
});

// 监听表单变化，清除错误信息
watch(
  () => emailForm,
  () => {
    respMessage.value = "";
  },
  { deep: true }
);

watch(
  () => emailForm.username,
  newVal => {
    cacheUsername(newVal);
  }
);

defineExpose({
  refreshUsername,
  clearErrorMessage
});
</script>

<style lang="scss" scoped>
@use "./index.scss";
</style>
