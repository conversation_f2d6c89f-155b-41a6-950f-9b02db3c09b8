<template>
  <LkDialog
    ref="LkDialogRef"
    :title="
      dialogStatus === 'add'
        ? t('trade_common_createRoleTitle')
        : t('trade_sccs_editRoles')
    "
    class="lk-maximum-dialog"
    @confirm="confrimLkDialog"
    @close="handleClose"
  >
    <template #default>
      <div class="create-team-dialog-form">
        <el-form
          ref="ruleFormRef"
          label-position="top"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
        >
          <el-form-item :label="t('trade_sccs_roleName')" prop="name">
            <el-input
              v-model="ruleForm.name"
              :placeholder="t('trade_common_inputText')"
              show-word-limit
              clearable
              maxlength="100"
            />
          </el-form-item>
          <el-form-item :label="t('trade_team_members')">
            <LkTeamSelectV2
              v-model="ruleForm.teamMemberIdList"
              filterable
              :options="memberList"
              clearable
              :placeholder="t('trade_common_selectText')"
              value-key="id"
              :props="{ label: 'username', value: 'teamMemberId' }"
              multiple
              :teamRenderFields="{
                avatar: 'avatar',
                label: 'username'
              }"
            />
          </el-form-item>
          <el-form-item class="role-permission-body">
            <SccsSettingEditCheckBox
              ref="SccsBaseToolRef"
              :sccsSettingData="sccsOrderSettingData"
              :sccsToolRolePermItemList="SccsRoleBusinessDetails"
            />
            <SccsSettingOrderCheckBox
              ref="SccsOrderRoleRef"
              :sccsSettingData="sccsOrderMilestoneData"
              :orderRolePerm="SccsRoleBusinessDetails.orderRolePermItemList"
              @handlePermissionValueChange="handlePermissionValueChange"
            />
            <SccsSettingMilestoneCheckBoxNext
              ref="SccsMilestoneRoleRef"
              :sccsSettingData="sccsOrderMilestoneData1"
              :orderScope="msOrderScope"
              :msRolePermData="SccsRoleBusinessDetails"
              @handleUpdateMsPermissionKey="handleUpdateOrderData"
            />
          </el-form-item>
        </el-form>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { reactive, ref } from "vue";
import LkDialog from "@/components/lkDialog/index";
import type { FormRules } from "element-plus";
import SccsSettingEditCheckBox from "./SccsSettingEditCheckBox.vue";
import SccsSettingOrderCheckBox from "./SccsSettingOrderCheckBox.vue";
import SccsSettingMilestoneCheckBoxNext from "./SccsSettingMilestoneCheckBoxNext.vue";
import {
  getSccsCoopRolePermissionTrees,
  createSccsCoopRole,
  updateSccsCoopRole,
  getSccsCoopRoleDetails,
  getSccsMemberList
} from "@/api/sccs";
import { message } from "@/utils/message";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";

interface RoleListProp {
  name: string;
  teamMemberIdList: string[];
}

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
const LkDialogRef = ref<HTMLElement | any>(null);
const SccsBaseToolRef = ref<HTMLElement | any>(null);
const SccsOrderRoleRef = ref<HTMLElement | any>(null);
const SccsMilestoneRoleRef = ref<HTMLElement | any>(null);
const ruleFormRef = ref<HTMLElement | any>(null);
const sccsOrderSettingData = ref<any>([]);
const sccsOrderMilestoneData1 = ref<any>([]);
const sccsOrderMilestoneData = ref<any>([]);
const SccsRoleBusinessDetails = ref<any>({});
const msOrderScope = ref<string>("");
let ruleForm = reactive<RoleListProp>({
  name: "",
  teamMemberIdList: []
});
const rules = reactive<FormRules<RoleListProp>>({
  name: [
    {
      required: true,
      message: t("trade_common_roleTip"),
      trigger: "blur"
    }
  ]
});
const memberList = ref<any[]>([]);
const dialogStatus = ref<string>("add");
const rowDetailId = ref<string>("");

const emit = defineEmits<{
  (e: "updateSccsSuccess"): void;
}>();

const handleClose = (): void => {
  ruleForm.name = "";
  ruleForm.teamMemberIdList = [];
  ruleFormRef.value.clearValidate();
};

const handlePermissionValueChange = (permissionValue: any) => {
  msOrderScope.value = permissionValue[0].orderScope;
};

const confrimLkDialog = async (): Promise<void> => {
  const sccsToolRolePermItemList = SccsBaseToolRef.value.getData();
  const orderRolePermItemList = SccsOrderRoleRef.value.getPermissionValue();
  const { milestoneRolePermItemList, milestonePermItem } =
    SccsMilestoneRoleRef.value.handleOtainMilestoneRoleData();

  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      const submitForm = {
        sccsId: props.basicInfo.id,
        orderRolePermItemList: orderRolePermItemList,
        sccsToolRolePermItemList: sccsToolRolePermItemList,
        milestoneRolePermItemList: milestoneRolePermItemList,
        milestonePermItem: milestonePermItem,
        ...ruleForm
      };
      const { code } =
        dialogStatus.value === "add"
          ? await createSccsCoopRole(submitForm)
          : await updateSccsCoopRole({
              ...submitForm,
              id: rowDetailId.value
            });
      if (code === 0) {
        message(t("trade_common_dealSuccess"), {
          customClass: "el",
          type: "success"
        });
        LkDialogRef.value.close();
        ruleFormRef.value.resetFields();
        handleClose();
        emit("updateSccsSuccess");
      }
    }
  });
};

const handleUpdateOrderData = (bool: boolean) => {
  if (bool) {
    SccsOrderRoleRef.value?.handleCheckAllChange(true);
  }
};

const open = async (row: any): Promise<void> => {
  getSccsMemberListFn();
  const { data } = await getSccsCoopRolePermissionTrees({
    sccsId: props.basicInfo.id,
    isDocking: false
  });
  sccsOrderSettingData.value = data[0];
  sccsOrderMilestoneData.value = data[1];
  sccsOrderMilestoneData1.value = data[2];
  if (row && row.id) {
    dialogStatus.value = "edit";
    const {
      id,
      name,
      teamMemberInfoList,
      sccsToolRolePermItemList,
      orderRolePermItemList,
      milestoneRolePermItemList,
      milestonePermItem,
      milestoneMessageSettingItemList
    } = row;
    rowDetailId.value = id;
    ruleForm.name = name;
    ruleForm.teamMemberIdList = Array.from(
      new Set(teamMemberInfoList.map(item => item.teamMemberId))
    );
    SccsRoleBusinessDetails.value = {
      sccsToolRolePermItemList: sccsToolRolePermItemList,
      orderRolePermItemList: orderRolePermItemList,
      milestoneRolePermItemList: milestoneRolePermItemList,
      milestonePermItem: milestonePermItem,
      milestoneMessageSettingItemList: milestoneMessageSettingItemList
    };
  } else {
    dialogStatus.value = "add";
    SccsRoleBusinessDetails.value = {
      sccsToolRolePermItemList: [],
      orderRolePermItemList: [],
      milestoneRolePermItemList: [],
      milestonePermItem: [],
      milestoneMessageSettingItemList: []
    };
  }

  LkDialogRef.value.open();
};

const getSccsMemberListFn = async (): Promise<void> => {
  const { code, data } = await getSccsMemberList({
    sccsId: props.basicInfo.id
  });
  memberList.value = data;
};

const setDetailData = async id => {
  const { data } = await getSccsCoopRoleDetails({ id: id });
  ruleForm.name = data.name;
  ruleForm.teamMemberIdList = data.teamMemberIdList;
  SccsRoleBusinessDetails.value = {
    sccsToolRolePermItemList: data.sccsToolRolePermItemList,
    orderRolePermItemList: data.orderRolePermItemList,
    milestoneRolePermItemList: data.milestoneRolePermItemList,
    milestonePermItem: data.milestonePermItem
  };
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.small-text {
  margin-top: 7px;
  font-size: 12px;
  line-height: 16px;
  color: #8c8c8c;
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
