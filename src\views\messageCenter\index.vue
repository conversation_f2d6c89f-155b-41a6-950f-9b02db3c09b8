<template>
  <div
    ref="messageCenterMainContainerRef"
    class="message-center-main-container"
  >
    <div v-if="drawerVisible" class="message-center-body">
      <el-drawer
        v-model="drawerVisible"
        :width="420"
        direction="ltr"
        class="message-center-drawer"
        :modal="true"
        modal-class="message-center-drawer-modal"
        :with-header="false"
        :z-index="1930"
        :esc-to-close="true"
      >
        <div class="message-center-header">
          <div class="message-center-left">
            <div
              class="message-center-search-col"
              :class="{ active: notifyMessageType === 'all' }"
              @click="handleSwitchMessageCenter('all')"
            >
              {{ t("trade_common_all") }}
            </div>
            <div
              class="message-center-search-col"
              :class="{ active: notifyMessageType === 'unread' }"
              @click="handleSwitchMessageCenter('unread')"
            >
              <el-badge
                :value="totalUnreadCount"
                :max="99"
                :show-zero="false"
                :offset="[2, 16]"
                :badge-style="{
                  padding: '0 4px',
                  // width: '18px',
                  'font-size': '10px'
                }"
              >
                {{ t("trade_common_unread") }}
              </el-badge>
            </div>
          </div>
          <div class="message-center-right">
            <el-popover
              trigger="click"
              placement="bottom"
              :width="200"
              :popper-style="{ padding: '8px 12px', zIndex: 3010 }"
              :append-to="messageCenterMainContainerRef"
            >
              <template #reference>
                <i class="iconfont link-more" />
              </template>
              <div class="message-center-popover">
                <div class="message-center-title">
                  <div class="message-center-notice-title">
                    {{ t("trade_message_realTimeNotification") }}
                  </div>
                  <div class="message-center-notice-widget">
                    <el-switch v-model="notifyEnabled" />
                  </div>
                </div>
                <div class="message-center-tip">
                  {{ t("trade_message_noticeTip") }}
                </div>
              </div>
            </el-popover>
            <i
              v-show="!!totalUnreadCount"
              class="iconfont link-read font12"
              @click="handleReadAllNotice"
            />
            <i class="iconfont link-close font12" @click="handleClosePopover" />
          </div>
        </div>
        <div class="message-center-container">
          <div
            v-for="messageItem in notifyList"
            :key="messageItem.id"
            :class="[
              'message-center-col',
              { active: currentMessageType === messageItem.messageType }
            ]"
            @click="handleEdit(messageItem.messageType, messageItem.id)"
          >
            <div class="message-center-left">
              <el-badge
                :value="messageItem.unreadCount"
                :max="99"
                :show-zero="false"
                :offset="[0, 3]"
                :badge-style="{
                  padding: '0 4px',
                  // width: '18px',
                  'font-size': '10px'
                }"
              >
                <i
                  class="iconfont message-icon"
                  :class="messageItem.messageType"
                />
              </el-badge>
            </div>
            <div class="message-center-right">
              <div class="message-center-right-title">
                <span class="message-center-title-text">
                  {{ messageItem.templateTitle }}
                </span>
                <span class="message-center-title-time">
                  {{
                    dayjs(new Date(messageItem.createTime)).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )
                  }}
                </span>
              </div>
              <div class="message-center-right-body">
                <ReText
                  :key="messageItem.templateContent"
                  lineClamp="2"
                  :tippyProps="{ delay: 50000 }"
                >
                  <div
                    class="message-center-right-tip"
                    v-html="messageItem.templateContent"
                  />
                </ReText>
                <i class="iconfont link-arrow-right" />
              </div>
              <div class="message-center-footer">
                <div class="message-center-left">
                  <LKAvater
                    class="colorOrange"
                    :size="18"
                    :teamInfo="{
                      avatar: messageItem.toTeamAvatar,
                      username: messageItem.toTeamName
                    }"
                    shape="square"
                  />
                  <span class="message-center-title-name">
                    {{ messageItem.toTeamName }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="notifyList.length === 0" class="message-center-empty">
            <el-empty
              :description="t('trade_common_emptyTip')"
              :image-size="400"
              :image="emptyImage"
            />
          </div>
        </div>
        <MessageNextDrawer
          ref="MessageNextDrawerRef"
          @handleUpdate="handleUpdate"
          @handleGotoMessage="handleGotoMessage"
        />
      </el-drawer>
    </div>
    <MessageCenterTooltip />
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import LKAvater from "@/components/lkAvatar/index";
import MessageNextDrawer from "./components/MessageNextDrawer.vue";
import MessageCenterTooltip from "./components/MessageCenterTooltip.vue";
import ReText from "@/components/ReText";
import { setMessageNotify } from "@/api/message";
import emptyImage from "@/assets/images/message/drawer-message-center.png";
import { useMessageCenterStoreHook } from "@/store/modules/messageCenter";
import { storeToRefs } from "pinia";
import { onClickOutside } from "@vueuse/core";

const { t } = useI18n();
const drawerVisible = ref<boolean>(false);
const MessageNextDrawerRef = ref<HTMLElement | any>(null);
const notifyMessageType = ref<string>("all");
const currentMessageType = ref<string>("");
const messageCenterMainContainerRef = ref<HTMLElement | any>(null);

onClickOutside(messageCenterMainContainerRef, () => {
  drawerVisible.value = false;
});

// 使用消息中心 store
const messageCenterStore = useMessageCenterStoreHook();
const {
  fetchMessageCenterData,
  markAllMessagesAsRead,
  resetUnreadMessageList
} = messageCenterStore;
const { messageList, unreadMessageList, totalUnreadCount, notifyEnabled } =
  storeToRefs(messageCenterStore);

// 计算当前显示的消息列表
const notifyList = computed(() => {
  return notifyMessageType.value === "all"
    ? messageList.value
    : unreadMessageList.value;
});

const open = async (): Promise<void> => {
  await fetchMessageCenterData();
  drawerVisible.value = true;
};

const close = () => {
  drawerVisible.value = false;
};

const handleSwitchMessageCenter = (messageType: string) => {
  notifyMessageType.value = messageType;
  currentMessageType.value = undefined;
  resetUnreadMessageList();
  MessageNextDrawerRef.value.close();
};

// 移除 handleInitNoticeData，直接使用 store 的方法

const handleEdit = async (messageType: string, id: string): Promise<void> => {
  currentMessageType.value = messageType;
  MessageNextDrawerRef.value.open(
    messageType,
    notifyMessageType.value === "unread" ? 0 : undefined
  );
};

const handleUpdate = async (messageType: string): Promise<void> => {
  MessageNextDrawerRef.value.open(
    messageType,
    notifyMessageType.value === "unread" ? 0 : undefined
  );
  await fetchMessageCenterData();
};

const handleGotoMessage = () => {
  close();
};

const handleReadAllNotice = async () => {
  try {
    await markAllMessagesAsRead();
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
  } catch (error) {
    console.error("标记所有消息已读失败:", error);
  }
};

const handleClosePopover = () => {
  drawerVisible.value = false;
};

const handleSetMessageNotify = async (): Promise<void> => {
  const { code } = await setMessageNotify({
    notifyEnable: notifyEnabled.value
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
  }
};

defineExpose({
  open,
  close,
  visible: drawerVisible.value,
  toggle: () => {
    if (drawerVisible.value) {
      close();
    } else {
      open();
    }
  }
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.message-center-drawer-modal {
  background: rgb(58 58 58 / 45%);
}

.message-center-header {
  position: relative;
  z-index: 2030 !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 55px;
  padding: 0 20px;
  border-bottom: 1px solid #e3e5e9;

  .message-center-left {
    display: flex;
    flex: 3;
    align-items: center;
    height: 100%;

    .message-center-search-col {
      position: relative;
      height: 100%;
      padding: 0 10px;
      font-size: 18px;
      font-weight: bolder;
      line-height: 54px;
      color: #262626;
      text-align: left;
      cursor: pointer;

      &.active {
        color: #0070d2;

        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          display: inline-block;
          width: 100%;
          height: 2px;
          content: "";
          background: #0070d2;
        }
      }
    }
  }

  .message-center-right {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: flex-end;
    width: 100px;
    height: 100%;

    .font12 {
      font-size: 12px;
    }

    .iconfont {
      cursor: pointer;

      &:hover {
        color: #0070d2;
      }
    }
  }
}

.message-center-popover {
  .message-center-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 9px;

    .message-center-notice-title {
      font-size: 14px;
      font-weight: bolder;
      line-height: 20px;
      color: #262626;
      text-align: right;
    }
  }

  .message-center-tip {
    font-size: 12px;
    line-height: 17px;
    color: #808080;
    text-align: left;
  }
}

.message-center-container {
  position: relative;
  z-index: 2030 !important;

  .message-center-col {
    display: flex;
    padding: 18px 16px 16px 20px;
    cursor: pointer;
    border-bottom: 1px solid #e3e5e9;

    &.active,
    &:hover {
      background: #f2f2f2;
    }

    .message-center-left {
      width: 42px;

      .message-icon {
        display: inline-block;
        width: 32px;
        height: 32px;
        line-height: 32px;
        color: #fff;
        text-align: center;
        border-radius: 50%;

        &.link-news-inform {
          background: #fdc333;
        }

        &.link-news-pa {
          background: #7b69e6;
        }

        &.link-news-ta {
          background: #3db0e1;
        }

        &.link-news-task {
          background: #3b8fea;
        }
      }
    }

    .message-center-right {
      flex: 1;
      min-width: 0;

      .message-center-right-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 20px;
        color: #595959;
        text-align: left;

        .message-center-title-text {
          flex: 1;
          min-width: 0;
          margin-right: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .message-center-title-time {
          flex-shrink: 0;
          font-size: 12px;
          color: #8c8c8c;
          white-space: nowrap;
        }
      }

      .message-center-right-body {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .message-center-right-tip {
          display: -webkit-box;
          flex: 1;
          overflow: hidden;
          font-size: 14px;
          line-height: 20px;
          color: #262626;
          text-align: left;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          word-break: break-all;
          -webkit-box-orient: vertical;
        }

        .iconfont {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      .message-center-footer {
        display: flex;
        align-items: center;
        margin-top: 9px;

        .message-center-left {
          display: inline-flex;
          flex: 1;
          align-items: center;
          margin-right: 20px;

          .message-center-title-name {
            margin-left: 2px;
            overflow: hidden;
            font-size: 12px;
            line-height: 17px;
            color: #8c8c8c;
            text-align: left;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.message-center-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 56px);
}
</style>
