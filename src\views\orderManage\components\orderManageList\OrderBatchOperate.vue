<template>
  <el-tooltip
    :content="t('trade_common_bulk_operation')"
    :disabled="!!buttonTextVisible"
  >
    <el-dropdown
      trigger="click"
      class="order-manage-btn"
      :disabled="selectedNumber <= 0"
      :class="[
        selectedNumber <= 0 ? 'order-manage-disabled-btn' : '',
        currentScreenWidth <= 1366 ? 'Screen_1366' : ''
      ]"
      @command="handleBatchOperate"
    >
      <span class="el-dropdown-link">
        <i class="iconfont link-batch-operation" />
        {{ buttonTextVisible ? t("trade_common_bulk_operation") : "" }}
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-if="userPermissionBoolean('batch_edit_order')"
            command="edit"
          >
            {{ t("trade_batch_editing") }}
          </el-dropdown-item>
          <el-dropdown-item
            v-if="userPermissionBoolean('batch_create_work_order')"
            command="createWorkOrder"
          >
            {{ t("trade_batch_create_workOrder") }}
          </el-dropdown-item>
          <el-dropdown-item
            v-if="userPermissionBoolean('batch_update_order_status')"
            command="modifyOrderStatus"
          >
            {{ t("trade_batch_modify_order_status") }}
          </el-dropdown-item>
          <el-dropdown-item
            v-if="userPermissionBoolean('batch_update_milestone_status')"
            command="modifyMilestoneStatus"
          >
            {{ t("trade_batch_modify_milestone_status") }}
          </el-dropdown-item>
          <el-dropdown-item command="download">
            {{ t("trade_order_batchDownloadFiles") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </el-tooltip>
  <LkDialog
    ref="downloadFileRef"
    :title="t('trade_order_batchDownloadFiles')"
    @confirm="handleDownloadFileConfirm"
    @close="handleCloseDownloadDialog"
  >
    <template #default>
      <div class="download-title">
        {{ t("trade_common_selected") }}
        {{ selectedNumber }}
        {{ t("trade_common_strip") }}
      </div>
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-position="top"
      >
        <el-form-item
          :label="t('trade_common_DownloadFileTip')"
          prop="widgetIds"
        >
          <el-select-v2
            v-model="ruleForm.widgetIds"
            filterable
            :options="fileWidgetOptions"
            :placeholder="t('trade_order_downFilesTip')"
            style="width: 100%"
            multiple
            clearable
          />
        </el-form-item>
      </el-form>
    </template>
  </LkDialog>
  <BatchModifyMsStatusDialog
    ref="modifyMilestoneStatusRef"
    :currentSccsId="currentSccsId"
    :selectedNumber="selectedNumber"
    :tableSelectedList="tableSelectedList"
    @confirm="handleModifyMilestoneStatusConfirm"
    @close="handleCloseModifyMilestoneStatusDialog"
    @handleReloadTableData="handleReloadTableData"
  />
  <BatchModifyOrderStatusDialog
    ref="modifyOrderStatusRef"
    :currentSccsId="currentSccsId"
    :selectedNumber="selectedNumber"
    :tableSelectedList="tableSelectedList"
    @confirm="handleModifyOrderStatusConfirm"
    @close="handleCloseModifyOrderStatusDialog"
    @handleReloadTableData="handleReloadTableData"
  />
</template>
<script lang="ts" setup>
import { ref, reactive, computed, inject } from "vue";
import { useI18n } from "vue-i18n";
import { type FormRules } from "element-plus";
import { useRoute } from "vue-router";
import LkDialog from "@/components/lkDialog/index";
import BatchModifyMsStatusDialog from "./BatchModifyMsStatusDialog.vue";
import BatchModifyOrderStatusDialog from "./BatchModifyOrderStatusDialog.vue";
import { getSccsFileWidgetList, batchDownloadFiles } from "@/api/order";
import { storageSession } from "@pureadmin/utils";

const props = defineProps({
  currentSccsId: {
    type: String as PropType<any>,
    default: ""
  },
  selectedNumber: {
    type: Number as PropType<any>,
    default: 0
  },
  tableSelectedList: {
    type: Array as PropType<any>,
    default: () => []
  },
  currentScreenWidth: {
    type: Number as PropType<any>,
    default: 0
  },
  presentView: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const buttonTextVisible = inject("buttonTextVisible");

const { t } = useI18n();
const route = useRoute();
const downloadFileRef = ref<HTMLElement | any>(null);
const ruleFormRef = ref<HTMLElement | any>(null);
const fileWidgetOptions = ref<any>([]);
const isIpadScreen = computed(() => {
  return props.currentScreenWidth <= 1366;
});
const ruleForm = ref<any>({
  widgetIds: []
});
const rules = reactive<FormRules<any>>({
  widgetIds: [
    {
      required: true,
      message: t("trade_order_downFilesTip"),
      trigger: "change"
    }
  ]
});
const userPermissionBoolean = str => {
  const userPermission: any[] = storageSession().getItem("userSccsPerm");
  if (userPermission.includes(str)) {
    return true;
  } else {
    return false;
  }
};

// 批量修改里程碑状态
const modifyMilestoneStatusRef = ref<HTMLElement | any>(null);
const handleModifyMilestoneStatusConfirm = () => {};
const handleCloseModifyMilestoneStatusDialog = () => {};

// 批量修改订单状态
const modifyOrderStatusRef = ref<HTMLElement | any>(null);
const handleModifyOrderStatusConfirm = () => {};
const handleCloseModifyOrderStatusDialog = () => {};

const handleBatchOperate = async (operateType: string) => {
  if (operateType === "download") {
    const sccsId = route.query.sccsId as string;
    const { code, data } = await getSccsFileWidgetList({
      sccsId: sccsId,
      widgetReqModule:
        props.presentView.viewType === "MILESTONE" ? "WORK_ORDER" : "ORDER",
      milestoneId:
        props.presentView.viewType === "MILESTONE" ? props.presentView.msId : ""
    });
    if (code === 0) {
      fileWidgetOptions.value = data;
      downloadFileRef.value.open();
    }
  } else if (operateType === "modifyMilestoneStatus") {
    // 1.打开弹窗
    modifyMilestoneStatusRef.value.open();
  } else if (operateType === "modifyOrderStatus") {
    // 1.打开弹窗
    modifyOrderStatusRef.value.open();
  }
};

const handleCloseDownloadDialog = () => {
  ruleFormRef.value.resetFields();
  ruleForm.value.widgetIds = [];
  downloadFileRef.value.close();
};

const handleDownloadFileConfirm = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async valid => {
    if (valid) {
      const gridCheckRecords = props.tableSelectedList;
      let orderIds = [];
      for (let record of gridCheckRecords) {
        if (props.presentView.viewType !== "MILESTONE") {
          orderIds.push(record.orderId);
        } else {
          orderIds.push(record.workOrderId);
        }
      }
      const { sccsId, sccsName } = route.query;
      const { code, data } = await batchDownloadFiles({
        sccsId: sccsId,
        sccsName: sccsName,
        order: props.presentView.viewType !== "MILESTONE",
        idList: orderIds,
        widgetIdList: ruleForm.value.widgetIds
      });
      if (code === 0) {
        window.open(data, "_blank");
        handleCloseDownloadDialog();
      }
    }
  });
};
const emit = defineEmits(["handleReloadTableData"]);

const handleReloadTableData = () => {
  emit("handleReloadTableData");
};
</script>
<style lang="scss" scoped>
@use "../index.scss";
</style>
