<template>
  <template
    v-if="widgetData && (widgetData.fromMoney || widgetData.targetMoney)"
  >
    <div class="widget-exchange-rate-row">
      <span
        v-if="widgetData.exchangeType === 'fixed'"
        class="widget-exchange-rate-unit"
      >
        {{ exchangeRateFixedTime }}
      </span>
      <span
        v-if="widgetData.exchangeType === 'realTime'"
        class="widget-exchange-rate-unit"
      >
        {{ exchangeRateRealTime }}
      </span>
      <span class="widget-exchange-rate">
        {{ widgetData.rate }}
      </span>

      <span class="widget-exchange-row-money">{{ widgetData.fromMoney }}</span>
      <span class="widget-exchange-row-money-type">
        {{ widgetData.fromMoneyType }}
      </span>
      <i class="iconfont link-switch-teams widget-exchange-icon" />
      <span class="widget-exchange-row-money">{{
        widgetData.targetMoney
      }}</span>
      <span class="widget-exchange-row-money-type">
        {{ widgetData.targetMoneyType }}
      </span>
    </div>
  </template>
  <div v-else>-</div>
</template>
<script lang="ts" setup>
import { storageLocal } from "@pureadmin/utils";
import { computed } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  widgetConfig: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();

const translationLang = storageLocal().getItem("translationLang");

/**
 * 获取汇率文本
 * @param type 汇率类型："fixed" 固定汇率，"real" 实时汇率
 * @returns {string}
 */
function getRateText(type: "fixed" | "real") {
  const rateObj = props.widgetConfig.props.rateObject;
  // 没有配置对象时，返回默认国际化文本
  if (!rateObj) {
    return t(
      type === "fixed"
        ? "trade_common_fixedExchangeRate"
        : "trade_common_realTimeExchangeRate"
    );
  }

  if (translationLang === "en") {
    return (
      rateObj[type === "fixed" ? "fixedEn" : "realEn"] ||
      rateObj[type === "fixed" ? "fixedCn" : "realCn"]
    );
  }

  return rateObj[type === "fixed" ? "fixedCn" : "realCn"];
}

const exchangeRateFixedTime = computed(() => getRateText("fixed"));
const exchangeRateRealTime = computed(() => getRateText("real"));
</script>

<style lang="scss" scoped>
.widget-exchange-rate-row {
  font-weight: normal;

  .widget-exchange-rate-unit {
    margin-right: 5px;
    font-size: 14px;
    line-height: 16px;
    color: #8c8c8c;
    text-align: left;
  }

  .widget-exchange-rate {
    margin-right: 30px;
    font-size: 14px;
    line-height: 16px;
    color: #262626;
    text-align: left;
  }

  .widget-exchange-row-money {
    margin-right: 5px;
    font-size: 14px;
    line-height: 16px;
    color: #262626;
    text-align: left;
  }

  .widget-exchange-icon {
    margin: 0 10px;
    font-size: 12px;
    color: #8c8c8c;
  }

  .widget-exchange-row-money-type {
    font-size: 14px;
    line-height: 20px;
    color: #808080;
    text-align: center;
  }
}
</style>
