<template>
  <LkDialog
    ref="LkDialogRef"
    :title="
      dialogStatus === 'add'
        ? t('trade_common_createRoleTitle')
        : t('trade_sccs_editRoles')
    "
    class="lk-maximum-dialog"
    @confirm="confrimLkDialog"
    @close="handleClose"
  >
    <template #default>
      <div class="create-team-dialog-form">
        <el-form
          ref="ruleFormRef"
          label-position="top"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          :scroll-to-error="true"
        >
          <el-form-item
            :label="t('trade_sccs_collaborativeRoleName')"
            prop="name"
          >
            <el-input
              v-model="ruleForm.name"
              :placeholder="t('trade_sccs_collaborativeRoleNameTip')"
              show-word-limit
              clearable
              maxlength="100"
              disabled
            />
          </el-form-item>
          <el-form-item :label="t('trade_team_members')">
            <LkTeamSelectV2
              v-model="ruleForm.teamMemberIdList"
              filterable
              :options="memberList"
              clearable
              :placeholder="t('trade_common_selectText')"
              value-key="id"
              :props="{ label: 'username', value: 'teamMemberId' }"
              multiple
              :teamRenderFields="{
                avatar: 'avatar',
                label: 'username'
              }"
            />
          </el-form-item>
          <el-form-item class="role-permission-body">
            <div class="role-mask" />
            <SccsOrderRoleTool
              ref="sccsOrderRoleToolRef"
              orderScope="coopTeam"
              :sccsOrderData="sccsOrderSettingData"
              :sccsMilestoneData="sccsOrderMilestoneData"
              :sccsToolRolePermItemList="SccsRoleBusinessDetails"
            />
          </el-form-item>
        </el-form>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { reactive, ref } from "vue";
import LkDialog from "@/components/lkDialog/index";
import type { FormRules } from "element-plus";
import { message } from "@/utils/message";
import LkTeamSelectV2 from "@/components/lkTeamSelectV2/index";
import SccsOrderRoleTool from "@/views/sccsManage/components/SccsOrderRoleTool.vue";
import {
  getSccsCoopRolePermissionTree,
  updateSccsCoopTeamRoleInCoopTeam,
  getSccsMemberList
} from "@/api/sccs";

interface RoleListProp {
  name: string;
  teamMemberIdList: string[];
}

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const sccsOrderRoleToolRef = ref<any>(null);
const ruleFormRef = ref<any>(null);
const sccsOrderSettingData = ref<any>([]);
const sccsOrderMilestoneData = ref<any>([]);
const SccsRoleBusinessDetails = ref<any>({});
let ruleForm = reactive<RoleListProp>({
  name: "",
  teamMemberIdList: []
});
const rules = reactive<FormRules<RoleListProp>>({
  name: [
    {
      required: true,
      message: t("trade_team_inputTeamRoleName"),
      trigger: "blur"
    }
  ]
});
const memberList = ref<any[]>([]);
const dialogStatus = ref<string>("add");
const rowDetailId = ref<string>("");

const emit = defineEmits<{
  (e: "updateSccsSuccess"): void;
}>();

const handleClose = (): void => {
  ruleForm.name = "";
  ruleForm.teamMemberIdList = [];
  ruleFormRef.value.clearValidate();
};

const confrimLkDialog = async (): Promise<void> => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      const commitData = sccsOrderRoleToolRef.value.handleObtainCommitData();
      const { code } = await updateSccsCoopTeamRoleInCoopTeam({
        id: rowDetailId.value,
        sccsId: props.basicInfo.id,
        ...commitData,
        ...ruleForm
      });
      if (code === 0) {
        message(t("trade_common_dealSuccess"), {
          customClass: "el",
          type: "success"
        });
        LkDialogRef.value.close();
        ruleFormRef.value.resetFields();
        handleClose();
        emit("updateSccsSuccess");
      }
    }
  });
};

const open = async (row): Promise<void> => {
  getSccsMemberListFn();
  const { data } = await getSccsCoopRolePermissionTree({
    sccsId: props.basicInfo.id
  });
  sccsOrderSettingData.value = data[0];
  sccsOrderMilestoneData.value = data[1];
  if (row && row.id) {
    dialogStatus.value = "edit";
    const {
      id,
      name,
      teamMemberInfoList,
      sccsToolRolePermItemList,
      orderRolePermItemList,
      milestoneRolePermItemList,
      milestonePermItem
    } = row;
    rowDetailId.value = id;
    ruleForm.name = name;

    ruleForm.teamMemberIdList = Array.from(
      new Set(teamMemberInfoList.map(item => item.teamMemberId))
    );
    SccsRoleBusinessDetails.value = {
      sccsToolRolePermItemList: sccsToolRolePermItemList,
      orderRolePermItemList: orderRolePermItemList,
      milestoneRolePermItemList: milestoneRolePermItemList,
      milestonePermItem: milestonePermItem
    };
  } else {
    dialogStatus.value = "add";
    SccsRoleBusinessDetails.value = {
      sccsToolRolePermItemList: [],
      orderRolePermItemList: [],
      milestoneRolePermItemList: [],
      milestonePermItem: []
    };
  }

  LkDialogRef.value.open();
};

const getSccsMemberListFn = async (): Promise<void> => {
  const { code, data } = await getSccsMemberList({
    sccsId: props.basicInfo.id
  });
  memberList.value = data;
};

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.small-text {
  margin-top: 7px;
  font-size: 12px;
  line-height: 16px;
  color: #8c8c8c;
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  position: relative;
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  .role-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2000;
    width: 100%;
    height: 100%;
    cursor: not-allowed;
    background: transparent;
  }

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}
</style>
