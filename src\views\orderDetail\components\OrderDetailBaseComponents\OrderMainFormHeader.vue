<template>
  <Transition
    leave-active-class="animate__animated animate__fadeOut animate__faster"
    enter-active-class="animate__animated animate__fadeIn animate__faster"
  >
    <div v-show="mainFormVisible" class="main-form-container">
      <el-scrollbar class="horizontal-scrollbar-only" height="400px">
        <div class="main-form-header">{{ t("trade_common_orderMessage") }}</div>
        <OrderDetailDescWidget
          v-if="mainFormData"
          ref="orderDetailRef"
          :widgetForm="mainFormData.widgetForm"
          :widgetData="mainFormData.widgetData"
        />
      </el-scrollbar>
    </div>
  </Transition>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";

defineProps({
  mainFormData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();

const mainFormVisible = ref<boolean>(false);

const handleOpenMainFormVisible = () => {
  mainFormVisible.value = !mainFormVisible.value;
};

defineExpose({
  open: () => (mainFormVisible.value = true),
  close: () => (mainFormVisible.value = false),
  visible: mainFormVisible,
  handleOpenMainFormVisible
});
</script>
<style lang="scss" scoped>
.main-form-header {
  margin-bottom: 24px;
  font-size: 16px;
  font-weight: bolder;
  line-height: 16px;
  color: #262626;
}

.main-form-container {
  position: absolute;
  top: 54px;
  z-index: 2200;
  width: 100%;
  padding: 43px 34px;
  margin-right: 24px;
  background: #fff;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 8px 13px 0 rgb(0 0 0 / 10%);
}

.order-detail-descriptions-widget {
  overflow-x: hidden;
}
</style>
