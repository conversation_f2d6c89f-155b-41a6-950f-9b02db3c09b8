<template>
  <div v-if="widgetForm?.length > 0" class="order-detail-descriptions-widget">
    <div
      v-for="widget in widgetForm"
      :key="widget['_fc_id']"
      class="order-detail-descriptions-row"
    >
      <div
        v-if="widget.type === 'DrPlaceholder'"
        :style="{
          display: 'inline-block',
          height: `${widget.props.height}px`
        }"
      />
      <div v-else-if="['DrCard'].includes(widget.type)">
        <WidgetCard
          :widgetData="widgetData"
          :widget="widget"
          :visibleFormExpression="visibleFormExpression"
        />
      </div>
      <el-row v-else>
        <el-col
          v-if="
            !(
              visibleFormExpression.hasOwnProperty(widget._fc_id) &&
              !visibleFormExpression[widget._fc_id]
            )
          "
          :span="24"
        >
          <div v-if="widget.type !== 'DrDivider'" class="widget-form-label">
            <div class="widget-form-label-text">
              <el-tooltip
                v-if="widget.info"
                :content="widgetInfoLange(widget)"
                placement="top"
                popper-class="widget-popper-label-class"
              >
                <i class="iconfont link-explain font12" />
              </el-tooltip>
              {{ widgetTitleLange(widget) }}
            </div>
          </div>
          <div class="widget-form-value">
            <LkWidgetForm
              :widgetData="getWidgetControlData(widget._fc_id, widget)"
              :widgetDataRow="getWidgetControlDataVal(widget._fc_id)"
              :widgetConfig="widget"
              :widgetTableData="getWidgetControlDataVal(widget._fc_id)"
              :visibleFormExpression="visibleFormExpression"
            />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
  <el-empty
    v-else
    style="background: #fff"
    :description="t('trade_common_emptyTip')"
    :image-size="314"
    :image="sccsGroupNoImage"
  />
</template>
<script lang="ts" setup>
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import LkWidgetForm from "@/components/lkWidgetForm";
import WidgetCard from "@/views/orderDetail/components/OrderDescribeWidget/widgetCard.vue";
import sccsGroupNoImage from "@/assets/images/sccs/sccs-no-image.png";
import { storageLocal } from "@pureadmin/utils";

const props = defineProps({
  taskMode: {
    type: String as PropType<string>,
    default: () => ""
  },
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  relateFormId: {
    type: String as PropType<string>,
    default: ""
  }
});

const { t } = useI18n();
const visibleFormExpression = ref<any>({});

const widgetInfoLange = computed(() => {
  return widget => {
    const translationLang = storageLocal().getItem("translationLang");
    return translationLang === "en"
      ? widget.props?.infoEn || widget.info
      : widget.info;
  };
});

const widgetTitleLange = computed(() => {
  return widget => {
    const translationLang = storageLocal().getItem("translationLang");
    return translationLang === "en"
      ? widget.props?.titleEn || widget.title
      : widget.title;
  };
});

const getWidgetControlData = computed(() => {
  return (widgetId: string, widget: any): string => {
    if (props.widgetData instanceof Array) {
      const widgetData = props.widgetData.find(
        widgetRow => widgetRow.widgetId === widgetId
      );

      return widget && widgetData
        ? [
            "DrFilesUpload",
            "DrImagesUpload",
            "DrExchangeRates",
            "DrCheckbox",
            "DrRelateCard"
          ].includes(widget.type)
          ? widgetData.obj
          : widgetData.label
        : "";
    } else {
      return "";
    }
  };
});

const getWidgetControlDataVal = computed(() => {
  return (widgetId: string): any => {
    if (props.widgetData instanceof Array) {
      const widgetData = props.widgetData.find(
        widgetRow => widgetRow.widgetId === widgetId
      );
      return widgetData ? widgetData : "";
    } else {
      return "";
    }
  };
});
</script>
