<template>
  <div class="user-login-container">
    <div class="user-login-title">{{ t("trade_login_userLogin") }}</div>
    <div class="user-login-tip">
      {{ t("trade_login_noAccount") }}
      <span class="middle-button" @click="handleRouter('/register')">
        {{ t("trade_login_register") }}
      </span>
    </div>
    <div class="user-login-form-item">
      <PasswordLogin
        v-show="loginModel === 'password'"
        ref="passwordRef"
        :remember="rememberMeCheck"
        @handleLoginSuccess="handleLoginSuccess"
      />
      <CheckMailLogin
        v-show="loginModel !== 'password'"
        ref="checkMailLoginRef"
        @handleLoginSuccess="handleLoginSuccess"
      />
      <div class="user-login-form-container-bottom">
        <div v-if="loginModel === 'password'" class="user-login-form-top">
          <div class="user-login-form-top-left">
            <el-checkbox
              v-model="rememberMeCheck"
              :label="t('trade_common_rememberMe')"
              size="large"
              @change="handleRemberMe"
            />
            <span
              class="el-button-custom-text font12"
              @click="handleRouter('/resetPassword')"
            >
              {{ t("trade_common_forgetPassword") }}
            </span>
          </div>
        </div>
        <div class="user-login-form-middle">
          <div class="user-login-form-middle-title">
            <div class="user-login-form-middle-line">
              <div class="line-with-text">{{ t("trade_common_or") }}</div>
            </div>
          </div>
          <div class="user-login-form-middle-box">
            <div class="middle-button">
              <el-image :src="wechat" fit="contain" />
              <p class="middle-box-text" size="large">
                {{ t("trade_common_weChat") }}
              </p>
            </div>
            <div
              v-show="loginModel === 'password'"
              class="middle-button"
              @click="switchLoginModel('mailCode')"
            >
              <el-image :src="emailPic" fit="contain" />
              <p class="middle-box-text" size="large">
                {{ t("trade_login_email") }}
              </p>
            </div>
            <div
              v-show="loginModel !== 'password'"
              class="middle-button"
              @click="switchLoginModel('password')"
            >
              <el-image :src="accountPic" fit="contain" />
              <p class="middle-box-text" size="large">
                {{ t("trade_login_account") }}
              </p>
            </div>
          </div>
        </div>
        <div class="user-login-form-bottom font14">
          {{ t("trade_login_loginAgree") }}
          <span class="el-button-custom-text" @click="handleOpenUserService">
            Linkincrease{{ t("trade_login_userServiceAgreement") }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import wechat from "@/assets/login/vchat.png";
import emailPic from "@/assets/login/email.png";
import accountPic from "@/assets/login/account.png";
import PasswordLogin from "./PasswordLogin.vue";
import CheckMailLogin from "./CheckMailLogin.vue";
import { useRouter, useRoute } from "vue-router";
import { storageLocal } from "@pureadmin/utils";
import { LoginSuccessResponseDataProp } from "../utils/type.d";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const rememberMeCheck = ref<boolean>(false);
const loginModel = ref<string>("password");
const passwordRef = ref<any>(null);
const checkMailLoginRef = ref<any>(null);
const passwordInfo = ref<any>({});

const switchLoginModel = (modelName: string): void => {
  loginModel.value = modelName;
};

const emit = defineEmits<{
  (e: "handleLoginSuccess", data: LoginSuccessResponseDataProp): void;
}>();

const handleRouter = (url: string): void => {
  router.push(url);
};

const handleRemberMe = (): void => {
  if (rememberMeCheck.value) {
    const { username, password } = passwordRef.value.userInfoForm;
    const base64Str = window.btoa(
      JSON.stringify({ username: username, password: password })
    );
    storageLocal().setItem("tradeUserInfo", base64Str);
  } else {
    storageLocal().removeItem("tradeUserInfo");
  }
};

const handleLoginSuccess = (data: LoginSuccessResponseDataProp) => {
  emit("handleLoginSuccess", data);
};

const handleOpenUserService = (): void => {
  window.open("https://www.linkincrease.com/nd.jsp?id=30&id=30#device=pc");
};

onMounted(() => {
  const email = route.query.email;
  const tradeUserInfo: string = storageLocal().getItem("tradeUserInfo");
  if (tradeUserInfo && !email) {
    const { username, password } = JSON.parse(window.atob(tradeUserInfo));
    passwordRef.value.userInfoForm.username = username;
    passwordRef.value.userInfoForm.password = password;
    passwordInfo.value = passwordRef.value?.userInfoForm;
    rememberMeCheck.value = true;
  }
  if (email) {
    passwordRef.value.userInfoForm.username = email;
  }
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
</style>
