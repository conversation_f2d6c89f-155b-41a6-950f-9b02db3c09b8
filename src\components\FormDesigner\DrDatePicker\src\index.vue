<template>
  <LocaleProvider>
    <el-date-picker
      v-model="widgetFormData[widgetConfigure._fc_id]"
      :type="getDatePicker(widgetConfigure.props.showType)"
      :placeholder="placeholder"
      :value-format="getValueFormat()"
      :format="getValueFormat()"
      :time-format="getTimeFormat()"
      :input-attrs="{ inputmode: 'none' }"
      v-bind="containerRef ? { appendTo: containerRef } : {}"
      popper-class="date-picker-popper"
      @blur="handlePaste"
    />
  </LocaleProvider>
</template>
<script lang="ts" setup>
import { inject, watch, ref, computed } from "vue";
import { ElDatePicker } from "element-plus";
import dayjs from "dayjs";
import { storageLocal } from "@pureadmin/utils";
import LocaleProvider from "@/components/LocaleProvider/index.vue";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const actuatorDefaultValue: any = inject("ActuatorDefaultValue", null);
const invocationDefaultValueExecutor: any = inject(
  "invocationDefaultValueExecutor",
  null
);
const containerRef = inject("containerRef", null);

const emit = defineEmits(["handleSubTableWidgetValueChange"]);

const getValueFormat = (): string => {
  switch (props.widgetConfigure.props.showType) {
    case "datetimesecond":
      return "YYYY-MM-DD HH:mm:ss";
    case "datetime":
      return "YYYY-MM-DD HH:mm";
    case "date":
      return "YYYY-MM-DD";
    case "month":
      return "YYYY-MM";
    case "year":
      return "YYYY";
    default:
      return "YYYY-MM-DD";
  }
};

const getTimeFormat = () => {
  return props.widgetConfigure.props.showType === "datetime"
    ? "HH:mm"
    : "HH:mm:ss";
};

const getDatePicker = (showType: string): any => {
  return showType === "datetimesecond" ? "datetime" : showType;
};

const standardDateValueFormat = (dataValue: any, format: string) => {
  widgetFormData.value[props.widgetConfigure._fc_id] = dayjs(
    dataValue,
    format
  ).format(getValueFormat());
};

const handlePaste = event => {
  const dateValue = event.target.value;
  // 1.YYYYMMDD
  if (dateValue.length === 8) {
    standardDateValueFormat(dateValue, "YYYYMMDD");
  }
  // 2.YYYY-MM-DD
  if (/^(\d{4})(-)(\d{2})(-)(\d{2})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "YYYY-MM-DD");
  }
  // 3.YYYY/MM/DD
  if (/^(\d{4})(\/)(\d{2})(\/)(\d{2})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "YYYY/MM/DD");
  }
  // 4.MM-DD-YYYY
  if (/^(\d{2})(-)(\d{2})(-)(\d{4})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "MM-DD-YYYY");
  }
  // 5.MM/DD/YYYY
  if (/^(\d{2})(\/)(\d{2})(\/)(\d{4})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "MM/DD/YYYY");
  }
  // 5.MM/DD/YY
  if (/^(\d{2})(\/)(\d{2})(\/)(\d{2})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "MM/DD/20YY");
  }
  // 6.MM-DD-YY
  if (/^(\d{2})(-)(\d{2})(-)(\d{2})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "MM-DD-20YY");
  }
  // 7.MM-DD
  if (/^(\d{2})(-)(\d{2})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "MM-DD");
  }
  // 8.MM/DD
  if (/^(\d{2})(\/)(\d{2})$/.test(dateValue)) {
    standardDateValueFormat(dateValue, "MM/DD");
  }
  // 9.MMMM DD,YYYY
  if (
    /^(January|February|March|April|May|June|July|August|September|October|November|December)( )(\d{2})(,)(\d{4})$/.test(
      dateValue
    )
  ) {
    standardDateValueFormat(dateValue, "MMMM DD,YYYY");
  }

  // 10.MMM DD,YYYY
  if (
    /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)( )(\d{2})(,)(\d{4})$/.test(
      dateValue
    )
  ) {
    standardDateValueFormat(dateValue, "MMM DD,YYYY");
  }

  // 11.DD MMM YYYY
  if (
    /^(\d{2})( )(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)( )(\d{4})$/.test(
      dateValue
    )
  ) {
    standardDateValueFormat(dateValue, "DD MMM YYYY");
  }

  // 12.DD-MMM-YYYY
  if (
    /^(\d{2})(-)(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)(-)(\d{4})$/.test(
      dateValue
    )
  ) {
    standardDateValueFormat(dateValue, "DD-MMM-YYYY");
  }

  // 13.MMM-DD
  if (
    /^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)(-)(\d{2})$/.test(
      dateValue
    )
  ) {
    standardDateValueFormat(dateValue, "MMM-DD");
  }
};

watch(
  () => ({ ...props.trendsForm }),
  (newVal, oldVal) => {
    const widgetId = props.widgetConfigure._fc_id;
    if (props.trendsForm[widgetId]) {
      const dateWidgetValue = new Date(props.trendsForm[widgetId]);
      widgetFormData.value[widgetId] =
        dayjs(dateWidgetValue).format(getValueFormat());
    } else {
      widgetFormData.value[widgetId] = undefined;
    }

    if (actuatorDefaultValue) {
      const defaultValueConfig =
        props.widgetConfigure.props.defaultValueConfig || {};

      if (
        defaultValueConfig.type === "relate" &&
        newVal &&
        oldVal &&
        newVal.hasOwnProperty(defaultValueConfig.content) &&
        oldVal.hasOwnProperty(defaultValueConfig.content) &&
        newVal[defaultValueConfig.content] !==
          oldVal[defaultValueConfig.content]
      ) {
        widgetFormData.value[widgetId] = invocationDefaultValueExecutor(
          props.widgetConfigure.props.defaultValueConfig
        );
      } else if (defaultValueConfig.type === "formula") {
        const formulasParams = defaultValueConfig.content.formulasParams
          ? defaultValueConfig.content.formulasParams.map(
              param => param.split("@@")[1]
            )
          : [];

        for (let formulasParam of formulasParams) {
          if (
            newVal &&
            oldVal &&
            newVal.hasOwnProperty(formulasParam) &&
            oldVal.hasOwnProperty(formulasParam) &&
            newVal[formulasParam] !== oldVal[formulasParam]
          ) {
            widgetFormData.value[widgetId] = invocationDefaultValueExecutor(
              props.widgetConfigure.props.defaultValueConfig
            );
            return;
          }
        }
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    const widgetId = props.widgetConfigure._fc_id;
    const newValFormat = newVal
      ? dayjs(newVal).format(getValueFormat())
      : undefined;
    const trendsValFormat = props.trendsForm[widgetId]
      ? dayjs(props.trendsForm[widgetId]).format(getValueFormat())
      : undefined;

    if (newValFormat !== trendsValFormat) {
      const valueObj = {
        label: widgetFormData.value[widgetId],
        obj: widgetFormData.value[widgetId],
        widgetId,
        $rowIndex: props.widgetRowIndex
      };
      if (props.widgetRowIndex !== -1) {
        emit(
          "handleSubTableWidgetValueChange",
          widgetId,
          valueObj,
          widgetFormData.value[widgetId]
        );
      } else {
        handleWidgetFormsValue(
          widgetId,
          valueObj,
          widgetFormData.value[widgetId]
        );
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
