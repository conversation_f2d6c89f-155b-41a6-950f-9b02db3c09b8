import * as acorn from "acorn";
import * as acornWalk from "acorn-walk";
import { calculateFormulasValue } from ".";
import dayjs from "dayjs";
import { BigNumber } from "bignumber.js";
import { cloneDeep, storageSession } from "@pureadmin/utils";
import { getOrderDetail, getOrderTemplateDetail } from "@/api/order";
import { getSccsMemberList, getSccsCoopTeamList } from "@/api/sccs";
import {
  handleAccordingToFormIdObtainData,
  TransformSubmitDataStructure,
  handleObtainDynamicDefaultValue
} from "../formDesignerUtils";
import { getSccsFieldVisibleConditions } from "@/api/common";

/**
 * 公式主执行器，将公式语句转化成ast语句
 * @param formulas 公式语句
 * @param formulasObject 公式计算因子的值
 * @returns
 */
export const calculateFormulasWidget = (
  formulas: string,
  formulasObjectData: any
) => {
  const ast = acorn.parse(formulas, { ecmaVersion: 2020 });
  let acornWalkData: any;
  const formulasObject = cloneDeep(formulasObjectData);

  acornWalk.simple(ast, {
    Literal(node: any) {
      if (node.value && (node.value + "").indexOf("@@") > -1) {
        const dataRoute = node.value.split("@@");
        acornWalkData =
          getNestedValue(formulasObject, dataRoute) !== null &&
          getNestedValue(formulasObject, dataRoute) !== undefined
            ? getNestedValue(formulasObject, dataRoute)
            : "";
      } else {
        acornWalkData = node.value;
      }
    },
    BinaryExpression(node: any) {
      acornWalkData = deepBinaryCalculate(node, formulasObject);
    },
    CallExpression(node: any) {
      acornWalkData = deepCalculate(node, formulasObject);
    }
  });

  return acornWalkData;
};

export const transformFormulasValue = (formulasValue: any) => {
  return typeof formulasValue === "string"
    ? formulasValue.replace(/&quot;/g, `"`).replace(/&&amp;/g, `\\`)
    : formulasValue;
};

export const getNestedValue = (obj, path) => {
  if (path[0] === "Formulas") {
    let detailFormList = Object.values(obj);
    for (let detailItem of detailFormList) {
      if (detailItem.hasOwnProperty(path[1])) {
        return detailItem[path[1]];
      } else {
        continue;
      }
    }
  } else {
    if (path.length === 2) {
      return path.reduce(
        (currentObj, key) =>
          currentObj !== null && currentObj !== undefined
            ? currentObj[key]
            : undefined,
        obj
      );
    } else if (path.length === 3) {
      if (!obj[path[0]]) return [];
      const pathValue =
        obj[path[0]][path[1]] instanceof Array
          ? obj[path[0]][path[1]].reduce((acc, obj) => {
              for (const key in obj) {
                let objKeyData =
                  obj[key] instanceof Array ? obj[key] : [obj[key]];
                if (acc[key]) {
                  // 如果结果对象中已有该属性，合并数组
                  acc[key] = [...acc[key], ...objKeyData];
                } else {
                  // 否则直接赋值
                  acc[key] = [...objKeyData];
                }
              }
              return acc;
            }, {})
          : obj[path[0]][path[1]];
      return pathValue ? pathValue[path[2]] : [];
    }
  }
};

/**
 * 根据传递进来的字符串进行转化，判断当前字符串是否是个日期
 * @param str
 * @returns
 */
const isDate = str => {
  if (!str) return false;
  if (str === "" || str === "-1") return false;
  if (str instanceof Array || typeof str === "boolean") return false;
  if (str instanceof Date) return true;
  if (typeof str === "number") return false;
  // 检查是否是数字时间戳
  if (/^\d+$/.test(str)) {
    if (`${str}`.length !== 10 || `${str}`.length !== 13) {
      return false;
    }
    const num = Number(str);
    return !isNaN(new Date(num).getTime());
  }

  if (typeof str === "object") {
    return new BigNumber(str).isNegative() || !!str.$isDayjsObject;
  }

  const regex = /[.,\/#!$%\^&\*;:{}=\_`~()]/g;
  if (typeof str === "string" && !!regex.test(str)) return false;

  // 检查是否符合ISO日期格式（YYYY-MM-DD）
  if (isValidDateFormat(str)) {
    return true;
  }

  // 其他情况使用Date.parse解析
  return !isNaN(Date.parse(str));
};

function isValidDateFormat(str) {
  // 定义所有可能的格式正则表达式
  const formatRegexes = [
    /^\d{4}$/, // YYYY
    /^\d{4}-(0[1-9]|1[0-2])$/, // YYYY-MM
    /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/, // YYYY-MM-DD
    /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01]) (0[0-9]|1[0-9]|2[0-3]):([0-5][0-9])$/, // YYYY-MM-DD HH:mm
    /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01]) (0[0-9]|1[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/ // YYYY-MM-DD HH:mm:ss
  ];

  // 检查是否符合任一格式
  const matchesFormat = formatRegexes.some(regex => regex.test(str));
  if (!matchesFormat) return false;

  // 验证日期是否有效
  return validateDateComponents(str);
}

// 辅助函数：验证日期组件是否有效
function validateDateComponents(str) {
  const parts = str.split(/[- :]/);
  const year = parseInt(parts[0]);
  const month = parts[1] ? parseInt(parts[1]) - 1 : 0;
  const day = parts[2] ? parseInt(parts[2]) : 1;
  const hours = parts[3] ? parseInt(parts[3]) : 0;
  const minutes = parts[4] ? parseInt(parts[4]) : 0;
  const seconds = parts[5] ? parseInt(parts[5]) : 0;

  // 验证年份
  if (year < 1000 || year > 9999) return false;

  // 如果有月份，验证月份
  if (parts[1] && (month < 0 || month > 11)) return false;

  // 如果有日期，验证日期
  if (parts[2]) {
    const date = new Date(year, month, day);
    if (
      date.getFullYear() !== year ||
      date.getMonth() !== month ||
      date.getDate() !== day
    ) {
      return false;
    }
  }

  // 验证时间部分
  if (parts[3] && (hours < 0 || hours > 23)) return false;
  if (parts[4] && (minutes < 0 || minutes > 59)) return false;
  if (parts[5] && (seconds < 0 || seconds > 59)) return false;

  return true;
}

/**
 * 计算执行语句
 * @returns
 */
export const deepBinaryCalculate = (node, formulasObject) => {
  if (node.type === "BinaryExpression") {
    let leftData;
    let rightData;
    if (node.left.type === "BinaryExpression") {
      leftData = deepBinaryCalculate(node.left, formulasObject);
    } else {
      leftData = deepCalculate(node.left, formulasObject);
    }
    if (node.right.type === "BinaryExpression") {
      rightData = deepBinaryCalculate(node.right, formulasObject);
    } else {
      rightData = deepCalculate(node.right, formulasObject);
    }
    return calculateFormulasLeftAndRightData(leftData, rightData, node);
  }
};

/**
 * 函数执行语句
 * @returns
 */
export const deepCalculate = (node, formulasObject) => {
  let nodeParams = [];

  if (node.type === "CallExpression") {
    let nodeParams = [];
    if (node.arguments.length > 0) {
      for (let childNode of node.arguments) {
        if (childNode.type === "Literal") {
          let paramsValue =
            (childNode.value + "").indexOf("@@") > -1
              ? getNestedValue(formulasObject, childNode.value.split("@@"))
              : childNode.value;

          if (paramsValue) {
            nodeParams.push(paramsValue);
          } else {
            nodeParams.push(null);
          }
        } else {
          const childDeepData = deepCalculate(childNode, formulasObject);
          nodeParams.push(childDeepData);
        }
      }
      return calculateFormulasValue(node.callee.name, nodeParams);
    } else {
      return calculateFormulasValue(node.callee.name, nodeParams);
    }
  } else if (node.type === "ArrayExpression") {
    // return node.elements;
    let nodeArrayElement = [];
    node.elements.forEach(element => {
      nodeArrayElement.push(deepCalculate(element, formulasObject));
    });
    return nodeArrayElement;
  } else if (node.type === "BinaryExpression") {
    let leftData;
    let rightData;
    if (node.left.type === "BinaryExpression") {
      leftData = deepBinaryCalculate(node.left, formulasObject);
    } else {
      leftData = deepCalculate(node.left, formulasObject);
    }
    if (node.right.type === "BinaryExpression") {
      rightData = deepBinaryCalculate(node.right, formulasObject);
    } else {
      rightData = deepCalculate(node.right, formulasObject);
    }
    return calculateFormulasLeftAndRightData(leftData, rightData, node);
  } else if (node.type === "Literal") {
    return (node.value + "").indexOf("@@") > -1
      ? getNestedValue(formulasObject, node.value.split("@@"))
      : node.value;
  } else if (node.type === "UnaryExpression") {
    return `${node.operator}${node.argument.value}`;
  }
  if (nodeParams.includes("Error: #VALUE!")) {
    return "Error: #VALUE!";
  } else {
    return calculateFormulasValue(node.callee?.name, nodeParams);
  }
};

function calculateFormulasLeftAndRightData(
  leftData: any,
  rightData: any,
  node: any
) {
  if (
    leftData !== null &&
    leftData !== undefined &&
    leftData !== "null" &&
    leftData !== "undefined" &&
    rightData !== null &&
    rightData !== undefined &&
    rightData !== "null" &&
    rightData !== "undefined"
  ) {
    if (isDate(leftData) || isDate(rightData)) {
      if (isDate(leftData) && isDate(rightData)) {
        if (node.operator === "==") {
          return dayjs(leftData).isSame(dayjs(rightData), "day");
        } else {
          const diffDays = dayjs(leftData).diff(dayjs(rightData), "day");
          return diffDays;
        }
      } else {
        if (node.operator === "==") {
          return dayjs(leftData).isSame(dayjs(rightData), "day");
        } else if (node.operator === "+") {
          if (isDate(leftData)) {
            return dayjs(leftData).add(rightData, "day");
          } else {
            return dayjs(rightData).add(leftData, "day");
          }
        } else {
          if (isDate(leftData)) {
            return dayjs(leftData).subtract(rightData, "day");
          } else {
            return dayjs(rightData).subtract(leftData, "day");
          }
        }
      }
    } else {
      if (isDate(leftData) || isDate(rightData)) {
        if (isDate(leftData) && isDate(rightData)) {
          const diffDays = dayjs(leftData).diff(dayjs(rightData), "day");
          return diffDays;
        } else {
          if (node.operator === "==") {
            return dayjs(leftData).isSame(dayjs(rightData), "day");
          } else if (node.operator === "+") {
            if (isDate(leftData)) {
              const rightResult = rightData ? rightData : 0;
              return dayjs(leftData).add(rightResult, "day");
            } else {
              const leftResult = leftData ? leftData : 0;
              return dayjs(rightData).add(leftResult, "day");
            }
          } else {
            return dayjs(leftData).subtract(rightData, "day");
          }
        }
      } else {
        let leftResult = `"${
          typeof leftData === "string"
            ? leftData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : leftData
              ? leftData
              : ""
        }"`;
        let rightResult = `"${
          typeof rightData === "string"
            ? rightData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : rightData
              ? rightData
              : ""
        }"`;

        if (typeof leftData === "string" && typeof rightData === "string") {
          return eval(`${leftResult}${node.operator}${rightResult}`);
        } else {
          if (
            (leftData === null || leftData === undefined) &&
            (rightData === null || rightData === undefined)
          ) {
            return undefined;
          } else {
            return eval(
              `${Number.isNaN(Number(leftData)) ? leftResult : Number(leftData)}${node.operator}${Number.isNaN(Number(rightData)) ? rightResult : Number(rightData)}`
            );
          }
        }
      }
    }
  } else {
    if (
      leftData !== null &&
      leftData !== undefined &&
      leftData !== "null" &&
      leftData !== "undefined" &&
      rightData !== null &&
      rightData !== undefined &&
      rightData !== "null" &&
      rightData !== "undefined"
    ) {
      return "";
    } else {
      if (isDate(leftData) || isDate(rightData)) {
        if (isDate(leftData) && isDate(rightData)) {
          const diffDays = dayjs(leftData).diff(dayjs(rightData), "day");
          return diffDays;
        } else {
          if (node.operator === "==") {
            return dayjs(leftData).isSame(dayjs(rightData), "day");
          } else if (node.operator === "+") {
            if (isDate(leftData)) {
              const rightResult = rightData ? rightData : 0;
              return dayjs(leftData).add(rightResult, "day");
            } else {
              const leftResult = leftData ? leftData : 0;
              return dayjs(rightData).add(leftResult, "day");
            }
          } else {
            if (isDate(leftData)) {
              return dayjs(leftData).subtract(rightData, "day");
            } else {
              return dayjs(rightData).subtract(leftData, "day");
            }
          }
        }
      } else {
        let leftResult = `"${
          typeof leftData === "string"
            ? leftData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : leftData
              ? leftData
              : ""
        }"`;
        let rightResult = `"${
          typeof rightData === "string"
            ? rightData.replace(/[\n]/g, "").replace(/"/g, "&quot;")
            : rightData
              ? rightData
              : ""
        }"`;

        if (typeof leftData === "string" && typeof rightData === "string") {
          return eval(`${leftResult}${node.operator}${rightResult}`);
        } else {
          if (
            (leftData === null || leftData === undefined) &&
            (rightData === null || rightData === undefined)
          ) {
            return undefined;
          } else {
            return eval(
              `${Number.isNaN(Number(leftData)) ? leftResult : Number(leftData)}${node.operator}${Number.isNaN(Number(rightData)) ? rightResult : Number(rightData)}`
            );
          }
        }
      }
    }
  }
}

function getUrlParams(): any {
  const reg = /(\w+)=([^&]+)/g;
  const params = {};
  let match;

  while ((match = reg.exec(window.location.href)) !== null) {
    params[match[1]] = match[2];
  }

  return params;
}

/**
 * 获取公式控件计算因子值
 * @param orderId 订单id，当有订单id的时候，则是已经有了订单；没有的话，则可以任务是创建订单的逻辑
 */
export const getDefaultConfigWidgetData = async (
  orderId: string,
  customParams?: {
    sccsId: string;
    templateId: string;
  }
) => {
  return new Promise(resolve => {
    // 获取sccsID，优先从URL参数获取，否则从自定义参数获取
    const sccsId: string =
      (getUrlParams().sccsId as string) || customParams?.sccsId;
    // 获取模板ID，优先从URL参数获取，否则从自定义参数获取
    const templateId: string =
      (getUrlParams().templateId as string) || customParams?.templateId;

    // 并行请求所需的基础数据
    Promise.all([
      getSccsMemberList({ sccsId: sccsId }), // 获取sccs成员列表
      getSccsCoopTeamList({ sccsId: sccsId }), // 获取sccs团队列表
      getOrderTemplateDetail({
        // 获取订单模板详情
        templateId: templateId,
        orderId: orderId
      }),
      getSccsFieldVisibleConditions({ sccsId: sccsId }) // 获取字段可见性条件
    ]).then(async resp => {
      let encapsulationData;
      let orderData;

      if (orderId) {
        // 如果有订单ID，表示是编辑已有订单，需要获取订单详情
        const { data } = await getOrderDetail({
          sccsId: sccsId,
          orderId: orderId
        });
        // 根据表单ID处理获取到的订单数据
        encapsulationData = await handleAccordingToFormIdObtainData(
          data,
          resp[2].data
        );
        orderData = data;
      } else {
        // 如果没有订单ID，表示是创建新订单，需要处理默认值
        const { widgetJsonList, id } = resp[2].data.mainForm;
        // 获取动态默认值
        let mainFormData = await handleObtainDynamicDefaultValue(
          widgetJsonList,
          TransformSubmitDataStructure([], [])
        );
        let mainFormObject = {};

        mainFormObject[id] = mainFormData;
        // 封装数据结构
        encapsulationData = {
          formulaObject: mainFormObject,
          formVisibleObject: { mainForm: mainFormObject },
          orderDetailData: {}
        };
      }

      // 将获取到的数据存储到会话存储中
      storageSession().setItem("widgetData", {
        sccsMemberList: resp[0].data, // sccs成员列表
        sccsCoopTeamList: resp[1].data, // sccs团队列表
        entrieTemplateForm: resp[2].data, // 模板表单数据
        templateData: encapsulationData, // 处理后的模板数据
        formVisibleTemplateFormConfigure: resp[3].data // 表单可见性配置
      });

      // 返回处理后的数据
      resolve({
        sccsMemberList: resp[0].data, // sccs成员列表
        formVisibleTemplateFormConfigure: resp[3].data,
        orderDetailData: encapsulationData.orderDetailData, // 订单详情数据
        orderTemplateData: resp[2].data, // 订单模板数据
        orderData: orderData // 订单数据（仅在编辑模式下有值）
      });
    });
  });
};
