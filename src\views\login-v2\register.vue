<template>
  <LoginLayout>
    <template #form>
      <div class="register-form-wrapper">
        <!-- 注册表单卡片 -->
        <div class="register-card">
          <!-- 头部 -->
          <div class="register-header">
            <!-- Logo -->
            <div class="logo-container">
              <el-image :src="logoImage" fit="contain" class="logo" />
            </div>
            <!-- 登录入口 -->
            <div class="login-link">
              <el-button type="primary" link @click="handleGoLogin">
                {{ t("trade_common_login") }}
              </el-button>
            </div>
          </div>

          <!-- 注册标题 -->
          <div class="register-title">
            <h2>{{ currentStepTitle }}</h2>
            <p v-if="!nextStep" class="register-subtitle">
              {{ t("trade_login_workEmail") }}
            </p>
          </div>

          <!-- 注册表单 -->
          <div class="register-form">
            <!-- 第一步：邮箱验证 -->
            <MailForm
              v-if="!nextStep"
              mode="register"
              errorStyle="margin-bottom: 10px"
              @handleEmailVerified="handleEmailVerified"
            />
            <!-- 第二步：创建档案 -->
            <UserRegisterNextForm
              v-else
              :email="userEmail"
              @handleLoginSuccess="handleLoginSuccess"
            />
          </div>

          <!-- 用户协议 -->
          <div v-if="!nextStep" class="user-agreement-wrap">
            <UserAgreement :isRegister="true" />
          </div>
        </div>
      </div>
    </template>
  </LoginLayout>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { setToken } from "@/utils/auth";
import LoginLayout from "./layout.vue";
import MailForm from "./components/MailForm.vue";
import UserRegisterNextForm from "./components/UserRegisterNextForm.vue";
import UserAgreement from "./components/UserAgreement.vue";
import logoImage from "@/assets/login/logo.png";

const { t } = useI18n();
const router = useRouter();

// 注册步骤状态
const nextStep = ref<boolean>(false);
const userEmail = ref<string>("");

// 当前步骤标题
const currentStepTitle = computed(() => {
  return nextStep.value
    ? t("trade_login_createProfile")
    : t("trade_login_userRegistration");
});

// 处理登录跳转
const handleGoLogin = () => {
  router.push("/login");
};

// 处理邮箱验证成功
const handleEmailVerified = (email: string) => {
  userEmail.value = email;
  nextStep.value = true;
};

// 处理注册登录成功
const handleLoginSuccess = (data: any) => {
  // 参考旧版实现的登录成功逻辑
  setToken(data)
    .then((userInfo: any) => {
      if (userInfo.latestLoginTeamId && userInfo.latestLoginTeamMemberId) {
        router.push("/");
      } else if (userInfo.anyTeamMember) {
        router.push("/selectTeam");
      } else {
        router.push("/createTeam");
      }
    })
    .catch((error: any) => {
      console.error("设置token失败:", error);
      ElMessage.error("注册失败，请重试");
    });
};
</script>

<style lang="scss" scoped>
// 响应式适配
@media screen and (width <= 768px) {
  .register-card {
    width: 90vw;
    max-width: 400px;
    padding: 32px 24px;
    margin: 0 20px;
  }

  .register-header {
    margin-bottom: 24px;

    .logo-container .logo {
      height: 32px;
    }

    .login-link {
      .login-text {
        font-size: 12px;
      }
    }
  }

  .register-title {
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
    }

    .register-subtitle {
      font-size: 12px;
    }
  }
}

@media screen and (width <= 480px) {
  .register-card {
    padding: 24px 16px;
  }

  .register-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

.register-form-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.register-card {
  width: 480px;
  padding: 48px;
  background: rgb(255 255 255 / 95%);
  backdrop-filter: blur(20px);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 24px;
  box-shadow: 0 24px 64px rgb(0 0 0 / 10%);
}

// 头部布局
.register-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;

  .logo-container {
    .logo {
      width: auto;
      height: 30px;
    }
  }

  .login-link {
    display: flex;
    gap: 8px;
    align-items: center;

    .login-text {
      font-size: 14px;
      color: #666;
    }
  }
}

// 注册标题
.register-title {
  margin-bottom: 32px;

  h2 {
    margin-bottom: 2px;
    font-family: "PingFang SC", "PingFang SC-Semibold";
    font-size: 20px;
    font-weight: bold;
    line-height: 28px;
    color: #262626;
    text-align: left;
  }

  .register-subtitle {
    font-family: "PingFang SC", "PingFang SC-Regular";
    font-size: 14px;
    font-weight: regular;
    line-height: 20px;
    color: #595959;
    text-align: left;
  }
}

// 注册表单
.register-form {
  margin-bottom: 24px;
}

// 用户协议
.user-agreement-wrap {
  margin-bottom: 22px;
}
</style>
