<template>
  <div style="display: inline-flex">
    <el-popover
      ref="targetRef"
      placement="bottom-end"
      trigger="click"
      width="auto"
    >
      <template #reference>
        <span>
          <el-tooltip
            :disabled="!!buttonTextVisible"
            class="box-item"
            effect="dark"
            :show-after="500"
            :content="t('trade_common_filter')"
            placement="bottom"
          >
            <span
              class="order-manage-btn"
              :class="[
                currentScreenWidth <= 1366 ? 'Screen_1366' : '',
                searchConditions.length !== 0 ? 'active' : ''
              ]"
            >
              <i class="iconfont link-filter" />
              <span v-if="searchConditions.length === 0 && buttonTextVisible">
                {{ t("trade_common_filter") }}
              </span>
              <span v-else-if="searchConditions.length !== 0">{{
                searchConditions.length
              }}</span>
            </span>
          </el-tooltip>
        </span>
      </template>
      <template #default>
        <div class="order-manage-search-body">
          <div class="order-manage-search-header">
            <div class="order-manage-search-title">
              {{ t("trade_common_filteringCriteria") }}
            </div>
          </div>
          <div class="order-manage-search-container">
            <div
              v-for="(item, index) in searchConditions"
              :key="item"
              class="order-manage-search-condition-row"
            >
              <LkSearchConditions
                ref="conditionRef"
                class="order-manage-search-condition-col"
                :condition="item"
                :conditionList="conditionList"
                :labelConditionMap="labelConditionMap"
              />
              <i
                class="iconfont link-close order-manage-search-condition-icon"
                @click="handleRemoveCondition(index)"
              />
            </div>

            <div
              class="order-manage-search-btn"
              @click="handleAddSearchCondition"
            >
              <i class="iconfont link-add" />
              {{ t("trade_common_filteringCriteriaBtn") }}
            </div>
          </div>
          <div class="order-manage-search-bottom">
            <el-button @click="handleResetConditions">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchConditions"
            >
              {{ t("trade_common_query") }}
            </el-button>
          </div>
        </div>
      </template>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import { inject, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import LkSearchConditions from "@/components/lkSearchConditions/index";

const props = defineProps({
  orderFilterList: {
    type: Array as PropType<any>,
    default: () => []
  },
  presentViewType: {
    type: String as PropType<string>,
    default: "ORDER"
  },
  presentConditions: {
    type: Object as PropType<any>,
    default: () => {}
  },
  currentScreenWidth: {
    type: Number as PropType<any>,
    default: 0
  }
});

const emit = defineEmits([
  "handleSearchTableByConditions",
  "handleOrderListFilterConditionList"
]);

const buttonTextVisible = inject("buttonTextVisible");

const { t } = useI18n();
const route = useRoute();
const conditionRef = ref<HTMLElement | any>(null);
const targetRef = ref<HTMLElement | any>(null);
const labelConditionMap = ref<any>();
const conditionList = ref<any>();
const searchConditions = ref<any>([]);

const handleAddSearchCondition = () => {
  searchConditions.value.push({
    firstData: [],
    firstDataObject: {},
    twoData: [],
    twoDataObject: {},
    operator: "",
    fieldTypeEnum: "CUSTOM",
    workOrderRange: null,
    rangeDateType: null,
    customerDateType: null,
    value: ""
  });
};

const handleRemoveCondition = (index: number) => {
  searchConditions.value.splice(index, 1);
};

const handleSearchConditions = () => {
  for (let i = 0, len = searchConditions.value.length; i < len; i++) {
    const conditionRow = searchConditions.value[i];
    const {
      firstDataObject,
      operator,
      fieldTypeEnum,
      twoDataObject,
      value,
      customerDateType,
      rangeDateType,
      workOrderRange
    } = conditionRow;

    if (
      !firstDataObject ||
      !operator ||
      !fieldTypeEnum ||
      (fieldTypeEnum === "OTHER_FIELD" && !twoDataObject.value) ||
      (fieldTypeEnum === "CUSTOM" &&
        !value &&
        firstDataObject.fieldType !== "DrDatePicker" &&
        !["IS_NULL", "IS_NOT_NULL"].includes(operator)) ||
      (fieldTypeEnum === "CUSTOM" &&
        firstDataObject.fieldType === "DrDatePicker" &&
        !["IS_NULL", "IS_NOT_NULL"].includes(operator) &&
        !customerDateType) ||
      (fieldTypeEnum === "CUSTOM" &&
        firstDataObject.fieldType === "DrDatePicker" &&
        !["IS_NULL", "IS_NOT_NULL"].includes(operator) &&
        customerDateType &&
        ![
          "THIS_WEEK",
          "LAST_WEEK",
          "NEXT_WEEK",
          "THIS_MONTH",
          "LAST_MONTH",
          "NEXT_MONTH",
          "THIS_YEAR",
          "LAST_YEAR",
          "NEXT_YEAR",
          "TODAY",
          "TOMORROW",
          "YESTERDAY"
        ].includes(customerDateType) &&
        !value) ||
      (fieldTypeEnum === "CUSTOM" &&
        firstDataObject.fieldType === "DrDatePicker" &&
        !["IS_NULL", "IS_NOT_NULL"].includes(operator) &&
        rangeDateType &&
        ![
          "THIS_WEEK",
          "LAST_WEEK",
          "NEXT_WEEK",
          "THIS_MONTH",
          "LAST_MONTH",
          "NEXT_MONTH",
          "THIS_YEAR",
          "LAST_YEAR",
          "NEXT_YEAR",
          "TODAY",
          "TOMORROW",
          "YESTERDAY"
        ].includes(rangeDateType) &&
        !value)
      // (workOrderRangeVisible.value && !workOrderRange)
    ) {
      ElMessage.error("请完善设置");
      return;
    }
  }

  targetRef.value.hide();
  emit("handleSearchTableByConditions", searchConditions.value);
};

const handleResetConditions = () => {
  searchConditions.value = [];
  targetRef.value.hide();
  emit("handleSearchTableByConditions", searchConditions.value);
};

watch(
  () => props.presentConditions,
  () => {
    labelConditionMap.value = props.presentConditions.labelConditionMap;
    conditionList.value = props.presentConditions.conditionList;
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.orderFilterList,
  () => {
    searchConditions.value = props.orderFilterList;
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped>
@use "../index.scss";

.order-manage-search-body {
  width: 840px;

  .order-manage-search-header {
    margin-bottom: 23px;
    font-size: 14px;
    line-height: 20px;
    color: #595959;
  }

  .order-manage-search-container {
    .order-manage-search-condition-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .order-manage-search-condition-icon {
        font-size: 12px;
        cursor: pointer;
      }
    }

    .order-manage-search-btn {
      width: fit-content;
      margin-top: 15px;
      font-size: 14px;
      line-height: 20px;
      color: #0070d2;
      vertical-align: middle;
      cursor: pointer;

      .iconfont {
        font-size: 13px;
      }
    }
  }

  .order-manage-search-bottom {
    text-align: right;
  }
}
</style>
