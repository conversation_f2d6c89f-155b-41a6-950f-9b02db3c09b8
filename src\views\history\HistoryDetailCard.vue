<template>
  <el-scrollbar>
    <el-collapse
      v-if="type === 'MAIN_WORK_ORDER'"
      v-model="activeNamesOrder"
      class="order-form-edit-collapse"
    >
      <el-collapse-item
        :title="t('trade_common_orderMessage')"
        name="MAIN_WORK_ORDER"
        class="order-manage-collapse"
      >
        <OrderDetailDescWidgetContrast
          :widgetForm="widgetForm"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
      </el-collapse-item>
      <el-collapse-item
        :title="t('trade_common_milestonePlan')"
        name="2"
        class="milestone-plan-collapse"
      >
        <el-timeline class="milestone-timeline-body">
          <el-timeline-item
            v-for="item in milestoneCard"
            :key="item.msId"
            placement="top"
          >
            <div class="milestone-timeline-header">
              <ReText
                type="info"
                :tippyProps="{ delay: 50000 }"
                class="milestone-timeline-title"
              >
                {{ item.name + "222" }}
              </ReText>
            </div>
            <el-card class="milestone-timeline-card">
              <div class="milestone-timeline-card-title">
                <span class="milestone-timeline-card-span">
                  {{ t("trade_common_in") }}：
                </span>
                <ReText
                  type="info"
                  :tippyProps="{ delay: 50000 }"
                  class="milestone-timeline-card-tip"
                >
                  {{ returnName(item) }}
                </ReText>
              </div>
              <div class="milestone-timeline-card-content">
                <el-row :gutter="20" style="width: 100%">
                  <el-col
                    v-for="prop in milestoneProps"
                    :key="prop.type"
                    :span="6"
                  >
                    <div class="milestone-timeline-info">
                      <el-row style="min-height: 24px">
                        <span class="milestone-timeline-span">
                          {{ t(prop.label) }}</span
                        >
                      </el-row>
                      <el-row style="min-height: 24px">
                        <span
                          v-if="returnValue(item, prop.type, true)"
                          class="milestone-timeline-span-last"
                          :class="
                            judgeValueReturnStatus(
                              returnValue(item, prop.type),
                              returnValue(item, prop.type, true)
                            )
                          "
                        >
                          {{ returnValue(item, prop.type, true) }}
                          <span
                            v-show="
                              ['delete', 'edit'].includes(
                                judgeValueReturnStatus(
                                  returnValue(item, prop.type),
                                  returnValue(item, prop.type, true)
                                )
                              )
                            "
                            class="delete-line"
                          />
                        </span>
                      </el-row>
                      <el-row style="min-height: 24px">
                        <span
                          v-if="
                            returnValue(item, prop.type) &&
                            judgeValueReturnStatus(
                              returnValue(item, prop.type),
                              returnValue(item, prop.type, true)
                            ) !== 'nochange'
                          "
                          class="milestone-timeline-value"
                          :class="
                            judgeValueReturnStatus(
                              returnValue(item, prop.type),
                              returnValue(item, prop.type, true)
                            )
                          "
                        >
                          {{ returnValue(item, prop.type) }}
                        </span>
                      </el-row>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="milestone-timeline-info">
                      <el-row style="min-height: 24px">
                        <span class="milestone-timeline-span">
                          {{ t("trade_common_directorPerson") }}</span
                        >
                      </el-row>
                      <el-row style="min-height: 24px">
                        <span
                          v-if="returnValue(item, 'username', true)"
                          class="milestone-timeline-span-last"
                          :class="
                            judgeValueReturnStatus(
                              returnValue(item, 'username'),
                              returnValue(item, 'username', true)
                            )
                          "
                        >
                          <LkAvatar
                            :teamInfo="{
                              avatar: returnValue(item, 'avatar'),
                              username: returnValue(item, 'username')
                            }"
                            :size="18"
                          />
                          {{ returnValue(item, "username", true) }}
                          <span
                            v-show="
                              ['delete', 'edit'].includes(
                                judgeValueReturnStatus(
                                  returnValue(item, 'username'),
                                  returnValue(item, 'username', true)
                                )
                              )
                            "
                            class="delete-line"
                          />
                        </span>
                      </el-row>
                      <el-row style="min-height: 24px">
                        <span
                          v-show="
                            returnValue(item, 'username') &&
                            judgeValueReturnStatus(
                              returnValue(item, 'username'),
                              returnValue(item, 'username', true)
                            ) !== 'nochange'
                          "
                          class="milestone-timeline-value"
                          :class="
                            judgeValueReturnStatus(
                              returnValue(item, 'username'),
                              returnValue(item, 'username', true)
                            )
                          "
                        >
                          <LkAvatar
                            :teamInfo="{
                              avatar: returnValue(item, 'avatar'),
                              username: returnValue(item, 'username')
                            }"
                            :size="18"
                          />
                          {{ returnValue(item, "username") }}
                        </span>
                      </el-row>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-collapse-item>
      <LKAnchor :anchorList="anchorList" />
    </el-collapse>
    <el-collapse
      v-else-if="type === 'MILE_STONE'"
      v-model="activeNamesMs"
      class="order-form-edit-collapse"
    >
      <el-collapse-item
        :title="t('trade_template_replyBtnName')"
        name="MILE_STONE"
        class="order-manage-collapse"
      >
        <OrderDetailDescWidgetContrast
          :widgetForm="widgetForm"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
      </el-collapse-item>
    </el-collapse>
    <el-collapse
      v-else-if="type === 'WORK_ORDER'"
      v-model="activeNamesWorkOrder"
      class="order-form-edit-collapse"
    >
      <el-collapse-item
        v-if="workOrderReplyData.length > 0"
        :title="t('trade_order_taskReply')"
        name="WORK_ORDER"
        class="order-manage-collapse"
      >
        <OrderDetailDescWidgetContrast
          :widgetForm="widgetForm"
          :widgetData="workOrderReplyData"
          :lastHistoryObj="lastHistoryWorkOrderReplyObj"
        />
      </el-collapse-item>
      <el-collapse-item
        v-for="(child, index) in workOrderForm"
        :key="index"
        :title="child.name"
        :name="'WORK_ORDER' + index"
        class="order-manage-collapse"
      >
        <OrderDetailDescWidgetContrast
          :widgetForm="child.widgetJsonList"
          :widgetData="widgetData"
          :lastHistoryObj="lastHistoryObj"
        />
      </el-collapse-item>
    </el-collapse>
  </el-scrollbar>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import OrderDetailDescWidgetContrast from "@/views/history/OrderDetailDescWidgetContrast.vue";
import LKAnchor from "@/components/lkAnchor/index";
import { getTimeLineStampFormat } from "@/utils/formateDate";
import LkAvatar from "@/components/lkAvatar/index";
import { ReText } from "@/components/ReText";

const activeNamesOrder = ref<any>(["MAIN_WORK_ORDER"]);
const activeNamesMs = ref<any>(["MILE_STONE"]);
const activeNamesWorkOrder = ref<any>(["WORK_ORDER"]);
const props = defineProps({
  type: {
    type: String as PropType<any>,
    default: () => ""
  },
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  workOrderReplyData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneCard: {
    type: Array as PropType<any>,
    default: () => []
  },
  milestoneData: {
    type: Array as PropType<any>,
    default: () => []
  },
  lastHistoryObj: {
    type: Object as PropType<any>,
    default: () => {}
  },
  lastHistoryWorkOrderReplyObj: {
    type: Object as PropType<any>,
    default: () => {}
  },
  workOrderForm: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const anchorList = ref<any>([
  {
    title: "trade_common_orderMessage",
    ref: "order-manage-collapse",
    name: "orderManage"
  },
  {
    title: "trade_common_milestonePlan",
    ref: "milestone-plan-collapse",
    name: "milestonePlan"
  }
]);

const milestoneProps = ref<any>([
  {
    label: "trade_common_planStart",
    type: "plannedStartDate"
  },
  {
    label: "trade_common_PlannedTimeConsumption",
    type: "plannedDay"
  },
  {
    label: "trade_common_planEnd",
    type: "plannedEndDate"
  }
]);

const { t } = useI18n();

const milestoneDatas = ref<any>([]);

watch(
  () => props.milestoneData,
  () => {
    milestoneDatas.value = props.milestoneData;
  },
  {
    deep: true,
    immediate: true
  }
);

const returnName = (item: any): string => {
  if (item.formList && item.formList.length > 0) {
    return item.formList.map(() => item.name).join(",");
  } else {
    return "";
  }
};

const returnValue = (item: any, type: string, lasted?: boolean): string => {
  let mapData = [];
  if (lasted) {
    mapData = props.lastHistoryObj?.milestoneList
      ? props.lastHistoryObj.milestoneList
      : [];
  } else {
    mapData = milestoneDatas.value;
  }
  switch (type) {
    case "plannedStartDate":
      let plannedStartDate = null;
      mapData?.map((e: any) => {
        if (e.msId === item.msId) {
          plannedStartDate = e.plannedStartDate;
        }
      });
      return getTimeLineStampFormat(plannedStartDate, "YYYY-MM-DD");
    case "plannedDay":
      let plannedDay = null;
      mapData?.map((e: any) => {
        if (e.msId === item.msId) {
          plannedDay = e.plannedDay;
        }
      });
      return plannedDay;
    case "plannedEndDate":
      let plannedEndDate = null;
      mapData?.map((e: any) => {
        if (e.msId === item.msId) {
          plannedEndDate = e.plannedEndDate;
        }
      });
      return getTimeLineStampFormat(plannedEndDate, "YYYY-MM-DD");
    case "avatar":
      let avatar = null;
      mapData?.map((e: any) => {
        if (e.msId === item.msId) {
          avatar = e.managerUserAvatar;
        }
      });
      return avatar;
    case "username":
      let username = null;
      mapData?.map((e: any) => {
        if (e.msId === item.msId) {
          username = e.managerUserName;
        }
      });
      return username;
    default:
      return null;
  }
};
const judgeValueReturnStatus = (
  currentValue?: string,
  lastValue?: string
): string => {
  let status = "";
  if (currentValue === lastValue || (!currentValue && !lastValue)) {
    status = "nochange";
  } else if (
    (lastValue === "" || lastValue === null || lastValue === undefined) &&
    currentValue !== "" &&
    currentValue !== null &&
    currentValue !== undefined
  ) {
    status = "add";
  } else if (
    (lastValue !== "" || lastValue !== null || lastValue !== undefined) &&
    (currentValue === null || currentValue === undefined || currentValue === "")
  ) {
    status = "delete";
  } else if (
    lastValue !== null &&
    currentValue !== null &&
    lastValue !== currentValue
  ) {
    status = "edit";
  }
  return status;
};
</script>
<style lang="scss" scoped>
::v-deep(.milestone-timeline-body) {
  .el-timeline-item {
    .el-timeline-item__tail {
      top: 16px;
      height: 80%;
      background: #cfd7e6;
    }

    .el-timeline-item__node {
      left: 1px;
      width: 8px;
      height: 8px;
    }

    .el-timeline-item__timestamp.is-top {
      padding: 0 !important;
      margin-bottom: 0 !important;
    }

    &:last-child {
      .el-timeline-item__tail {
        display: block !important;
      }
    }

    .milestone-timeline-header {
      display: flex;
      align-items: center;
      height: 16px;
      margin-bottom: 10px;
      // line-height: 16px;

      .milestone-timeline-title {
        max-width: calc(100% - 60px);
        font-size: 14px;
        font-weight: bold;
        color: #262626;
      }

      .milestone-timeline-tag {
        padding: 4px 5px;
        margin-left: 5px;
        font-size: 12px;
        line-height: 14px;
        color: #fdad4d;
        background: rgb(253 173 77 / 15%);
        border-radius: 10px;
      }
    }

    .milestone-timeline-card {
      .el-card__body {
        padding: 0 !important;

        .milestone-timeline-card-title {
          display: flex;
          padding: 0 13px;
          margin: 0 5px;
          line-height: 40px;
          border-bottom: 1px solid #e3e5e9;

          .milestone-timeline-card-span {
            white-space: nowrap;
          }

          .milestone-timeline-card-tip {
            font-size: 14px;
            color: #595959;
          }
        }

        .milestone-timeline-card-content {
          display: flex;
          padding: 19px;

          .milestone-timeline-span-last {
            position: relative;

            &.delete,
            &.edit {
              padding: 0 6px;
              background: #fff1f0;
              border: 1px solid #f56c6c;
            }

            .delete-line {
              position: absolute;
              top: 50%;
              left: 0;
              width: 100%;
              height: 1px;
              background: #262626;
            }
          }

          .milestone-timeline-value {
            min-height: 24px;

            &.add,
            &.edit {
              padding: 0 6px;
              background: #f1ffeb;
              border: 1px solid #67c23a;
            }
          }

          .milestone-timeline-info {
            display: flex;
            flex-direction: column;

            .el-row {
              margin-bottom: 10px;
            }
          }

          .el-form-item {
            flex: 1;
            margin-right: 24px;

            &:last-child {
              margin-right: 0;
            }

            .el-form-item__content {
              width: 100% !important;

              .el-input {
                width: 100% !important;

                .el-input__wrapper {
                  width: 100% !important;
                }
              }

              .el-input-number {
                width: 100% !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>
