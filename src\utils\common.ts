import {
  getTradeCoopSccsRolePermission,
  getTradeSccsRolePermission
} from "@/api/order";
import { storageSession } from "@pureadmin/utils";
import { BigNumber } from "bignumber.js";

/**
 * 数字控件：将数字类型转化成千分位，使用bigNumber.js进行进度锁定
 * @param value
 * @returns
 */
export const formatThousandNumber = (value: string | number) => {
  if (value === 0) return "0";
  if (!value) return "";
  return new BigNumber(value).toFormat();
};

/**
 * 数字控件：将千分位转化成数字类型，使用bigNumber.js进行进度锁定
 * @param value
 * @returns
 */
export const formatNumberValueOf = (value: string | number) => {
  if (!value) return "";
  return new BigNumber(value.toString().replace(/,/g, "")).valueOf();
};

/**
 * 获取容器内当前在可视范围内的DIV元素
 * @param {HTMLElement} container - 要检查的容器元素
 * @param {Object} [options] - 配置选项
 * @param {number} [options.threshold=0] - 可见比例阈值(0-1)
 * @param {boolean} [options.horizontal=false] - 是否检测水平方向可见性
 * @returns {HTMLElement[]} 可见的DIV元素数组
 */
export function getVisibleDivs(container, options: Record<string, any> = {}) {
  const { threshold = 0.006, horizontal = false } = options;

  // 获取所有DIV元素
  const divs = Array.from(
    container?.querySelectorAll(".order-detail-region-body") || []
  );
  if (divs.length === 0) return [];

  // 获取视口尺寸和滚动位置
  const viewportHeight =
    window.innerHeight || document.documentElement.clientHeight;
  const viewportWidth =
    window.innerWidth || document.documentElement.clientWidth;
  const scrollY = window.pageYOffset || document.documentElement.scrollTop;
  const scrollX = window.pageXOffset || document.documentElement.scrollLeft;

  // 计算可视区域边界
  const viewportTop = scrollY + 89;
  const viewportBottom = scrollY + viewportHeight;
  const viewportLeft = scrollX;
  const viewportRight = scrollX + viewportWidth;

  // 筛选可见的DIV
  return divs.filter((div: HTMLElement) => {
    const rect = div.getBoundingClientRect();

    // 转换为文档坐标
    const divTop = rect.top + scrollY;
    const divBottom = rect.bottom + scrollY;
    const divLeft = rect.left + scrollX;
    const divRight = rect.right + scrollX;

    // 计算垂直方向可见比例
    const verticalVisible =
      Math.min(divBottom, viewportBottom) - Math.max(divTop, viewportTop);
    const verticalRatio = verticalVisible / rect.height;

    // 计算水平方向可见比例
    let visibleRatio = verticalRatio;
    if (horizontal) {
      const horizontalVisible =
        Math.min(divRight, viewportRight) - Math.max(divLeft, viewportLeft);
      const horizontalRatio = horizontalVisible / rect.width;
      visibleRatio = Math.min(verticalRatio, horizontalRatio);
    }

    // 检查是否满足阈值
    return visibleRatio > threshold;
  });
}

/**
 * 定义差异条目的类型
 * type: 变化类型 (added, removed, modified)
 * oldValue: 变化前的值 (仅适用于 removed 或 modified)
 * newValue: 变化后的值 (仅适用于 added 或 modified)
 */
type DiffEntry = {
  type: "added" | "removed" | "modified";
  oldValue?: any;
  newValue?: any;
};

/**
 * 定义深度差异结果对象的类型。
 * 它可以是嵌套的，每个属性/索引的值可以是另一个 DeepDiffObject (表示子对象的差异)，
 * 或者是 DiffEntry (表示该属性/索引本身的差异)。
 *
 * 这里的 key: string 包含了对象属性名和数组的字符串化索引 (如 '0', '1')。
 */
interface DeepDiffObject {
  [key: string]: DiffEntry | DeepDiffObject;
}

/**
 * 深度比较两个对象，并返回它们的差异。
 * 差异以一个嵌套对象的形式返回，结构与原对象相似。
 *
 * @param obj1 - 第一个值（旧值），可以是对象、数组或原始类型。
 * @param obj2 - 第二个值（新值），可以是对象、数组或原始类型。
 * @returns 差异对象 (DeepDiffObject)，如果根元素是直接修改的原始值或类型不匹配的对象则返回 DiffEntry，如果没有差异则返回 undefined。
 */
export function deepDiff(
  obj1: any,
  obj2: any
): DeepDiffObject | DiffEntry | undefined {
  // 1. 快速检查：如果两个值严格相等，则没有差异
  if (obj1 === obj2) {
    return undefined;
  }

  // 2. 处理原始类型或类型不匹配的情况
  // 如果其中一个不是对象（包括 null），或者它们的类型（对象 vs 数组 vs 原始类型）不同，
  // 那么这是一个值的直接修改。
  // 注意：typeof null 是 'object'，所以要特别处理 null。
  const isObj1Object = typeof obj1 === "object" && obj1 !== null;
  const isObj2Object = typeof obj2 === "object" && obj2 !== null;

  if (
    !isObj1Object ||
    !isObj2Object ||
    Object.prototype.toString.call(obj1) !==
      Object.prototype.toString.call(obj2)
  ) {
    // 当根节点就是基本类型差异，或者从一个对象变成另一个基本类型，或者类型（对象/数组/原始类型）发生变化时，
    // 直接返回一个 DiffEntry。
    return {
      type: "modified",
      oldValue: obj1,
      newValue: obj2
    };
  }

  // 到这里，我们知道 obj1 和 obj2 都是非 null 的对象，且类型相同（都是普通对象或都是数组）。

  // 3. 处理数组类型 (深度比较)
  if (Array.isArray(obj1) && Array.isArray(obj2)) {
    const arrayDiff: DeepDiffObject = {}; // 使用 DeepDiffObject 来存储索引对应的差异
    const maxLength = Math.max(obj1.length, obj2.length);
    let hasArrayChanges = false;

    for (let i = 0; i < maxLength; i++) {
      const val1 = obj1[i];
      const val2 = obj2[i];

      if (i < obj1.length && i >= obj2.length) {
        // 元素在旧数组中存在，新数组中不存在 (被移除)
        arrayDiff[i.toString()] = { type: "removed", oldValue: val1 };
        hasArrayChanges = true;
      } else if (i >= obj1.length && i < obj2.length) {
        // 元素在新数组中存在，旧数组中不存在 (被添加)
        arrayDiff[i.toString()] = { type: "added", newValue: val2 };
        hasArrayChanges = true;
      } else {
        // 元素在两个数组中都存在，进行深度比较
        const elementDiff = deepDiff(val1, val2);
        if (elementDiff !== undefined) {
          // 如果有差异，添加到 arrayDiff 对象中，键为字符串化的索引
          arrayDiff[i.toString()] = elementDiff;
          hasArrayChanges = true;
        }
      }
    }
    return hasArrayChanges ? arrayDiff : undefined;
  }

  // 4. 处理普通对象类型 (深度比较)
  const diff: DeepDiffObject = {};
  const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
  let hasObjectChanges = false;

  for (const key of allKeys) {
    const val1 = obj1[key];
    const val2 = obj2[key];

    // 属性只存在于 obj1 (被移除)
    if (key in obj1 && !(key in obj2)) {
      diff[key] = {
        type: "removed",
        oldValue: val1
      };
      hasObjectChanges = true;
    }
    // 属性只存在于 obj2 (被添加)
    else if (!(key in obj1) && key in obj2) {
      diff[key] = {
        type: "added",
        newValue: val2
      };
      hasObjectChanges = true;
    }
    // 属性在两个对象中都存在，需要递归比较
    else {
      const nestedDiff = deepDiff(val1, val2);
      if (nestedDiff !== undefined) {
        diff[key] = nestedDiff;
        hasObjectChanges = true;
      }
    }
  }

  // 如果差异对象有键，则表示存在差异；否则返回 undefined
  return hasObjectChanges ? diff : undefined;
}

export function getOrderPermission(sccsId: string | any, orderId: string) {
  return new Promise(async resolve => {
    const coopTeamMark: string = storageSession().getItem("coopTeamMark");

    if (coopTeamMark === "mainTeam") {
      const { data } = await getTradeSccsRolePermission({
        sccsId: sccsId,
        orderIdList: orderId
      });
      resolve(data);
    } else {
      const { data } = await getTradeCoopSccsRolePermission({
        sccsId: sccsId,
        orderIdList: orderId
      });
      resolve(data);
    }
  });
}

/**
 * 反转义HTML字符和JSON转义字符
 * 用于处理富文本编辑器等组件中被过度转义的内容
 * @param {string} value - 需要反转义的字符串
 * @returns {string} 反转义后的字符串
 */
export const unescapeHtmlAndJson = (value: string): string => {
  if (typeof value !== "string" || !value) return value;

  return (
    value
      // 处理 JSON 转义的双引号
      .replace(/\\"/g, '"')
      // 处理 HTML 实体转义
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, " ")
  );
};
