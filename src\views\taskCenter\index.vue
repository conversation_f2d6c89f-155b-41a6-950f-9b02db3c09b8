<template>
  <div class="task-center-container">
    <div class="task-center-container-left">
      <el-menu
        :default-active="menuActive"
        :default-openeds="['EMAIL_COOP']"
        class="el-menu-vertical-demo"
        @select="handleMenuClick"
      >
        <el-menu-item
          v-for="(menuItem, menuItemIndex) in menuList.filter(e => !e.children)"
          :key="menuItemIndex"
          :index="menuItem.type"
        >
          <template #title>
            <svg class="svg-icon svg-icon-coop" aria-hidden="true">
              <use :xlink:href="`#${menuItem.icon}`" />
            </svg>
            <span class="el-menu-title">
              <ReText
                type="info"
                class="order-detail-region-header-titlte"
                :tippyProps="{ delay: 50000 }"
              >
                {{ t(menuItem.title) }}
              </ReText></span
            >
            <span v-if="menuItem.badge" class="el-menu-badge">{{
              menuItem.badge > 99 ? "99+" : menuItem.badge
            }}</span>
          </template>
        </el-menu-item>
        <el-sub-menu
          v-for="(subMenuItem, subMenuItemIndex) in menuList.filter(
            e => e.children && e.children.length > 0
          )"
          :key="subMenuItemIndex"
          :index="subMenuItem.type"
        >
          <template #title>
            <span>{{ t(subMenuItem.title) }}</span>
          </template>
          <el-menu-item
            v-for="(item, itemIndex) in subMenuItem.children"
            :key="itemIndex"
            :index="item.type"
          >
            <template #title>
              <svg class="svg-icon svg-icon-coop" aria-hidden="true">
                <use :xlink:href="`#${item.icon}`" />
              </svg>
              <span class="el-menu-title">
                <ReText
                  type="info"
                  class="order-detail-region-header-titlte"
                  :tippyProps="{ delay: 50000 }"
                >
                  {{ t(item.title) }}
                </ReText></span
              >
              <span v-if="item.badge" class="el-menu-badge">{{
                item.badge
              }}</span>
            </template>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
    <div class="task-center-container-right">
      <div class="task-center-container-right-header">
        <TaskCenterHeader
          :menuActive="menuActive"
          :tabList="currentHeaderTabList"
          :taskCenterHeaderParams="taskCenterHeaderParams"
          :creatorData="creatorData"
          @beforeEnter="getCreatorListByMenuActive"
          @handleTabClick="handleTabClick"
          @handleSearchTypeClick="handleSearchTypeClick"
          @handleSearchClear="handleSearchClear"
          @handleSearchChange="handleSearchChange"
          @handleSearchTableByConditions="handleSearchTableByConditions"
          @viewUpdata="viewUpdata"
        />
      </div>
      <div
        v-loading="loading && pageInfo.pageNo === 1"
        class="task-center-container-right-container"
      >
        <TaskCenterList
          v-if="
            [
              'MY_USER_TASK',
              'MY_TEAM_TASK',
              'MY_CREATE_TASK',
              'MY_FAVORITE_TASK'
            ].includes(menuActive)
          "
          :loading="loading"
          :disabled="disabled"
          :noMore="noMore"
          :pageInfo="pageInfo"
          :currentMenuTabList="currentMenuTabList"
          @loadList="loadList"
          @update="refreshPage"
        />
        <MyReceiveCoopEmailList
          v-if="menuActive === 'MY_FAVORITE_EMAIL_COOP'"
          :receiveTableData="currentMenuTabList"
          :disabled="disabled"
          :loading="loading"
          :noMore="noMore"
          @loadReceiveEmailList="loadList"
          @update="updateReceiveCoopEmailList"
        />
        <MyInitiateCoopEmailList
          v-if="menuActive === 'MY_FAVORITE_EMAIL_TASK'"
          :initiateCoopEmailList="currentMenuTabList"
          :disabled="disabled"
          :loading="loading"
          :noMore="noMore"
          @loadInitiateCoopEmailList="loadList"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import TaskCenterList from "./components/TaskCenterList.vue";
import MyReceiveCoopEmailList from "./components/MyReceiveCoopEmailList.vue";
import MyInitiateCoopEmailList from "./components/MyInitiateCoopEmailList.vue";
import TaskCenterHeader from "./components/TaskCenterHeader.vue";
import { ReText } from "@/components/ReText";

import {
  getUserTaskPage,
  getTeamPendingTaskCount,
  getUserPendingTaskCount,
  getCoopEmailPendingNumber,
  getCoopEmailPage,
  getMyReceiveCoopEmailPage,
  getCreatorList,
  getPromoter
} from "@/api/taskCenter";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

import { computed, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";

interface menuItem {
  type?: string;
  title: string;
  icon?: string;
  badge?: number;
  children?: menuItem[];
  headerTabList?: tabItem[];
  conditionList?: condition[];
}
interface tabItem {
  title: string;
  type: string;
  badge?: number;
  default?: boolean;
  tip?: string;
  isShowTip?: boolean;
  showRightText?: boolean;
  showViewUpdata?: boolean;
}
interface condition {
  customerDateType: string;
  operator: string;
  queryField: string;
  rangeDateType: string;
  value: string;
}
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const menuActive = ref<any>("MY_USER_TASK");
const menuList = ref<menuItem[]>([
  // 我的任务 可过滤出【全部】【待处理】【已处理】【已完成】，默认展示【待处理】
  {
    type: "MY_USER_TASK",
    title: "trade_my_task",
    icon: "link-my-tasks",
    badge: 0,
    headerTabList: [
      // 全部
      {
        title: "trade_common_all",
        type: "ALL",
        tip: "trade_common_all_usertask_tip",
        showRightText: true,
        isShowTip: true
      },
      //     待处理
      {
        title: "trade_pending_processing",
        type: "PENDING",
        default: true,
        tip: "trade_pending_processing_usertask_tip",
        isShowTip: true,
        showRightText: true,
        badge: 0
      },
      //   已处理
      {
        title: "trade_processed",
        type: "PROCESSED",
        tip: "trade_processed_usertask_tip",
        showRightText: true,
        isShowTip: true
      },
      //   已完成
      {
        title: "trade_order_stateCompleted",
        type: "COMPLETE",
        tip: "trade_order_stateCompleted_usertask_tip",
        showRightText: true,
        isShowTip: true
      }
    ],
    conditionList: []
  },
  // 团队任务 可过滤出【全部】【待指派】【已指派】【已完成】，默认展示【待指派】
  {
    type: "MY_TEAM_TASK",
    title: "trade_team_task",
    icon: "link-team-tasks",
    badge: 0,
    headerTabList: [
      // 全部
      {
        title: "trade_common_all",
        type: "ALL",
        tip: "trade_common_all_teamtask_tip",
        isShowTip: true,
        showRightText: true
      },
      //  待指派
      {
        title: "trade_to_be_assigned",
        type: "PENDING",
        default: true,
        tip: "trade_to_be_assigned_teamtask_tip",
        isShowTip: true,
        showRightText: true,
        badge: 0
      },
      //   已指派
      {
        title: "trade_ASSIGNED",
        tip: "trade_ASSIGNED_teamtask_tip",
        isShowTip: true,
        showRightText: true,
        type: "PROCESSED"
      },
      //   已完成
      {
        title: "trade_order_stateCompleted",
        tip: "trade_order_stateCompleted_usertask_tip",
        isShowTip: true,
        showRightText: true,
        type: "COMPLETE"
      }
    ],
    conditionList: []
  },
  // 我发起的 可过滤出【全部】【进行中】【已完成】，默认展示【进行中】
  {
    type: "MY_CREATE_TASK",
    title: "trade_I_initiated_it",
    icon: "link-my-idea",
    headerTabList: [
      // 全部
      {
        title: "trade_common_all",
        type: "ALL",
        tip: "trade_common_all_createtask_tip",
        showRightText: true,
        isShowTip: true
      },
      //   进行中
      {
        title: "trade_home_inProgress",
        type: "PENDING",
        default: true,
        showRightText: true,
        tip: "trade_home_inProgress_createtask_tip",
        isShowTip: true,
        badge: 0
      },
      //   已完成
      {
        title: "trade_order_stateCompleted",
        tip: "trade_order_stateCompleted_usertask_tip",
        isShowTip: true,
        showRightText: true,
        type: "COMPLETE"
      }
    ],
    conditionList: []
  },
  // 我关注的 可过滤出【全部】【进行中】【已完成】，默认展示【全部】
  {
    type: "MY_FAVORITE_TASK",
    title: "trade_what_I_am_concerned_about",
    icon: "link-my-focus",
    headerTabList: [
      // 全部
      {
        title: "trade_common_all",
        default: true,
        tip: "trade_common_all_favoritetask_tip",
        isShowTip: true,
        showRightText: true,
        type: "ALL"
      },
      //     进行中
      {
        title: "trade_home_inProgress",
        type: "PENDING",
        tip: "trade_home_inProgress_favoritetask_tip",
        isShowTip: true,
        showRightText: true,
        badge: 0
      },
      //   已完成
      {
        title: "trade_order_stateCompleted",
        tip: "trade_order_stateCompleted_usertask_tip",
        isShowTip: true,
        showRightText: true,
        type: "COMPLETE"
      }
    ],
    conditionList: []
  },
  // 邮件协作
  {
    type: "EMAIL_COOP",
    title: "trade_order_emailCollaborate",
    children: [
      // 我发起的 可过滤出【全部】【进行中】【已完成】，默认展示【进行中】
      {
        type: "MY_FAVORITE_EMAIL_TASK",
        title: "trade_I_initiated_it",
        icon: "link-my-idea",
        headerTabList: [
          // 全部
          {
            title: "trade_common_all",
            tip: "trade_common_all_email_tip",
            isShowTip: true,
            showViewUpdata: true,
            type: "ALL"
          },
          //     进行中
          {
            title: "trade_home_inProgress",
            type: "PENDING",
            tip: "trade_home_inProgress_email_tip",
            isShowTip: true,
            showViewUpdata: true,
            default: true,
            badge: 0
          },
          //   已完成
          {
            title: "trade_order_stateCompleted",
            tip: "trade_order_stateCompleted_usertask_tip",
            isShowTip: true,
            showViewUpdata: true,
            type: "COMPLETE"
          }
        ],
        conditionList: []
      },
      // 我协作的 可过滤出【全部】【待处理】【已处理】【已完成】，默认展示【待处理】
      {
        type: "MY_FAVORITE_EMAIL_COOP",
        title: "trade_I_collaborate_with",
        icon: "link-email-tasks",
        badge: 0,
        headerTabList: [
          // 全部
          {
            title: "trade_common_all",
            tip: "trade_common_all_receiver_tip",
            isShowTip: true,
            type: "ALL"
          },
          //     待处理
          {
            title: "trade_pending_processing",
            type: "PENDING",
            tip: "trade_pending_processing_receiver_tip",
            isShowTip: true,
            default: true,
            badge: 0
          },
          //   已处理
          {
            title: "trade_processed",
            tip: "trade_processed_usertask_tip",
            isShowTip: true,
            type: "PROCESSED"
          },
          //   已完成
          {
            title: "trade_order_stateCompleted",
            tip: "trade_order_stateCompleted_usertask_tip",
            isShowTip: true,
            type: "COMPLETE"
          }
        ],
        conditionList: []
      }
    ]
  }
]);

const currentHeaderTabList = ref<tabItem[]>();
const taskCenterHeaderParams = ref({
  currentTab: "",
  searchValue: "",
  searchPlaceholder: "trade_order_mask_and_msName_placeHolder",
  searchTypeOptions: [
    //
    {
      value: "RECENT",
      label: "trade_email_createTimeSearch"
    },
    {
      value: "EARLIEST",
      label: "trade_the_earliest_to_initiate"
    }
  ],
  showRightText: false,
  showViewUpdata: false,
  submitNew: false,
  searchType: "RECENT",
  filterType: "ALL",
  tip: "",
  isShowTip: false,
  menuConditionMap: null
});
// 任务列表参数
const pageInfo = ref({
  pageNo: 1,
  pageSize: 10
});
const loading = ref(false);
const noMore = ref(false);
const disabled = computed(() => {
  return loading.value || noMore.value;
});
const currentMenuTabList = ref([]);

const handleMenuClick = val => {
  menuActive.value = val;
  taskCenterHeaderParams.value.searchType = "RECENT";
};
const handleTabClick = item => {
  taskCenterHeaderParams.value.currentTab = item.type;
  currentMenuTabList.value = [];
  pageInfo.value.pageNo = 1;
  taskCenterHeaderParams.value.tip = item.tip;
  loadList();
  initData();
};
const handleSearchTypeClick = type => {
  taskCenterHeaderParams.value.searchType = type;
  currentMenuTabList.value = [];
  pageInfo.value.pageNo = 1;
  loadList();
};
const handleSearchClear = () => {
  taskCenterHeaderParams.value.searchValue = "";
};
const handleSearchChange = val => {
  currentMenuTabList.value = [];
  taskCenterHeaderParams.value.searchValue = val;
  pageInfo.value.pageNo = 1;
  loadList();
};
const viewUpdata = () => {
  taskCenterHeaderParams.value.submitNew =
    !taskCenterHeaderParams.value.submitNew;
  currentMenuTabList.value = [];
  pageInfo.value.pageNo = 1;
  loadList();
};
// 筛选条件变更查询
const handleSearchTableByConditions = conditions => {
  currentMenuTabList.value = [];
  taskCenterHeaderParams.value.menuConditionMap.set(
    menuActive.value,
    conditions
  );
  pageInfo.value.pageNo = 1;
  loadList();
};

const updateReceiveCoopEmailList = () => {
  currentMenuTabList.value = [];
  pageInfo.value.pageNo = 1;
  getCoopEmailPendingNumber({
    email: JSON.parse(localStorage.getItem("user-info")).email
  }).then(res => {
    if (res.code === 0) {
      menuList.value.find(e => e.type === "EMAIL_COOP").children[1].badge =
        res.data;
      menuList.value
        .find(e => e.type === "EMAIL_COOP")
        .children[1].headerTabList.find(e => e.type === "PENDING").badge =
        res.data;
    }
  });
  loadList();
};

const creatorData = ref({});
// 获取当前菜单和状态的创建人数据
const getCreatorListByMenuActive = async () => {
  if (menuActive.value === "MY_FAVORITE_EMAIL_TASK") {
    creatorData.value = [];
    return;
  } else if (menuActive.value === "MY_FAVORITE_EMAIL_COOP") {
    const params = {
      email: JSON.parse(localStorage.getItem("user-info")).email
    };
    const { data } = await getPromoter(params);
    creatorData.value = data;
  } else {
    const params = {
      status: taskCenterHeaderParams.value.currentTab,
      taskQueryType: menuActive.value
    };
    const { data } = await getCreatorList(params);
    creatorData.value = data;
  }
};

const loadList = async () => {
  loading.value = true;
  let res = null;
  if (
    [
      "MY_USER_TASK",
      "MY_TEAM_TASK",
      "MY_CREATE_TASK",
      "MY_FAVORITE_TASK"
    ].includes(menuActive.value)
  ) {
    res = await getUserTaskPage({
      pageNo: pageInfo.value.pageNo,
      pageSize: pageInfo.value.pageSize,
      sortBy: null,
      descending:
        taskCenterHeaderParams.value.searchType === "RECENT" ? true : false,
      orderBy: "createTime",
      otherPropertyList: [],
      otherDirectionList: [true, false],
      createTime: null,
      keyword: taskCenterHeaderParams.value.searchValue,
      status: taskCenterHeaderParams.value.currentTab,
      taskQueryType: menuActive.value,
      taskConditionList:
        taskCenterHeaderParams.value.menuConditionMap.get(menuActive.value) ||
        []
    });
  } else if (menuActive.value === "MY_FAVORITE_EMAIL_COOP") {
    res = await getMyReceiveCoopEmailPage({
      email: JSON.parse(localStorage.getItem("user-info")).email,
      pageNo: pageInfo.value.pageNo,
      pageSize: pageInfo.value.pageSize,
      sortBy: "createTime",
      descending:
        taskCenterHeaderParams.value.searchType === "RECENT" ? true : false,
      orderBy: "createTime",
      otherPropertyList: [],
      otherDirectionList: [true, false],
      createTime: null,
      keyword: taskCenterHeaderParams.value.searchValue,
      submitNew: taskCenterHeaderParams.value.submitNew, // 查看是否有更新
      taskConditionReqVO:
        taskCenterHeaderParams.value.menuConditionMap.get(
          "MY_FAVORITE_EMAIL_COOP"
        ) || [],
      coopEmailStatus: taskCenterHeaderParams.value.currentTab
    });
  } else if (menuActive.value === "MY_FAVORITE_EMAIL_TASK") {
    res = await getCoopEmailPage({
      email: JSON.parse(localStorage.getItem("user-info")).email,
      pageNo: pageInfo.value.pageNo,
      pageSize: pageInfo.value.pageSize,
      sortBy: "createTime",
      descending:
        taskCenterHeaderParams.value.searchType === "RECENT" ? true : false,
      orderBy: "createTime",
      otherPropertyList: [],
      otherDirectionList: [true, false],
      createTime: null,
      keyword: taskCenterHeaderParams.value.searchValue,
      submitNew: taskCenterHeaderParams.value.submitNew, // 查看是否有更新
      taskConditionReqVO:
        taskCenterHeaderParams.value.menuConditionMap.get(
          "MY_FAVORITE_EMAIL_TASK"
        ) || [],
      coopEmailStatus: taskCenterHeaderParams.value.currentTab
    });
  }

  if (res.code === 0) {
    pageInfo.value.pageNo++;
    currentMenuTabList.value = currentMenuTabList.value.concat(res.data.list);
    if (currentMenuTabList.value.length >= res.data.total) {
      noMore.value = true;
    } else {
      noMore.value = false;
    }
    loading.value = false;
  }
};
const initRouter = (): void => {
  menuActive.value = route.query.taskType;
  taskCenterHeaderParams.value.menuConditionMap = new Map();
  menuList.value.forEach(e => {
    if (e.children) {
      e.children.forEach(f => {
        taskCenterHeaderParams.value.menuConditionMap.set(f.type, []);
      });
    } else {
      taskCenterHeaderParams.value.menuConditionMap.set(e.type, []);
    }
  });
  taskCenterHeaderParams.value.menuConditionMap.set(
    route.query.taskType,
    route.query.conditions ? JSON.parse(route.query.conditions) : []
  );
  useMultiTagsStoreHook().handleTags("push", {
    path: `/taskCenter`,
    name: "taskCenter",
    query: route.query,
    meta: {
      title: {
        zh: t("trade_task_center")
      },
      dynamicLevel: 1
    }
  });
  // 路由跳转
  router.push({ name: "taskCenter", query: route.query });
};
// 刷新页面
const refreshPage = () => {
  currentMenuTabList.value = [];
  pageInfo.value.pageNo = 1;
  loadList();
  initData();
};

initRouter();
const initData = () => {
  Promise.all([
    getTeamPendingTaskCount(), // 团队任务 待处理数量
    getUserPendingTaskCount(), // 我的任务 待处理数量
    getCoopEmailPendingNumber({
      email: JSON.parse(localStorage.getItem("user-info")).email
    }) // 邮件协作 待处理数量
  ])
    .then(res => {
      // 任务中心数量赋值
      const MY_TEAM_TASK_count = res[0].data.count;
      menuList.value.find(e => e.type === "MY_TEAM_TASK").badge =
        MY_TEAM_TASK_count;
      menuList.value
        .find(e => e.type === "MY_TEAM_TASK")
        .headerTabList.find(e => e.type === "PENDING").badge =
        MY_TEAM_TASK_count;
      menuList.value.find(e => e.type === "MY_USER_TASK").badge =
        res[1].data.count;
      menuList.value
        .find(e => e.type === "MY_USER_TASK")
        .headerTabList.find(e => e.type === "PENDING").badge =
        res[1].data.count;
      menuList.value.find(e => e.type === "EMAIL_COOP").children[1].badge =
        res[2].data;
      menuList.value
        .find(e => e.type === "EMAIL_COOP")
        .children[1].headerTabList.find(e => e.type === "PENDING").badge =
        res[2].data;
    })
    .catch(err => {
      console.log("initData", err);
    });
};
watch(
  () => menuActive.value,
  val => {
    const menuItem = menuList.value.find(e => {
      if (e.children) {
        return e.children.find(f => f.type === val);
      } else {
        return e.type === val;
      }
    });
    currentHeaderTabList.value =
      menuItem.headerTabList ||
      menuItem.children.find(e => e.type === val).headerTabList;
    taskCenterHeaderParams.value.currentTab = currentHeaderTabList.value.find(
      e => e.default
    ).type;
    taskCenterHeaderParams.value.tip = currentHeaderTabList.value.find(
      e => e.default
    ).tip;
    taskCenterHeaderParams.value.showRightText =
      currentHeaderTabList.value.find(e => e.default).showRightText || false;
    taskCenterHeaderParams.value.showViewUpdata =
      currentHeaderTabList.value.find(e => e.default).showViewUpdata || false;
    if (val === "MY_FAVORITE_EMAIL_COOP") {
      // 我协作的
      taskCenterHeaderParams.value.searchPlaceholder =
        "trade_theme_and_order_identification_placeholder";
      taskCenterHeaderParams.value.searchTypeOptions = [
        //
        {
          value: "RECENT",
          label: "trade_email_createTimeSearch"
        },
        {
          value: "UPDATETIMESEARCH",
          label: "trade_email_updateTimeSearch"
        }
      ];
    } else {
      taskCenterHeaderParams.value.searchPlaceholder =
        "trade_order_mask_and_msName_placeHolder";
      taskCenterHeaderParams.value.searchTypeOptions = [
        //
        {
          value: "RECENT",
          label: "trade_email_createTimeSearch"
        },
        {
          value: "EARLIEST",
          label: "trade_the_earliest_to_initiate"
        }
      ];
    }
    menuItem.conditionList =
      taskCenterHeaderParams.value.menuConditionMap.get(val);
    currentMenuTabList.value = [];
    taskCenterHeaderParams.value.searchValue = "";
    pageInfo.value.pageNo = 1;
    loadList();
    initData();
  },
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  initData();
});
</script>
<style lang="scss" scoped>
@use "./components/index.scss";
</style>
