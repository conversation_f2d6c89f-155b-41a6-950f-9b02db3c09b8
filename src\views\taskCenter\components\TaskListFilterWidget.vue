<template>
  <div style="display: inline-flex">
    <el-popover
      ref="targetRef"
      placement="bottom-end"
      trigger="click"
      width="auto"
      @before-enter="beforeEnter"
    >
      <template #reference>
        <span
          class="order-manage-btn"
          :class="{
            active: searchConditions.length !== 0
          }"
        >
          <i class="iconfont link-filter" />
          <span v-if="searchConditions.length === 0">
            {{ t("trade_common_filter") }}
          </span>
          <span v-else>{{ searchConditions.length }}</span>
        </span>
      </template>
      <template #default>
        <div class="order-manage-search-body">
          <div class="order-manage-search-header">
            <div class="order-manage-search-title">
              {{ t("trade_common_filteringCriteria") }}
            </div>
          </div>
          <div class="order-manage-search-container">
            <div
              v-for="(item, index) in searchConditions"
              :key="item"
              class="order-manage-search-condition-row"
            >
              <TcSearchConditions
                ref="conditionRef"
                class="order-manage-search-condition-col"
                :condition="item"
                :conditionList="conditionList"
                :creatorData="creatorData"
                :labelConditionMap="labelConditionMap"
              />
              <i
                class="iconfont link-close order-manage-search-condition-icon"
                @click="handleRemoveCondition(index)"
              />
            </div>

            <div
              class="order-manage-search-btn"
              @click="handleAddSearchCondition"
            >
              <i class="iconfont link-add" />
              {{ t("trade_common_filteringCriteriaBtn") }}
            </div>
          </div>
          <div class="order-manage-search-bottom">
            <el-button @click="handleResetConditions">
              {{ t("trade_common_reSet") }}
            </el-button>
            <el-button
              type="primary"
              color="#0070D2"
              @click="handleSearchConditions"
            >
              {{ t("trade_common_query") }}
            </el-button>
          </div>
        </div>
      </template>
    </el-popover>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import TcSearchConditions from "@/views/taskCenter/components/TcSearchConditions/index.ts";

const props = defineProps({
  orderFilterList: {
    type: Array as PropType<any>,
    default: () => []
  },
  presentViewType: {
    type: String as PropType<string>,
    default: "ORDER"
  },
  creatorData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const emit = defineEmits([
  "beforeEnter",
  "handleSearchTableByConditions",
  "handleOrderListFilterConditionList"
]);

const { t } = useI18n();
const conditionRef = ref<HTMLElement | any>(null);
const targetRef = ref<HTMLElement | any>(null);
const labelConditionMap = ref<any>();
const conditionList = ref<any>();
const searchConditions = ref<any>([]);

const handleAddSearchCondition = () => {
  searchConditions.value.push({
    queryField: "",
    operator: "",
    customerDateType: "",
    rangeDateType: "",
    value: ""
  });
};

const handleRemoveCondition = (index: number) => {
  searchConditions.value.splice(index, 1);
};

const handleSearchConditions = () => {
  for (let i = 0, len = searchConditions.value.length; i < len; i++) {
    const conditionRow = searchConditions.value[i];
    const { customerDateType, operator, queryField, rangeDateType, value } =
      conditionRow;
    if (
      !operator ||
      (!value &&
        queryField === "CREATE_MEMBER_ID" &&
        !["IS_NULL", "IS_NOT_NULL"].includes(operator)) ||
      (queryField !== "CREATE_MEMBER_ID" &&
        !["IS_NULL", "IS_NOT_NULL"].includes(operator) &&
        customerDateType &&
        ![
          "THIS_WEEK",
          "LAST_WEEK",
          "NEXT_WEEK",
          "THIS_MONTH",
          "LAST_MONTH",
          "NEXT_MONTH",
          "THIS_YEAR",
          "LAST_YEAR",
          "NEXT_YEAR",
          "TODAY",
          "TOMORROW",
          "YESTERDAY"
        ].includes(customerDateType) &&
        !value) ||
      (queryField !== "CREATE_MEMBER_ID" &&
        !["IS_NULL", "IS_NOT_NULL"].includes(operator) &&
        rangeDateType &&
        ![
          "THIS_WEEK",
          "LAST_WEEK",
          "NEXT_WEEK",
          "THIS_MONTH",
          "LAST_MONTH",
          "NEXT_MONTH",
          "THIS_YEAR",
          "LAST_YEAR",
          "NEXT_YEAR",
          "TODAY",
          "TOMORROW",
          "YESTERDAY"
        ].includes(rangeDateType) &&
        !value)
    ) {
      ElMessage.error("请完善设置");
      return;
    }
  }

  targetRef.value.hide();
  emit("handleSearchTableByConditions", searchConditions.value);
};
const beforeEnter = () => {
  // 弹窗弹出前请求创建人接口获取数据
  emit("beforeEnter");
};
const handleResetConditions = () => {
  searchConditions.value = [];
  targetRef.value.hide();
  emit("handleSearchTableByConditions", searchConditions.value);
};

watch(
  () => props.orderFilterList,
  () => {
    searchConditions.value = props.orderFilterList;
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped>
.order-manage-btn {
  display: inline-block;
  padding: 6px 11px;
  margin-left: 10px;
  font-size: 14px;
  line-height: 19px;
  color: #595959;
  text-align: left;
  cursor: pointer;
  border-radius: 4px;

  ::v-deep(.el-dropdown-link) {
    &:hover {
      background: #e5e5e5;
    }
  }

  &.active {
    color: #0070d2;
    background: #eef8ff;

    &:hover {
      background: #eef8ff;
    }
  }

  &:hover {
    background: #e5e5e5;
  }

  .iconfont {
    margin-right: 6px;
    font-size: 12px;

    &:only-child {
      margin-right: 0;
    }
  }

  &.is-disabled {
    color: #bfbfbf !important;
  }
}

.order-manage-search-body {
  width: 770px;

  .order-manage-search-header {
    margin-bottom: 23px;
    font-size: 14px;
    line-height: 20px;
    color: #595959;
  }

  .order-manage-search-container {
    .order-manage-search-condition-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .order-manage-search-condition-icon {
        font-size: 12px;
        cursor: pointer;
      }
    }

    .order-manage-search-btn {
      width: fit-content;
      margin-top: 15px;
      font-size: 14px;
      line-height: 20px;
      color: #0070d2;
      vertical-align: middle;
      cursor: pointer;

      .iconfont {
        font-size: 13px;
      }
    }
  }

  .order-manage-search-bottom {
    text-align: right;
  }
}
</style>
