<template>
  <template v-if="widgetFormData">
    <el-radio-group
      v-if="widgetConfigure.props.layout !== 'select'"
      v-model="widgetFormData[widgetConfigure._fc_id]"
      class="widget-radio-group"
    >
      <el-radio
        v-for="(item, index) in widgetConfigure.props.options"
        :key="index"
        :value="item.value"
        :style="{
          display:
            widgetConfigure.props.layout === 'vertical'
              ? 'flex'
              : 'inline-flex',
          width: widgetConfigure.props.layout === 'vertical' ? '100%' : 'auto'
        }"
        @click.prevent="handleCancelRadio(item.value)"
      >
        <template #default>
          <div class="radio-span" :style="{ background: item.color }">
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ widgetOptionItem(item) }}
            </ReText>
          </div>
        </template>
      </el-radio>
    </el-radio-group>
    <el-select-v2
      v-else
      v-model="widgetFormData[widgetConfigure._fc_id]"
      :options="widgetConfigure.props.options"
      :placeholder="placeholder"
      clearable
      :collapse-tags="!!widgetConfigure.inTableColumns"
      collapse-tags-tooltip
      :max-collapse-tags="1"
      :teleported="widgetRowIndex === -1"
      popper-class="widget-select"
    >
      <template #tag>
        <el-tag
          v-for="item in widgetFormData[widgetConfigure._fc_id]"
          :key="item.value"
          :color="getSelectOptions(item).color"
          style="
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            word-wrap: break-word;
          "
        >
          <ReText
            type="info"
            :tippyProps="{ delay: 50000 }"
            class="dr-radio-title"
          >
            {{ getSelectOptions(item).label }}
          </ReText>
          <i
            class="iconfont link-close dr-radio-icon"
            @click="handleDelete(item)"
          />
        </el-tag>
      </template>
      <template #label="{ value }">
        <el-tag
          :color="getSelectOptions(value)?.color"
          style="
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            word-wrap: break-word;
          "
        >
          <ReText
            type="info"
            :tippyProps="{ delay: 50000 }"
            class="dr-radio-title"
          >
            {{ getSelectOptions(value)?.label }}
          </ReText>
        </el-tag>
      </template>
      <template #default="{ item }">
        <div class="select-v2-item">
          <el-radio
            :model-value="widgetFormData[widgetConfigure._fc_id]"
            :label="item?.label"
            :value="item?.value"
          >
            <span class="select-v2-text" :style="{ background: item?.color }">
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                {{ item?.label }}
              </ReText>
            </span>
          </el-radio>
        </div>
      </template>
    </el-select-v2>
  </template>
</template>

<script lang="ts" setup>
import { computed, inject, ref, watch } from "vue";
import { ElRadio, ElTag, ElSelectV2, ElRadioGroup } from "element-plus";
import { ReText } from "@/components/ReText";
import { cloneDeep, storageLocal } from "@pureadmin/utils";

interface SelectOptionProp {
  value: string;
  label: string;
  color: string;
}

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  isRenderDefaultValue: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const widgetOptionItem = computed(() => item => {
  const translationLang = storageLocal().getItem("translationLang");
  return translationLang === "en" ? item.labelEn || item.label : item.label;
});

const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const emit = defineEmits(["handleSubTableWidgetValueChange"]);

const getSelectOptions = (value: string): SelectOptionProp => {
  const option = props.widgetConfigure.props.options.find(
    widget => widget.value === value
  );
  return option as SelectOptionProp;
};

const handleCancelRadio = (value: string): void => {
  widgetFormData.value[props.widgetConfigure._fc_id] =
    value === widgetFormData.value[props.widgetConfigure._fc_id] ? "" : value;
};

const handleDelete = (value: string): void => {
  const arr = widgetFormData.value[props.widgetConfigure._fc_id];
  const index = arr.findIndex(widget => widget === value);
  if (index !== -1) arr.splice(index, 1);
};

const getWidgetValueLabel = () => {
  const options = props.widgetConfigure.props.options;
  const widgetValue = widgetFormData.value[props.widgetConfigure._fc_id];
  const item = options.find(option => option.value === widgetValue);
  return item
    ? `<span class="select-v2-text" style="background: ${item.color}">${item.label}</span>`
    : "";
};

// 监听 trendsForm 变化，若被清空则同步清空 widgetFormData
watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  newVal => {
    if (newVal === undefined || newVal === null || newVal === "") {
      widgetFormData.value[props.widgetConfigure._fc_id] = "";
    } else {
      const val = cloneDeep(newVal);
      widgetFormData.value[props.widgetConfigure._fc_id] =
        val instanceof Array ? val.join(",") : val;
    }
  },
  { immediate: true }
);

watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    const optionValues = props.widgetConfigure.props.options.find(
      option => option.checked
    )?.value;

    if (
      ((optionValues && optionValues !== newVal) ||
        newVal !== props.trendsForm[props.widgetConfigure._fc_id]) &&
      !(!newVal && !oldVal)
    ) {
      const widgetId = props.widgetConfigure._fc_id;
      const widgetLabelText = getWidgetValueLabel();
      const emitData = {
        obj: widgetFormData.value[widgetId],
        label: widgetLabelText,
        widgetId,
        $rowIndex: props.widgetRowIndex
      };
      if (props.widgetRowIndex !== -1) {
        emit(
          "handleSubTableWidgetValueChange",
          widgetId,
          emitData,
          widgetFormData.value[widgetId]
        );
      } else {
        handleWidgetFormsValue(
          widgetId,
          emitData,
          widgetFormData.value[widgetId]
        );
      }
    }
  },
  { deep: true, immediate: true }
);
</script>

<style lang="scss" scoped>
.el-radio-group {
  width: 100%;

  .el-radio {
    max-width: 100% !important;
    margin-right: 10px !important;

    ::v-deep(.el-radio__label) {
      box-sizing: content-box;
      display: inline-flex;
      align-items: center;
      max-width: calc(100% - 20px);
      height: 100%;

      .radio-span {
        overflow: hidden;
        text-overflow: ellipsis;
        word-wrap: break-word;
        vertical-align: middle;
      }
    }
  }

  .el-radio.is-checked {
    .radio-span {
      color: #0070d2;
    }
  }
}

.widget-radio-group {
  .el-radio {
    .el-radio__label {
      display: inline-flex;
      align-items: center;
    }

    .radio-span {
      padding: 5px;
      font-size: 12px;
      line-height: 12px;
      color: #262626;
      text-align: center;
      background: #f2f2f2;
      border-radius: 3px;

      ::v-deep(.el-text) {
        font-size: 12px;
        color: #262626;
      }
    }
  }
}

.el-select-dropdown__item {
  .el-radio {
    display: flex;
    align-items: center;
    width: 100%;
    height: auto !important;

    ::v-deep(.el-radio__label) {
      box-sizing: content-box;
      display: flex;
      width: calc(100% - 20px);

      .select-v2-text {
        width: auto;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        word-wrap: break-word;

        .el-text {
          height: 100%;
          font-size: 12px;
          line-height: inherit;
          color: #262626;
        }
      }
    }
  }
}

.select-v2-text {
  display: inline-block;
  max-width: 100%;
  padding: 0 6px;
  line-height: 20px;
  color: #262626 !important;
  word-wrap: break-word;
  white-space: nowrap;
  border-radius: 3px;
}

.checkbox-tag {
  display: inline-flex;
  align-items: center;
  max-width: 100%;
  padding: 0 6px;
  background: #efefef;
  border-radius: 3px;

  .el-text {
    height: 24px !important;
  }

  .dr-checkbox-title {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    font-size: 12px;
    color: #262626;
    text-overflow: ellipsis;
    word-wrap: break-word;
  }
}

.dr-radio-title {
  font-weight: 500;
  color: #262626;
}

.dr-radio-icon {
  margin-left: 6px;
  font-size: 8px;
  font-weight: 500;
  color: #262626;
}
</style>

<style lang="scss">
.widget-select {
  background: red;

  .el-select-dropdown__item {
    display: flex;
    align-items: center;

    .select-v2-item {
      width: auto;
      max-width: 100%;
    }
  }
}
</style>
