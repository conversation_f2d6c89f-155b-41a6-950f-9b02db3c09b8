import { http } from "@/utils/http";
import type { Result } from "./type";
import type { ApiTeamCreateType } from "@/views/createTeam/types/types.d";
import type { TeamMemberRoleList } from "@/views/teamManage/types/type.d";

/**
 * 创建团队
 * @param data
 * @returns
 */
export const createTeam = (data: ApiTeamCreateType) => {
  return http.request<Result>("post", "/trade/team/create", { data });
};

/**
 * 获取团队角色下拉选项
 * @param params
 * @returns
 */
export const getTeamRoleOption = (params: { withOwner: boolean }) => {
  return http.request<Result>("get", "/trade/team-role/options", { params });
};

/**
 * 邀请用户
 * @param data
 * @returns
 */
export const inviteMember = (
  data: { email: string; teamRoleIds: string[] }[]
) => {
  return http.request<Result>("post", "/trade/team-member/invite", { data });
};

/**
 * 通过团队码得到团队
 * @param data
 * @returns
 */
export const getTeamByCode = (params: { teamCode: string }) => {
  return http.request<Result>("get", "/trade/team/get-by-code", { params });
};

/**
 * 邀请团队成为协作团队
 * @param data
 * @returns
 */
export const inviteTeam = (data: {
  invitedTeamId: string;
  remark?: string | null;
}) => {
  return http.request<Result>("post", "/trade/team-invite-record/create", {
    data
  });
};

/**
 * 更新贸易端团队信息
 * @param data
 * @returns
 */
export const updateTeam = (data: ApiTeamCreateType) => {
  return http.request<Result>("put", "/trade/team/update", {
    data
  });
};

/**
 * 获得贸易端团队角色分页
 * @param data
 * @returns
 */
export const getTeamRoleList = (data: TeamMemberRoleList) => {
  return http.request<Result>("post", "/trade/team-role/page", {
    data
  });
};

/**
 * 创建贸易端团队角色
 * @returns
 */
export const getTeamRolePermissionTree = () => {
  return http.request<Result>(
    "get",
    "/trade/team-role/get-team-role-permission-tree"
  );
};

/**
 * 创建贸易端团队角色
 * @param data
 * @returns
 */
export const createTeamRole = data => {
  return http.request<Result>("post", "/trade/team-role/create", { data });
};

/**
 * 更新贸易端团队角色
 * @param data
 * @returns
 */
export const updateTeamRole = data => {
  return http.request<Result>("put", "/trade/team-role/update", { data });
};

/**
 * 删除贸易端团队角色
 * @param data
 * @returns
 */
export const deleteTeamRole = (params: { id: string }) => {
  return http.request<Result>("delete", "/trade/team-role/delete", { params });
};

/**
 * 获取当前用户在当前团队可见sccs列表
 * @param params
 * @returns
 */
export const getUserSccsList = () => {
  return http.request<Result>("get", "/trade/sccs/sccs-list-by-user");
};

/**
 * 获取当前团队的角色下拉选项
 * @param params
 * @returns
 */
export const getUserTeamRoleList = (params: { withOwner: boolean }) => {
  return http.request<Result>("get", "/trade/team-role/options", { params });
};

/**
 * 获取团队成员分页数据
 * @param params
 * @returns
 */
export const getTeamMemberPage = (params: { withOwner: boolean }) => {
  return http.request<Result>("get", "/trade/team-member/page", { params });
};

/**
 * 更新团队头像
 * @param data
 * @returns
 */
export const updateTeamAvatar = (data: { id: string; teamAvatar: string }) => {
  return http.request<Result>("post", "/trade/team/avatar", { data });
};

/**
 * 更新用户头像
 * @param data
 * @returns
 */
export const updateUserAvatar = (data: { avatar: string }) => {
  return http.request<Result>("post", "/trade/user/update-avatar", { data });
};

/**
 * 获得贸易端团队邀请协作团队记录分页
 * @param params
 * @returns
 */
export const inviteTeamRecord = (params: any) => {
  return http.request<Result>("get", "/trade/team-invite-record/page", {
    params
  });
};

/**
 * 删除贸易端团队邀请协作团队记录
 * @param params
 * @returns
 */
export const deleteInviteTeam = (params: { id: string }) => {
  return http.request<Result>("delete", "/trade/team-invite-record/delete", {
    params
  });
};

/**
 * 团队管理获取sccs列表
 * @param params
 * @returns
 */
export const getSccsList = (params: { sccsReqEnum: string }) => {
  return http.request<Result>("get", "/trade/sccs/list-sccs", {
    params
  });
};
