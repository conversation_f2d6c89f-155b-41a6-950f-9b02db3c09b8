<template>
  <div class="role-per-col">
    <el-checkbox
      v-model="checkAll"
      class="checkbox-parent"
      :indeterminate="isIndeterminate"
      :disabled="disabled"
      @change="handleCheckAllChange"
    >
      {{ props.checkboxData.typeName }}
    </el-checkbox>
    <el-checkbox-group
      v-model="checkedCities"
      @change="handleCheckedCitiesChange"
    >
      <el-checkbox
        v-for="city in props.checkboxData.childrenList"
        :key="city"
        class="checkbox-group-col"
        :label="city.label"
        :disabled="disabled"
        :value="city.value"
      >
        <div class="checkbox-span">
          {{ city.label }}
          <span v-if="city.remark" class="checkbox-tip">{{ city.remark }}</span>
        </div>
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
<script lang="ts" setup>
import { ref, PropType, watchEffect } from "vue";

const checkAll = ref(false);
const isIndeterminate = ref(false);
const checkedCities = ref([]);
let cities = {
  childrenList: []
};

const props = defineProps({
  checkboxData: {
    type: Object as PropType<any>
  },
  bindData: {
    type: Object as PropType<any>
  },
  disabled: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const handleCheckAllChange = (val: boolean) => {
  let checkedList = [];
  cities.childrenList.map(child => checkedList.push(child.value));
  checkedCities.value = val ? checkedList : [];
  isIndeterminate.value = false;
};

const handleCheckedCitiesChange = (value: string[]) => {
  const checkedCount = value.length;
  checkAll.value = checkedCount === cities.childrenList.length;
  isIndeterminate.value =
    checkedCount > 0 && checkedCount < cities.childrenList.length;
};

watchEffect(() => {
  let bindDataIds: string[] = [];
  props.checkboxData.childrenList.map(child => {
    if (props.bindData.includes(child.value)) {
      bindDataIds.push(child.value);
    }
  });
  checkedCities.value = bindDataIds;
  const checkedCount = bindDataIds.length;
  cities = props.checkboxData;
  checkAll.value = checkedCount === cities.childrenList.length;
  isIndeterminate.value =
    checkedCount > 0 && checkedCount < cities.childrenList.length;
});

defineExpose({
  checkedCities
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  margin-bottom: 10px;
  font-weight: bolder;
}

.checkbox-group-col {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
