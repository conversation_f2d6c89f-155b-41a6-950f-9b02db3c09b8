<template>
  <LoginLayout>
    <template #form>
      <div class="login-form-wrapper">
        <!-- 登录表单卡片 -->
        <div class="login-card">
          <!-- 头部 -->
          <div class="login-header">
            <!-- Logo -->
            <div class="logo-container">
              <el-image :src="logoImage" fit="contain" class="logo" />
            </div>
            <!-- 注册入口 -->
            <div class="register-link">
              <el-button type="primary" link @click="handleGoRegister">
                {{ t("trade_login_register") }}
              </el-button>
            </div>
          </div>

          <!-- 登录类型选项卡 -->
          <div class="login-tabs">
            <el-tabs
              v-model="activeTab"
              class="login-tabs-container"
              @tab-change="handleTabChange"
            >
              <el-tab-pane
                :label="t('trade_login_by_password')"
                name="password"
              >
                <PasswordLoginForm
                  ref="passwordLoginRef"
                  @handleLoginSuccess="handleLoginSuccess"
                />
              </el-tab-pane>
              <el-tab-pane :label="t('trade_login_by_email')" name="email">
                <MailForm
                  ref="mailFormRef"
                  mode="login"
                  errorStyle="margin-bottom: -36px"
                  @handleLoginSuccess="handleLoginSuccess"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </template>
  </LoginLayout>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { setToken } from "@/utils/auth";
import LoginLayout from "./layout.vue";
import PasswordLoginForm from "./components/PasswordLoginForm.vue";
import MailForm from "./components/MailForm.vue";
import logoImage from "@/assets/login/logo.png";

const { t } = useI18n();
const router = useRouter();

// 选项卡状态
const activeTab = ref<string>("password");

const mailFormRef = ref<any>(null);
const passwordLoginRef = ref<any>(null);

// 处理选项卡切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
  if (tabName === "password") {
    passwordLoginRef.value.refreshUsername();
  } else {
    mailFormRef.value.refreshUsername();
  }
};

// 处理注册跳转
const handleGoRegister = () => {
  router.push("/register");
};

// 处理登录成功
const handleLoginSuccess = (data: any) => {
  // 参考旧版实现的登录成功逻辑
  setToken(data)
    .then((userInfo: any) => {
      if (userInfo.latestLoginTeamId && userInfo.latestLoginTeamMemberId) {
        router.push("/");
      } else if (userInfo.anyTeamMember) {
        router.push("/selectTeam");
      } else {
        router.push("/createTeam");
      }
    })
    .catch((error: any) => {
      console.error("设置token失败:", error);
      ElMessage.error("登录失败，请重试");
    });
};
</script>

<style lang="scss" scoped>
// 响应式适配
@media screen and (width <= 768px) {
  .login-card {
    width: 90vw;
    max-width: 400px;
    padding: 32px 24px;
    margin: 0 20px;
  }

  .login-header {
    margin-bottom: 24px;

    .logo-container .logo {
      height: 32px;
    }

    .register-link {
      .register-text {
        font-size: 12px;
      }
    }
  }

  .login-title {
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
    }
  }

  .login-tabs {
    margin-bottom: 24px;
  }
}

@media screen and (width <= 480px) {
  .login-card {
    padding: 24px 16px;
  }

  .login-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

.login-form-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.login-card {
  width: 480px;
  padding: 48px;
  background: rgb(255 255 255 / 95%);
  backdrop-filter: blur(20px);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 24px;
  box-shadow: 0 24px 64px rgb(0 0 0 / 10%);
}

// 头部布局
.login-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;

  .logo-container {
    .logo {
      width: auto;
      height: 30px;
    }
  }

  .register-link {
    display: flex;
    gap: 8px;
    align-items: center;

    .register-text {
      font-size: 14px;
      color: #666;
    }
  }
}

// 登录标题
.login-title {
  margin-bottom: 32px;
  text-align: center;

  h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
  }
}

// 选项卡样式
.login-tabs {
  :deep(.login-tabs-container) {
    .el-tabs__header {
      margin-bottom: 32px;
      border-bottom: 1px solid #efefef;

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__nav {
        .el-tabs__item {
          padding: 0 6px;
          margin-right: 28px;
          font-family: "PingFang SC", "PingFang SC-Semibold";
          font-size: 16px;
          line-height: 22px;
          color: #595959;
          text-align: left;

          &.is-active {
            color: #0070d2;
          }
        }
      }
    }

    .el-tabs__content {
      .el-tab-pane {
        padding: 0;

        .el-form-item.is-required > .el-form-item__label::before {
          display: none;
        }
      }
    }
  }
}
</style>
