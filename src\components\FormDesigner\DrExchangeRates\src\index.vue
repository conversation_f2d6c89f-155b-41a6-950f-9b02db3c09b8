<template>
  <div class="exchange-rates-row">
    <div v-if="widgetCurrencyType === 'fixed'" class="exchange-rates-col-span">
      {{ exchangeRateFixedTime }}
    </div>
    <div
      v-if="widgetCurrencyType === 'realTime'"
      class="exchange-rates-col-span"
    >
      {{ exchangeRateRealTime }}
    </div>
    <el-input-number
      v-if="widgetCurrencyType === 'fixed'"
      v-model="changeRateString"
      class="change-rate-input-number"
      clearable
      :controls="false"
      :value-on-clear="null"
      @change="handleComputedChangeRate"
    />
    <div class="exchange-rates-col">
      <el-input
        v-model="conversionBeforeMoney"
        type="number"
        :placeholder="widgetConfigure.props?.placeholder"
        @change="handleComputedChangeRate"
      >
        <template #append>
          <el-select-v2
            v-model="exchangeSignSelect"
            :options="exchangeRateOptions"
            @change="handleExChangeSign"
          />
        </template>
      </el-input>
    </div>
    <div class="exchange-rates-icon">
      <i class="iconfont link-switch-teams" />
    </div>
    <div class="exchange-rates-col">
      <el-input
        v-model="conversionAfterMoney"
        type="number"
        readonly
        :disabled="true"
        :placeholder="widgetConfigure.props?.placeholder"
      >
        <template #append>{{ widgetConfigure.props.targetCurrency }}</template>
      </el-input>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, inject, watch, computed } from "vue";
import { BigNumber } from "bignumber.js";
import { exchangeRates } from "@/api/common";
import { exchangeRateOptions } from "./currency.ts";
import { cloneDeep, storageLocal } from "@pureadmin/utils";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const changeRateString = ref<any>(undefined);
const conversionBeforeMoney = ref<number>(0);
const conversionAfterMoney = ref<number>(0);
const targetCurrencyRate = ref<string>("");
const exchangeSignSelect = ref<string>("CNY");
const emit = defineEmits(["handleUpdateWidgetData"]);

const widgetCurrencyType = computed(() => {
  return props.widgetConfigure.props.exchangeType;
});

const exchangeRateFixedTime = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  if (!props.widgetConfigure.props.rateObject) {
    return "固定汇率";
  }
  return translationLang === "en"
    ? props.widgetConfigure.props.rateObject.fixedEn ||
        props.widgetConfigure.props.rateObject.fixedCn
    : props.widgetConfigure.props.rateObject.fixedCn;
});

const exchangeRateRealTime = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");
  if (!props.widgetConfigure.props.rateObject) {
    return "实时汇率";
  }
  return translationLang === "en"
    ? props.widgetConfigure.props.rateObject.realEn ||
        props.widgetConfigure.props.rateObject.realCn
    : props.widgetConfigure.props.rateObject.realCn;
});

const handleComputedChangeRate = async () => {
  if (widgetCurrencyType.value === "fixed") {
    let str = new BigNumber(conversionBeforeMoney.value).multipliedBy(
      new BigNumber(changeRateString.value).toString()
    );
    conversionAfterMoney.value = +str.toString();
  } else {
    const { data } = await exchangeRates({
      fromCoin: exchangeSignSelect.value,
      toCoin: props.widgetConfigure.props.targetCurrency
    });
    targetCurrencyRate.value = data;
    let str = new BigNumber(conversionBeforeMoney.value).multipliedBy(
      new BigNumber(targetCurrencyRate.value)
    );
    conversionAfterMoney.value = +str.toString();
  }
  RenderWidgetData();
};

const RenderWidgetData = () => {
  const widgetId = props.widgetConfigure._fc_id;
  const exchangeRateObject = {
    fromMoneyType: exchangeSignSelect.value,
    targetMoneyType: props.widgetConfigure.props.targetCurrency,
    fromMoney: conversionBeforeMoney.value,
    targetMoney: conversionAfterMoney.value,
    exchangeType: widgetCurrencyType.value,
    rate:
      widgetCurrencyType.value === "fixed"
        ? changeRateString.value
        : targetCurrencyRate.value
  };
  if (props.widgetRowIndex !== -1) {
    emit("handleUpdateWidgetData", "DrExchangeRates", {
      obj: exchangeRateObject,
      label: "",
      widgetType: props.widgetConfigure.type,
      widgetId: widgetId,
      $rowIndex: props.widgetRowIndex
    });
  } else {
    handleWidgetFormsValue(
      widgetId,
      {
        obj: exchangeRateObject,
        label: "",
        widgetType: props.widgetConfigure.type,
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      exchangeRateObject
    );
  }
};

const handleExChangeSign = async () => {
  const { data } = await exchangeRates({
    fromCoin: exchangeSignSelect.value,
    toCoin: props.widgetConfigure.props.targetCurrency
  });
  targetCurrencyRate.value = data;
  handleComputedChangeRate();
};

const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const handleSubTableWidgetFormsValue: any = inject(
  "handleSubTableWidgetFormsValue",
  null
);

watch(
  () => props.widgetConfigure,
  async () => {
    if (widgetCurrencyType.value === "realTime") {
      const { data } = await exchangeRates({
        fromCoin: exchangeSignSelect.value,
        toCoin: props.widgetConfigure.props.targetCurrency
      });
      targetCurrencyRate.value = data;
    }
  },
  {
    deep: true
  }
);

watch(
  () => props.trendsForm[props.widgetConfigure._fc_id],
  () => {
    const exchangeRatesValue = cloneDeep(
      props.trendsForm[props.widgetConfigure._fc_id]
    );
    if (exchangeRatesValue && Object.keys(exchangeRatesValue).length > 0) {
      conversionBeforeMoney.value = exchangeRatesValue.fromMoney;
      if (exchangeRatesValue.fromMoneyType) {
        exchangeSignSelect.value = exchangeRatesValue.fromMoneyType;
      }
      conversionAfterMoney.value = exchangeRatesValue.targetMoney;
      changeRateString.value = exchangeRatesValue.rate;
    } else {
      conversionBeforeMoney.value = undefined;
      conversionAfterMoney.value = undefined;
      changeRateString.value = undefined;
    }
  },
  {
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.exchange-rates-row {
  display: flex;
  align-items: center;
  width: 100%;

  .exchange-rates-col-span {
    margin-right: 10px;
    font-size: 14px;
    font-weight: normal;
    line-height: 16px;
    color: #8c8c8c;
    text-align: left;
  }

  .change-rate-input-number.el-input-number {
    width: 100px !important;
    margin: 0 9px;
  }

  .exchange-rates-col {
    ::v-deep(.el-input__wrapper) {
      width: 140px !important;
    }

    ::v-deep(.el-input-group__append) {
      background: transparent !important;

      .el-select {
        width: 86px;
        color: #808080 !important;

        ::v-deep(.el-select__wrapper) {
          padding: 3px 7px !important;
        }
      }
    }
  }

  .exchange-rates-icon {
    margin: 0 10px;

    .iconfont {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .change-rate-input-number {
    width: 60px !important;
    margin-right: 10px;

    ::v-deep(.el-input) {
      height: 32px;
    }
  }
}
</style>
