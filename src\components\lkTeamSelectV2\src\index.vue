<template>
  <el-select-v2
    v-bind="$attrs"
    :options="memberOptions"
    remote
    :remote-method="handleRemoteMethod"
  >
    <template #default="{ item }">
      <span
        v-if="!item.activate && teamRenderFields.label !== 'teamName'"
        class="tag-register-tip"
      >
        ({{ t("trade_common_unregistered") }})
      </span>
      <el-avatar
        :class="teamRenderFields.label === 'teamName' ? 'colorOrange' : ''"
        :src="item[teamRenderFields.avatar]"
        :size="18"
        fit="cover"
      >
        {{ item[teamRenderFields.label]?.substring(0, 1) }}
      </el-avatar>
      {{ item[teamRenderFields.label] }}
      <span v-if="teamRenderFields.label !== 'teamName'">
        ({{ item.email || item.account }})
      </span>
      <span v-if="teamRenderFields.label === 'teamName'">
        ({{ item.teamShortName }})
      </span>
    </template>
    <template #label="{ label, value }">
      <span
        v-if="
          !getUserInfo(value).activate && teamRenderFields.label !== 'teamName'
        "
        class="tag-register-tip"
      >
        ({{ t("trade_common_unregistered") }})
      </span>
      <LKAvatar
        :teamInfo="{
          avatar: getUserAvatar(value),
          username: label
        }"
        :class="teamRenderFields.label === 'teamName' ? 'colorOrange' : ''"
        :size="18"
      />{{ label }}
    </template>
  </el-select-v2>
</template>
<script lang="ts" setup>
import { ref, useAttrs, watchEffect } from "vue";
import { useI18n } from "vue-i18n";
import LKAvatar from "@/components/lkAvatar/index";

const { t } = useI18n();
const attrs = useAttrs();
const memberOptions = ref<any>([]);

const props = defineProps({
  teamRenderFields: {
    type: String as PropType<string>,
    default: ""
  },
  options: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

watchEffect(() => {
  if (props.options.length > 0) {
    memberOptions.value = props.options;
  }
});

const handleRemoteMethod = (query: string): void => {
  if (query !== "") {
    const memberList = (props.options as any).filter(memberItem => {
      if (props.teamRenderFields.label !== "teamName") {
        return (
          memberItem.username.toUpperCase().indexOf(query.toUpperCase()) !==
            -1 ||
          (memberItem.email &&
            memberItem.email.toUpperCase().indexOf(query.toUpperCase()) !==
              -1) ||
          (memberItem.account &&
            memberItem.account.toUpperCase().indexOf(query.toUpperCase()) !==
              -1)
        );
      } else {
        return (
          memberItem.teamName.toUpperCase().indexOf(query.toUpperCase()) !==
            -1 ||
          (memberItem.teamShortName &&
            memberItem.teamShortName
              .toUpperCase()
              .indexOf(query.toUpperCase()) !== -1)
        );
      }
    });
    memberOptions.value = memberList;
  } else {
    memberOptions.value = props.options;
  }
};

const getUserAvatar = (id: string) => {
  const memberItem = (props.options as any).find(
    //@ts-ignore
    memberLi => memberLi[attrs.props.value] === id
  );
  return memberItem ? memberItem[props.teamRenderFields.avatar] : "";
};

const getUserInfo = (id: string) => {
  const memberItem = (props.options as any).find(
    //@ts-ignore
    memberLi => memberLi[attrs.props.value] === id
  );
  return memberItem ? memberItem : {};
};
</script>
<style lang="scss" scoped>
.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}
</style>
