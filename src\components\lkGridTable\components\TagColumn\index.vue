<template>
  <div
    ref="tableColumnRef"
    class="table-container"
    :style="{
      maxWidth: `${width - 20}px`,
      ...style
    }"
    style="pointer-events: auto; cursor: pointer"
  >
    <el-tooltip
      :disabled="!isEllipsis"
      :content="tagList.map(item => item.content).join(',')"
      placement="top"
    >
      <div v-if="tagList.length === 1" class="table-col">
        <el-tag
          v-for="tagItem in tagList"
          :key="tagItem.content"
          type="info"
          size="small"
          effect="plain"
          :color="tagItem.bgValue"
          :style="{
            maxWidth: '100%'
          }"
        >
          <ReText
            class="tag-text"
            type="info"
            :tippyProps="{ delay: 50000 }"
            style="pointer-events: auto; cursor: pointer"
          >
            {{ tagItem.content }}
          </ReText>
        </el-tag>
      </div>
      <div v-else class="table-col tab-col-multiple">
        <el-tag
          v-for="tagItem in multipleTagList"
          :key="tagItem.content"
          type="info"
          size="small"
          effect="plain"
          :color="tagItem.bgValue"
        >
          <ReText
            class="tag-text"
            type="info"
            :tippyProps="{ delay: 50000 }"
            style="pointer-events: auto; cursor: pointer"
          >
            {{ tagItem.content }}
          </ReText>
        </el-tag>
        <span v-if="isEllipsis">...</span>
      </div>
    </el-tooltip>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { ElTag } from "element-plus";
import ReText from "@/components/ReText";

const props = defineProps({
  tagList: {
    type: Array as PropType<any>,
    default: () => []
  },
  width: {
    type: String as PropType<string>,
    default: ""
  },
  style: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const tableColumnRef = ref<HTMLElement | any>(null);
const multipleTagList = ref<any>([]);

const isEllipsis = computed(() => {
  return (
    multipleTagList.value.length !== props.tagList.length &&
    props.tagList.length > 1
  );
});

const getTextWidth = text => {
  const canvas = document.createElement("canvas");
  const context = canvas.getContext("2d");

  // 设置字体样式
  context.font = "12px Arial";

  // 测量文本
  const metrics = context.measureText(text);

  // 返回文本宽度
  return metrics.width;
};

onMounted(() => {
  // 选择你要监听的DOM元素
  const element = tableColumnRef.value;
  const containerWidth = element.clientWidth - 20;

  // 创建一个ResizeObserver实例并传入回调函数
  const resizeObserver = new ResizeObserver(entries => {
    for (let entry of entries) {
      if (props.tagList.length > 1) {
        let tagWidthTotal = 0;
        let tagIndex = 0;
        const TAG_PADDING = 14;
        const TAG_MARGIN = 10;
        const ELLIPSIS_WIDTH = 40;
        for (let i = 0, len = props.tagList.length; i < len; i++) {
          const tagItem = props.tagList[i];
          if (tagWidthTotal + ELLIPSIS_WIDTH < containerWidth) {
            const textWidth = getTextWidth(tagItem.content);
            tagWidthTotal +=
              textWidth + TAG_PADDING + TAG_MARGIN > containerWidth
                ? containerWidth
                : textWidth + TAG_PADDING + TAG_MARGIN;
            tagIndex = i + 1;
          }
        }
        multipleTagList.value = props.tagList.slice(0, tagIndex);
      }
    }
  });

  // 开始观察元素
  resizeObserver.observe(element);
});
</script>
<style lang="scss">
.table-container {
  display: inline-flex;
  align-items: center;
  width: 100%;
  height: 100%;

  .table-col {
    width: 100%;

    .el-tag {
      display: inline-block;
      // max-width: 600px; // 会导致溢出显示省略号失效
      max-width: 100%;
      margin-right: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border: 0 none;

      .el-tag__content {
        display: inline-block;
        max-width: 100%;
        height: 100%;

        .el-text {
          height: 100%;
          font-size: 12px;
          font-weight: bold;
          line-height: 20px;
          color: #262626;
        }
      }
    }
  }
}
</style>
