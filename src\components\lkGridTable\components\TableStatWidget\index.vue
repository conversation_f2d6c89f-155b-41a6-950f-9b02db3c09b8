<template>
  <el-select
    v-model="statisticalValue"
    :placeholder="t('trade_order_statistical')"
    placement="top-end"
    class="el-statistical-select"
    :class="{ 'el-statistical-not-select': !statisticalValue }"
    @change="handleStatisticalChange"
  >
    <template #label>
      <ReText type="info" :tippyProps="{ delay: 50000 }">
        <div>
          {{ operator }}{{ calculationResults
          }}<span v-if="isPercentWidget">%</span>
        </div>
      </ReText>
    </template>
    <el-option v-for="item in options" :key="item.value" :value="item.value">
      <template #default>
        <div class="statistical-option-col">
          <div class="statistical-option-text">{{ item.label }}</div>
          <i class="iconfont link-tick" />
        </div>
      </template>
    </el-option>
  </el-select>
</template>

<script lang="tsx" setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { BigNumber } from "bignumber.js";
import { ReText } from "@/components/ReText";
import { formatNumberValueOf } from "@/utils/common";
import {
  getOrderColumnStatistics,
  getWorkOrderColumnStatistics
} from "@/api/order";
import { useLkGridStore } from "../../lkGrid/src/store";
import {
  computedColumnStatistical,
  fieldNameTransformer
} from "../../lkGrid/src/utils";

const props = defineProps({
  fieldName: {
    type: String as PropType<string>,
    default: ""
  },
  columnConfig: {
    type: Object as PropType<any>,
    default: () => {}
  },
  statisticsResultList: {
    type: Array as PropType<any>,
    default: () => []
  },
  presentView: {
    type: Object as PropType<any>,
    default: () => {}
  },
  selectedRow: {
    type: Array as PropType<any>,
    default: () => []
  },
  colStatisticsSearchCondition: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();
const route = useRoute();
const statisticalValue = ref<string>("");
const calculationResults = ref<any>();
const operator = ref<string>("");
const options = ref<any[]>([]);
const { referencedDataForSubTable } = useLkGridStore();
const widgetItemInfo = computed<any>(() => {
  return props.columnConfig?.widgetItemInfo || {};
});
const precision = computed<number>(() => {
  return widgetItemInfo.value?.precision || 0;
});

const fieldName = computed(() => fieldNameTransformer(props.fieldName));
const fieldNameForLocal = computed(() =>
  fieldNameTransformer(props.fieldName, true)
);

const operatorTextMap = {
  COUNT: t("trade_common_count"),
  NOT_FILLED: t("trade_common_NotFilled"),
  FILLED: t("trade_common_filled"),
  UNIQUE_VALUE: t("trade_common_UniqueValue"),
  SUM: t("trade_common_sum"),
  AVERAGE: t("trade_common_average"),
  MAX: t("trade_common_max"),
  MIN: t("trade_common_min"),
  NOT_DISPLAYING: t("trade_common_NotDisplaying")
};

const isPercentWidget = computed(() => {
  return (
    props.columnConfig.widgetType === "PERCENT" &&
    !["COUNT", "NOT_FILLED", "FILLED", "UNIQUE_VALUE"].includes(
      statisticalValue.value
    )
  );
});

const emit = defineEmits(["handleStatisticalResult"]);

const handleStatisticalChange = async (val: string) => {
  const selectedRecords: any[] = props.selectedRow;

  handleComputedStatisticalResult(val, selectedRecords);
};

const handleComputedStatisticalResult = async (
  type: string,
  selectedRecords: any
) => {
  if (!type) {
    emit("handleStatisticalResult", {
      val: "",
      result: "",
      widgetId: props.fieldName
    });
    return;
  }

  if (selectedRecords && selectedRecords.length > 0) {
    calculationResults.value = computedColumnStatistical(
      fieldNameForLocal.value,
      type,
      selectedRecords
    );
    operator.value = operatorTextMap[type || "NOT_DISPLAYING"];
    emit("handleStatisticalResult", {
      val: type,
      result: calculationResults.value,
      widgetId: props.fieldName
    });
  } else {
    const sccsId = route.query.sccsId as string;
    if (!!type) {
      const { code, data } =
        props.presentView.viewType === "MILESTONE"
          ? await getWorkOrderColumnStatistics({
              sccsId: sccsId,
              sourceIdList: referencedDataForSubTable
                ? [
                    referencedDataForSubTable.workOrderId ||
                      referencedDataForSubTable.orderId
                  ]
                : [],
              milestoneId: props.presentView.msId,
              widgetList: [fieldName.value],
              milestone: !!(
                props.columnConfig.msId &&
                ![
                  "MS_REPLY_WIDGET",
                  "WORK_ORDER_REPLY_WIDGET",
                  "WORK_ORDER_WIDGET",
                  "MS_LABEL"
                ].includes(props.columnConfig.type)
              ),
              widgetStatisticsList: [
                {
                  type: type,
                  widgetId: fieldName.value
                }
              ],
              ...props.colStatisticsSearchCondition
            })
          : await getOrderColumnStatistics({
              sccsId: sccsId,
              sourceIdList: referencedDataForSubTable
                ? [
                    referencedDataForSubTable.workOrderId ||
                      referencedDataForSubTable.orderId
                  ]
                : [],
              widgetList: [fieldName.value],
              milestone: !!(
                props.columnConfig.msId &&
                ![
                  "MS_REPLY_WIDGET",
                  "WORK_ORDER_REPLY_WIDGET",
                  "WORK_ORDER_WIDGET",
                  "MS_LABEL"
                ].includes(props.columnConfig.type)
              ),
              widgetStatisticsList: [
                {
                  type: type,
                  widgetId: fieldName.value
                }
              ],
              ...props.colStatisticsSearchCondition
            });
      if (code === 0) {
        const dataValue = data.find(item => item.widgetId === fieldName.value);

        if (["SUM", "AVERAGE", "MAX", "MIN"].includes(type)) {
          if (precision.value) {
            if (widgetItemInfo.value?.useThousandSeparator) {
              calculationResults.value = `${new BigNumber(dataValue.value).toFormat(precision.value)}`;
            } else {
              calculationResults.value = `${new BigNumber(
                dataValue.value
              ).toFormat(precision.value, {
                groupSeparator: "",
                decimalSeparator: "."
              })}`;
            }
          } else {
            if (props.columnConfig.widgetType === "RATE") {
              calculationResults.value = dataValue.value;
            } else {
              calculationResults.value = `${new BigNumber(
                dataValue.value
              ).toFormat(precision.value, {
                groupSeparator: "",
                decimalSeparator: "."
              })}`;
            }
          }
        } else {
          calculationResults.value = `${dataValue.value}`;
        }

        operator.value = operatorTextMap[type || "NOT_DISPLAYING"];

        emit("handleStatisticalResult", {
          val: type,
          result: calculationResults.value,
          widgetId: props.fieldName
        });
      }
    }
  }
};

const findMaxWithThousandSeparators = numberStrings => {
  // 全局配置（可选：若需修改分隔符，在此设定）
  BigNumber.set({
    FORMAT: {
      groupSeparator: ",", // 默认千分位符号
      decimalSeparator: "."
    }
  });
  // 转换字符串为 BigNumber 数组
  const bnArray = numberStrings.map(str => {
    const groupSep = BigNumber.config().FORMAT.groupSeparator;
    const numericStr = `${str}`.replace(new RegExp(`\\${groupSep}`, "g"), "");
    return new BigNumber(numericStr);
  });
  // 比较取最大值
  const maxBn = bnArray.reduce(
    (max, current) => (current.gt(max) ? current : max),
    new BigNumber(0)
  );
  // 格式化为带千分位的字符串
  return maxBn.toFormat();
};

watch(
  () => props.columnConfig,
  () => {
    if (
      props.columnConfig &&
      (["INPUT_NUMBER", "PERCENT", "RATE"].includes(
        props.columnConfig.widgetType
      ) ||
        ["actualDay", "plannedDay"].includes(props.columnConfig.shortName) ||
        ["actualDay", "plannedDay"].includes(props.columnConfig.name) ||
        props.columnConfig.name.includes("_workOrderNumber"))
    ) {
      options.value = [
        {
          value: "",
          label: operatorTextMap.NOT_DISPLAYING
        },
        {
          value: "COUNT",
          label: operatorTextMap.COUNT
        },
        {
          label: operatorTextMap.SUM,
          value: "SUM"
        },
        {
          label: operatorTextMap.AVERAGE,
          value: "AVERAGE"
        },
        {
          label: operatorTextMap.MAX,
          value: "MAX"
        },
        {
          label: operatorTextMap.MIN,
          value: "MIN"
        },
        {
          value: "NOT_FILLED",
          label: operatorTextMap.NOT_FILLED
        },
        {
          value: "FILLED",
          label: operatorTextMap.FILLED
        },
        {
          value: "UNIQUE_VALUE",
          label: operatorTextMap.UNIQUE_VALUE
        }
      ];
      statisticalValue.value = "SUM";
    } else if (
      props.columnConfig &&
      ["IMAGE_UPLOAD", "FILE_UPLOAD", "RICH_TEXT"].includes(
        props.columnConfig.widgetType
      )
    ) {
      options.value = [
        {
          value: "",
          label: operatorTextMap.NOT_DISPLAYING
        },
        {
          value: "COUNT",
          label: operatorTextMap.COUNT
        },
        {
          value: "NOT_FILLED",
          label: operatorTextMap.NOT_FILLED
        },
        {
          value: "FILLED",
          label: operatorTextMap.FILLED
        }
      ];
    } else {
      options.value = [
        {
          value: "",
          label: operatorTextMap.NOT_DISPLAYING
        },
        {
          value: "COUNT",
          label: operatorTextMap.COUNT
        },
        {
          value: "NOT_FILLED",
          label: operatorTextMap.NOT_FILLED
        },
        {
          value: "FILLED",
          label: operatorTextMap.FILLED
        },
        {
          value: "UNIQUE_VALUE",
          label: operatorTextMap.UNIQUE_VALUE
        }
      ];
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.statisticsResultList,
  () => {
    const statisticsWidget = props.statisticsResultList.find(
      statisticsResult => statisticsResult.widgetId === props.fieldName
    );
    if (statisticsWidget) {
      statisticalValue.value = statisticsWidget.val;
      const dataOption = options.value.find(
        option => option.value === statisticalValue.value
      );

      if (["SUM", "AVERAGE", "MAX", "MIN"].includes(statisticalValue.value)) {
        if (precision.value) {
          if (widgetItemInfo.value?.useThousandSeparator) {
            calculationResults.value = `${new BigNumber(
              formatNumberValueOf(statisticsWidget.result)
            ).toFormat(precision.value, {
              groupSeparator: ",",
              decimalSeparator: ".",
              groupSize: 3
            })}`;
          } else {
            calculationResults.value = `${new BigNumber(
              formatNumberValueOf(statisticsWidget.result)
            ).toFormat(precision.value, {
              groupSeparator: "",
              decimalSeparator: "."
            })}`;
          }
        } else {
          if (props.columnConfig.widgetType === "RATE") {
            calculationResults.value = statisticsWidget.result;
          } else {
            calculationResults.value = `${new BigNumber(
              formatNumberValueOf(statisticsWidget.result)
            ).toFormat(precision.value, {
              groupSeparator: "",
              decimalSeparator: "."
            })}`;
          }
        }
      } else {
        calculationResults.value = `${statisticsWidget.result}`;
      }

      operator.value = dataOption.label;
    } else {
      calculationResults.value = "";
      operator.value = "";
      statisticalValue.value = "";
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  handleComputedStatisticalResult
});
</script>
<style lang="scss">
.el-statistical-select {
  pointer-events: auto;
  cursor: pointer;

  .el-select__wrapper {
    padding: 4px 6px;
    box-shadow: none !important;

    .el-select__selected-item.el-select__placeholder {
      .el-text {
        display: inline-flex;
        height: 100%;
      }

      div {
        overflow: hidden;
        font-size: 12px;
        color: #797979;
        text-align: right !important;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
    }

    .el-select__selected-item {
      font-size: 12px;
      color: #797979;
      text-align: right !important;
    }
  }

  &.el-statistical-not-select {
    .el-select__selected-item.el-select__placeholder {
      display: none;
    }

    .el-select__suffix {
      display: none;
    }

    &:hover {
      .el-select__selected-item.el-select__placeholder {
        display: block !important;
      }

      .el-select__suffix {
        display: block !important;
      }
    }
  }
}

.el-select-dropdown__item {
  padding: 0 14px;

  .statistical-option-col {
    display: flex;

    .statistical-option-text {
      flex: 1;
    }

    .iconfont {
      display: none;
      font-size: 12px;
    }
  }

  &.is-selected {
    .iconfont {
      display: block;
    }
  }
}
</style>
