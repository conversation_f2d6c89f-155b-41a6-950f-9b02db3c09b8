<template>
  <ListTable
    ref="tableInstanceRef"
    class="lk-grid-table"
    :options="option"
    :style="{
      'max-height': `${height - 50}px`,
      height: `${tableActualHeight - 50}px`,
      'z-index': 2000
    }"
  />
  <TableStatistics
    ref="TableStatisticsRef"
    :column="tableColumns"
    :statisticsResultList="TableStatisticsResultList"
    :presentView="presentView"
    :selectedRow="tableSelectedRecords"
    :colStatisticsSearchCondition="colStatisticsSearchCondition"
    :isOperate="true"
    @handleScrollChange="handleScrollChange"
    @handleStatisticalResult="handleStatisticalResult"
  />
  <el-dialog
    v-model="openDialogVisible"
    class="lk-maximum-dialog"
    :class="{
      'lk-sub-table-dialog': openDialogWidget.widgetType === 'TABLE_FORM'
    }"
    append-to-body
    align-center
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="clearReferencedDataForSubTable"
  >
    <Editor
      v-if="openDialogWidget.widgetType === 'RICH_TEXT'"
      v-model="valueHtml"
      :defaultConfig="{ readOnly: true }"
    />
    <LKSubTable
      v-if="openDialogWidget.widgetType === 'TABLE_FORM'"
      ref="subTableRef"
      :widgetConfig="widgetSubTableConfig"
      :widgetData="widgetSubTableData"
      :presentView="props.presentView"
      :tableStatisticsFlag="true"
      tableFirstType=""
    />
  </el-dialog>
</template>
<script lang="tsx" setup>
import { computed, h, markRaw, nextTick, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import { BigNumber } from "bignumber.js";
import { VTable } from "@visactor/vue-vtable";
import { SearchComponent } from "@visactor/vtable-search";
import { cloneDeep, storageLocal } from "@pureadmin/utils";
import { formatNumberValueOf } from "@/utils/common";
import { ElRate, ElTooltip } from "element-plus";
import { Editor } from "@wangeditor/editor-for-vue";
import { dict } from "@/utils/dict";
import { useLkGridStore } from "./store";
import { computedColumnStatistical, fieldNameTransformer } from "./utils";
import { TableOperateProp } from "@/types/type.d";
import { debounce } from "lodash-es";
import {
  dayjs,
  FileColumn,
  TableOperate,
  TableCheckbox,
  AvatarGroup,
  StateColumn,
  TagColumn,
  ImageListColumn,
  TableFilter,
  TableStatistics,
  msNoDataImage,
  LkAvatar,
  LKSubTable,
  LabelColumn,
  LkAvatarGroupNext
} from "./import";
import {
  getOrderColumnStatistics,
  getOrderTemplateDetail,
  getSubTableInfoData,
  getWorkOrderColumnStatistics
} from "@/api/order";
import ReText from "@/components/ReText";

const props = defineProps({
  milestoneIdList: {
    type: Array as PropType<any>,
    default: () => []
  },
  height: {
    type: Number as PropType<number>,
    default: 0
  },
  tableOpeateLists: {
    type: Object as PropType<TableOperateProp[]>,
    default: () => {}
  },
  presentView: {
    type: Object as PropType<any>,
    default: () => {}
  },
  // 当前视图存储的统计数据字段
  colStatisticsItemList: {
    type: Array as PropType<any>,
    default: () => []
  },
  orderPermissionList: {
    type: Array as PropType<any>,
    default: () => []
  },
  colStatisticsSearchCondition: {
    type: Object as PropType<any>,
    default: () => {}
  },
  relationTodo: {
    type: Boolean as PropType<boolean>,
    default: true
  }
});

const { t } = useI18n();
const route = useRoute();
const TableStatisticsRef = ref<any | HTMLElement>(null);
const valueHtml = ref<string>("");
let fixedNumber = markRaw<any>(0);
const tableInstanceRef = ref<HTMLElement | any>(null);
const openDialogVisible = ref<boolean>(false);
const openDialogWidget = ref<any>({});
const tableColumns = ref<any>([]);
const tableActualHeight = ref<number>(0);
const tableRecord = ref<any>([]);
const tableSelectedRecords = ref<any[]>([]);
const widgetSubTableData = ref<any>([]); // 工单子表数据
const widgetSubTableConfig = ref<any>([]); // 工单子表配置
const TableStatisticsResultList = ref<any[]>([]); // 表格统计字段数据
const { clearReferencedDataForSubTable, setReferencedDataForSubTable } =
  useLkGridStore();

const tableInstance: any = computed(() => {
  return tableInstanceRef.value.vTableInstance;
});

const searchInstance: any = computed(() => {
  return new SearchComponent({
    table: tableInstanceRef.value.vTableInstance,
    skipHeader: true,
    autoJump: true // 搜索完成后是否自动跳转到搜索结果的第一条
  });
});

const COLOR_LIST = [
  "E6F8FC",
  "EFFADE",
  "DCF9FF",
  "CEEEFF",
  "E6FDF2",
  "D9F7E9",
  "D6FCEA",
  "CCF4F1",
  "E7F3FF",
  "DFEAF4",
  "F7F6DA",
  "D3E5FE",
  "F1EDFD",
  "E4DDF7",
  "EADAFC",
  "F6D5FC",
  "FFE4E4",
  "FCDBDB",
  "F7DED8",
  "FEE5CC"
];
const option = markRaw({
  records: [],
  columns: [],
  defaultHeaderRowHeight: 36,
  defaultRowHeight: 40,
  columnWidthComputeMode: "only-body",
  limitMaxAutoWidth: 300,
  rightFrozenColCount: 1,
  hover: {
    highlightMode: "row",
    disableHover: true
  },
  select: {
    highlightMode: "row",
    disableSelect: true
  },
  customConfig: {
    createReactContainer: true
  },
  resize: {
    columnResizeMode: "header"
  },
  tooltip: {
    isShowOverflowTextTooltip: true,
    overflowTextTooltipDisappearDelay: 500,
    confine: false
  },
  emptyTip: {
    text: t("trade_common_emptyTip"),
    textStyle: {
      color: "#797979",
      fontSize: 14,
      lineHeight: 22
    },
    icon: {
      width: 342,
      height: 168,
      image: msNoDataImage
    }
  },
  theme: {
    headerStyle: {
      color: "#797979",
      fontSize: "13",
      fontWeight: "bolder",
      textAlign: "center",
      borderColor: "#e8e8e8"
    },
    bodyStyle: {
      color: "#262626",
      fontSize: "13",
      textAlign: "left",
      borderColor: "#e8e8e8"
    },
    scrollStyle: {
      visible: "never",
      hoverOn: false,
      width: 10,
      scrollRailColor: "#fbfbfb",
      scrollSliderColor: "#d3d3d3"
    },
    buttonStyle: {
      buttonColor: "transparent",
      buttonBorderColor: "transparent",
      buttonHoverColor: "transparent",
      buttonHoverBorderColor: "transparent"
    }
  }
});

const columnSlots = ref<any>({
  STATE: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);
      const tableRow = tableInstance.value.getCellOriginRecord(col, row);

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const statusList = dict.getDictByCode("milestone_status");
      const stateItem = statusList.find(
        status => status.value === tableRow[fieldName]
      );

      const translationLang = storageLocal().getItem("translationLang");
      const stateText =
        translationLang === "en" ? stateItem?.labelEn : stateItem?.label;

      const container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //@ts-ignore
        vue: {
          element: h(StateColumn, {
            content: value,
            orderText: stateText
          }),
          container: table.bodyDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  LABELTAG: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      if (value) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(LabelColumn, {
              content: value?.labelValue,
              style: value?.style,
              width: width
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  USERINFO: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const {
        creatorAvatar,
        creatorUsername,
        updaterUsername,
        orderCreatorAvatar,
        orderCreateTime,
        orderCreatorUsername,
        createTime,
        orderCreatorEmail,
        creatorEmail,
        updaterAvatar,
        updateTime,
        updaterEmail,
        orderUpdaterAvatar,
        orderUpdateTime,
        orderUpdaterUsername,
        orderUpdaterEmail
      } = tableInstance.value.getCellOriginRecord(args.col, args.row);

      let avatar = "";
      let timeStamp = "";
      let username = "";
      let email = "";
      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      if (fieldName === "orderCreateInfo") {
        avatar =
          props.presentView.viewType === "ORDER"
            ? creatorAvatar
            : orderCreatorAvatar;
        timeStamp =
          props.presentView.viewType === "ORDER" ? createTime : orderCreateTime;
        username =
          props.presentView.viewType === "ORDER"
            ? creatorUsername
            : orderCreatorUsername;
        email =
          props.presentView.viewType === "ORDER"
            ? creatorEmail
            : orderCreatorEmail;
      } else if (fieldName === "orderUpdateInfo") {
        avatar =
          props.presentView.viewType === "ORDER"
            ? updaterAvatar
            : orderUpdaterAvatar;
        timeStamp =
          props.presentView.viewType === "ORDER" ? updateTime : orderUpdateTime;
        username =
          props.presentView.viewType === "ORDER"
            ? updaterUsername
            : orderUpdaterUsername;
        email =
          props.presentView.viewType === "ORDER"
            ? updaterEmail
            : orderUpdaterEmail;
      } else if (fieldName === "workOrderCreateInfo") {
        avatar = creatorAvatar;
        timeStamp = createTime;
        username = creatorUsername;
        email = creatorEmail;
      }

      let container;
      if (username) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-start",
          //@ts-ignore
          vue: {
            element: h(
              "div",
              {
                style: {
                  display: "flex",
                  "align-items": "center"
                }
              },
              [
                h(LkAvatar, {
                  teamInfo: {
                    avatar: avatar,
                    username: username,
                    coop: false,
                    status: false,
                    email: email
                  },
                  size: 26
                }),
                h(
                  "div",
                  {
                    style: {
                      fontSize: "12px",
                      color: "#595959",
                      marginLeft: "5px"
                    }
                  },
                  dayjs(new Date(timeStamp)).format("YYYY-MM-DD HH:mm:ss")
                )
              ]
            ),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  USERIMAGE: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      let teamInfo = {};
      let container;

      if (
        fieldName.indexOf("_manager") > -1 &&
        table.records[row - 1][fieldName] &&
        table.records[row - 1][fieldName].hasOwnProperty("username")
      ) {
        teamInfo = table.records[row - 1][fieldName];
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(LkAvatar, {
              teamInfo: teamInfo,
              size: 26
            }),
            container: table.bodyDomContainer
          }
        });
      } else if (fieldName.indexOf("_manager") === -1) {
        const widget = table.records[row - 1].widgetList.find(
          widget => widget.widgetId === fieldName
        );
        if (widget) {
          container = new VTable.CustomLayout.Group({
            height,
            width,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            //@ts-ignore
            vue: {
              element: h(LkAvatar, {
                teamInfo: {
                  avatar: widget.showObj[0].avatar,
                  username: widget.showObj[0].name,
                  email: widget.showObj[0].email
                },
                size: 26,
                shape: widget.widgetType === "COOP_TEAM" ? "square" : "circle",
                class: widget.widgetType === "COOP_TEAM" ? "colorOrange" : ""
              }),
              container: table.bodyDomContainer
            }
          });
        }
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  RATE: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //@ts-ignore
        vue: {
          element: h(
            ElTooltip,
            {
              content: value ? parseFloat(value).toString() : "0",
              placement: "top"
            },
            {
              default: () =>
                h(ElRate, {
                  modelValue: value ? parseFloat(value) : 0,
                  disabled: true,
                  max: value ? parseFloat(value) : 0,
                  style: "pointer-events: auto; cursor: pointer"
                })
            }
          ),
          container: table.bodyDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  SELECTION: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const regex =
        /<span\s[^>]*?style\s*=\s*["'][^>]*?background:\s*([^;>"']+)[^>]*>([^<]+)<\/span>/gi;

      const matches = [];
      let match;
      while ((match = regex.exec(value)) !== null) {
        const [, bgValue, content] = match;
        matches.push({ bgValue, content });
      }

      let container;
      if (matches instanceof Array && matches.length > 0) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          flexWrap: "wrap",
          justifyContent: "center",
          alignItems: "center",
          //@ts-ignore
          vue: {
            element: h(TagColumn, {
              tagList: matches,
              width: width
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  FILEUPLOAD: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      if (value && value instanceof Array) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(FileColumn, {
              width: width,
              filePreviewList: value,
              fileList: value
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  IMAGELIST: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      let container;
      if (value) {
        const imageList = value instanceof Array ? value : [{ url: value }];
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(ImageListColumn, {
              imageList: imageList,
              size: { width: width, height: height }
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  AVATARGROUP: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const tableData = tableInstance.value.getCellOriginRecord(
        args.col,
        args.row
      );

      let avatarListData = value;
      let operateTime = undefined;
      if (fieldName === "workOrderReplyInfo") {
        avatarListData = tableData?.workOrderUserInfo?.replyUserList;
        operateTime = tableData?.workOrderUserInfo?.replyTime;
      } else if (fieldName === "workOrderCollectInfo") {
        avatarListData = tableData?.workOrderUserInfo?.editUserList;
        operateTime = tableData?.workOrderUserInfo?.editTime;
      } else if (fieldName === "milestoneManager") {
        avatarListData = tableData.managerMemberId
          ? [
              {
                user: true,
                userAvatar: tableData.managerUserAvatar,
                edited: false,
                coopUser: false,
                userName: tableData.managerUserName,
                email: tableData.managerUserEmail,
                userId: tableData.managerMemberId,
                activate: true
              }
            ]
          : [];
      } else if (fieldName.includes("_replyInfo")) {
        avatarListData = value;
        operateTime = value?.length
          ? Math.max(...value.map(item => item.latestEditeDate)) || undefined
          : undefined;
      } else if (fieldName.includes("_collectInfo")) {
        avatarListData = value;
        operateTime = value?.length
          ? Math.max(...value.map(item => item.latestEditeDate)) || undefined
          : undefined;
      }

      const PERSON_AVATAR_MODE = {
        workOrderReplyInfo: "reply",
        workOrderCollectInfo: "collect",
        milestoneManager: ""
      };
      const getAvatarGroupMode = () => {
        if (fieldName.includes("_replyInfo")) {
          return "reply";
        } else if (fieldName.includes("_collectInfo")) {
          return "collect";
        } else {
          return PERSON_AVATAR_MODE[fieldName];
        }
      };

      let container;
      if (
        avatarListData &&
        avatarListData instanceof Array &&
        avatarListData.length > 0
      ) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(LkAvatarGroupNext, {
              avatarListGroup: avatarListData,
              mode: getAvatarGroupMode(),
              operateTime: operateTime,
              size: 26
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  AVATARCUSTOMGROUP: {
    customLayout: args => {
      const { table, row, col, rect } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      const tableData = tableInstance.value.getCellOriginRecord(
        args.col,
        args.row
      );

      const widgetObject = tableData?.widgetList.find(
        widget => widget.widgetId === fieldName
      );

      let container;
      if (
        widgetObject &&
        widgetObject.showObj &&
        widgetObject.showObj.length > 0
      ) {
        const avatarListData = widgetObject.showObj.map(widget => {
          return Object.assign(widget, {
            coopTeamUser: !["COOP_TEAM", "MULTIPLE_COOP_TEAM"].includes(
              widgetObject.widgetType
            ),
            edited: false,
            user: !["COOP_TEAM", "MULTIPLE_COOP_TEAM"].includes(
              widgetObject.widgetType
            ),
            activate: ["COOP_TEAM", "MULTIPLE_COOP_TEAM"].includes(
              widgetObject.widgetType
            )
              ? true
              : widget.activate
          });
        });

        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(AvatarGroup, {
              avatarListGroup: avatarListData,
              size: 26
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  DATEPICKER: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);
      const fieldName = tableInstance.value.getCellHeaderPaths(
        args.col,
        args.row
      ).colHeaderPaths[0].field;

      // 判断值是否是合法的日期，并按 YYYY-MM-DD 输出
      let formattedDate = "";
      if (value) {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          formattedDate = value;
        }
      }

      const container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //@ts-ignore
        vue: {
          element: h(
            "span",
            {
              style: {
                color: "#262626",
                fontSize: "13px"
              }
            },
            formattedDate
          ),
          container: table.bodyDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  },
  EXCHANGE_RATE: {
    customLayout: args => {
      const { table, row, col, rect, value } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);

      const container = new VTable.CustomLayout.Group({
        height,
        width,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //@ts-ignore
        vue: {
          element: h(
            ReText,
            {
              style: {
                color: "#262626",
                fontSize: "12px",
                whiteSpace: "nowrap",
                pointerEvents: "auto",
                cursor: "default",
                userSelect: "none"
              }
            },
            value && (value.fromMoney || value.targetMoney)
              ? `${
                  value.exchangeType === "fixed"
                    ? t("trade_common_fixedExchangeRate")
                    : value.exchangeType === "realTime"
                      ? t("trade_common_realTimeExchangeRate")
                      : ""
                }  ${value.rate ?? ""} ${value.fromMoney ?? ""}${value.fromMoneyType ? value.fromMoneyType : ""} ~ ${value.targetMoney ?? ""}${value.targetMoneyType ? value.targetMoneyType : ""}`
              : ""
          ),
          container: table.bodyDomContainer
        }
      });

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  }
});

const handleListTableSearch = (keyword: string) => {
  const searchResult = searchInstance.value.search(keyword);
  return searchResult;
};

const handleRenderColumnsInfo = (columns: any[]) => {
  tableColumns.value = columns;

  let fieldColumns = [
    {
      field: "isCheck",
      title: "",
      width: 40,
      headerType: props.relationTodo ? "checkbox" : "text",
      cellType: props.relationTodo ? "checkbox" : "radio"
    },
    {
      field: "relationship",
      title: "",
      width: 20,
      headerStyle: {
        borderLineWidth: [1, 1, 1, 0]
      },
      style: {
        borderLineWidth: [1, 1, 1, 0]
      },
      mergeCell: (v1, v2, args) => {
        return false;
      },
      headerType: "text",
      customLayout: args => {
        const { table, row, col, rect, value } = args;
        const { height, width } = rect ?? table.getCellRect(col, row);

        const container = new VTable.CustomLayout.Group({
          height,
          width: width,
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
          //@ts-ignore
          vue: {
            element: h(TableCheckbox, {
              presentViewInfo: props.presentView,
              rowData: table.records[row - 1]
            }),
            container: table.bodyDomContainer
          }
        });

        return {
          rootContainer: container,
          renderDefault: false
        };
      }
    }
  ];
  for (let column of columns) {
    let color = "";
    if (
      ["MILESTONE", "MS_REPLY", "MS_REPLY_WIDGET", "MS_LABEL"].includes(
        column.type
      )
    ) {
      const index = props.milestoneIdList.findIndex(
        milestone => milestone === column.msId
      );
      color = COLOR_LIST[index % 20];
    }

    let fieldObject: any = {};
    if (column.widgetType === "RATE") {
      fieldObject = columnSlots.value["RATE"];
    } else if (column.widgetType === "FILE_UPLOAD") {
      fieldObject = columnSlots.value["FILEUPLOAD"];
    } else if (["IMAGE_UPLOAD", "SIGNATURE"].includes(column.widgetType)) {
      fieldObject = columnSlots.value["IMAGELIST"];
    } else if (["RADIO", "CHECKBOX"].includes(column.widgetType)) {
      fieldObject = columnSlots.value["SELECTION"];
    } else if (column.type === "ORDER_LABEL" || column.type === "MS_LABEL") {
      fieldObject = columnSlots.value["LABELTAG"];
    } else if (
      (column.type === "ORDER_SPECIAL" && column.name === "orderState") ||
      (column.type === "MILESTONE" && column.shortName === "status") ||
      (column.type === "MILESTONE" && column.name === "status") ||
      (column.type === "MILESTONE" && column.name === "milestoneStatus")
    ) {
      fieldObject = columnSlots.value["STATE"];
    } else if (
      ["orderCreateInfo", "orderUpdateInfo", "workOrderCreateInfo"].includes(
        column.name
      )
    ) {
      fieldObject = columnSlots.value["USERINFO"];
    } else if (column.shortName === "manager" || column.name === "manager") {
      fieldObject = columnSlots.value["USERIMAGE"];
    } else if (
      column.type === "MS_REPLY" ||
      column.name.indexOf("replyInfo") > -1 ||
      ["workOrderReplyInfo", "workOrderCollectInfo"].includes(column.name) ||
      column.name === "milestoneManager"
    ) {
      fieldObject = columnSlots.value["AVATARGROUP"];
    } else if (
      ["MEMBER", "COOP_TEAM", "MULTIPLE_COOP_TEAM", "MULTIPLE_MEMBER"].includes(
        column.widgetType
      )
    ) {
      fieldObject = columnSlots.value["AVATARCUSTOMGROUP"];
    } else if (column.widgetType === "DATE_PICKER") {
      fieldObject = columnSlots.value["DATEPICKER"];
    } else if (column.widgetType === "EXCHANGE_RATE") {
      fieldObject = columnSlots.value["EXCHANGE_RATE"];
    }

    let columnConfig = {};
    if (["TABLE_FORM", "RICH_TEXT"].includes(column.widgetType)) {
      columnConfig = {
        cellType(args) {
          return args.value ? "button" : "text";
        },
        text: "查看",
        style: {
          color: "#2082ED",
          textAlign: "center"
        }
      };
    }

    let columnWidthCompareValue = {
      orderCreateInfo: 190,
      orderUpdateInfo: 190,
      workOrderCreateInfo: 190,
      workOrderReplyInfo: 240,
      workOrderCollectInfo: 240,
      planTime: 190,
      actualTime: 190
    };

    const translationLang = storageLocal().getItem("translationLang");

    fieldColumns.push(
      Object.assign(
        {
          field: column.name,
          title:
            translationLang === "en"
              ? column.labelEn || column.label
              : column.label,
          width: columnWidthCompareValue.hasOwnProperty(column.name)
            ? columnWidthCompareValue[column.name]
            : column.name.indexOf("replyInfo") > -1
              ? 240
              : column.msId
                ? 300
                : column.width,
          hide: !column.show,
          headerStyle: {
            bgColor:
              props.presentView.viewType !== "ORDER" ? `#fff` : `#${color}`,
            padding: [10, 5]
          },
          style: () => {
            if (["INPUT_NUMBER", "PERCENT"].includes(column.widgetType)) {
              return { padding: 10, textAlign: "right" };
            }
            return { padding: 10 };
          },
          fieldFormat: record => {
            if (column.widgetType === "PERCENT") {
              if (
                record[column.name] === undefined ||
                record[column.name] === null ||
                record[column.name] === ""
              ) {
                return "";
              } else {
                let realValue;
                if (column.widgetItemInfo.useThousandSeparator) {
                  realValue = column.widgetItemInfo.precision
                    ? new BigNumber(
                        formatNumberValueOf(record[column.name])
                      ).toFormat(column.widgetItemInfo.precision)
                    : new BigNumber(formatNumberValueOf(record[column.name]));
                } else {
                  realValue = column.widgetItemInfo.precision
                    ? new BigNumber(
                        formatNumberValueOf(record[column.name])
                      ).toFormat(column.widgetItemInfo.precision, {
                        groupSeparator: "",
                        decimalSeparator: "."
                      })
                    : new BigNumber(formatNumberValueOf(record[column.name]));
                }
                return `${realValue}%`;
              }
            } else if (column.widgetType === "INPUT_NUMBER") {
              if (
                record[column.name] === undefined ||
                record[column.name] === null ||
                record[column.name] === ""
              ) {
                return record[column.name];
              } else {
                let realValue;

                if (column.widgetItemInfo.useThousandSeparator) {
                  realValue = column.widgetItemInfo.precision
                    ? new BigNumber(
                        formatNumberValueOf(record[column.name])
                      ).toFormat(column.widgetItemInfo.precision)
                    : new BigNumber(formatNumberValueOf(record[column.name]));
                } else {
                  realValue = column.widgetItemInfo.precision
                    ? new BigNumber(
                        formatNumberValueOf(record[column.name])
                      ).toFormat(column.widgetItemInfo.precision, {
                        groupSeparator: "",
                        decimalSeparator: "."
                      })
                    : new BigNumber(formatNumberValueOf(record[column.name]));
                }
                return realValue;
              }
            }
            return record[column.name];
          },
          headerCustomLayout: args => {
            const { table, row, col, rect } = args;
            const { height, width } = rect ?? table.getCellRect(col, row);

            const container = new VTable.CustomLayout.Group({
              height,
              width: width,
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
              //@ts-ignore
              vue: {
                element: h(TableFilter, {
                  column: {
                    title: column.label,
                    info: column.info,
                    $required: false,
                    props: { titleEn: column.labelEn, info: column.infoEn }
                  },
                  width: width
                }),
                container: table.headerDomContainer
              }
            });

            return {
              rootContainer: container,
              renderDefault: false
            };
          }
        },
        fieldObject,
        columnConfig
      )
    );
  }
  let operate: any = {
    field: "operate",
    title: "",
    width: 68,
    cellType: "text",
    customLayout: args => {
      const { table, row, col, rect } = args;
      const { height, width } = rect ?? table.getCellRect(col, row);
      const tableRow = tableInstance.value.getCellOriginRecord(col, row);

      let container;
      if (props.tableOpeateLists) {
        container = new VTable.CustomLayout.Group({
          height,
          width,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          //@ts-ignore
          vue: {
            element: h(TableOperate, {
              tableOpeateLists: props.tableOpeateLists,
              orderPermissionList: props.orderPermissionList,
              row: tableRow,
              presentView: props.presentView,
              onHandleOperate: (slotName: string, data: any) =>
                handleOperate(slotName, data)
            }),
            container: table.bodyDomContainer
          }
        });
      }

      return {
        rootContainer: container,
        renderDefault: false
      };
    }
  };
  fieldColumns.push(operate);
  fixedNumber = columns.filter(column => column.fixed).length + 2;

  tableInstance.value.updateColumns(fieldColumns);
  tableInstance.value.frozenColCount = fixedNumber;
  tableInstance.value.renderWithRecreateCells();
};

const emit = defineEmits([
  "handleOperate",
  "handleDblClick",
  "handleOrderViewUndergo",
  "handleGetCheckboxRecords",
  "handleGetRadioRecord"
]);

const handleOperate = (slotName: string, data: any) => {
  const { row } = data;
  emit("handleOperate", { slot: slotName, row: row });
};

const setCheckboxRecords = (
  records: any,
  needExecuteStatisticalResult = false
) => {
  tableSelectedRecords.value = records;

  if (needExecuteStatisticalResult) {
    executeStatisticalResult();
  }
};

/**
 * 设置复选框状态,在例如筛选的动作之后，已选的数据复选状态会丢失，需要重新设置
 * @param records 数据
 */
const patchTableCheckboxStatus = records => {
  const tableRecords = handleGetTableRecords();

  records.forEach(record => {
    const recordField =
      props.presentView.viewType === "MILESTONE" ? "workOrderId" : "orderId";
    const index = tableRecords.findIndex(
      item => item[recordField] === record[recordField]
    );
    if (index !== -1) {
      if (props.relationTodo) {
        tableInstanceRef.value.vTableInstance.setCellCheckboxState(
          0, // 列
          index + 1, // 行
          true
        );
      } else {
        tableInstanceRef.value.vTableInstance.setCellRadioState(0, index + 1);
      }
    }
  });
};

const handleJumpTableColumn = (type: string) => {
  if (type === "next") {
    return searchInstance.value.next();
  } else if (type === "prev") {
    return searchInstance.value.prev();
  } else if (type === "clear") {
    return searchInstance.value.clear();
  }
};

const handleReloadTableData = (tableData: any) => {
  tableActualHeight.value =
    tableData.length === 0 ? 500 : (tableData.length + 1) * 40 + 56;
  tableRecord.value = tableData;
  tableInstance.value.setRecords(tableData);
  tableInstance.value.setScrollTop(0);
  tableInstance.value.setScrollLeft(0);
};

const handleReloadRecord = () => {
  tableInstance.value.setRecords(tableRecord.value);
  tableInstance.value.setScrollTop(0);
  tableInstance.value.setScrollLeft(0);
};

const handleClearCheckboxState = () => {
  emit("handleGetCheckboxRecords", []);
  tableInstance.value.clearSelected();
};

const handleGetCheckboxRecords = () => {
  return tableInstance.value.getSelectedCellInfos();
};

const handleGetTableRecords = () => {
  return tableInstanceRef.value.vTableInstance.records;
};

/**
 * 统计表格排序执行逻辑，同步数据表格滚动条
 * @param e
 */
const handleScrollChange = e => {
  tableInstance.value.setScrollLeft(e.scrollLeft);
};

/**
 * 统计执行结果字段
 * @param data
 */
const handleStatisticalResult = data => {
  const index = TableStatisticsResultList.value.findIndex(
    statisticsResult => statisticsResult.widgetId === data.widgetId
  );
  if (index === -1) {
    TableStatisticsResultList.value.push(data);
  } else {
    TableStatisticsResultList.value.splice(index, 1, data);
  }
  handleAssembleViewData();
};

/**
 * 视图发生变化需要提交数据
 */
const handleAssembleViewData = () => {
  const columnList = [];
  const columns = tableInstance.value.getAllColumnHeaderCells()[0];
  for (let column of tableColumns.value) {
    const childColumn = columns.find(
      childColumn => childColumn.field === column.name
    );
    if (childColumn) {
      const columnConfig = Object.assign(column, {
        fixed: fixedNumber > childColumn.col,
        sortable: true,
        show: !!childColumn,
        width: tableInstance.value.getColWidth(childColumn.col)
      });
      columnList.push(columnConfig);
    }
  }

  // 列统计=>todo
  let colStatisticsItemList = TableStatisticsResultList.value.map(
    statisticsResult => {
      return {
        colStatisticType: statisticsResult.val,
        colOptWidgetId: statisticsResult.widgetId,
        crossReferenceId: null,
        replyWorkOrderData: false
      };
    }
  );

  emit("handleOrderViewUndergo", {
    colStatistics: colStatisticsItemList,
    column: columnList
  });
};

watch(
  () => props.colStatisticsItemList,
  () => {
    if (props.colStatisticsItemList.length > 0) {
      TableStatisticsResultList.value = props.colStatisticsItemList.map(
        colStatistics => {
          return {
            val: colStatistics.type,
            result: colStatistics.value,
            widgetId: colStatistics.widgetId
          };
        }
      );
    } else {
      TableStatisticsResultList.value = [];
    }
  },
  {
    deep: true,
    immediate: true
  }
);

/**
 * 表格选中行变更则触发数据统计变更
 */
const executeStatisticalResult = debounce(
  async () => {
    if (tableSelectedRecords.value.length > 0) {
      TableStatisticsResultList.value = cloneDeep(
        TableStatisticsResultList.value
      ).map(widget => {
        const selectedRecords: any[] = tableSelectedRecords.value;
        const calculationResults = computedColumnStatistical(
          fieldNameTransformer(widget.widgetId, true),
          widget.val,
          selectedRecords
        );
        return {
          val: widget.val,
          result: calculationResults,
          widgetId: widget.widgetId
        };
      });
    } else {
      const widgetList = cloneDeep(TableStatisticsResultList.value).map(
        widgetField => widgetField.widgetId
      );
      const widgetStatisticsList = cloneDeep(
        TableStatisticsResultList.value
      ).map(widgetField => {
        return {
          widgetId: widgetField.widgetId,
          type: widgetField.val
        };
      });
      const sccsId = route.query.sccsId as string;
      const { code, data } =
        props.presentView.viewType === "MILESTONE"
          ? await getWorkOrderColumnStatistics({
              sccsId: sccsId,
              sourceIdList: [],
              milestoneId: props.presentView.msId,
              widgetList: widgetList,
              milestone: false,
              widgetStatisticsList: widgetStatisticsList,
              ...props.colStatisticsSearchCondition
            })
          : await getOrderColumnStatistics({
              sccsId: sccsId,
              sourceIdList: [],
              widgetList: widgetList,
              milestone: false,
              widgetStatisticsList: widgetStatisticsList,
              ...props.colStatisticsSearchCondition
            });

      if (code === 0) {
        TableStatisticsResultList.value = data.map(col => {
          const widgetItem = cloneDeep(TableStatisticsResultList.value).find(
            widget => widget.widgetId === col.widgetId
          );

          return {
            val: widgetItem.val,
            result: col.value,
            widgetId: widgetItem.widgetId
          };
        });
      }
    }
  },
  500,
  {
    leading: false,
    trailing: true
  }
);

onMounted(() => {
  // 挂载子表点击事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.BUTTON_CLICK, e => {
    const tableRow = tableInstance.value.getCellOriginRecord(e.col, e.row);
    const fieldName = tableInstance.value.getCellHeaderPaths(e.col, e.row)
      .colHeaderPaths[0].field;
    const widget = tableRow.widgetList.find(
      widget => widget.widgetId === fieldName
    );

    setReferencedDataForSubTable(tableRow);

    nextTick(async () => {
      if (widget.widgetType === "RICH_TEXT") {
        valueHtml.value = widget.obj;
        openDialogWidget.value = widget;
        openDialogVisible.value = true;
      } else if (widget.widgetType === "TABLE_FORM") {
        const templateId = route.query.templateId as string;

        Promise.all([
          getSubTableInfoData({
            typeEnum:
              props.presentView.viewType === "MILESTONE"
                ? "WORK_ORDER"
                : "MAIN_FORM",
            relatedId:
              props.presentView.viewType === "MILESTONE"
                ? tableRow.workOrderId
                : tableRow.orderId
          }),
          getOrderTemplateDetail({
            templateId: templateId,
            orderId: tableRow.orderId
          })
        ]).then(resp => {
          const subWidgetList = resp[0].data[widget.widgetId];
          if (subWidgetList) {
            const subTableAssemblyData = Object.values(
              subWidgetList.reduce((res, item) => {
                res[item.rowIndex]
                  ? res[item.rowIndex].push(item)
                  : (res[item.rowIndex] = [item]);
                return res;
              }, {})
            );

            const formWidgetJsonList =
              props.presentView.viewType === "MILESTONE"
                ? resp[1].data.milestoneList
                    .find(ms => ms.msId === props.presentView.msId)
                    .formList.flatMap(item => item.widgetJsonList)
                : resp[1].data.mainForm.widgetJsonList;

            const tableConfig = formWidgetJsonList.find(
              column => column._fc_id === widget.widgetId
            );
            widgetSubTableConfig.value = tableConfig;
            widgetSubTableData.value = subTableAssemblyData;
            openDialogWidget.value = widget;
            openDialogVisible.value = true;
          }
        });
      }
    });
  });

  // 挂载双击事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.DBLCLICK_CELL, e => {
    const tableRow = tableInstance.value.getCellOriginRecord(e.col, e.row);
    emit("handleDblClick", tableRow);
  });

  // 列宽调整事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.RESIZE_COLUMN, e => {
    handleAssembleViewData();
  });

  // 更改复选框状态
  tableInstance.value.on(
    VTable.ListTable.EVENT_TYPE.CHECKBOX_STATE_CHANGE,
    e => {
      const recordRow = tableInstance.value.getRecordByCell(e.col, e.row);
      const checkBoolean =
        tableInstance.value.getCheckboxState("isCheck")[e.row - 1];

      const areAllTrue = tableInstance.value
        .getCheckboxState("isCheck")
        .every(v => v === true);
      const areAllFalse = tableInstance.value
        .getCheckboxState("isCheck")
        .every(v => v === false || v === undefined);

      if (areAllTrue || areAllFalse) {
        tableSelectedRecords.value = areAllTrue ? tableRecord.value : [];
      } else {
        const tableRecords = cloneDeep(tableSelectedRecords.value);
        const recordField =
          props.presentView.viewType === "MILESTONE"
            ? "workOrderId"
            : "orderId";
        if (checkBoolean) {
          if (
            !tableRecords.find(r => r[recordField] === recordRow[recordField])
          ) {
            tableRecords.push(recordRow);
          }
        } else {
          const index = tableRecords.findIndex(
            record => record[recordField] === recordRow[recordField]
          );
          tableRecords.splice(index, 1);
        }
        tableSelectedRecords.value = tableRecords;
      }
      executeStatisticalResult();
      emit("handleGetCheckboxRecords", tableSelectedRecords.value);
    }
  );

  // 修改单选框状态
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.RADIO_STATE_CHANGE, e => {
    const recordRow = tableInstance.value.getRecordByCell(e.col, e.row);
    emit("handleGetRadioRecord", recordRow);
  });

  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.CLICK_CELL, e => {
    if (e.targetIcon && e.targetIcon.name === "filter") {
      const { left, top, width, height, bottom, right } = e.targetIcon.position;
    }
  });

  // 滚动条发生滚动事件
  tableInstance.value.on(VTable.ListTable.EVENT_TYPE.SCROLL, e => {
    if (e.scrollLeft > 0 && e.event) {
      TableStatisticsRef.value.handleScrollChange(e);
    }
  });
});

defineExpose({
  setCheckboxRecords,
  tableInstanceRef,
  executeStatisticalResult,
  patchTableCheckboxStatus,
  handleGetTableRecords,
  handleRenderColumnsInfo,
  handleReloadTableData,
  handleReloadRecord,
  handleListTableSearch,
  handleJumpTableColumn,
  handleClearCheckboxState,
  handleGetCheckboxRecords
});
</script>
<style lang="scss">
.lk-sub-table-dialog {
  .el-dialog__body {
    height: calc(100% - 20px);
  }
}

.lk-grid-table {
  .vtable {
    overflow: inherit !important;
  }
}

.vtable__bubble-tooltip-element {
  .vtable__bubble-tooltip-element__content {
    font-size: 14px !important;
  }
}
</style>
