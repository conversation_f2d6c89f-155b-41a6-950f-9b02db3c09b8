<template>
  <div class="select-team-container">
    <div class="select-team-body">
      <div class="select-team-title">{{ t("trade_team_selectTeamTip") }}</div>
      <el-scrollbar height="400px">
        <div
          v-for="item in teamList"
          :key="item.id"
          class="select-team-col"
          @click="handleSelectTeam(item.id)"
        >
          <LkAvatar
            class="colorOrange"
            :teamInfo="{ avatar: item.teamAvatar, username: item.teamName }"
          />
          <span class="select-team-name">{{ item.teamName }}</span>
          <FontIcon icon="link-arrowhead-right" />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import { onBeforeRouteLeave } from "vue-router";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import LkAvatar from "@/components/lkAvatar/index";
import { getMyTeamList } from "@/api/common";
import { switchTeam } from "@/utils/auth";

const { t } = useI18n();
let teamList = ref<any[]>([]);
const router = useRouter();

const handleSelectTeam = async (teamId: string): Promise<void> => {
  await switchTeam(teamId, false);
  router.push("/");
};

onMounted(async () => {
  // window.addEventListener("popstate", () => {
  //   window.history.forward();
  // });
  const res = await getMyTeamList();
  if (res.code === 0) {
    if (res.data.length === 0) {
      router.push({ path: "/createTeam" });
    } else {
      teamList.value = res.data;
    }
  }
});

onUnmounted(() => {
  // window.removeEventListener("popstate", () => {});
});

onBeforeRouteLeave((to, from, next) => {
  // router.go(1);
  next();
});
</script>
<style lang="scss" scoped>
.select-team-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f6fa;

  .select-team-body {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 490px;
    padding: 33px 41px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 10px 0 rgb(0 0 0 / 10%);
    transform: translate(-50%, -50%);

    .select-team-title {
      margin-bottom: 28px;
      font-size: 24px;
      font-weight: bolder;
      line-height: 33px;
      color: #262626;
      text-align: center;
    }

    .select-team-col {
      display: flex;
      align-items: center;
      padding: 7px 10px;
      margin-bottom: 14px;
      cursor: pointer;
      background: #fff;
      border: 1px solid #ededed;
      border-radius: 4px;
      box-shadow: 0 2px 6px 0 rgb(1 6 62 / 7%);

      .select-team-name {
        flex: 1;
        align-items: center;
        max-width: 300px;
        margin: 0 8px;
        overflow: hidden;
        font-size: 14px;
        color: #262626;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .iconfont {
        align-items: center;
        font-size: 18px;
        color: #aaaeb9;
      }
    }
  }
}
</style>
