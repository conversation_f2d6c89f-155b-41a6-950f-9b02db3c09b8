<template>
  <div class="sccs-group-setting-body">
    <LkDialog
      ref="LkDialogRef"
      :noFooter="true"
      :width="1200"
      :title="t('trade_sccs_group_setting')"
      :append-to-body="true"
      :align-center="false"
      class="sccs-group-setting-dialog"
      @close="handleClose"
    >
      <template #default>
        <div class="sccs-group-setting-container">
          <div class="sccs-group-setting-left">
            <div class="sccs-group-setting-title">
              <span class="sccs-text-body">
                {{ t("trade_home_groupList") }}
              </span>
              <span class="sccs-icon-body" @click="handleAdd">
                <i class="iconfont link-add" />
              </span>
            </div>
            <div class="sccs-group-setting-group-list">
              <el-scrollbar
                class="no-scrollbar"
                max-height="calc(70vh - 122px)"
              >
                <draggable
                  :list="sccsGroupList"
                  :disabled="!enabled"
                  item-key="groupName"
                  class="w-100"
                  ghost-class="ghost"
                  chosen-class="chosen"
                  animation="300"
                  @start="dragging = true"
                  @end="handleDraggingEnd"
                >
                  <template #item="{ element }">
                    <div
                      class="sccs-group-draggable-col"
                      :class="{
                        'not-draggable': !enabled,
                        active: SccsGroupId === element.id
                      }"
                      @click.stop.prevent="handleSetSccsGroup(element)"
                    >
                      <n-tag
                        :key="element.id"
                        class="item"
                        :class="{ 'not-draggable': !enabled }"
                      >
                        <i class="iconfont link-drag font12" />
                        <span class="sccs-group-draggable-col-title">
                          {{ element.groupName }}
                        </span>
                        <el-popover
                          v-if="element.editable"
                          placement="bottom"
                          trigger="click"
                          width="78"
                          :popperStyle="{ padding: 0, 'min-width': '78px' }"
                        >
                          <template #reference>
                            <i
                              class="iconfont link-more font16 hoverIcon"
                              @click.stop.prevent
                            />
                          </template>
                          <template #default>
                            <div
                              class="sccs-group-dialog-btn"
                              @click.stop.prevent="handleEdit(element)"
                            >
                              <i class="iconfont link-edit" />
                              {{ t("trade_common_edit") }}
                            </div>
                            <div
                              class="sccs-group-dialog-btn sccs-group-dialog-danger-btn"
                              @click.stop.prevent="handleDelete(element.id)"
                            >
                              <i class="iconfont link-ashbin" />
                              {{ t("trade_common_delete") }}
                            </div>
                          </template>
                        </el-popover>
                      </n-tag>
                    </div>
                  </template>
                </draggable>
              </el-scrollbar>
            </div>
          </div>
          <div class="sccs-group-setting-right">
            <el-scrollbar>
              <div
                v-for="item in sccsGroupList"
                :key="item.id"
                class="sccs-group-setting-table-container"
              >
                <div class="sccs-group-setting-table-title" :data-id="item.id">
                  {{ item.groupName }}
                </div>
                <el-table
                  v-if="item.sccsInfoList?.length > 0"
                  :data="item.sccsInfoList"
                  border
                  style="width: 100%"
                  :tooltip-options="{ showAfter: 500 }"
                >
                  <el-table-column
                    prop="sccsName"
                    :label="t('trade_home_sccsName')"
                    show-overflow-tooltip
                    width="462"
                  />
                  <el-table-column
                    prop="teamName"
                    :label="t('trade_home_inTeam')"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <svg
                        v-if="scope.row.coopTeamMark"
                        class="icon svg-icon svg-icon-coop"
                        aria-hidden="true"
                      >
                        <use xlink:href="#link-coop" />
                      </svg>
                      {{ scope.row.teamName }}
                    </template>
                  </el-table-column>

                  <el-table-column align="center" width="78">
                    <template #default="scope">
                      <el-popover
                        ref="popoverRef"
                        placement="bottom-end"
                        :width="230"
                        trigger="click"
                        :popper-style="{ padding: 0 }"
                        @before-enter="handleSelectActiveId(item.id)"
                      >
                        <template #reference>
                          <div>
                            <i class="iconfont link-move-to" />
                          </div>
                        </template>

                        <div class="sccs-popover-container">
                          <div class="sccs-popover-top">
                            <div class="sccs-popover-title">
                              {{ t("trade_home_moveTo") }}
                            </div>
                            <el-scrollbar max-height="230px">
                              <div
                                v-for="item in sccsGroupList"
                                :key="item.id"
                                :class="[
                                  'sccs-group-name',
                                  sccsGroupMoveActiveId === item.id
                                    ? 'is-active'
                                    : ''
                                ]"
                                @click="handleSelectActiveId(item.id)"
                              >
                                {{ item.groupName }}
                                <i class="iconfont link-tick" />
                              </div>
                            </el-scrollbar>
                          </div>
                          <div class="sccs-popover-bottom">
                            <el-button plain @click="handleCancel">
                              {{ t("trade_common_cancel") }}
                            </el-button>
                            <el-button
                              type="primary"
                              color="#0070D2"
                              @click="handleSubmit(item, scope.row.id)"
                            >
                              {{ t("trade_common_sure") }}
                            </el-button>
                          </div>
                        </div>
                      </el-popover>
                    </template>
                  </el-table-column>
                </el-table>
                <el-empty
                  v-else
                  :description="t('trade_common_emptyTip')"
                  :image-size="176"
                  :image="SccsNoGroupImage"
                  style="background: #fff"
                />
              </div>
            </el-scrollbar>
          </div>
        </div>
      </template>
    </LkDialog>
    <SccsGroupDialog
      ref="SccsGroupDialogRef"
      @updateSccsGroup="updateSccsGroup"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, markRaw } from "vue";
import { useI18n } from "vue-i18n";
import draggable from "vuedraggable";
import { ElMessageBox, ElMessage } from "element-plus";
import { WarningFilled } from "@element-plus/icons-vue";
import SccsNoGroupImage from "@/assets/images/home/<USER>";
import { teamCooperationCache } from "@/utils/cooperatioTeam";
import LkDialog from "@/components/lkDialog";
import SccsGroupDialog from "./SccsGroupDialog.vue";
import {
  getSccsGroupList,
  updateSccsGroupSort,
  moveSccsGroup,
  deleteSccsGroup
} from "@/api/sccs";

const { t } = useI18n();
const LkDialogRef = ref<any | HTMLElement>(null);
const popoverRef = ref<any | HTMLElement>(null);
const SccsGroupDialogRef = ref<any | HTMLElement>(null);
const dragging = ref<boolean>(false);
const enabled = ref<boolean>(true);
const sccsGroupList = ref<any[]>([]);
const sccsGroupMoveActiveId = ref<string>("");
const SccsGroupId = ref<string>("");

const emit = defineEmits<{
  (e: "handleClose"): void;
}>();

const open = (): void => {
  getSccsGroup();
  LkDialogRef.value.open();
};

const getSccsGroup = async (): Promise<void> => {
  const { code, data } = await getSccsGroupList({
    isContainSccsInfo: true,
    isEmpty: false
  });
  if (code === 0) {
    sccsGroupList.value = data;
  }
};

const handleSetSccsGroup = element => {
  const elementEl = document.querySelector(
    `.sccs-group-setting-table-title[data-id="${element.id}"]`
  );
  if (elementEl) {
    elementEl.scrollIntoView({ behavior: "smooth" });
  }
  SccsGroupId.value = element.id;
};

const handleDraggingEnd = async (): Promise<void> => {
  let sccsDragEndIds = [];
  sccsGroupList.value.map(sccsList => sccsDragEndIds.push(sccsList.id));
  dragging.value = false;
  const { code } = await updateSccsGroupSort(sccsDragEndIds);
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    getSccsGroup();
  }
};

const handleCancel = (): void => {
  for (let i = 0, len = popoverRef.value.length; i < len; i++) {
    popoverRef.value[i].hide();
  }
};

const handleEdit = (data): void => {
  SccsGroupDialogRef.value.open(data);
};

const handleDelete = async (id: string): Promise<void> => {
  ElMessageBox.confirm(
    t("trade_home_delSccsGroupTip"),
    t("trade_home_delSccsGroupTitle"),
    {
      confirmButtonText: t("trade_common_confirm"),
      cancelButtonText: t("trade_common_cancel"),
      confirmButtonClass: "confrim-message-btn-class",
      cancelButtonClass: "cancel-message-btn-class",
      type: "error",
      icon: markRaw(WarningFilled),
      center: true
    }
  ).then(async () => {
    const { code } = await deleteSccsGroup({ id: id });
    if (code === 0) {
      ElMessage({
        message: t("trade_common_deleteSuccess"),
        type: "success"
      });
      getSccsGroup();
    }
  });
};

const updateSccsGroup = (): void => {
  getSccsGroup();
};

const handleSubmit = async (data: any, sccsId: string): Promise<void> => {
  //@ts-ignore
  const { id: teamId } = await teamCooperationCache.currentlyUsedTeam();
  const { code } = await moveSccsGroup({
    fromSccsGroupId: data.id,
    toSccsGroupId: sccsGroupMoveActiveId.value,
    sccsId: sccsId,
    teamId: teamId
  });
  if (code === 0) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    getSccsGroup();
    handleCancel();
  }
};

const handleSelectActiveId = (id: string): void => {
  sccsGroupMoveActiveId.value = id;
};

const handleClose = (): void => {
  emit("handleClose");
};

const handleAdd = (): void => {
  SccsGroupDialogRef.value.open();
};

defineExpose({
  open
});
</script>
<style lang="scss">
.sccs-group-setting-dialog {
  max-height: 70vh;
  padding: 0 !important;
  border-radius: 10px;

  .el-dialog__header {
    height: 54px;
    padding: 0 22px;
    line-height: 54px;
    border-bottom: 1px solid #e8e8e8;

    .el-dialog__title {
      font-size: 18px;
      font-weight: bold;
      line-height: 25px;
      color: #262626;
    }
  }

  .el-dialog__body {
    height: 920px;
    max-height: calc(70vh - 54px);

    .sccs-group-setting-container {
      display: flex;
      height: 100%;

      .sccs-group-setting-left {
        width: 240px;
        padding: 0 11px 0 18px;
        border-right: 1px solid #e9eaec;

        .sccs-group-setting-title {
          display: flex;
          justify-content: space-between;
          margin: 19px 0;

          .sccs-text-body {
            font-size: 14px;
            font-weight: bold;
            line-height: 20px;
            color: #262626;
          }

          .sccs-icon-body {
            display: inline-block;
            width: 20px;
            height: 20px;
            text-align: center;
            cursor: pointer;

            .iconfont {
              font-size: 12px;
              color: #0070d2;
            }

            &:hover {
              background: #ccecff;
            }
          }
        }

        .sccs-group-setting-group-list {
          .sccs-group-draggable-col {
            padding: 8px 10px 8px 6px;
            cursor: pointer;

            &:hover {
              background: #ebf4ff;
            }

            &.active {
              background: #ebf4ff;
            }

            .item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;

              .iconfont {
                vertical-align: middle;
              }

              .sccs-group-draggable-col-title {
                flex: 1;
                align-items: center;
                margin-left: 8px;
                overflow: hidden;
                font-size: 14px;
                color: #595959;
                text-align: left;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }

      .sccs-group-setting-right {
        flex: 1;
        padding: 18px 20px;
        background: rgb(248 248 248);

        .sccs-group-setting-table-container {
          margin-bottom: 5px;

          .sccs-group-setting-table-title {
            margin: 23px 0 12px;
            font-size: 16px;
            font-weight: bold;
            line-height: 22px;
            color: #262626;
            text-align: left;
          }
        }
      }
    }
  }
}

.sccs-group-dialog-btn {
  height: 40px;
  line-height: 40px;
  text-align: center;
  cursor: pointer;

  &.sccs-group-dialog-danger-btn {
    color: #cf1421;
  }

  &:hover {
    background: #f2f2f2;
  }
}

.sccs-popover-container {
  .sccs-popover-top {
    // margin-top: 8px;

    .sccs-popover-title {
      padding-left: 16px;
      font-size: 14px;
      line-height: 36px;
      color: #797979;
    }
  }

  .sccs-popover-bottom {
    height: 50px;
    padding: 0 22px;
    line-height: 50px;
    text-align: right;
    border-top: 1px solid rgb(151 151 151 / 17%);
  }

  .sccs-group-name {
    position: relative;
    height: 36px;
    padding: 0 18px;
    overflow: hidden;
    line-height: 36px;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;

    .iconfont {
      position: absolute;
      top: 50%;
      right: 18px;
      display: none;
      font-size: 10px;
      transform: translateY(-50%);
    }

    &:hover {
      background: #f5f5f5;
    }

    &.is-active {
      color: #2082ed;

      .iconfont {
        display: block;
      }
    }
  }
}
</style>
