<template>
  <div class="team-member-info-container team-module-area-container">
    <div class="team-module-area-header">
      <div class="team-module-area-title">
        {{ t("trade_team_collaborateTeamManage") }}
      </div>
      <el-button type="primary" color="#0070D2" @click="handleAdd">
        <FontIcon icon="link-add" />{{ t("trade_common_increase") }}
      </el-button>
    </div>
    <div class="team-module-area-body">
      <el-form
        ref="FormRef"
        label-position="top"
        label-width="auto"
        :model="form"
      >
        <el-row :gutter="20">
          <el-col :span="7">
            <el-form-item :label="`${t('trade_team_inputSearch')}：`">
              <el-input
                v-model="form.keyword"
                clearable
                :placeholder="t('trade_sccs_collaboratorsManage')"
                @change="handleSearch"
                @blur="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item :label="`${t('trade_sccs_collaborativeRole')}：`">
              <el-select-v2
                v-model="form.sccsCoopTeamRoleId"
                filterable
                :options="sccsRoleList"
                clearable
                :placeholder="t('trade_common_selectText')"
                :props="{ label: 'label', value: 'value' }"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item :label="`${t('trade_sccs_dataFence')}：`">
              <el-select-v2
                v-model="form.sccsDataFenceId"
                filterable
                :options="sccsFenceList"
                clearable
                :placeholder="t('trade_common_selectText')"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" />
          <el-col :span="7">
            <el-form-item :label="`${t('trade_sccs_colltip')}：`">
              <el-select-v2
                v-model="form.milestoneId"
                filterable
                :options="sccsMileList"
                clearable
                :placeholder="t('trade_common_selectText')"
                :props="{ label: 'name', value: 'id' }"
                @change="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item style="margin-top: 30px">
              <el-button @click="resetForm">
                {{ t("trade_common_reSet") }}
              </el-button>
              <el-button type="primary" color="#0070D2" @click="handleSearch">
                {{ t("trade_common_query") }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table
        :data="tableData"
        :border="true"
        stripe
        max-height="calc(100vh - 280px)"
        :tooltip-options="{ showAfter: 500 }"
        :empty-text="
          loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
        "
      >
        <el-table-column type="expand">
          <template #default="props">
            <div style="padding: 20px 15px">
              <el-table
                :data="props.row.userList"
                border
                stripe
                header-row-class-name="table-header-class"
              >
                <el-table-column
                  :label="t('trade_team_members')"
                  prop="username"
                >
                  <template #default="{ row }">
                    <el-tag class="tag-col">
                      <span v-if="!row.activate" class="tag-register-tip">
                        ({{ t("trade_common_unregistered") }})
                      </span>
                      <LkAvatar
                        :size="18"
                        fit="cover"
                        :teamInfo="{
                          avatar: row.avatar,
                          username: row.username,
                          email: row.email || row.account,
                          coop: true
                        }"
                      />
                      {{ row.username }}
                    </el-tag>
                    <el-tag v-if="row.isDocking" class="tag-col tag-manager">
                      {{ t("trade_sccs_coop_role_contact_person") }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="t('trade_common_email')"
                  prop="account"
                />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_team_teamName')"
          prop="teamName"
          width="160"
          show-overflow-tooltip
        />
        <el-table-column
          :label="t('trade_team_teamShortName')"
          prop="teamShortName"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          :label="t('trade_team_teamCode')"
          prop="teamCode"
          width="80"
          show-overflow-tooltip
        />
        <el-table-column
          :label="t('trade_sccs_collaborativeRole')"
          prop="sccsCoopTeamRoleList"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="{ row }">
            <ReText
              v-for="(item, index) in row.sccsCoopTeamRoleList"
              :key="index"
              class="tag-col"
              type="info"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.name }}
            </ReText>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_sccs_dataFence')"
          prop="sccsDataFenceList"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="{ row }">
            <ReText
              v-for="(item, index) in row.sccsDataFenceList"
              :key="index"
              class="tag-col"
              type="info"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.dataFenceName }}
            </ReText>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('trade_sccs_colltip')"
          prop="milestoneList"
          class-name="el-table-sccs-setting-cell"
        >
          <template #default="{ row }">
            <ReText
              v-for="(item, index) in row.milestoneList"
              :key="index"
              class="tag-col"
              type="info"
              :tippyProps="{ delay: 50000 }"
            >
              {{ item.name }}
            </ReText>
          </template>
        </el-table-column>
        <el-table-column width="70" align="center">
          <template #default="{ row }">
            <LkTableOperate
              :tableOpeateLists="tableOpeateLists"
              :messageBoxConfirmObject="messageBoxConfirmObject"
              :row="row"
              @handleOperate="handleOperate"
            />
          </template>
        </el-table-column>
      </el-table>
      <LkPagination
        ref="LkPaginationRef"
        :total="total"
        @updatePagination="handleSearch"
      />
      <SccsAddMembershipDialog
        ref="SccsAddMembershipDialogRef"
        :basicInfo="basicInfo"
        :groupId="groupId"
        @handleSuccess="handleSearch"
      />
      <SccsEditMembershipDialog
        ref="SccsEditMembershipDialogRef"
        :basicInfo="basicInfo"
        :groupId="groupId"
        @handleSuccess="handleSearch"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import LkPagination from "@/components/lkPagination/index";
import LkAvatar from "@/components/lkAvatar/index";
import SccsAddMembershipDialog from "./SccsCoopManage/SccsAddMembershipDialog.vue";
import SccsEditMembershipDialog from "./SccsCoopManage/SccsEditMembershipDialog.vue";
import { message } from "@/utils/message";
import { ReText } from "@/components/ReText";
import {
  getSccsFenceList,
  getSccsCoopTeamRoleList,
  getSccsMileStoneList,
  getSccsCoopTeamListPage,
  deleteSccsCoopTeamInvite
} from "@/api/sccs";
import LkTableOperate from "@/components/lkTableOperate/index";
import {
  TableOperateProp,
  MessageBoxConfrimProp,
  TableOperateResultProp
} from "@/types/type.d";

const { t } = useI18n();
const form = reactive({
  keyword: "",
  sccsCoopTeamRoleId: "",
  milestoneId: "",
  sccsDataFenceId: ""
});
let tableData = ref<any[]>([]);
let sccsFenceList = ref<any[]>([]);
let sccsRoleList = ref<any[]>([]);
let sccsMileList = ref<any[]>([]);
const SccsAddMembershipDialogRef = ref<any>(null);
const SccsEditMembershipDialogRef = ref<any>(null);
const LkPaginationRef = ref<any>(null);
let total = ref<number>(0);
let loading = ref<boolean>(false);
const messageBoxConfirmObject = ref<MessageBoxConfrimProp>({
  messageBoxTip: "trade_team_sureDeleteCollaborateTeam",
  messageBoxTitle: "trade_team_deleteTeam",
  messageBoxTipArray: ["teamName"]
});
const tableOpeateLists = ref<TableOperateProp[]>([
  {
    icon: "link-setting",
    title: "trade_common_settingRole",
    slotName: "edit"
  },
  {
    icon: "link-ashbin",
    title: "trade_common_delete",
    slotName: "delete",
    iconClassName: "link-delete-icon"
  }
]);

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const init = (): void => {
  loading.value = true;
  const sccsId = props.basicInfo.id;
  Promise.all([
    getSccsFenceList({ sccsId: sccsId }),
    getSccsCoopTeamRoleList({ sccsId: sccsId }),
    getSccsMileStoneList({ sccsId: sccsId })
  ])
    .then(res => {
      sccsFenceList.value = res[0].data;
      sccsRoleList.value = res[1].data;
      sccsMileList.value = res[2].data;
      handleSearch();
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 300);
    });
};

const handleOperate = async (
  slotName: string,
  data: TableOperateResultProp
): Promise<void> => {
  const { row } = data;
  if (slotName === "edit") {
    handleSettingRole(row);
  } else {
    handleDelete(row.id);
  }
};

const resetForm = () => {
  form.milestoneId = "";
  form.keyword = "";
  form.sccsDataFenceId = "";
  form.sccsCoopTeamRoleId = "";
  handleSearch();
};

const handleSearch = async (): Promise<void> => {
  loading.value = true;
  const pageParams = LkPaginationRef.value.pageParams;
  const res = await getSccsCoopTeamListPage({
    ...form,
    ...pageParams,
    ...{ sccsId: props.basicInfo.id }
  });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
  setTimeout(() => {
    loading.value = false;
  }, 300);
};

const handleAdd = (): void => {
  SccsAddMembershipDialogRef.value.open();
};

const handleDelete = async (id: string): Promise<void> => {
  const { code } = await deleteSccsCoopTeamInvite({
    id: id,
    sccsId: props.basicInfo.id
  });
  if (code === 0) {
    message(t("trade_common_deleteSuccess"), {
      customClass: "el",
      type: "success"
    });
    handleSearch();
  }
};

const handleSettingRole = (row: any) => {
  SccsEditMembershipDialogRef.value.open(row);
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@use "./index.scss";
// ::v-deep(.el-table) {
//   .el-table__body .el-table__row {
//     height: 60px !important;
//   }
// }
::v-deep(.el-table) {
  .table-header-class {
    .el-table__cell {
      background: #f7f7f7 !important;
    }
  }
}
</style>
