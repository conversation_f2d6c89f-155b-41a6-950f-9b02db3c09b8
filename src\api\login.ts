import { http } from "@/utils/http";
import type {
  Result,
  LoginFormState,
  ResetPwdByEmailProp,
  MailLoginProp,
  VerifyCodeProp,
  VerifyCodeEmailProp,
  RegisterProp
} from "./type";

/**
 * 使用账号密码登录
 * @param data
 * @returns
 */
export const loginApi = (data: LoginFormState) => {
  return http.request<Result>("post", "/trade/auth/login", { data });
};

/**
 * 验证码登录
 * @param data
 * @returns
 */
export const mailLogin = (data: MailLoginProp) => {
  return http.request<Result>("post", "/trade/auth/mail-login", { data });
};

/**
 * 获取用户信息
 * @returns
 */
export const getUserInfo = () => {
  return http.request<Result>("get", "/trade/user/get-user-info");
};

/**
 * 登出接口
 * @returns
 */
export const logoutApi = () => {
  return http.request<Result>("post", "/trade/auth/logout");
};

/**
 * 获取验证码
 * @param data
 * @returns
 */
export const getSendVerifyCode = (data: VerifyCodeProp) => {
  return http.request<Result>("post", "/trade/user/send-verify-code", { data });
};

/**
 * 贸易端用户校验邮件验证码
 * @param data
 * @returns
 */
export const checkMailCode = (data: VerifyCodeEmailProp) => {
  return http.request<Result>("post", "/trade/user/check-mail-code", { data });
};

/**
 * 注册
 * @param data
 * @returns
 */
export const register = (data: RegisterProp) => {
  return http.request<Result>("post", "/trade/user/register", { data });
};

/**
 * 重置密码
 * @param data
 * @returns
 */
export const resetPswByEmail = (data: ResetPwdByEmailProp) => {
  return http.request<Result>("post", "/trade/user/reset-psw-by-email", {
    data
  });
};

/**
 * 刷新token
 * @param data
 * @returns
 */
export const refreshToken = (params: any) => {
  return http.request<Result>("post", "/trade/auth/refresh-token", {
    params
  });
};
