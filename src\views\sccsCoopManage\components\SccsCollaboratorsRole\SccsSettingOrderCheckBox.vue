<template>
  <div class="role-per-col">
    <div v-if="formElReadonly" class="role-mask" />
    <el-checkbox
      v-model="orderAllCheckFlag"
      class="checkbox-parent"
      :disabled="formElReadonly"
      @change="handleCheckAllChange"
    >
      {{ sccsSettingData.label }}
    </el-checkbox>
    <el-checkbox-group
      v-model="orderCheckedList"
      :disabled="formElReadonly"
      @change="handleOrderItemChecked"
    >
      <el-checkbox
        v-for="(orderToolItem, index) in orderToolPermList"
        :key="orderToolItem.key"
        class="checkbox-group-col"
        :label="orderToolItem.label"
        :value="orderToolItem.key"
      >
        <div class="checkbox-span-body">
          <div class="checkbox-flex">{{ orderToolItem.label }}</div>
          <div class="checkbox-flex">
            <el-radio-group
              v-model="permissionValue[index].orderScope"
              :disabled="formElReadonly"
              @change="handlePermissionValueChange"
            >
              <el-radio
                v-for="orderScope in orderToolItem.orderScopeList"
                :key="orderScope.value"
                :value="orderScope.value"
              >
                {{ orderScope.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch } from "vue";

interface PermissionKeyProp {
  orderScope?: string;
  permissionKey?: string;
}

const props = defineProps({
  sccsSettingData: {
    type: Object as PropType<any>
  },
  orderRolePerm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  formElReadonly: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const orderAllCheckFlag = ref<boolean>(false);
const orderCheckedList = ref<any>([]);
const permissionValue = ref<PermissionKeyProp[]>([]);

const emit = defineEmits(["handlePermissionValueChange"]);

const orderToolPermList = computed(() => {
  return props.sccsSettingData.childrenList;
});

/**
 * 全选
 */
const handleCheckAllChange = (val: boolean) => {
  const checkedList: string[] = orderToolPermList.value.map(
    orderTool => orderTool.key
  );
  orderCheckedList.value = val ? checkedList : [];
  permissionValue.value = permissionValue.value.map(item => ({
    ...item,
    orderScope: val ? item.orderScope || "ALL_COOP_ORDER" : undefined
  }));
  emit("handlePermissionValueChange", permissionValue.value);
  orderAllCheckFlag.value = val;
};

const handleOrderItemChecked = (value: string[]) => {
  const permissionItem = permissionValue.value.find(
    permission => permission.permissionKey === "view_main_form"
  );
  permissionItem.orderScope = value.length === 0 ? "" : "ALL_COOP_ORDER";
  orderAllCheckFlag.value = value.length !== 0;
  emit("handlePermissionValueChange", permissionValue.value);
};

const handlePermissionValueChange = () => {
  emit("handlePermissionValueChange", permissionValue.value);
};

const getPermissionValue = () => {
  return orderAllCheckFlag.value ? permissionValue.value : [];
};

watch(
  () => props.sccsSettingData,
  () => {
    permissionValue.value = props.sccsSettingData.childrenList.map(
      orderTool => {
        return {
          permissionKey: orderTool.key,
          orderScope: ""
        };
      }
    );
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.orderRolePerm,
  () => {
    if (Array.isArray(props.orderRolePerm) && props.orderRolePerm.length > 0) {
      permissionValue.value = [...props.orderRolePerm];
      orderCheckedList.value = permissionValue.value.map(
        item => item.permissionKey
      );
      orderAllCheckFlag.value = true;

      const viewMainFormItem = permissionValue.value.find(
        item => item.permissionKey === "view_main_form"
      );
      if (viewMainFormItem) {
        if (orderCheckedList.value.includes("view_main_form")) {
          viewMainFormItem.orderScope =
            viewMainFormItem.orderScope || "ALL_COOP_ORDER";
        } else {
          viewMainFormItem.orderScope = "";
        }
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  permissionValue,
  getPermissionValue,
  handleCheckAllChange
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  margin-bottom: 10px;
  font-weight: bolder;
}

.checkbox-group-col {
  width: 100% !important;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  ::v-deep(.el-checkbox__label) {
    flex: 1 !important;

    .checkbox-span-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .checkbox-flex {
        flex: 1;

        &:nth-child(2) {
          padding-right: 20px;
          text-align: right;
        }
      }
    }
  }
}

.role-per-col {
  position: relative;

  .role-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2000;
    width: 100%;
    height: 100%;
    cursor: not-allowed;
    background: transparent;
  }
}
</style>
