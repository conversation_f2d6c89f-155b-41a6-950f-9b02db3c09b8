<template>
  <div v-if="loading" v-loading="loading" style="height: 20vh" />
  <div v-if="tableData.length === 0 && !loading" class="team-empty-body">
    <el-empty
      :description="t('trade_common_notPlanTip')"
      :image-size="342"
      :image="tradeSccsImage"
    />
  </div>
  <div v-else-if="!loading" class="team-module-area-body">
    <span class="sccs-plan-tip-text">
      <i class="iconfont link-tips-warm" />
      <span class="sccs-plan-tip-text-red">
        {{ t("trade_common_fileds") }}
      </span>
      {{ t("trade_common_planedTip") }}
    </span>
    <el-table
      :data="tableData"
      stripe
      header-row-class-name="table-header-class"
      :tooltip-options="{ showAfter: 500 }"
      max-height="calc(100vh - 180px)"
      :empty-text="
        loading ? t('trade_common_loadingText') : t('trade_common_emptyTip')
      "
    >
      <el-table-column
        :label="t('trade_common_milestone')"
        width="160"
        class-name="table-cell-name"
        prop="milestoneName"
        show-overflow-tooltip
      />
      <el-table-column
        :label="t('trade_sccs_plannedRange')"
        prop="teamMemberInfoList"
      >
        <template #default="scope">
          <div class="sccs-planned-msg">
            <el-select
              v-model="scope.row.timeType"
              :placeholder="t('trade_sccs_selectTimePoint')"
              style="width: 154px"
              clearable
            >
              <el-option
                v-for="item in timeTypeEnum"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <template #default>
                  <ReText type="info" :tippyProps="{ delay: 50000 }">
                    {{ item.label }}
                  </ReText>
                </template>
              </el-option>
            </el-select>
            =
            <el-select-v2
              v-model="scope.row.calculationDateId"
              :placeholder="t('trade_sccs_selectTimeType')"
              style="width: 170px"
              clearable
              filterable
              popper-class="timeTypeClass"
              :popper-append-to-body="true"
              :options="milestoneCloneDataFields"
            >
              <template #label="{ value }">
                <span
                  :class="[
                    getWidgetClass(value)?.deleted ? 'widget-deleted' : ''
                  ]"
                >
                  {{ getWidgetClass(value)?.label }}
                </span>
              </template>
              <template #default="data">
                <ReText type="info" :tippyProps="{ delay: 50000 }">
                  <span style="color: #262626">{{ data.item.label }}</span>
                </ReText>
              </template>
            </el-select-v2>
            <el-select
              v-model="scope.row.relativeDateType"
              style="width: 100px; margin-left: 5px"
              clearable
              @change="val => handleChangeRelativeDateType(val, scope.row)"
            >
              <el-option
                v-for="item in relativeDateType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-input-number
              v-if="['BEFORE', 'AFTER'].includes(scope.row.relativeDateType)"
              v-model="scope.row.relativeDays"
              :min="1"
              :max="99999999"
              :controls="false"
              :precision="0"
              style="width: 80px; margin-left: 5px"
              clearable
            />
            <span
              v-if="['BEFORE', 'AFTER'].includes(scope.row.relativeDateType)"
              style="margin-left: 5px"
            >
              {{ t("trade_common_day") }}
            </span>
          </div>
          <div class="sccs-planned-row-tip">
            <div
              v-if="scope.row.timeType && scope.row.timeType !== 'PLAN_END'"
              class="sccs-planned-formula-text"
            >
              {{ t("trade_sccs_plannedEnd") }}
            </div>
            <div
              v-if="scope.row.timeType && scope.row.timeType !== 'PLAN_START'"
              class="sccs-planned-formula-text"
            >
              {{ t("trade_sccs_plannedStart") }}
            </div>
            <div
              v-if="getTableRowValidate(scope.row)"
              class="sccs-plan-tip-text-red"
            >
              {{ t("trade_sccs_planSettingTip") }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        width="160"
        :label="t('trade_sccs_programmeDuration')"
        prop="teamMemberInfoList"
        class-name="table-cell-second-name"
        show-overflow-tooltip
      >
        <template #default="scope">
          <el-input-number
            v-model="scope.row.plannedDurationDays"
            style="width: 72px"
            :min="1"
            :max="99999999"
            :controls="false"
            :precision="0"
            clearable
          />
          <span style="margin-left: 5px">{{ t("trade_common_day") }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { message } from "@/utils/message";
import { cloneDeep, deepEqual } from "@pureadmin/utils";
import tradeSccsImage from "@/assets/images/home/<USER>";
import { ReText } from "@/components/ReText";
import {
  getSccsMilestonePlanList,
  getMileStonePlanListDateField,
  saveSccsMilestonePlanList
} from "@/api/sccs";

const { t } = useI18n();
const loading = ref<boolean>(false);
let tableData = ref<any[]>([]);
const planIsChange = ref<boolean>(false);
const milestoneDataFields = ref<any[]>([]);
const milestoneCloneDataFields = ref<any[]>([]);
let timeTypeEnum = [
  {
    label: t("trade_sccs_planned_start_date"),
    value: "PLAN_START"
  },
  {
    label: t("trade_sccs_planned_end_date"),
    value: "PLAN_END"
  }
];
let relativeDateType = [
  {
    label: t("trade_common_before"),
    value: "BEFORE"
  },
  {
    label: t("trade_common_nowDay"),
    value: "TODAY"
  },
  {
    label: t("trade_common_afterDay"),
    value: "AFTER"
  }
];

const props = defineProps({
  basicInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  groupId: {
    type: String as PropType<string>,
    default: ""
  }
});

const emit = defineEmits<{
  (e: "handleChange"): void;
  (e: "handleSettingDisabled", bool: boolean): void;
}>();

const getWidgetClass = (val: string) => {
  const index = milestoneDataFields.value.findIndex(
    widget => widget.value === val
  );
  if (index === -1) {
    const item = milestoneCloneDataFields.value.find(
      widget => widget.value === val
    );
    return item;
  } else {
    const item = milestoneDataFields.value.find(widget => widget.value === val);
    return item;
  }
};

const handleChangeRelativeDateType = (value: string, row: any): void => {
  if (!value) {
    row.relativeDays = undefined;
  }
};
const getTableRowValidate = (row: any): boolean => {
  const { timeType, calculationDateId, relativeDateType, relativeDays } = row;

  if (
    planIsChange.value &&
    !timeType &&
    !calculationDateId &&
    !relativeDateType &&
    !relativeDays
  ) {
    return false;
  }
  if (relativeDateType === "TODAY") {
    return (
      planIsChange.value &&
      (!timeType || !calculationDateId || !relativeDateType)
    );
  } else {
    return (
      planIsChange.value &&
      (!timeType || !calculationDateId || !relativeDateType || !relativeDays)
    );
  }
};

const handleSearchTable = async (): Promise<void> => {
  loading.value = true;
  return Promise.all([
    getSccsMilestonePlanList({
      sccsId: props.basicInfo.id
    }),
    getMileStonePlanListDateField({
      sccsId: props.basicInfo.id
    })
  ])
    .then(res => {
      if (res[0].code === 0) {
        tableData.value = res[0].data.map(row => {
          return Object.assign(row, {
            calculationDateId:
              row.calculationDateObject && row.calculationDateObject.value
                ? row.calculationDateObject.value
                : ""
          });
        });
      }
      if (res[1].code === 0) {
        milestoneDataFields.value = res[1].data;
        milestoneCloneDataFields.value = res[1].data.filter(
          dataRow => !dataRow.deleted
        );
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleSaveTableData = async (): Promise<void> => {
  const commitTableData = cloneDeep(tableData.value);
  commitTableData.map(tableItem => {
    const calculationDateObject = milestoneDataFields.value.find(
      milestoneField => milestoneField.value === tableItem.calculationDateId
    );
    tableItem["calculationDateObject"] = calculationDateObject;
  });
  const { code } = await saveSccsMilestonePlanList({
    sccsId: props.basicInfo.id,
    detailList: commitTableData
  });
  if (code === 0) {
    message(t("trade_common_updateSuccess"), {
      customClass: "el",
      type: "success"
    });
    await handleSearchTable();
  }
};

watch(
  () => props.basicInfo.id,
  () => {
    handleSearchTable();
  },
  {
    deep: true,
    immediate: true
  }
);

// 添加一个变量存储快照
let tableDataSnapshot = cloneDeep(tableData.value);

watch(
  () => tableData.value,
  newValue => {
    if (tableDataSnapshot.length > 0) {
      // 移除 calculationDateObject 并比较数据
      const removeCalcObj = obj => {
        const { calculationDateObject, ...rest } = obj;
        return rest;
      };

      const newValueClone = cloneDeep(newValue).map(removeCalcObj);
      const snapshotClone = cloneDeep(tableDataSnapshot).map(removeCalcObj);

      if (!deepEqual(newValueClone, snapshotClone)) {
        planIsChange.value = true;
        emit("handleChange");
      }
    }

    for (let tableRow of newValue) {
      if (getTableRowValidate(tableRow)) {
        emit("handleSettingDisabled", true);
        return;
      }
      emit("handleSettingDisabled", false);
    }

    // 更新快照
    tableDataSnapshot = cloneDeep(newValue);
  },
  {
    deep: true
  }
);

defineExpose({
  handleSaveTableData
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

::v-deep(.table-cell-name) {
  vertical-align: baseline;

  .cell {
    height: 32px;
    line-height: 32px;
  }
}

::v-deep(.table-cell-second-name) {
  vertical-align: baseline;
}

.team-module-area-body {
  margin-top: 0 !important;

  .team-module-area-btn {
    position: absolute;
    top: 0;
    right: 0;
  }
}

.sccs-plan-tip-text {
  display: inline-block;
  margin: 10px 0;
  font-size: 12px;
  line-height: 16px;
  color: #8c8c8c;
  text-align: left;

  .iconfont {
    margin-right: 5px;
    font-size: 12px;
  }

  .sccs-plan-tip-text-red {
    margin-right: 6px;
    color: #f56c6c;
    text-decoration: line-through;
  }
}

::v-deep(.el-table) {
  .table-header-class {
    .el-table__cell {
      background: #f7f7f7 !important;
    }
  }
}

.sccs-planned-row-tip {
  display: flex;
  align-items: center;

  .sccs-planned-formula-text {
    margin: 10px 0;
    font-size: 12px;
    line-height: 14px;
    color: #808080;
    text-align: left;
  }

  .sccs-plan-tip-text-red {
    margin-left: 5px;
    font-size: 12px;
    color: #e62412;
  }
}

.tag-col {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 12px;
  line-height: 20px;
  color: #595959;
  background: #f0f0f0;
  border: 0 none;
  border-radius: 4px;
}

.tag-register-tip {
  margin-right: 5px;
  color: #fa8d0a;
}

.role-permission-body {
  padding-bottom: 12px;
  background: #f5f5f5;
  border-radius: 12px;

  ::v-deep(.role-per-col) {
    box-sizing: border-box;
    width: 100%;
    padding: 22px 0 32px 16px;
    margin: 12px 12px 0;
    background: #fff;

    .el-checkbox {
      display: flex;

      .checkbox-span {
        width: 100%;
        font-size: 14px;
        line-height: 16px;
        color: #595959;

        .checkbox-tip {
          margin-left: 14px;
          font-size: 12px;
          line-height: 16px;
          color: #8c8c8c;
        }
      }
    }
  }
}

.team-empty-body {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

.widget-deleted {
  color: #f56c6c;
  text-decoration: line-through;
}
</style>
<style lang="scss">
.timeTypeClass {
  width: 300px !important;

  .el-select-dropdown {
    width: 100% !important;

    .el-select-dropdown__list.el-vl__window {
      width: 100% !important;
    }
  }
}
</style>
