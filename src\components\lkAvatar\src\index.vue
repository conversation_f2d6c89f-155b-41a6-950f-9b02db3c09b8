<template>
  <el-popover
    placement="bottom"
    width="auto"
    trigger="click"
    style="pointer-events: auto; cursor: pointer"
  >
    <template #reference>
      <div
        class="lk-avater-container"
        style="pointer-events: auto; cursor: pointer"
        @click.stop
      >
        <el-avatar
          :src="avatarInfo.avatarLink"
          :size="44"
          v-bind="$attrs"
          fit="cover"
        >
          {{ avatarInfo.userName }}
        </el-avatar>
        <svg v-if="avatarInfo.coop" class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#link-coop" />
        </svg>
        <div
          v-if="avatarInfo.status"
          class="lk-avatar-mask"
          :style="{
            width: `${$attrs.size}px`,
            height: `${$attrs.size}px`,
            'line-height': `${$attrs.size}px`
          }"
        >
          <i class="iconfont link-tick" />
        </div>
      </div>
    </template>
    <div
      class="avatar-body"
      style="pointer-events: auto; cursor: pointer"
      @click.stop
    >
      <div
        class="avatar-body-container"
        style="pointer-events: auto; cursor: pointer"
        @click.stop
      >
        <el-avatar
          :src="avatarInfo.avatarLink"
          :size="44"
          v-bind="$attrs"
          fit="cover"
        >
          {{ avatarInfo.userName }}
        </el-avatar>
        <svg v-if="avatarInfo.coop" class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#link-coop" />
        </svg>
      </div>
      <div class="avatar-right" @click.stop>
        <ReText type="info" :tippyProps="{ delay: 50000 }">
          {{ avatarInfo.username }}
          {{ avatarInfo.email ? `(${avatarInfo.email})` : "" }}
        </ReText>
      </div>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import { ref, watchEffect } from "vue";
import { ElPopover, ElAvatar } from "element-plus";
import { storageLocal } from "@pureadmin/utils";
import { ReText } from "@/components/ReText";

interface userAvatarInfo {
  avatarLink: string | null;
  userName: string | null;
  username: string | null;
  email: string | null;
  status: string | null;
  coop: boolean | null;
}

interface TeamInfo {
  id?: string;
  avatar: string;
  username: string;
}

const props = defineProps({
  teamInfo: {
    type: Object as PropType<TeamInfo>
  }
});

const avatarInfo = ref<userAvatarInfo>({
  avatarLink: "",
  userName: "",
  username: "",
  status: "",
  coop: false,
  email: ""
});

watchEffect(() => {
  if (props.teamInfo) {
    avatarInfo.value.avatarLink = props.teamInfo?.avatar;
    avatarInfo.value.userName = props.teamInfo?.username?.substring(0, 1);
    avatarInfo.value.username = props.teamInfo?.username;
    avatarInfo.value.status = props.teamInfo?.status;
    avatarInfo.value.coop = props.teamInfo?.coop;
    avatarInfo.value.email = props.teamInfo?.email;
  } else {
    //@ts-ignore
    const { avatar, username } = storageLocal().getItem("user-info");
    avatarInfo.value.avatarLink = avatar;
    avatarInfo.value.userName = username?.substring(0, 1);
    avatarInfo.value.username = props.teamInfo?.username;
    avatarInfo.value.status = "";
    avatarInfo.value.coop = false;
  }
});

const reloadAvatar = (): void => {
  if (props.teamInfo) {
    avatarInfo.value.avatarLink = props.teamInfo?.avatar;
    avatarInfo.value.userName = props.teamInfo?.username?.substring(0, 1);
    avatarInfo.value.status = props.teamInfo?.status;
  } else {
    //@ts-ignore
    const { avatar, username } = storageLocal().getItem("user-info");
    avatarInfo.value.userName = username?.substring(0, 1);
    avatarInfo.value.avatarLink = avatar;
    avatarInfo.value.status = "";
  }
};

defineExpose({
  reloadAvatar
});
</script>
<style lang="scss" scoped>
.lk-avater-container {
  position: relative;
  display: inline-block;

  .el-avatar {
    cursor: pointer;
    background: #2082ed;
  }

  .lk-avatar-mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1001;
    text-align: center;
    background: rgb(0 0 0 / 45%);
    border-radius: 50%;

    .iconfont {
      font-size: 10px;
      color: #00cc7b;
    }
  }

  .svg-icon {
    position: absolute;
    right: 0;
    bottom: -3px;
    z-index: 1000;
    width: 13px;
    height: 13px;
  }
}

.avatar-body {
  display: flex;
  align-items: center;

  .avatar-body-container {
    position: relative;
    display: inline-block;

    .svg-icon {
      position: absolute;
      right: 0;
      bottom: -3px;
      z-index: 1000;
      width: 13px;
      height: 13px;
    }
  }

  .avatar-right {
    display: flex;
    flex: 1;
    align-items: center;
    max-width: calc(100% - 25px);
    margin-left: 4px;

    .el-text {
      font-size: 12px;
      color: #262626;
    }
  }
}
</style>
