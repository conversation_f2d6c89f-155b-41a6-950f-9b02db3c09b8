<template>
  <el-drawer
    v-model="drawer"
    class="team-manage-drawer-body"
    size="94%"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <template #header="{ titleId }">
      <el-button
        v-perms="'team_setting'"
        disabled
        style="flex: none; height: 24px"
      >
        {{ t("trade_team_setTeam") }}
      </el-button>
      <div :id="titleId" class="draw-header-title">
        {{ drawerTeamName }}
      </div>
    </template>
    <el-tabs
      v-model="activeName"
      class="left-tabs-body w210"
      tab-position="left"
      style="height: 100%"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="(item, index) in tabPaneList"
        :key="index"
        :name="item.name"
        :lazy="true"
        style="height: 100%"
      >
        <template #label>
          <span class="custom-tabs-label">
            <FontIcon :icon="item.iconfont" />
            <span class="custom-tabs-text">{{ t(item.title) }}</span>
          </span>
        </template>
        <component :is="item.component" v-if="activeName === item.name" />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { useI18n } from "vue-i18n";
import TeamBasicInfo from "./TeamBasicInfo.vue";
import TeamMembersInfo from "./TeamMembersInfo.vue";
import TeamMembershipInfo from "./TeamMembershipInfo.vue";
import TeamCollaborationInfo from "./TeamCollaborationInfo.vue";
import type { TabsPaneContext } from "element-plus";

const { t } = useI18n();
const drawer = ref<boolean>(false);
const drawerTeamName = ref<string>("");
const activeName = ref("basicInfo");
const route = useRoute();
const tabPaneList = ref<any[]>([
  {
    title: "trade_team_baseInfo",
    iconfont: "link-basic-info",
    component: TeamBasicInfo,
    name: "basicInfo"
  },
  {
    title: "trade_team_teamRole",
    iconfont: "link-team-role",
    component: TeamMembersInfo,
    name: "membersInfo"
  },
  {
    title: "trade_team_membersManage",
    iconfont: "link-team-members",
    component: TeamMembershipInfo,
    name: "membershipInfo"
  },
  {
    title: "trade_team_collaborateTeamManage",
    iconfont: "link-coop-team-manage",
    component: TeamCollaborationInfo,
    name: "collaborationInfo"
  }
  // {
  //   title: t("trade_team_moreSet"),
  //   iconfont: "link-basic-info",
  //   component: "basicInfo"
  // },
  // {
  //   title: t("trade_team_operateLog"),
  //   iconfont: "link-operation-log",
  //   component: "basicInfo"
  // }
]);

const props = defineProps({
  teamName: {
    type: String as PropType<string>,
    default: ""
  }
});

onMounted(() => {
  setTimeout(() => {
    const locateTo = route.query?.locateTo as string;
    if (locateTo && locateTo === "coopTeamManage") {
      drawer.value = true;
      activeName.value = "collaborationInfo";
    }
  }, 500);
});

const handleClose = (): void => {
  activeName.value = "basicInfo";
};

const handleClick = (tab: TabsPaneContext) => {
  //@ts-ignore
  if (tab && tab.name) {
    //@ts-ignore
    activeName.value = tab.name;
  }
};

const open = async (): Promise<void> => {
  drawer.value = true;
  drawerTeamName.value = props.teamName;
};

defineExpose({
  open
});
</script>
<style lang="scss">
.team-manage-drawer-body {
  max-width: 1200px;

  .el-drawer__header {
    height: 42px;
    padding: 0 16px 0 13px;
    margin: 0;
    line-height: 42px;
    border-bottom: 1px solid #ebebeb;

    .el-drawer__close-btn {
      i {
        color: #8c8c8c;
      }
    }
  }

  .el-drawer__body {
    padding: 0;

    .left-tabs-body {
      .el-tabs__header {
        width: 210px;

        .el-tabs__nav-wrap {
          width: 100%;
          padding-top: 12px;

          .el-tabs__nav {
            width: 100%;

            .el-tabs__item {
              .custom-tabs-label {
                display: inline-block;
                width: 100%;
                text-align: left;

                .iconfont {
                  margin-right: 3px;
                  font-size: 14px;
                }

                .custom-tabs-text {
                  font-size: 14px;
                  color: #262626;
                }

                .custom-tabs-number {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  margin-left: 6px;
                  font-size: 12px;
                  line-height: 16px;
                  color: #fff;
                  text-align: center;
                  background: #e62412;
                  border-radius: 50%;
                }
              }

              &:hover {
                .custom-tabs-label {
                  .iconfont {
                    color: #262626;
                  }
                }
              }
            }

            .el-tabs__item.is-active {
              background: #e6f1ff;

              .custom-tabs-label {
                .custom-tabs-text {
                  color: #0070d2;
                }
              }

              &:hover {
                .custom-tabs-label {
                  .iconfont {
                    color: #0070d2;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
