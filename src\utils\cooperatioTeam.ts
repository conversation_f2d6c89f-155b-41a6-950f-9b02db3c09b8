import { storageLocal, storageSession } from "@pureadmin/utils";
import { getMyTeamList } from "@/api/common";
import { getSystemUserInfo } from "@/utils/auth";

class TeamCacheProxy {
  protected teamCacheList: any;
  protected userInfo: any;
  constructor() {
    this.userInfo = storageLocal().getItem("user-info");
  }

  public async init(): Promise<void> {
    const res = await getMyTeamList();
    if (res?.code === 0) {
      this.teamCacheList = res.data;
    }
  }

  public async currentlyUsedTeam(): Promise<void> {
    let userPresentTeam: any =
      sessionStorage.getItem("userPresentTeam") === "undefined";
    if (!userPresentTeam) {
      const { accessToken, refreshToken, expires, username } =
        storageLocal().getItem("user-info") as any;
      await getSystemUserInfo({ accessToken, refreshToken, expires });
      sessionStorage.setItem("username", username);
      userPresentTeam = storageSession().getItem("userPresentTeam");
      return userPresentTeam;
    }
    return userPresentTeam;
  }

  public async getCacheTeamOtherList() {
    try {
      let userPresentTeam: any =
        sessionStorage.getItem("userPresentTeam") === "undefined";
      let userMemberTeam: any =
        sessionStorage.getItem("userMemberTeam") === "undefined";

      if (!userMemberTeam || !userPresentTeam) {
        const { accessToken, refreshToken, expires, username } =
          storageLocal().getItem("user-info") as any;
        await getSystemUserInfo({ accessToken, refreshToken, expires });
        sessionStorage.setItem("username", username);
        userPresentTeam = storageSession().getItem("userPresentTeam");
        userMemberTeam = storageSession().getItem("userMemberTeam");

        return userMemberTeam;
      } else {
        const index = userMemberTeam.findIndex(
          userMember => userMember.id === userPresentTeam.id
        );
        userMemberTeam.splice(index, 1);
        return userMemberTeam;
      }
    } catch (err) {
      window.location.href = "/createTeam";
    }
  }
}

export const teamCooperationCache = new TeamCacheProxy();
