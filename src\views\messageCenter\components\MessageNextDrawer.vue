<template>
  <el-drawer
    v-model="nextDrawer"
    :width="420"
    direction="ltr"
    class="message-center-child-drawer"
    modal-class="message-center-child-drawer-modal"
    :modal="true"
    :show-close="false"
    :z-index="1930"
    style="border-left: 1px solid #f2f2f2"
    :esc-to-close="true"
  >
    <template #header="{ close }">
      <div class="drawer-header-body">
        <span class="drawer-header-left">
          {{ t("trade_common_notice") }}
        </span>
        <div class="drawer-header-right">
          <i
            v-show="!!unReadCount"
            class="iconfont link-read icon iconRead"
            @click="handleSetAllRead"
          />
          <i class="iconfont link-close icon icon1" @click="close" />
        </div>
      </div>
    </template>
    <template #default>
      <div
        v-infinite-scroll="loadMessageList"
        :infinite-scroll-disabled="infiniteScrollDisabled"
        :infinite-scroll-distance="100"
        class="drawer-message-list-body"
      >
        <div class="drawer-message-list-row">
          <div
            v-for="item in messageStore.detailMessageList"
            :key="item.id"
            class="message-center-drawer-col"
            :class="{ 'message-center-drawer-col-read': !!item.readStatus }"
            @click="handleClickMessage(item)"
          >
            <div class="message-center-drawer-last-icon">
              <LKAvater
                v-if="!item.fromUserAvatar || item.fromUserAvatar.includes('/')"
                :size="32"
                :teamInfo="{
                  avatar: item.fromUserAvatar,
                  username: item.fromUserName
                }"
              />
              <i
                v-else
                class="iconfont message-icon"
                :class="`link-${item.fromUserAvatar || item.messageType}`"
              />
            </div>
            <div class="message-center-drawer-text-body">
              <div class="message-center-drawer-header">
                <div class="message-center-drawer-title">
                  <span class="message-center-drawer-title-text">
                    {{ item.templateTitle }}
                  </span>
                  <span
                    :class="[
                      'message-center-drawer-title-time',
                      item.readStatus ? 'is-read' : ''
                    ]"
                  >
                    {{
                      dayjs(new Date(item.createTime)).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    }}
                  </span>
                </div>
                <i
                  v-if="!item.readStatus"
                  class="iconfont link-read readIcon"
                  @click.stop="handleSetReadStatus(item)"
                />
              </div>

              <div class="message-center-drawer-text-body">
                <ReText
                  :key="item.templateContent"
                  lineClamp="2"
                  :tippyProps="{ delay: 50000 }"
                >
                  <div
                    class="message-center-drawer-tip"
                    v-html="item.templateContent"
                  />
                </ReText>
                <i
                  v-if="!item.readStatus"
                  class="iconfont link-arrow-right next-icon"
                />
              </div>

              <div class="message-center-drawer-footer">
                <div class="message-center-drawer-avatar">
                  <LKAvater
                    :size="18"
                    shape="square"
                    :teamInfo="{
                      avatar: item.toTeamAvatar,
                      username: item.toTeamName
                    }"
                  />
                  <span class="message-center-drawer-text">
                    {{ item.toTeamName }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-show="messageStore.detailLoading"
          class="drawer-message-small-tip"
          :style="{
            paddingTop:
              messageStore.detailMessageList.length === 0 ? '10px' : '0'
          }"
        >
          {{ t("trade_common_loadingText") }}...
        </div>

        <div
          v-show="!messageStore.detailLoading"
          class="drawer-message-small-tip"
        >
          {{ t("trade_message_no_message") }}
        </div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import LKAvater from "@/components/lkAvatar/index";
import ReText from "@/components/ReText";
import { useMessageCenterStoreHook } from "@/store/modules/messageCenter";
import { useRouter } from "vue-router";
import { storageLocal } from "@pureadmin/utils";
import { switchTeam } from "@/utils/auth";

const { t } = useI18n();
const nextDrawer = ref<boolean>(false);
const messageStore = useMessageCenterStoreHook();
const messageType = ref<string>("");
const readStatus = ref<number>(undefined);
const router = useRouter();
const userInfo: any = storageLocal().getItem("user-info");

const unReadCount = computed(() => {
  return messageStore.detailMessageList.filter(item => !item.readStatus).length;
});

const infiniteScrollDisabled = computed(() => {
  return !messageStore.detailPagination.hasMore || messageStore.detailLoading;
});

const open = async (
  messageTypeStr: string,
  readStatusParam: number | undefined
): Promise<void> => {
  nextDrawer.value = true;
  messageType.value = messageTypeStr;
  readStatus.value = readStatusParam;
  messageStore.resetDetailPagination();
  await messageStore.fetchMessageDetail(messageTypeStr, readStatusParam);
};

const close = (): void => {
  nextDrawer.value = false;
};

const emit = defineEmits<{
  (e: "handleUpdate", messageType: string): void;
  (e: "handleGotoMessage"): void;
}>();

const handleSetReadStatus = async (
  messageRow: any,
  showTips: boolean = true
): Promise<void> => {
  const success = await messageStore.markMessageAsRead(messageRow.id);
  if (success) {
    if (showTips) {
      ElMessage({
        message: t("trade_common_updateSuccess"),
        type: "success"
      });
    }
    // emit("handleUpdate", messageType.value); // 会重新请求，需要本地计算未读数量，改成store中更新数据了
  }
};

const handleSetAllRead = async (): Promise<void> => {
  const success = await messageStore.markAllMessagesAsRead(messageType.value);
  if (success) {
    ElMessage({
      message: t("trade_common_updateSuccess"),
      type: "success"
    });
    // emit("handleUpdate", messageType.value);
  }
};

const loadMessageList = (): void => {
  if (messageStore.detailPagination.hasMore) {
    messageStore.fetchMessageDetail(messageType.value, readStatus.value, true);
  }
};

const handleClickMessage = (item: any): void => {
  handleSetReadStatus(item, false);
  if (item.link) {
    const currentUrl = new URL(window.location.href);
    const targetUrl = new URL(item.link);

    const goto = (url: string) => {
      const urlObj = new URL(url);
      const teamId = urlObj.searchParams.get("toTeamId");
      if (teamId && userInfo?.latestLoginTeamId !== teamId) {
        switchTeam(teamId, true, urlObj.pathname + urlObj.search + urlObj.hash);
      } else {
        router.push(urlObj.pathname + urlObj.search + urlObj.hash);
      }
    };

    if (
      currentUrl.hostname === "localhost" || // 本地开发环境
      currentUrl.hostname === targetUrl.hostname
    ) {
      goto(item.link);
    } else {
      window.open(item.link, "_blank");
    }
    emit("handleGotoMessage");
  }
};

defineExpose({
  open,
  close
});
</script>
<style lang="scss" scoped>
@use "../index.scss";

.drawer-message-list-body {
  height: 100%;
  overflow: auto;
  background: #f2f2f2;

  .drawer-message-list-row {
    background: #f2f2f2 !important;

    .message-center-drawer-col {
      display: flex;
      padding: 18px 20px 14px;
      margin: 12px 14px;
      cursor: pointer;
      background: #fff;
      border-radius: 6px;

      &.message-center-drawer-col-read {
        .message-center-drawer-title {
          color: #a5a5a5 !important;
        }

        .message-center-drawer-text-body {
          .message-center-drawer-tip {
            color: #a5a5a5 !important;
            word-break: break-all;

            * {
              color: #a5a5a5 !important;
            }
          }
        }
      }

      .message-center-drawer-last-icon {
        width: 42px;

        .message-icon {
          display: inline-block;
          width: 32px;
          height: 32px;
          line-height: 32px;
          color: #fff;
          text-align: center;
          background: #3db0e1;
          border-radius: 50%;

          &.link-news-inform {
            background: #3be8ab;
          }
        }
      }

      .message-center-drawer-text-body {
        flex: 1;
        min-width: 0;
        word-break: break-all;

        .message-center-drawer-header {
          position: relative;
          display: flex;
          align-items: center;
          min-width: 0;
          margin-bottom: 7px;

          .message-center-drawer-title {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: space-between;
            min-width: 0;
            font-size: 14px;
            line-height: 20px;
            color: #595959;
            text-align: left;

            .message-center-drawer-title-text {
              flex: 1;
              min-width: 0;
              margin-right: 8px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .message-center-drawer-title-time {
              flex-shrink: 0;
              width: 116px;
              overflow: hidden;
              font-size: 12px;
              color: #8c8c8c;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .readIcon {
            position: absolute;
            top: 0;
            right: 0;
            flex-shrink: 0;
            font-size: 12px;
            color: #808080;
            cursor: pointer;
            visibility: hidden;

            &:hover {
              color: #0070d2;
            }
          }
        }

        .message-center-drawer-text-body {
          display: flex;
          align-items: center;
          justify-content: space-between;
          min-width: 0;
          margin-bottom: 10px;

          .message-center-drawer-tip {
            flex: 1;
            min-width: 0;
            overflow: hidden;
            font-size: 14px;
            line-height: 20px;
            color: #262626;
            text-align: left;
            word-break: break-all;
          }

          .next-icon {
            font-size: 12px;
            color: #808080;
          }
        }

        .message-center-drawer-footer {
          display: flex;
          align-items: center;
          min-width: 0;
          word-break: break-all;

          .message-center-drawer-avatar {
            display: inline-flex;
            flex: 1;
            align-items: center;
            min-width: 0;

            .message-center-drawer-text {
              min-width: 0;
              margin-left: 4px;
              overflow: hidden;
              font-size: 12px;
              color: #a5a5a5;
              text-align: left;
              text-overflow: ellipsis;
              word-break: break-all;
              white-space: nowrap;
            }
          }
        }
      }

      &:hover {
        .message-center-drawer-header {
          .message-center-drawer-title {
            .message-center-drawer-title-time:not(.is-read) {
              width: 90px;
              margin-right: 26px;
            }
          }

          .readIcon {
            visibility: visible;
          }
        }
      }
    }
  }
}

.drawer-message-small-tip {
  padding-bottom: 10px;
  font-size: 12px;
  // height: 30px;
  line-height: 30px;
  color: #8c8c8c;
  text-align: center;
}
</style>
<style lang="scss">
.message-center-child-drawer-modal {
  left: 474px !important;
  // background: rgb(58 58 58 / 45%);
  background: transparent;
}

.message-center-child-drawer {
  .el-drawer__header {
    height: 54px;
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #f2f2f2;

    .drawer-header-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 17px 0 20px;

      .drawer-header-left {
        flex: 1;
        font-size: 18px;
        font-weight: bolder;
        line-height: 25px;
        color: #262626;
        text-align: left;
      }

      .drawer-header-right {
        display: flex;
        gap: 20px;
        justify-content: space-between;
        justify-content: flex-end;
        width: 50px;

        .icon {
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #0070d2;
          }
        }
      }
    }
  }
}
</style>
