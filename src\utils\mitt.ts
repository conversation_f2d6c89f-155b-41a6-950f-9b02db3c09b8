import type { Emitter } from "mitt";
import mitt from "mitt";

/** 全局公共事件需要在此处添加类型 */
type Events = {
  openPanel: string;
  tagViewsChange: string;
  tagViewsShowModel: string;
  logoChange: boolean;
  changLayoutRoute: string;
  handleSccsOrderMilestoneChange: Object;
  handleCheckMilestoneCollaboratiors: string;
  changeLanguage: string;
  updateUserInfo: string;
  updateUserInfoAvatar: string;
  handleUpdateOrderInfo: string;
  handleSccsOrderViewMainForm: string;
  handleCancelMilestoneChecked: string;
  handleOrderViewUndergoTableConfig: any;
  handleOrderViewUndergoTableSort: any;
  handleOrderViewInfo: any;
  handleOrderViewUndergoTableFilter: any;
  handleUpdateTableConfig: any;
  handleUpdateFieldFixed: any;
  handleUpdateTableFilters: any;
  handleReloadOrderPageData: any;
  widgetUploadLoading: any;
  handleUpdateTableData: any;
  handleSubTableColumnChange: any;
  handleSwitchDetailLoading: any;
  handleWorkerPostMessage: any;
  handleFieldVisible: any;
  updateUnReadCount: any;
  handleWorkOrderEmailCoopCount: any;
  viewSubTableInEmailCoop: any; // 邮件协作查看子表功能，暂时解决方案
};

export const emitter: Emitter<Events> = mitt<Events>();
