# 平台本地运行端口号
VITE_PORT = 8080

# 开发环境读取配置文件路径
VITE_PUBLIC_PATH = /

# 开发环境路由历史模式（Hash模式传"hash"、HTML5模式传"h5"、Hash模式带base参数传"hash,base参数"、HTML5模式带base参数传"h5,base参数"）
VITE_ROUTER_HISTORY = "h5"

VITE_PROXY=[["/api","https://test-trade-api.linkincrease.com.cn"]]

# 路径
VITE_BASE_URL=https://dev-trade-api.linkincrease.com.cn/

# 前缀
VITE_API_ADMIN_PREFIX=admin-api

VITE_API_TRADE_PREFIX=trade-api

VITE_DROP_CONSOLE=false

VITE_DROP_DEBUGGER=false
