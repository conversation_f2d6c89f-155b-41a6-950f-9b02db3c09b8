{"name": "linkincrease-trade-pro", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "concurrently": "pnpm dev", "api-service": "tsx watch ./server/api-server.js", "serve": "concurrently \"pnpm run concurrently\" \"pnpm run api-service\"", "deploy": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build --mode development", "deploy:prod": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build --mode production", "build:staging": "rimraf dist && vite build --mode staging", "build": "npm run deploy && pm2 startOrRestart ecosystem.config.cjs", "build:prod": "npm run deploy:prod && pm2 startOrRestart ecosystem.config.cjs", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "keywords": ["pure-admin-thin", "vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "homepage": "https://github.com/pure-admin/pure-admin-thin/tree/i18n", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/pure-admin-thin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@formulajs/formulajs": "^4.4.9", "@microsoft/fetch-event-source": "^2.0.1", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.2.0", "@pureadmin/utils": "^2.4.8", "@types/express": "^5.0.3", "@types/ua-parser-js": "^0.7.39", "@visactor/vtable": "^1.17.2", "@visactor/vtable-search": "^1.17.2", "@visactor/vue-vtable": "^1.17.2", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "@vueuse/core": "^10.11.1", "@vueuse/math": "^11.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "acorn": "^8.14.0", "acorn-walk": "^8.3.4", "animate.css": "^4.1.1", "axios": "^1.7.4", "bignumber.js": "^9.1.2", "body-parser": "^2.2.0", "concurrently": "^9.2.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dayjs": "^1.11.12", "defu": "^6.1.4", "element-plus": "^2.9.7", "express": "^4.21.2", "husky": "^9.1.6", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "luckyexcel": "^1.0.1", "mammoth": "^1.9.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pdfjs-dist": "^2.16.105", "pinia": "^2.2.2", "pinyin-pro": "^3.24.2", "pm2": "^6.0.8", "qs": "^6.13.0", "responsive-storage": "^2.2.0", "signature_pad": "^5.0.4", "sortablejs": "^1.15.2", "ua-parser-js": "^1.0.39", "vue": "^3.5.13", "vue-demi": "0.14.6", "vue-i18n": "^11.1.2", "vue-router": "^4.4.3", "vue-tippy": "^6.4.4", "vue-types": "^5.1.3", "vuedraggable": "^4.1.0", "vxe-table": "4.7.87", "xgplayer": "^3.0.21", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@commitlint/cli": "^19.4.0", "@commitlint/config-conventional": "^19.2.2", "@commitlint/types": "^19.0.3", "@eslint/js": "^9.9.0", "@faker-js/faker": "^8.4.1", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^4.0.0", "@pureadmin/theme": "^3.2.0", "@types/gradient-string": "^1.1.6", "@types/js-cookie": "^3.0.6", "@types/node": "^20.17.30", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-vue": "^5.1.2", "@vitejs/plugin-vue-jsx": "^4.0.1", "autoprefixer": "^10.4.20", "boxen": "^7.1.1", "cssnano": "^7.0.5", "eslint": "^9.9.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "gradient-string": "^2.0.2", "lint-staged": "^15.2.9", "postcss": "^8.4.41", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "rimraf": "^5.0.10", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "stylelint": "^16.8.2", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.2", "svgo": "^3.3.2", "tailwindcss": "^3.4.10", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^5.4.1", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-checker": "^0.7.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-css": "^1.0.4", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-inspector": "^5.1.3", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.0.29"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"are-we-there-yet": "*", "sourcemap-codec": "*", "domexception": "*", "w3c-hr-time": "*", "inflight": "*", "npmlog": "*", "rimraf": "*", "stable": "*", "gauge": "*", "abab": "*", "glob": "*"}, "peerDependencyRules": {"allowedVersions": {"eslint": "9"}}, "onlyBuiltDependencies": ["core-js", "esbuild"]}}