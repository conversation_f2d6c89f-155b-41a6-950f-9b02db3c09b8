<template>
  <el-input
    v-if="widget.type === 'DrInput'"
    v-model="bindValue"
    class="width140"
    clearable
    @change="handleChangeWidgetValue"
  />
  <el-date-picker
    v-if="widget.type === 'DrDatePicker'"
    v-model="bindValue"
    :type="widgetDateShowType"
    class="width180"
    :teleported="teleported"
    format="YYYY/MM/DD HH:mm:ss"
    value-format="YYYY/MM/DD HH:mm:ss"
    @change="handleChangeWidgetValue"
  />
  <el-input-number
    v-if="widget.type === 'DrInputNumber'"
    v-model="bindValue"
    class="width140"
    controls-position="right"
    clearable
    @change="handleChangeWidgetValue"
  />
  <el-input
    v-if="widget.type === 'DrTextarea'"
    v-model="bindValue"
    :rows="1"
    resize="none"
    class="width140"
    type="textarea"
    clearable
    @change="handleChangeWidgetValue"
  />
  <el-rate
    v-if="widget.type === 'DrRate'"
    v-model="bindValue"
    :max="widgetOptions.starCount || 5"
    @change="handleChangeWidgetValue"
  />
  <el-input-number
    v-if="widget.type === 'DrPercentage'"
    v-model="bindValue"
    :min="0"
    :max="100"
    clearable
    :controls="false"
    @change="handleChangeWidgetValue"
  >
    <template #suffix>
      <span>%</span>
    </template>
  </el-input-number>
  <el-select-v2
    v-if="widget.type === 'DrRadio'"
    v-model="bindValue"
    class="width140 mr10"
    filterable
    :options="widgetOptions"
    clearable
    :multiple="multiple"
    collapse-tags
    :teleported="teleported"
    collapse-tags-tooltip
    :max-collapse-tags="1"
    @change="handleChangeWidgetValue"
  />
  <el-select-v2
    v-if="widget.type === 'DrCheckbox'"
    v-model="bindValue"
    class="width140 mr10"
    filterable
    :options="widgetOptions"
    clearable
    :teleported="teleported"
    :multiple="multiple"
    collapse-tags
    collapse-tags-tooltip
    :max-collapse-tags="1"
    @change="handleChangeWidgetValue"
  />
  <el-input
    v-if="widget.type === 'DrAddress'"
    v-model="bindValue"
    class="width140"
    clearable
    @change="handleChangeWidgetValue"
  />
  <el-input
    v-if="widget.type === 'DrLocation'"
    v-model="bindValue"
    class="width140"
    clearable
    @change="handleChangeWidgetValue"
  />
  <el-select-v2
    v-if="
      ['DrSCCSMemberSingleSelect', 'DrSCCSMemberMultipleSelect'].includes(
        widget.type
      )
    "
    v-model="bindValue"
    filterable
    class="width260 mr10"
    :teleported="teleported"
    :multiple="multiple"
    :options="widgetControlData?.sccsMemberList || []"
    clearable
    :props="{ label: 'username', value: 'userId' }"
    @change="handleChangeWidgetValue"
  >
    <template #default="{ item }">
      <span v-if="!item.activate" class="tag-register-tip">
        ({{ t("trade_common_unregistered") }})
      </span>
      <LkAvatar
        :teamInfo="{
          avatar: item.avatar,
          username: item.username
        }"
        :size="18"
      />
      {{ item.username }}({{ item.account }})
    </template>
    <template #label="{ value }">
      <span
        v-if="!getWidgetUserValue(value)?.activate"
        class="tag-register-tip"
      >
        ({{ t("trade_common_unregistered") }})
      </span>
      <LkAvatar
        :teamInfo="{
          avatar: getWidgetUserValue(value)?.avatar,
          username: getWidgetUserValue(value)?.username
        }"
        :size="18"
      />
      {{ getWidgetUserValue(value)?.username }}
    </template>
  </el-select-v2>
  <el-select-v2
    v-if="
      [
        'DrSCCSGroupMemberSingleSelect',
        'DrSCCSGroupMemberMultipleSelect'
      ].includes(widget.type)
    "
    v-model="bindValue"
    filterable
    class="width260 mr10"
    :teleported="teleported"
    :multiple="multiple"
    :options="widgetControlData?.sccsCoopTeamList || []"
    clearable
    :props="{ label: 'teamName', value: 'teamId' }"
    @change="handleChangeWidgetValue"
  >
    <template #default="{ item }">
      <LkAvatar
        class="colorOrange"
        shape="square"
        :teamInfo="{
          avatar: item.teamAvatar,
          username: item.teamName
        }"
        :size="18"
      />
      {{ item.teamName }}({{ item.teamShortName }})
    </template>
    <template #label="{ value }">
      <LkAvatar
        class="colorOrange"
        shape="square"
        :teamInfo="{
          avatar: getWidgetValue(value).teamAvatar,
          username: getWidgetValue(value).teamName
        }"
        :size="18"
      />
      {{ getWidgetValue(value).teamName }}
    </template>
  </el-select-v2>
</template>
<script lang="ts" setup>
import { nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { storageSession } from "@pureadmin/utils";
import LkAvatar from "@/components/lkAvatar/index";

const props = defineProps({
  widget: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetValue: {
    type: Object as PropType<any>,
    default: () => {}
  },
  multiple: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  widgetOptions: {
    type: Array as PropType<any>,
    default: () => []
  },
  widgetDateShowType: {
    type: String as PropType<string>,
    default: "datetime"
  },
  teleported: {
    type: Boolean as PropType<boolean>,
    default: false
  }
});

const { t } = useI18n();
const bindValue = ref<any>("");
const widgetControlData = ref<any>({});

const emit = defineEmits<{
  (e: "handleChange", col: any, data: any): void;
  (e: "handleWidgetValue", value: string, label: string): void;
}>();

const handleChangeWidgetValue = () => {
  // 在单选，多选控件的情况下获取文本内容
  let label = "";
  if (
    props.widgetOptions &&
    props.widgetOptions.length > 0 &&
    ["DrRadio", "DrCheckbox"].includes(props.widget.type)
  ) {
    if (bindValue.value instanceof Array) {
      bindValue.value.forEach(widgetValue => {
        label += `,${props.widgetOptions.find(widget => widget.value === widgetValue).label}`;
      });
    } else {
      label += `,${props.widgetOptions.find(widget => widget.value === bindValue.value).label}`;
    }
    label = label.substr(1);
  }
  if (
    ["DrSCCSMemberSingleSelect", "DrSCCSMemberMultipleSelect"].includes(
      props.widget.type
    ) &&
    widgetControlData.value.sccsMemberList &&
    widgetControlData.value.sccsMemberList.length > 0
  ) {
    if (bindValue.value instanceof Array) {
      bindValue.value.forEach(widgetValue => {
        label += `,${widgetControlData.value.sccsMemberList.find(widget => widget.userId === widgetValue).username}`;
      });
    } else {
      label += `,${widgetControlData.value.sccsMemberList.find(widget => widget.userId === bindValue.value).username}`;
    }
    label = label.substr(1);
  }
  if (
    [
      "DrSCCSGroupMemberSingleSelect",
      "DrSCCSGroupMemberMultipleSelect"
    ].includes(props.widget.type) &&
    widgetControlData.value.sccsCoopTeamList &&
    widgetControlData.value.sccsCoopTeamList.length > 0
  ) {
    if (bindValue.value instanceof Array) {
      bindValue.value.forEach(widgetValue => {
        label += `,${widgetControlData.value.sccsCoopTeamList.find(widget => widget.teamId === widgetValue).teamName}`;
      });
    } else {
      label += `,${widgetControlData.value.sccsCoopTeamList.find(widget => widget.teamId === bindValue.value).teamName}`;
    }
    label = label.substr(1);
  }
  emit(
    "handleWidgetValue",
    bindValue.value instanceof Array
      ? bindValue.value.join(",")
      : bindValue.value,
    label
  );
};

const getWidgetUserValue = value => {
  const sccsMemberItem = widgetControlData.value.sccsMemberList.find(
    sccsMember => sccsMember.userId === value
  );
  return sccsMemberItem;
};

const getWidgetValue = value => {
  const sccsMemberItem = widgetControlData.value.sccsCoopTeamList.find(
    sccsMember => sccsMember.teamId === value
  );
  return sccsMemberItem;
};

const clearData = () => {
  bindValue.value = null;
};

watch(
  [() => props.widgetValue, () => props.multiple],
  () => {
    nextTick(() => {
      if (props.widgetValue) {
        if (
          [
            "DrRadio",
            "DrCheckbox",
            "DrSCCSMemberSingleSelect",
            "DrSCCSMemberMultipleSelect",
            "DrSCCSGroupMemberSingleSelect",
            "DrSCCSGroupMemberMultipleSelect"
          ].includes(props.widget.type) &&
          props.multiple
        ) {
          bindValue.value = props.widgetValue.split(",");
        } else {
          bindValue.value = props.widgetValue;
        }
      }
      const widgetData: any = storageSession().getItem("widgetData");
      widgetControlData.value = widgetData;
    });
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  clearData
});
</script>
