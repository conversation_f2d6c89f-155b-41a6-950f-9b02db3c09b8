<template>
  <div class="order-detail-region-body">
    <OrderDetailHeader
      widgetTilte="trade_order_basicInfo"
      :relationIconType="relationIconType"
      :widgetButtons="widgetButtons"
      :milestoneData="milestoneRow"
      @handleTrigger="handleTrigger"
    />
    <slot name="orderRelations" />
    <!-- 取消原因显示 -->
    <div
      v-if="orderCancelData.reasonList || orderCancelData.cancelRemark"
      class="order-cancel-info"
    >
      <div class="cancel-title">取消原因：</div>
      <div class="cancel-content">
        <div v-if="orderCancelData.reasonList" class="reason-list">
          <span
            v-for="(reason, index) in reasonListArray"
            :key="index"
            class="reason-tag"
          >
            {{ reason }}
          </span>
        </div>
        <div v-if="orderCancelData.cancelRemark" class="cancel-remark">
          {{ orderCancelData.cancelRemark }}
        </div>
      </div>
    </div>
    <OrderDetailDescWidget
      :widgetForm="mainFromWidgetJsonLists"
      :widgetData="getMainFormWidgetData"
      :formId="milestoneRow.id"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import OrderDetailHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDetailHeader.vue";
import { cloneDeep } from "lodash-es";
import { calculateVisibleFormList } from "@/utils/formVisibleExpression";
import { formatNumberValueOf, formatThousandNumber } from "@/utils/common";

const props = defineProps({
  taskMode: {
    type: String as PropType<string>,
    default: () => ""
  },
  widgetForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneRow: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderCancelData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  orderStateFlag: {
    type: String as PropType<string>,
    default: ""
  },
  relationIconType: {
    type: String as PropType<string>,
    default: () => ""
  },
  visibleFormData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const widgetButtons = ref<any>([]);
const mainFromWidgetJsonLists = ref<any[]>([]);

const getMainFormWidgetData = computed(() => {
  return props.widgetData.map(widgetData => {
    const widgetItem = props.widgetForm.find(
      widgetFormItem => widgetFormItem._fc_id === widgetData.widgetId
    );
    if (widgetItem && widgetItem.type === "DrTableForm") {
      const tableColumns = widgetItem.children
        .slice(1)
        .map(tableWidget => tableWidget.children[0]);
      let tableWidgetData = widgetData.childrenList.map(trWidgetData => {
        return trWidgetData.map(tdWidgetData => {
          if (
            tdWidgetData.widgetType === "INPUT_NUMBER" ||
            tdWidgetData.widgetType === "PERCENT"
          ) {
            const tdColumn = tableColumns.find(
              column => column._fc_id === tdWidgetData.widgetId
            );
            const widgetObjectValue = tdColumn.props.useThousandSeparator
              ? formatThousandNumber(tdWidgetData.obj)
              : formatNumberValueOf(tdWidgetData.obj);
            return Object.assign(tdWidgetData, {
              label: widgetObjectValue
            });
          } else {
            return tdWidgetData;
          }
        });
      });
      return Object.assign(widgetData, { childrenList: tableWidgetData });
    } else {
      return widgetData;
    }
  });
});

const getOrderState = (orderStateList): boolean => {
  return orderStateList.includes(props.orderStateFlag);
};

// 处理取消原因列表
const reasonListArray = computed(() => {
  if (!props.orderCancelData.reasonList) return [];
  return props.orderCancelData.reasonList
    .split(",")
    .map(reason => reason.trim());
});

watch(
  () => props.orderStateFlag,
  () => {
    if (!props.orderStateFlag) return;

    widgetButtons.value = [
      {
        type: "editOrder",
        icon: "link-edit",
        label: "trade_common_edit",
        btnPermission: {
          coopTeam: ["edit_order"],
          mainTeam: ["edit_order"],
          customPermission: getOrderState(["ON_GOING"])
        }
      },
      // {
      //   type: "repeat",
      //   icon: "link-repeat",
      //   label: "trade_common_repeat_order",
      //   btnPermission: {
      //     coopTeam: ["clone_order"],
      //     mainTeam: ["clone_order"],
      //     customPermission: true
      //   }
      // },
      // {
      //   type: "association",
      //   icon: "link-order-association",
      //   label: "trade_order_association",
      //   btnPermission: {
      //     coopTeam: ["relate_order"],
      //     mainTeam: ["relate_order"],
      //     customPermission: true
      //   }
      // },
      // {
      //   type: "sccsAssociation",
      //   icon: "link-sccs-association",
      //   label: "trade_order_sccsassociation",
      //   btnPermission: {
      //     coopTeam: ["relate_sccs_order"],
      //     mainTeam: ["relate_sccs_order"],
      //     customPermission: true
      //   }
      // },
      {
        type: "orderCompletion",
        icon: "link-manual-completion",
        label: "trade_order_completion",
        btnPermission: {
          coopTeam: ["update_order_status"],
          mainTeam: ["update_order_status"],
          customPermission: getOrderState(["ON_GOING"])
        }
      },
      {
        type: "orderCancel",
        icon: "link-cancel",
        label: "trade_order_cancel",
        btnPermission: {
          coopTeam: ["update_order_status"],
          mainTeam: ["update_order_status"],
          customPermission: getOrderState(["ON_GOING"])
        }
      },
      {
        type: "orderRestart",
        icon: "link-restart",
        label: "trade_order_restart",
        btnPermission: {
          coopTeam: ["update_order_status"],
          mainTeam: ["update_order_status"],
          customPermission: getOrderState(["CANCEL", "COMPLETE"])
        }
      },
      {
        icon: "link-more-operation",
        label: "trade_common_more",
        dropdownList: [
          {
            type: "orderHistory",
            icon: "link-history",
            label: "trade_common_history",
            btnPermission: {
              coopTeam: ["All"],
              mainTeam: ["All"],
              customPermission: true
            }
          },
          {
            type: "orderTrackRecord",
            icon: "link-track-record",
            label: "trade_order_track",
            btnPermission: {
              coopTeam: ["view_track_record"],
              mainTeam: ["view_track_record"],
              customPermission: true
            }
          },
          {
            type: "deleteOrder",
            icon: "link-ashbin",
            label: "trade_order_delete",
            class: "delete_order_class",
            btnPermission: {
              coopTeam: ["delete_order"],
              mainTeam: ["delete_order"],
              customPermission: true
            }
          }
        ]
      }
    ];
  },
  {
    immediate: true
  }
);

watch(
  [() => props.visibleFormData, () => props.widgetForm],
  () => {
    if (props.widgetForm.length > 0) {
      const mainFormWidgetJsonList = calculateVisibleFormList(
        cloneDeep(props.widgetForm),
        props.visibleFormData
      );
      mainFromWidgetJsonLists.value = mainFormWidgetJsonList;
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const handleTrigger = (item: any) => {
  emit("handleTrigger", item);
};

const emit = defineEmits<{
  (e: "handleTrigger", data): void;
}>();
</script>
<style lang="scss" scoped>
@use "../style/index.scss";

.order-cancel-info {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  background-color: #f0f1f7;
  border-radius: 6px 6px 0 0;

  .cancel-title {
    flex: 0 0 auto;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 24px;
    color: #797979;
  }

  .cancel-content {
    display: flex;
    flex: 1 1 0%;
    flex-direction: column;

    .reason-list {
      display: flex;
      flex-wrap: wrap;
      word-break: break-all;

      .reason-tag {
        box-sizing: border-box;
        display: inline-block;
        max-width: 100%;
        padding: 3px 6px;
        margin: 0 8px 8px 0;
        font-size: 12px;
        color: #262626;
        word-wrap: break-word;
        white-space: normal;
        background-color: #e5e5e5;
        border-radius: 4px;
      }
    }

    .cancel-remark {
      font-size: 14px;
      line-height: 1.5;
      color: #262626;
      word-break: break-all;
      border-radius: 4px;
    }
  }
}
</style>
<style lang="scss">
.order-region-popper {
  .el-dropdown-menu__item {
    height: 36px;
    font-size: 14px;
    line-height: 36px;

    .iconfont {
      font-size: 14px;
    }

    &.disabled {
      color: #999;
      cursor: not-allowed;
      background-color: #f0f0f0;

      &:hover {
        color: #999 !important;
      }
    }

    &.delete_order_class {
      color: #cf1421;

      &:hover {
        color: #cf1421 !important;
      }
    }

    &.success {
      color: #39cb51;

      &:hover {
        color: #39cb51 !important;
      }
    }
  }
}
</style>
