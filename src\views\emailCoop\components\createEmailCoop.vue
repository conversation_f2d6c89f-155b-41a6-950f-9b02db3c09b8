<template>
  <LkDialog
    ref="CreateEmailCollaborateRef"
    class="lk-maximum-dialog create-email-collaborate-dialog"
    :title="t('trade_email_create_collaboration')"
    append-to-body
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <template #header="{ titleClass }">
      <div :class="titleClass">
        <div class="header-left">
          {{ t("trade_email_create_collaboration") }}
        </div>
        <div
          class="work-order-header-small-button"
          :class="{ active: basicInfoDrawer }"
        >
          <i class="iconfont link-basic-info" @click="handleOpenBasicInfo" />
        </div>
      </div>
    </template>

    <template #default>
      <div class="create-email-collaborate-container">
        <!-- 左侧表单区域 -->
        <div class="create-email-collaborate-left">
          <el-scrollbar>
            <el-form
              ref="CreateEmailRef"
              :model="CreateEmailForm"
              label-position="top"
              :rules="rules"
            >
              <div class="create-email-collaborate-form">
                <!-- 表单配置列表 -->
                <div class="create-email-collaborate-form-list">
                  <div
                    v-for="item in formList"
                    :key="item.id"
                    class="create-email-collaborate-form-li"
                  >
                    <ReText
                      type="info"
                      :tippyProps="{ delay: 5000 }"
                      class="create-email-collaborate-form-li-left"
                    >
                      {{ item.name }}
                    </ReText>
                    <div class="create-email-collaborate-form-li-right">
                      <span
                        v-if="item.editable"
                        class="create-email-collaborate-form-tag"
                      >
                        {{ t("trade_email_editable") }}
                      </span>
                      <el-switch
                        v-model="workOrderCoopEmailObject[item.id].formProperty"
                        :width="28"
                        size="small"
                        active-value="HIDDEN"
                        inactive-value="DISPLAY"
                        :active-text="t('trade_common_hiddenText')"
                        @change="handleWorkOrderEmailChange(item.id)"
                      />
                    </div>
                  </div>
                </div>

                <!-- 收件邮箱 -->
                <el-form-item prop="emailList" class="custom-form-item">
                  <template #label>
                    <span class="custom-form-label-body">
                      <span class="custom-form-label-title">
                        {{ t("trade_email_receiveLettersEmail") }}
                      </span>
                      <el-tooltip
                        :content="t('trade_email_emailListTip')"
                        placement="top"
                        :show-after="500"
                        popper-class="widget-popper-label-class"
                      >
                        <i class="iconfont link-explain font12" />
                      </el-tooltip>
                    </span>
                  </template>

                  <el-select
                    v-model="CreateEmailForm.emailList"
                    multiple
                    filterable
                    clearable
                    allow-create
                    default-first-option
                    :reserve-keyword="false"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in emailOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>

                <!-- 邮件主题 -->
                <el-form-item
                  :label="t('trade_email_emailsubject')"
                  prop="subject"
                >
                  <el-input v-model="CreateEmailForm.subject" clearable />
                </el-form-item>

                <!-- 附件上传 -->
                <el-form-item :label="t('trade_email_files')">
                  <div class="create-email-collaborate-file-form">
                    <div class="create-email-collaborate-form-tip">
                      {{ t("trade_email_collaborateFormtip") }}
                    </div>
                    <el-select-v2
                      v-model="emailUploadFile"
                      class="create-email-collaborate-form-select"
                      :options="sccsFileList"
                      multiple
                      @change="handleUpdateEmailFiles"
                    />
                    <el-upload
                      ref="uploadRef"
                      v-model:file-list="fileList"
                      class="avatar-uploader"
                      :action="uploadRouter"
                      :data="uploadParams"
                      :multiple="true"
                      drag
                      :on-success="handleUploadSuccess"
                      :on-remove="handleRemove"
                    >
                      <template #trigger>
                        <div class="upload-tip-body">
                          <i class="iconfont link-upload upload-tip-icon" />
                          <div class="upload-tip-text">
                            {{ t("trade_component_uploadtip") }}
                          </div>
                        </div>
                      </template>
                    </el-upload>
                  </div>
                </el-form-item>

                <!-- 备注 -->
                <el-form-item :label="t('trade_common_remark')">
                  <LKEditor @handleChange="handleUpdateRemark" />
                </el-form-item>
              </div>
            </el-form>
          </el-scrollbar>
        </div>

        <!-- 右侧预览区域 -->
        <div class="create-email-collaborate-right">
          <el-scrollbar>
            <LkTrendsAggregationForm
              ref="TrendsAggregationFormRef"
              :sccsId="route.query.sccsId"
              :workOrderId="commitWorkOrderId"
              :formList="formList"
              :hiddenFormIdList="hiddenFormIdList"
              :formWidgetData="
                formList.flatMap(item =>
                  item.editable ? [] : item.widgetData || []
                )
              "
              :trendsFormFlag="true"
              :operationalFactorData="operationalFactorData"
              :workOrderAllowOperation="
                formList.filter(form => form.editable).map(form => form.id)
              "
              :defaultValueActuatorFlag="false"
            />
          </el-scrollbar>
          <LKAnchor :anchorList="visibleAnchorList" />
        </div>

        <!-- 遮罩层 -->
        <div
          v-if="basicInfoDrawer"
          class="overlay"
          @click="basicInfoDrawer = false"
        />
      </div>

      <!-- 基本信息弹窗 -->
      <Transition
        leave-active-class="animate__animated animate__fadeOut animate__faster"
        enter-active-class="animate__animated animate__fadeIn animate__faster"
      >
        <div v-if="basicInfoDrawer" class="work-order-tabs-container">
          <el-scrollbar class="horizontal-scrollbar-only" max-height="400px">
            <OrderDetailDescWidget
              :widgetForm="mainFormWidgetData"
              :widgetData="mainWidgetData"
            />
          </el-scrollbar>
        </div>
      </Transition>
    </template>
  </LkDialog>
</template>

<script lang="ts" setup>
import { reactive, ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import type { UploadProps, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { isEmail } from "@pureadmin/utils";

import LkDialog from "@/components/lkDialog";
import LKAnchor from "@/components/lkAnchor/index";
import LKEditor from "@/components/lkEditor/index";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import { ReText } from "@/components/ReText";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";

import {
  createEmailCollaborate,
  getCoopEmailStructure,
  getFileWidgetData,
  getSccsFileWidgetList
} from "@/api/email";

import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";

const { t } = useI18n();
const route = useRoute();

// 上传配置
const uploadRouter = ref<string>(
  import.meta.env.VITE_BASE_URL +
    import.meta.env.VITE_API_TRADE_PREFIX +
    "/trade/file/upload"
);
const uploadParams = ref<any>({
  ossFileType: "TRADE_SCCS",
  sccsId: route.query.sccsId
});

const CreateEmailCollaborateRef = ref<any | HTMLElement>(null);
const CreateEmailRef = ref<any | HTMLElement>(null);
const TrendsAggregationFormRef = ref<any | HTMLElement>(null);

const formList = ref<any>([]);
const sccsFileList = ref<any>([]);
const basicInfoDrawer = ref<boolean>(false);
const emailUploadFile = ref<any>([]);
const workOrderCoopEmailObject = ref<any>({});
const fileList = ref<any>([]);
const commitWorkOrderId = ref<string>("");
const emailCollapseActive = ref<string[]>([]);
const anchorList = ref<any>([]);
const mainFormWidgetData = ref<any>();
const mainWidgetData = ref<any>([]);
const operationalFactorData = ref<any>({});
const emailOptions = ref<any>([]);

// 表单数据
const CreateEmailForm = ref<any>({
  emailList: [],
  subject: "",
  remark: "",
  remarkText: ""
});

// 添加计算属性来计算隐藏的表单ID列表
const hiddenFormIdList = computed(() => {
  return Object.entries(workOrderCoopEmailObject.value)
    .filter(([id, config]: [string, any]) => config.formProperty === "HIDDEN")
    .map(([id]) => id);
});

// 添加计算属性来过滤显示的锚点列表
const visibleAnchorList = computed(() => {
  return anchorList.value.filter(
    anchor => !hiddenFormIdList.value.includes(anchor.name)
  );
});

const validateEmail = (rule: any, value: any, callback: any) => {
  if (value.length > 0) {
    for (let i = 0, len = value.length; i < len; i++) {
      const email = value[i];
      if (!isEmail(email)) {
        callback(new Error(t("trade_login_truemail")));
        return;
      }
    }
    callback();
  } else {
    callback();
  }
};

const rules = reactive<FormRules<any>>({
  emailList: [
    { required: true, message: t("trade_login_mailText"), trigger: "blur" },
    { validator: validateEmail, trigger: "change" }
  ],
  subject: [
    {
      required: true,
      message: t("trade_email_emailsubjectTip"),
      trigger: "blur"
    }
  ]
});

const emit = defineEmits(["handleCreateSuccess"]);

const handleOpenBasicInfo = (): void => {
  basicInfoDrawer.value = !basicInfoDrawer.value;
};

const loadEmailCache = () => {
  // 弹窗打开的时候判断浏览器是否有收件邮箱缓存数据，如果有则直接赋值给emailOptions
  const emailList = JSON.parse(localStorage.getItem("cachedEmails") || "[]");
  if (emailList.length !== 0) {
    emailOptions.value = emailList.map(email => ({
      value: email,
      label: email
    }));
  }
};

const saveEmailCache = () => {
  // 获取当前收件邮箱列表
  const currentEmails = CreateEmailForm.value.emailList;
  // 从 localStorage 中读取已有的收件邮箱列表
  const cachedEmails = JSON.parse(localStorage.getItem("cachedEmails") || "[]");

  // 将当前收件邮箱列表与缓存邮箱合并，去重，并将最新使用的邮箱放在最前面，最多保留10条
  let updatedEmails = [];
  if (Array.isArray(currentEmails)) {
    // 先将当前邮箱列表放在前面
    updatedEmails = [...currentEmails.reverse()];
    // 再将缓存邮箱中不在当前邮箱列表中的邮箱追加到后面
    cachedEmails.forEach(email => {
      if (!updatedEmails.includes(email)) {
        updatedEmails.push(email);
      }
    });
    // 只保留前10条
    updatedEmails = updatedEmails.slice(0, 10);
  }

  localStorage.setItem("cachedEmails", JSON.stringify(updatedEmails));
  emailOptions.value = updatedEmails.map(email => ({
    value: email,
    label: email
  }));
};

const open = async (
  msId: string,
  workOrderId: string,
  customSccsId?: string
) => {
  const sccsId = (route.query.sccsId as string) || customSccsId;
  commitWorkOrderId.value = workOrderId;

  try {
    const [structureRes, fileListRes] = await Promise.all([
      getCoopEmailStructure({
        sccsId: sccsId,
        milestoneId: msId,
        workOrderId: workOrderId
      }),
      getSccsFileWidgetList({
        sccsId: sccsId,
        milestoneId: msId,
        widgetReqModule: "WORK_ORDER_COOP_EMAIL"
      })
    ]);

    // 处理表单数据
    formList.value = structureRes.data.map(form => {
      const isOrderInfo = form.formType === "MAIN_FORM";
      const widgetData = TransformSubmitDataStructure(
        isOrderInfo ? form.widgetDataForOrderList : form.widgetDataList,
        form.widgetDataSubForOrderMap,
        form.formType === "MAIN_FORM"
          ? form.linkedReferenceForOrderMap
          : form.linkedReferenceMap
      );
      return { ...form, widgetData };
    });

    mainFormWidgetData.value = structureRes.data[0].widgetJsonList;
    mainWidgetData.value = formList.value[0].widgetData;
    sccsFileList.value = fileListRes.data;

    // 初始化工作订单邮件对象和锚点列表
    const obj = {};
    const anchorLists = [];

    structureRes.data.forEach(form => {
      obj[form.id] = {
        editable: form.editable,
        formPubMongoId: form.id,
        formProperty: "DISPLAY"
      };
      anchorLists.push({
        title: form.name,
        ref: `collaspse-${form.id}`,
        name: form.id,
        state: form.editable,
        stateText: form.editable
          ? "trade_email_editable"
          : "trade_email_readonly"
      });
    });

    anchorList.value = anchorLists;
    workOrderCoopEmailObject.value = obj;
    emailCollapseActive.value = structureRes.data.map(dataItem => dataItem.id);
    operationalFactorData.value = getEntireFormData(msId, workOrderId);

    // 加载邮箱缓存
    loadEmailCache();

    // 打开对话框
    CreateEmailCollaborateRef.value.open();
  } catch (error) {
    console.error("Failed to load email cooperation data:", error);
  }
};

const handleUpdateRemark = (remark: string, valueObj: any) => {
  CreateEmailForm.value.remark = remark;
  CreateEmailForm.value.remarkText = valueObj.text;
};

const handleWorkOrderEmailChange = (id: string) => {
  const workConfigList = Object.values(workOrderCoopEmailObject.value).filter(
    (EmailConfig: any) => EmailConfig.editable
  );

  if (workConfigList.length !== 0) {
    const workDisplayList = workConfigList.filter(
      (workConfig: any) => workConfig.formProperty === "DISPLAY"
    );
    if (workDisplayList.length === 0) {
      workOrderCoopEmailObject.value[id].formProperty = "DISPLAY";
      ElMessage.error(t("trade_email_displayTip"));
      return;
    }
  }
};

const handleClose = () => {
  CreateEmailForm.value.emailList = [];
  CreateEmailForm.value.subject = "";
  CreateEmailForm.value.remark = "";
  basicInfoDrawer.value = false;
  fileList.value = [];
  CreateEmailCollaborateRef.value.close();
};

const handleUploadSuccess: UploadProps["onSuccess"] = uploadFile => {
  fileList.value.pop();
  const fileName = uploadFile.data.split("/").slice(-1)[0];
  fileList.value.push({ url: uploadFile.data, name: fileName });
};

const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  fileList.value = uploadFiles;
};

const handleUpdateEmailFiles = async () => {
  try {
    const { code, data } = await getFileWidgetData({
      workOrderId: commitWorkOrderId.value,
      widgetIdList: emailUploadFile.value
    });

    if (code === 0) {
      data.forEach(file => {
        const index = fileList.value.findIndex(
          fileListItem => fileListItem.url === file.url
        );
        if (index === -1) {
          fileList.value.push(file);
        }
      });
    }
  } catch (error) {
    console.error("Failed to update email files:", error);
  }
};

const handleConfirm = async () => {
  const workOrderCoopEmailFormVOList = Object.values(
    workOrderCoopEmailObject.value
  );

  const widgetItemDataList =
    await TrendsAggregationFormRef.value.handleGainWidgetFormData(false);
  const linkedReferenceSourceList =
    TrendsAggregationFormRef.value.linkedRefrenceSourceDataLists;

  // widgetItemDataList 为false 说明表单校验不通过
  if (widgetItemDataList === false) {
    return;
  }

  CreateEmailRef.value.validate(async valid => {
    if (valid) {
      try {
        const { code } = await createEmailCollaborate({
          workOrderId: commitWorkOrderId.value,
          ...CreateEmailForm.value,
          workOrderCoopEmailFormVOList: workOrderCoopEmailFormVOList,
          workOrderCoopEmailAttachmentVOList: fileList.value,
          widgetItemDataList: widgetItemDataList,
          linkedReferenceSaveList: linkedReferenceSourceList
        });

        if (code === 0) {
          ElMessage({
            message: t("trade_common_updateSuccess"),
            type: "success"
          });

          emit("handleCreateSuccess");
          CreateEmailCollaborateRef.value.close();

          // 保存邮箱缓存
          saveEmailCache();
        }
      } catch (error) {
        console.error("Failed to create email collaboration:", error);
      }
    }
  });
};

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
@use "./index.scss";

.work-order-tabs-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3333;
  box-sizing: border-box;
  width: 100%;
  height: 480px;
  padding: 33px 43px;
  background: #fff;
  border-top: 1px solid #e5e5e5;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 8px 13px 0 rgb(0 0 0 / 10%);

  &.nopadding {
    padding: 16px 26px !important;
  }
}
</style>

<style lang="scss">
.create-email-collaborate-dialog {
  .dialog-footer {
    padding: 0 30px;
  }

  .el-dialog__headerbtn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 54px;
  }
}
</style>
