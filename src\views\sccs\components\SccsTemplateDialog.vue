<template>
  <div class="sccs-create-template-body">
    <LkDialog
      ref="LkDialogRef"
      :noFooter="true"
      :width="1200"
      :title="t('trade_template_manage')"
      :append-to-body="true"
      :align-center="false"
      class="sccs-group-setting-dialog"
    >
      <template #default>
        <el-tabs v-model="activeName" class="template-tabs">
          <el-tab-pane :label="t('trade_common_all')" name="all">
            <el-scrollbar max-height="calc(70vh - 140px)" :noresize="true">
              <el-row :gutter="20">
                <el-col v-for="item in templateList" :key="item.id" :span="8">
                  <div class="template-col">
                    <span class="template-icon-body">
                      <i
                        :class="['iconfont', item.icon]"
                        :style="{ color: item.iconColor }"
                      />
                    </span>
                    <span class="template-description-body">
                      <div class="template-description-title">
                        {{ item.bizTemplateName }}
                      </div>
                      <div class="template-description-tip">
                        {{ item.description }}
                      </div>
                    </span>
                    <div class="template-col-hover">
                      <el-button
                        type="primary"
                        color="#0070D2"
                        @click="handleCreateSccs(item)"
                        >{{ t("trade_common_use") }}</el-button
                      >
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-tab-pane>
          <el-tab-pane
            v-for="item in templateIndustryList"
            :key="item.id"
            :label="item.industryName"
            :name="item.id"
          >
            <el-scrollbar max-height="620px" :noresize="true">
              <el-row :gutter="20">
                <el-col
                  v-for="item in templateList.filter(
                    templateLi => templateLi.industryId === item.id
                  )"
                  :key="item.id"
                  :span="8"
                >
                  <div class="template-col">
                    <span class="template-icon-body">
                      <i
                        :class="['iconfont', item.icon]"
                        :style="{ color: item.iconColor }"
                      />
                    </span>
                    <span class="template-description-body">
                      <div class="template-description-title">
                        {{ item.bizTemplateName }}
                      </div>
                      <div class="template-description-tip">
                        {{ item.description }}
                      </div>
                    </span>
                    <div class="template-col-hover">
                      <el-button
                        type="primary"
                        color="#0070D2"
                        @click="handleCreateSccs(item)"
                        >{{ t("trade_common_use") }}</el-button
                      >
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </template>
    </LkDialog>
    <SccsCreateDialog
      ref="SccsCreateDialogRef"
      @updateSccsGroup="updateSccsGroup"
    />
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog";
import { getTemplateIndustryList, getBizTemplateList } from "@/api/sccs";
import SccsCreateDialog from "./SccsCreateDialog.vue";

const { t } = useI18n();
const LkDialogRef = ref<any>(null);
const SccsCreateDialogRef = ref<any>(null);
const activeName = ref("all");
const templateIndustryList = ref<any[]>([]);
const templateList = ref<any[]>([]);

const open = async (): Promise<void> => {
  Promise.all([getTemplateIndustryList(), getBizTemplateList()]).then(res => {
    if (res[0].code === 0 && res[1].code === 0) {
      templateIndustryList.value = res[0].data;
      templateList.value = res[1].data;
      LkDialogRef.value.open();
    }
  });
};

const emit = defineEmits<{
  (e: "createSccsSuccess"): void;
}>();

const updateSccsGroup = (): void => {
  LkDialogRef.value.close();
  emit("createSccsSuccess");
};

const handleCreateSccs = (data: any): void => {
  SccsCreateDialogRef.value.open(data.id);
};

defineExpose({
  open
});
</script>
<style lang="scss">
.sccs-group-setting-dialog {
  .el-tabs {
    .el-tabs__header {
      height: 54px !important;

      .el-tabs__nav-scroll {
        padding: 0 22px !important;
      }
    }

    .el-tabs__content {
      .el-row {
        padding: 0 22px !important;

        .el-col {
          .template-col {
            position: relative;
            display: flex;
            padding: 18px 37px 18px 14px;
            margin: 16px 0;
            cursor: pointer;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 6px 0 rgb(1 6 62 / 11%);

            &:hover {
              .template-col-hover {
                display: flex;
              }
            }

            .template-icon-body {
              display: inline-block;
              width: 92px;
              height: 86px;
              line-height: 86px;
              text-align: center;
              background: #f9f9f9;

              .iconfont {
                font-size: 46px;
              }
            }

            .template-description-body {
              margin: 4px 0 4px 15px;

              .template-description-title {
                margin-bottom: 13px;
                font-size: 16px;
                font-weight: bolder;
                line-height: 20px;
                color: #262626;
              }

              .template-description-tip {
                max-height: 40px;
                overflow: hidden;
                font-size: 14px;
                line-height: 20px;
                color: #797979;
                text-overflow: ellipsis;
              }
            }

            .template-col-hover {
              position: absolute;
              top: 0;
              left: 0;
              display: none;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
              vertical-align: middle;
              background: rgb(68 68 68 / 90%);
              border-radius: 4px;

              .el-button {
                padding: 8px 32px;
              }
            }
          }
        }
      }

      .el-scrollbar__bar.is-horizontal {
        display: none !important;
      }
    }
  }
}
</style>
