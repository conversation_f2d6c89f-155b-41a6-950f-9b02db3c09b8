<template>
  <div class="user-login-container">
    <div v-if="!nextStep" class="user-login-title">
      {{ t("trade_login_userRegistration") }}
    </div>
    <div v-if="!nextStep" class="user-login-tip">
      {{ t("trade_login_workEmail") }}
    </div>
    <div v-if="!nextStep" class="user-login-tip">
      {{ t("trade_login_alreadyHaveAnAccount") }}
      <span class="middle-button" @click="jumpLogin">
        {{ t("trade_common_login") }}
      </span>
    </div>
    <div v-if="nextStep" class="user-login-title">
      {{ t("trade_login_createProfile") }}
    </div>
    <div class="user-login-form-item">
      <CheckMailLogin
        v-if="!nextStep"
        ref="codeRef"
        :mailCodeType="mailCodeType"
      >
        <template #default>
          <el-button
            class="user-login-button"
            type="primary"
            @click="handleSendVerifyCode"
            >{{ t("trade_login_verifyEmail") }}</el-button
          >
        </template>
      </CheckMailLogin>
      <UserRegisterNext
        v-else
        :email="userEmail"
        @handleLoginSuccess="handleLoginSuccess"
      />
    </div>
    <div v-if="!nextStep" class="user-login-form-bottom font14">
      <div class="el-button-custom-row">
        {{ t("trade_login_registerAndAgree") }}
        <span class="el-button-custom-text" @click="handleOpenUserService">
          Linkincrease{{ t("trade_login_userServiceAgreement") }}
        </span>
      </div>
      <div class="el-button-custom-row">
        {{ t("trade_login_read") }}
        <span class="el-button-custom-text">
          Linkincrease{{ t("trade_login_privacyPolicy") }}
        </span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import CheckMailLogin from "./CheckMailLogin.vue";
import { checkMailCode } from "@/api/login";
import { VerifyCodeEmailProp } from "@/api/type";
import UserRegisterNext from "./UserRegisterNext.vue";
import { useRouter, useRoute } from "vue-router";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const mailCodeType = ref<string>("TRADE_USER_REGISTER");
const codeRef = ref<any>(null);
const nextStep = ref<boolean>(false);
const userEmail = ref<string>("");

const handleSendVerifyCode = (): Promise<void> => {
  const formEl: any = codeRef.value.ruleFormRef;
  if (!formEl) return;
  formEl.validate(async valid => {
    if (valid) {
      userEmail.value = codeRef.value.userInfoForm.username;
      const checkMailCodeForm: VerifyCodeEmailProp = {
        email: codeRef.value.userInfoForm.username,
        mailCode: codeRef.value.userInfoForm.mailCode,
        //@ts-ignore
        mailCodeType: mailCodeType.value
      };
      const response = await checkMailCode(checkMailCodeForm);
      if (response.code !== 0) {
        codeRef.value.setResponseMessage(response.msg);
      } else {
        nextStep.value = true;
      }
    }
  });
};

const jumpLogin = (): void => {
  router.push("/login");
};

const handleOpenUserService = (): void => {
  window.open("https://www.linkincrease.com/nd.jsp?id=30&id=30#device=pc");
};

const emit = defineEmits<{
  (e: "handleLoginSuccess", data: any): void;
}>();

const handleLoginSuccess = (data): void => {
  emit("handleLoginSuccess", data);
};

onMounted(() => {
  nextStep.value = !!route.query.nextStep as boolean;
  userEmail.value = route.query.email as string;
});
</script>
<style lang="scss" scoped>
@use "./index.scss";

.user-login-form-item {
  margin-bottom: 87px;
}

.user-login-form-bottom {
  .el-button-custom-row {
    margin-bottom: 10px;
    text-align: center;

    .el-button-custom-text {
      color: #0070d2;
      cursor: pointer;
    }
  }
}
</style>
