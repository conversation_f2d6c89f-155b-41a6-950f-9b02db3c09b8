import { http } from "@/utils/http";
import type { Result } from "./type";

/**
 * 设置消息实时通知
 * @param data
 * @returns
 */
export const setMessageNotify = (params: { notifyEnable: boolean }) => {
  return http.request<Result>("put", "/trade/user/extend/set-notify", {
    params
  });
};

/**
 * 消息中心-最近3个月
 * @param data
 * @returns
 */
export const getMessageCenter = (readStatus?: number) => {
  return http.request<Result>("get", "/trade/message-center/message-center", {
    params: {
      readStatus
    }
  });
};

/**
 * 消息中心-列表页-最近3个月
 * @param data
 * @returns
 */
export const getMessageCenterNode = (params: {
  messageType: string;
  toUserId?: string;
  readStatus?: number;
  pageNo?: number;
  pageSize?: number;
}) => {
  return http.request<Result>(
    "get",
    "/trade/message-center/message-center-node",
    {
      params
    }
  );
};

/**
 * 标记消息为已读
 * @param data
 * @returns
 */
export const setMessageRead = params => {
  return http.request<Result>("put", "/trade/message-center/update-read", {
    params
  });
};

/**
 * 标记所有消息为已读
 * @param data
 * @returns
 */
export const setAllMessageRead = params => {
  return http.request<Result>("put", "/trade/message-center/update-all-read", {
    params
  });
};
