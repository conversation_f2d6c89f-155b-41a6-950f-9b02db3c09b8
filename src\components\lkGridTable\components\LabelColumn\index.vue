<template>
  <div
    style="pointer-events: auto; cursor: pointer"
    :style="{
      'max-width': `${width - 20}px`
    }"
  >
    <el-tag
      class="label-tag"
      effect="plain"
      round
      :style="{
        'border-color': style
      }"
      size="small"
    >
      <ReText type="info" :tippyProps="{ delay: 50000 }">
        <span :style="{ color: style }">{{ content }}</span>
      </ReText>
    </el-tag>
  </div>
</template>
<script lang="ts" setup>
import { ElTag } from "element-plus";
import { ReText } from "@/components/ReText";

defineProps({
  content: {
    type: String as PropType<string>,
    default: ""
  },
  style: {
    type: String as PropType<string>,
    default: ""
  },
  width: {
    type: String as PropType<string>,
    default: ""
  }
});
</script>
<style lang="scss" scoped>
.label-tag {
  display: inline-block;
  width: 100%;
  text-align: left;

  ::v-deep(.el-tag__content) {
    display: flex;
    align-items: center;
    height: 100%;

    .el-text {
      font-size: 12px;
    }
  }
}
</style>
