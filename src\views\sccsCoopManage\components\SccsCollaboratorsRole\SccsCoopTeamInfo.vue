<template>
  <div class="role-per-col">
    <div class="checkbox-span-body">
      <div class="checkbox-flex checkbox-flex-title">
        {{ sccsSettingData.label }}
      </div>
      <div class="checkbox-flex">
        <el-radio-group v-model="msNotifyRadio">
          <el-radio value="ALL_COOP_ORDER">
            {{ t("trade_sccs_allCoopOrder") }}
          </el-radio>
          <el-radio value="CUSTOM">
            {{ t("trade_common_custom") }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <div class="role-notice-col">
      <i class="iconfont link-tips-notice" />
      {{ t("trade_coop_teamTip2") }}
    </div>
    <div
      v-for="item in sccsSettingData.childrenList"
      :key="item.key"
      class="message-list"
    >
      <el-collapse v-model="msActiveIds">
        <el-collapse-item :title="item.label" :name="item.key">
          <template #icon="{ isActive }">
            <span class="icon-ele">
              <el-icon v-if="isActive"><CaretBottom /></el-icon>
              <el-icon v-else><CaretRight /></el-icon>
            </span>
          </template>
          <div
            v-for="col in item.childrenList"
            :key="col"
            class="collapese-item-col"
          >
            <span class="collapese-item-span">{{ col.label }}</span>
            <span class="collapese-item-remark">{{ col.remark }}</span>
            <span class="collaspse-item-switch">
              <el-switch
                v-model="msMessageSettingObject[col.msId][col.key]"
                :active-value="true"
                :inactive-value="false"
                size="small"
              />
            </span>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { CaretBottom, CaretRight } from "@element-plus/icons-vue";

const { t } = useI18n();
const msActiveIds = ref<string[]>([]);
const msNotifyRadio = ref<string>("");
const msMessageSettingObject = ref<any>({});

const props = defineProps({
  sccsSettingData: {
    type: Object as PropType<any>
  },
  sccsToolRolePermItemList: {
    type: Object as PropType<any>
  }
});

watch(
  () => props.sccsToolRolePermItemList,
  () => {
    const milestoneMessageSettingItemList =
      props.sccsToolRolePermItemList.milestoneMessageSettingItemList;

    msActiveIds.value = milestoneMessageSettingItemList.map(
      msMessage => msMessage.msId
    );

    let msMessageObject = {};
    for (let msMessageItem of milestoneMessageSettingItemList) {
      let operate = {};
      for (let workOperate of msMessageItem.milestoneMessageSettingList) {
        operate[workOperate.permissionKey] = workOperate.status;
      }
      msMessageObject[msMessageItem.msId] = operate;
    }
    msMessageSettingObject.value = msMessageObject;
    msNotifyRadio.value =
      props.sccsToolRolePermItemList.messageNotifySettingScope;
  },
  {
    deep: true,
    immediate: true
  }
);

const getMsMessageData = (): any => {
  let msDataItemList = [];
  for (let msId in msMessageSettingObject.value) {
    let msDataItemObject: any = {};
    msDataItemObject.msId = msId;

    let msMessageSettingList = [];
    Object.entries(msMessageSettingObject.value[msId]).map(msItem => {
      let msMessageSettingObject = {};
      msMessageSettingObject["permissionKey"] = msItem[0];
      msMessageSettingObject["status"] = msItem[1] ? 1 : 0;
      msMessageSettingList.push(msMessageSettingObject);
    });
    msDataItemObject.milestoneMessageSettingList = msMessageSettingList;
    msDataItemList.push(msDataItemObject);
  }

  return {
    milestoneMessageSettingItemList: msDataItemList,
    messageNotifySettingScope: msNotifyRadio.value
  };
};

defineExpose({
  getMsMessageData
});
</script>
<style lang="scss" scoped>
.checkbox-parent {
  margin-bottom: 10px;
  font-weight: bolder;
}

.role-per-col {
  box-sizing: border-box;
}

.role-notice-col {
  margin-top: 16px;
  font-size: 12px;
  line-height: 16px;
  color: #8c8c8c;

  .iconfont {
    margin-right: 3px;
    font-size: 12px;
    vertical-align: baseline;
  }
}

.checkbox-span-body {
  display: flex;
  flex: 1 !important;
  width: 100% !important;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  .checkbox-span-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .checkbox-flex-title {
    font-size: 14px;
    font-weight: bolder;
    line-height: 16px;
    color: #262626;
    text-align: left;
  }

  .checkbox-flex {
    flex: 1;

    &:nth-child(2) {
      padding-right: 20px;
      text-align: right;
    }
  }
}

::v-deep(.el-collapse) {
  padding: 0 17px;
  border: 0 none;

  .el-collapse-item__header {
    position: relative;
    display: flex;
    flex-flow: row-reverse;
    width: auto !important;
    border: 0 none !important;

    .checkbox-group-col {
      margin-bottom: 0 !important;
    }

    .icon-ele {
      margin-top: 3px;
      margin-right: 5px;
      font-size: 18px;
      color: #8c8c8c;
      vertical-align: middle;
    }
  }

  .el-collapse-item__wrap {
    border-bottom: 0 none;
  }

  .el-collapse-item__content {
    padding: 0 !important;
  }
}

.collapese-item-col {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  margin-left: 20px;

  .collapese-item-span {
    margin-right: 28px;
    font-size: 14px;
    line-height: 16px;
    color: #595959;
    text-align: left;
  }

  .collapese-item-remark {
    font-size: 14px;
    line-height: 16px;
    color: #8c8c8c;
    text-align: left;
  }

  .collaspse-item-switch {
    margin-left: 15px;
  }
}
</style>
