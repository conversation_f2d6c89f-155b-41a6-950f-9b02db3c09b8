<template>
  <LkDialog
    ref="LkDialogRef"
    class="email-message-box-dialog"
    title=""
    width="420"
    style="height: 174px"
    append-to-body
    @confirm="handleConfirm"
  >
    <template #header>
      <div class="message-box-header">
        <svg class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#link-tips-warm" />
        </svg>
        <div class="message-box-content">
          是否引用
          <span class="message-box-number">{{ number }}</span>
          条数据到工单？
        </div>
      </div>
    </template>
    <template #default>
      <div class="message-box-body">{{ t("trade_email_useSubTableTip") }}</div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";

const { t } = useI18n();
const LkDialogRef = ref<any | HTMLElement>();
const number = ref<number>(0);

const emit = defineEmits(["handleConfirm"]);

const open = numberEmail => {
  number.value = numberEmail;
  LkDialogRef.value.open();
};

const close = () => {
  LkDialogRef.value.close();
};

const handleConfirm = () => {
  emit("handleConfirm");
};

defineExpose({
  open,
  close
});
</script>
<style lang="scss" scoped>
.message-box-header {
  display: flex;
  align-items: center;

  .svg-icon {
    width: 19px;
    height: 19px;
  }

  .message-box-content {
    margin-left: 8px;
    font-size: 14px;
    font-weight: bolder;
    line-height: 24px;
    color: #262626;

    .message-box-number {
      padding: 0 5px;
      color: #e62412;
    }
  }
}

.message-box-body {
  height: 54px;
  padding-left: 26px;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  color: #595959;
}
</style>
