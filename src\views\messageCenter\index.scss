::v-deep(.message-center-drawer-modal) {
  margin-left: 55px !important;

  // background: #000;
}

::v-deep(.message-center-drawer) {
  z-index: 3004 !important;
  width: 420px !important;
  border-right: 1px solid var(--pure-border-color);

  // box-shadow: 9000px 0 0 9000px rgb(58 58 58 / 45%) !important;
  box-shadow: 10px 0 10px 0 rgb(0 0 0 / 5%) !important;

  .el-drawer__body {
    position: relative;
    padding: 0 !important;
    background: #fff;

    .message-center-drawer-body {
      position: relative;
      z-index: 3002 !important;
      height: 100%;

      .el-tabs {
        height: 100%;

        .el-tab-pane {
          height: 100%;

          .message-center-drawer-list {
            height: 100%;

            .message-center-drawer-row {
              cursor: pointer;
            }

            .el-badge__content {
              width: 16px;
              height: 16px;
              font-size: 10px;
            }

            .message-center-drawer-icon {
              width: 32px;
              height: 32px;
              font-size: 14px;
              line-height: 32px;
              color: #fff;
              text-align: center;
              background: #3b8fea;
              border-radius: 50%;

              ::v-deep(.el-avatar) {
                font-size: 14px !important;
              }
            }

            .message-center-drawer-col {
              display: flex;
              width: 100%;
              height: 130px;
              padding: 18px 20px 15px;
              cursor: pointer;
              background: #fff;
              border-bottom: 1px solid #e3e5e9;

              &:first-child {
                border-top: 1px solid #e3e5e9;
              }

              &:hover {
                background: #f9f9f9;
              }

              &.active {
                background: #f9f9f9;
              }

              .message-center-drawer-text-body {
                flex: 1;
                margin-right: 10px;
                margin-left: 10px;
                word-break: break-all;

                .message-center-drawer-title {
                  margin-bottom: 8px;
                  font-size: 14px;
                  line-height: 20px;
                  color: #595959;
                }

                .message-center-drawer-tip {
                  max-height: 40px;
                  margin-bottom: 10px;
                  font-size: 14px;
                  line-height: 20px;
                  color: #262626;

                  &.text-ellipsis-single {
                    display: -webkit-box;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }
                }

                .message-center-drawer-avatar {
                  display: flex;
                  align-items: center;

                  .message-center-drawer-text {
                    align-items: center;
                    margin-left: 2px;
                    font-size: 12px;
                    color: #8c8c8c;
                    vertical-align: middle;

                    &.text-ellipsis-single {
                      display: -webkit-box;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      -webkit-line-clamp: 1;
                      -webkit-box-orient: vertical;
                    }
                  }
                }
              }

              .message-center-drawer-next-icon {
                display: flex;
                align-items: center;
                font-size: 18px;
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }

    .el-tabs__header {
      height: 55px !important;
      margin-bottom: 0 !important;

      .el-tabs__nav-wrap {
        height: 100% !important;

        .el-tabs__nav-scroll {
          height: 100% !important;

          .el-tabs__nav {
            height: 100% !important;
          }
        }
      }

      .el-tabs__item {
        height: 100% !important;
        padding: 0 !important;
        margin: 0 13px;
        font-size: 18px;
        font-weight: bold;
        color: #262626;

        &.is-active {
          color: #0070d2 !important;
        }

        .el-badge__content {
          width: 16px;
          height: 16px;
          font-size: 10px;
        }
      }
    }

    .message-center-drawer-group {
      position: absolute;
      top: 14.5px;
      right: 30px;
      display: flex;
      align-items: center;

      .iconfont1 {
        font-size: 14px;
        color: #8c8c8c;
      }

      .iconfont2 {
        margin-left: 10px;
        font-size: 10px;
        color: #8c8c8c;
        vertical-align: middle;
        cursor: pointer;

        &:hover {
          color: #2082ed;
        }
      }

      .iconfont3 {
        margin-left: 24px;
        font-size: 13px;
        color: #8c8c8c;
        cursor: pointer;
      }
    }
  }
}

::v-deep(.message-center-child-drawer) {
  // inset: 0 0 0 474px !important;
  z-index: 2000 !important;
  width: 474px !important;
  border-right: 1px solid var(--pure-border-color);

  // box-shadow: 10px 0 10px 0 rgb(0 0 0 / 5%) !important;
  box-shadow: none !important;

  .el-drawer__body {
    padding: 0 !important;
    background: #fff;

    .message-center-drawer-body {
      position: relative;
      height: 100%;
      background: #f2f2f2;
      box-shadow: none;

      .message-center-drawer-header {
        position: relative;
        display: flex;
        height: 55px;
        line-height: 55px;
        background: #fff;

        .message-center-drawer-header-title {
          flex: 1;
          padding-left: 22px;
          font-size: 18px;
          font-weight: bolder;
          color: #262626;
        }

        .message-center-drawer-group {
          position: static !important;
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: end;
          padding-right: 22px;

          .iconfont2 {
            margin-left: 10px;
            font-size: 10px;
            color: #8c8c8c;
            vertical-align: middle;
            cursor: pointer;

            &:hover {
              color: #2082ed;
            }
          }

          .iconfont3 {
            margin-left: 24px;
            font-size: 13px;
            color: #8c8c8c;
            cursor: pointer;
          }
        }
      }
    }

    .message-center-drawer-list {
      .el-badge__content {
        width: 16px;
        height: 16px;
        font-size: 10px;
      }

      .message-center-drawer-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        color: #fff;
        text-align: center;
        background: #3b8fea;
        border-radius: 50%;

        ::v-deep(.el-avatar) {
          font-size: 14px;
        }
      }

      .message-center-drawer-col {
        display: flex;
        height: 130px;
        padding: 18px 20px 15px;
        margin: 18px 13px 13px;
        cursor: pointer;
        background: #fff;
        border-bottom: 1px solid #e3e5e9;

        &:first-child {
          border-top: 1px solid #e3e5e9;
        }

        &:hover {
          background: #f9f9f9;
        }

        .message-center-drawer-text-body {
          flex: 1;
          margin-right: 10px;
          margin-left: 10px;
          word-break: break-all;

          .message-center-drawer-title {
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 20px;
            color: #595959;
          }

          .message-center-drawer-tip {
            margin-bottom: 10px;

            p {
              font-size: 14px;
              line-height: 20px;
              color: #262626;
            }
          }

          .message-center-drawer-avatar {
            display: flex;
            align-items: center;

            .message-center-drawer-text {
              align-items: center;
              margin-left: 2px;
              font-size: 12px;
              color: #8c8c8c;
              vertical-align: middle;
            }
          }
        }

        .message-center-drawer-next-icon {
          display: flex;
          align-items: center;
          font-size: 18px;
          color: #8c8c8c;
        }
      }
    }
  }
}

.drawer-notice-body {
  .drawer-notice-col {
    display: flex;
    align-items: center;

    .drawer-notice-title {
      flex: 1;
      font-size: 14px;
      line-height: 20px;
      color: #262626;
    }
  }

  .drawer-notice-bottom {
    margin-top: 9px;

    .drawer-notice-title {
      font-size: 12px;
      line-height: 17px;
      color: #8c8c8c;
    }
  }
}
