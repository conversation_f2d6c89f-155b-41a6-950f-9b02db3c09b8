<template>
  <div
    ref="containerRef"
    style="width: 100%; pointer-events: auto; cursor: pointer"
  >
    <el-checkbox-group
      v-if="widgetConfigure.props.layout !== 'select'"
      v-model="widgetFormData[widgetConfigure._fc_id]"
      style="overflow-y: auto"
    >
      <el-checkbox
        v-for="(item, index) in widgetConfigure.props.options"
        :key="index"
        :value="item.value"
        :style="{
          display:
            widgetConfigure.props.layout === 'vertical'
              ? 'flex'
              : 'inline-flex',
          width: widgetConfigure.props.layout === 'vertical' ? '100%' : 'auto'
        }"
      >
        <template #default>
          <div
            class="checkbox-span"
            :style="{
              background: item.color
            }"
          >
            <ReText type="info" :tippyProps="{ delay: 50000 }">
              {{ widgetOptionItem(item) }}
            </ReText>
          </div>
        </template>
      </el-checkbox>
    </el-checkbox-group>
    <el-select-v2
      v-else
      v-model="widgetFormData[widgetConfigure._fc_id]"
      :options="widgetConfigure.props.options"
      :placeholder="placeholder"
      clearable
      multiple
      :collapse-tags="!!widgetConfigure.inTableColumns"
      collapse-tags-tooltip
      :max-collapse-tags="1"
      :teleported="widgetRowIndex === -1"
      :fit-input-width="shouldUseFitInputWidth ? 200 : undefined"
      @click.stop
    >
      <template #tag>
        <span
          v-for="item in widgetFormData[widgetConfigure._fc_id]"
          :key="item.value"
          class="checkbox-tag"
          :style="{
            background: getSelectOptions(item)?.color
          }"
        >
          <ReText type="info" :tippyProps="{ delay: 50000 }">
            <span class="dr-checkbox-title">
              {{ getSelectOptions(item)?.label }}
            </span>
          </ReText>
          <i
            class="iconfont link-close dr-checkbox-icon"
            @click.stop="handleDelete(item)"
          />
        </span>
      </template>
      <template #default="{ item }">
        <el-checkbox
          :model-value="
            widgetFormData[widgetConfigure._fc_id] instanceof Array
              ? widgetFormData[widgetConfigure._fc_id].includes(item.value)
              : []
          "
          :label="item.value"
          :value="item.value"
          class="select-v2-checkbox"
        >
          <ReText
            class="select-v2-text"
            :style="{
              background: item.color
            }"
          >
            {{ item.label }}
          </ReText>
        </el-checkbox>
      </template>
    </el-select-v2>
  </div>
</template>
<script lang="ts" setup>
import { computed, inject, ref, watch } from "vue";
import { ElCheckbox, ElSelectV2, ElCheckboxGroup } from "element-plus";
import { ReText } from "@/components/ReText";
import { cloneDeep, isEqual } from "lodash-es";
import { storageLocal } from "@pureadmin/utils";
import { useElementSize } from "@vueuse/core";

interface SelectOptionProp {
  value: string;
  label: string;
  color: string;
}

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const widgetOptionItem = computed(() => {
  return item => {
    const translationLang = storageLocal().getItem("translationLang");
    return translationLang === "en" ? item.labelEn || item.label : item.label;
  };
});

const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);

const emit = defineEmits(["handleSubTableWidgetValueChange"]);

const getWidgetValueLabel = (widgetValue: any) => {
  if (widgetValue && widgetValue instanceof Array && widgetValue.length > 0) {
    const widgetOptions = props.widgetConfigure.props.options;
    let labelText = [];
    widgetValue.map(widgetItemValue => {
      const widgetItem = widgetOptions.find(
        option => option.value === widgetItemValue
      );
      labelText.push(
        `<span class="select-v2-text" style="background: ${widgetItem.color}">${widgetItem.label}</span>`
      );
    });
    return labelText.length > 0 ? labelText.join(",") : "";
  } else {
    return "";
  }
};

const getSelectOptions = (value: string): SelectOptionProp => {
  const option = props.widgetConfigure.props.options.find(
    widget => widget.value === value
  );
  return option as SelectOptionProp;
};

const handleDelete = (value: string): void => {
  const index = widgetFormData.value[props.widgetConfigure._fc_id].findIndex(
    widget => widget === value
  );
  widgetFormData.value[props.widgetConfigure._fc_id].splice(index, 1);
};

const handleFormatCheckboxData = () => {
  const optionWidgetIds = props.widgetConfigure.props.options.map(
    option => option.value
  );
  const orderMap = {};
  optionWidgetIds.forEach((item, index) => {
    orderMap[item] = index;
  });

  const checkboxWidgetValue = cloneDeep(
    widgetFormData.value[props.widgetConfigure._fc_id]
  );
  const sortedArray = [...checkboxWidgetValue].sort((a, b) => {
    // 获取元素在参考顺序中的索引
    const indexA = orderMap[a] !== undefined ? orderMap[a] : Infinity;
    const indexB = orderMap[b] !== undefined ? orderMap[b] : Infinity;

    // 比较索引
    return indexA - indexB;
  });
  return sortedArray;
};

const containerRef = ref<HTMLElement>();
const { width: containerWidth } = useElementSize(containerRef);

// 计算是否应该使用 fit-input-width
const shouldUseFitInputWidth = computed(() => {
  return containerWidth.value < 160;
});

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    if (newVal && newVal.length > 0) {
      widgetFormData.value[props.widgetConfigure._fc_id] = cloneDeep(newVal);
    } else {
      widgetFormData.value[props.widgetConfigure._fc_id] = [];
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => widgetFormData.value[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (!isEqual(newVal, props.trendsForm[props.widgetConfigure._fc_id])) {
      const widgetId = props.widgetConfigure._fc_id;
      const checkboxDataList = handleFormatCheckboxData();
      const widgetLabelText = getWidgetValueLabel(checkboxDataList);

      if (props.widgetRowIndex !== -1) {
        emit(
          "handleSubTableWidgetValueChange",
          widgetId,
          {
            obj: checkboxDataList,
            label: widgetLabelText,
            widgetId: widgetId,
            $rowIndex: props.widgetRowIndex
          },
          checkboxDataList
        );
      } else {
        handleWidgetFormsValue(
          widgetId,
          {
            obj: checkboxDataList,
            label: widgetLabelText,
            widgetId: widgetId,
            $rowIndex: props.widgetRowIndex
          },
          checkboxDataList
        );
      }
    }
  },
  {
    deep: true
  }
);
</script>
<style lang="scss" scoped>
.el-checkbox-group {
  width: 100%;
  height: 100%;
  overflow: hidden auto;

  .el-checkbox {
    max-width: 100% !important;
    margin-right: 10px !important;

    ::v-deep(.el-checkbox__label) {
      box-sizing: content-box;
      max-width: calc(100% - 20px);

      .checkbox-span {
        overflow: hidden;
        text-overflow: ellipsis;
        word-wrap: break-word;
        vertical-align: middle;

        .el-text {
          display: inline-block;
          height: 100%;
          font-size: 12px;
          line-height: 12px;
          color: #262626;
        }
      }
    }
  }

  .el-checkbox.is-checked {
    .checkbox-span {
      font-weight: 400;
      color: #0070d2;
    }
  }
}

.el-select-dropdown__item {
  .el-checkbox {
    width: 100%;

    ::v-deep(.el-checkbox__label) {
      width: calc(100% - 20px);
    }
  }
}

.select-v2-checkbox {
  ::v-deep(.select-v2-text) {
    display: inline-block;
    max-width: 100%;
    height: 20px;
    padding: 0 6px;
    overflow: hidden;
    line-height: 20px;
    color: #262626 !important;
    text-overflow: ellipsis;
    word-wrap: break-word;
    border-radius: 3px;
  }
}

.select-v2-text {
  display: inline-block;
  max-width: 100%;
  height: 20px;
  padding: 0 6px;
  overflow: hidden;
  line-height: 20px;
  color: #262626 !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  border-radius: 3px;
}

.checkbox-tag {
  display: inline-flex;
  align-items: center;
  max-width: 100%;
  padding: 0 6px;
  background: #efefef;
  border-radius: 3px;

  .el-text {
    height: 24px !important;
  }

  .dr-checkbox-title {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    font-size: 12px;
    color: #262626;
    text-overflow: ellipsis;
    word-wrap: break-word;
    vertical-align: middle;
  }
}

.checkbox-span {
  padding: 5px;
  font-size: 12px;
  line-height: 12px;
  color: #262626;
  text-align: center;
  background: #f2f2f2;
  border-radius: 3px;
}

.select-v2-tag {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border: 0 none;
}

.dr-checkbox-title {
  overflow: hidden;
  font-weight: 500;
  color: #262626;
}

.dr-checkbox-icon {
  margin-left: 6px;
  font-size: 8px;
  font-weight: 500;
  color: #262626;
  cursor: pointer;
}
</style>
