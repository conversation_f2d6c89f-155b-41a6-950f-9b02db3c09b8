<template>
  <el-popover
    :width="324"
    placement="right"
    trigger="click"
    :show-arrow="false"
    :hide-after="0"
    popper-class="team-manage-popper"
    @show="handlePopperVisible"
  >
    <template #reference>
      <LkNoPopoverAvatar
        :size="32"
        class="colorOrange avatarFont16"
        shape="square"
        fit="cover"
        :teamInfo="{
          avatar: currentTeam?.teamAvatar,
          username: currentTeamName
        }"
      />
    </template>
    <template #default>
      <div class="team-manage-body">
        <div class="team-manage-top">
          <div class="team-manage-title">{{ t("trade_team_currentTeam") }}</div>
          <div class="team-manage-content">
            <LkNoPopoverAvatar
              :size="32"
              shape="square"
              class="colorOrange avatarFont16"
              fit="cover"
              :teamInfo="{
                avatar: currentTeam?.teamAvatar,
                username: currentTeamName
              }"
            />
            <div class="team-manage-name">{{ currentTeam?.teamName }}</div>

            <div class="team-manage-iconfont">
              <FontIcon icon="link-tick" />
            </div>
          </div>
          <div v-perms="'team_setting'" class="team-manage-btn-area">
            <div class="team-manage-btn">
              <span class="team-manage-span" @click="handleOpenDialog(1)">
                <FontIcon icon="link-invite-members" />
                {{ t("trade_team_inviteMembers") }}
              </span>
            </div>
            <div class="team-manage-btn">
              <span class="team-manage-span" @click="handleOpenDialog(2)">
                <FontIcon icon="link-setting" />
                {{ t("trade_team_setTeam") }}
              </span>
            </div>
          </div>
        </div>
        <div class="team-manage-bottom">
          <template v-if="teamList.length > 1">
            <div class="team-manage-title">
              {{ t("trade_team_switchTeam") }}
            </div>
            <el-scrollbar max-height="312px" class="team-manage-list">
              <div
                v-for="item in teamList.filter(
                  team => team.id !== currentTeam?.id
                )"
                :key="item.id"
                class="team-manage-content"
                @click="handleSwitchTeam(item.id)"
              >
                <LkNoPopoverAvatar
                  :size="32"
                  shape="square"
                  fit="cover"
                  class="colorOrange avatarFont16"
                  :teamInfo="{
                    avatar: item.teamAvatar,
                    username: item.teamName
                  }"
                />
                <ReText
                  type="info"
                  class="team-manage-name"
                  :tippyProps="{ delay: 50000 }"
                >
                  {{ item.teamName }}
                </ReText>
                <div class="team-manage-iconfont">
                  <el-badge
                    v-if="item.toDoTaskCount !== 0"
                    :value="30"
                    class="item"
                    :offset="[0, 2]"
                  >
                    <FontIcon icon="link-to-do" />
                  </el-badge>
                  <FontIcon v-else icon="link-to-do" />
                </div>
              </div>
            </el-scrollbar>
          </template>
          <div
            class="team-manage-create-area"
            :style="teamList.length > 1 ? '' : 'margin-top: 8px'"
            @click="handleOpenDialog(0)"
          >
            <div class="team-manage-btn">
              <FontIcon icon="link-add" />
            </div>
            <span class="team-manage-text">
              {{ t("trade_perCenter_createTeam") }}
            </span>
          </div>
        </div>
      </div>
    </template>
  </el-popover>
  <Teleport to="body">
    <CreateTeamDialog
      ref="CreateTeamDialogRef"
      @createSuccess="createSuccess"
    />
    <InviteMembersDialog
      ref="InviteMembersDialogRef"
      @createSuccess="createSuccess"
    />
    <TeamManageDrawer
      ref="TeamManageDrawerRef"
      :teamName="currentTeam?.teamName"
    />
  </Teleport>
</template>
<script lang="ts" setup>
import { computed, onMounted, onUpdated, ref } from "vue";
import { useI18n } from "vue-i18n";
import { teamCooperationCache } from "@/utils/cooperatioTeam";
import CreateTeamDialog from "../createTeam/component/createTeamDialog.vue";
import InviteMembersDialog from "../createTeam/component/inviteMembersDialog.vue";
import TeamManageDrawer from "./components/teamManageDrawer.vue";
import LkNoPopoverAvatar from "@/components/lkNoPopoverAvatar";
import { setUserRole, switchTeam } from "@/utils/auth";
import { joinMember } from "@/api/member";
import { ReText } from "@/components/ReText";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { getMyTeamList } from "@/api/common";
import { storageLocal } from "@pureadmin/utils";

const { t } = useI18n();
const CreateTeamDialogRef = ref<any>(null);
const InviteMembersDialogRef = ref<any>(null);
const TeamManageDrawerRef = ref<any>(null);
let teamList = ref<any[]>([]);
let currentTeam = ref<any>({});
const router = useRouter();

const currentTeamName = computed(() => {
  return currentTeam.value?.teamName
    ? currentTeam.value?.teamName.substring(0, 1)
    : "";
});

const handleOpenDialog = (index: number): void => {
  if (index === 0) {
    CreateTeamDialogRef.value.open();
  } else if (index === 1) {
    InviteMembersDialogRef.value.open();
  } else if (index === 2) {
    TeamManageDrawerRef.value.open();
  }
};

const createSuccess = async (id?: any): Promise<void> => {
  getTeamManageData();
  if (id) {
    joinMember({ teamId: id }).then(res => {
      if (res.code === 0) {
        switchTeam(id);
      }
    });
  }
};

const handleSwitchTeam = async (id: string): Promise<void> => {
  switchTeam(id);
};

const getTeamManageData = async (): Promise<void> => {
  teamList.value = (await teamCooperationCache.getCacheTeamOtherList()) as any;
  currentTeam.value = await teamCooperationCache.currentlyUsedTeam();
  if (!currentTeam.value) {
    ElMessage.error(t("trade_team_member_not_have_the_team"));
    if (teamList.value.length > 0) {
      router.push("/selectTeam");
    } else {
      router.push("/createTeam");
    }
  }
};

const handlePopperVisible = async () => {
  getTeamManageData();
};

onMounted(async () => {
  const userInfo: any = storageLocal().getItem("user-info");
  setUserRole(userInfo.latestLoginTeamId).then(() => {
    getTeamManageData();
  });
});

onUpdated(() => {
  if (!currentTeam.value) {
    getTeamManageData();
  }
});
</script>
<style lang="scss" scoped>
.team-manage-body {
  background: url("../../assets/images/team/teamBg.png") no-repeat top center;
  background-position: -8px -14px;
  background-size: 110% 108%;

  .team-manage-title {
    padding-top: 24px;
    margin: 0 0 18px 12px;
    font-size: 13px;
    line-height: 18px;
    color: #999;
  }

  .team-manage-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 20px 18px 12px;

    ::v-deep(.el-avatar) {
      .team-avater {
        width: 100%;
        height: 100%;
        line-height: 32px;
        background: #ec7840;
      }
    }

    .team-manage-name {
      flex: 1;
      align-items: center;
      padding: 0 10px;
      margin-left: 9px;
      overflow: hidden;
      font-size: 14px;
      line-height: 20px;
      color: #262626;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }

    .iconfont {
      font-size: 14px;
      color: #0070d2;
    }
  }

  .team-manage-top {
    .team-manage-btn-area {
      display: flex;
      height: 44px;
      border-bottom: 1px solid #f0f0f0;

      .team-manage-btn {
        position: relative;
        flex: 1;
        text-align: center;
        cursor: pointer;

        &:hover {
          background: #eef8ff;
        }

        .team-manage-span {
          width: 100%;
          height: 44px;
          line-height: 44px;
          color: #0070d2;

          .iconfont {
            margin-right: 2px;
          }
        }

        &:first-child {
          &::after {
            position: absolute;
            top: 50%;
            right: 0;
            display: inline-block;
            width: 1px;
            height: 17px;
            content: "";
            background: #f0f0f0;
            transform: translateY(-50%);
          }
        }
      }
    }
  }

  .team-manage-bottom {
    padding-bottom: 1px;

    .team-manage-title {
      margin-bottom: 8px;
    }

    .team-manage-list {
      .team-manage-content {
        height: 52px;
        padding: 0 12px;
        margin: 0;
        cursor: pointer;

        ::v-deep(.team-manage-name) {
          flex: 1;
          margin-right: 6px;
          margin-left: 6px;
        }

        &:hover {
          background: #f5f5f5;
        }

        .team-manage-iconfont {
          width: 30px;

          .iconfont {
            font-size: 18px;
            color: #8a8a8a !important;
          }

          ::v-deep(.el-badge__content) {
            width: 18px !important;
            height: 18px !important;
            border-radius: 50%;
          }
        }
      }
    }

    .team-manage-create-area {
      display: flex;
      align-items: center;
      height: 44px;
      padding: 0 12px;
      // margin-top: 8px;
      margin-bottom: 10px;
      cursor: pointer;

      &:hover {
        background: #f5f5f5;
      }

      .team-manage-btn {
        width: 32px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        cursor: pointer;
        border: 1px dashed #bfbfbf;
        border-radius: 8px;
      }

      .team-manage-text {
        flex: 1;
        align-items: center;
        margin-left: 9px;
        font-size: 14px;
        color: #595959;
      }
    }
  }
}
</style>
<style lang="scss">
.team-manage-popper {
  padding: 0 !important;
  // min-height: 500px;
  border-radius: 8px;
}
</style>
