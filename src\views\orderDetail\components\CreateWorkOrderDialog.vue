<template>
  <el-drawer
    v-model="drawerVisible"
    class="lk-maximum-drawer"
    size="83%"
    body-class="create-work-order-drawer-container"
    footer-class="create-work-order-drawer-footer"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :with-header="false"
  >
    <el-tabs
      v-model="drawerTabsValue"
      type="card"
      addable
      class="create-work-order-tabs"
      @edit="handleTabsEdit"
    >
      <template #add-icon>
        <div class="add-work-order-tabs-header" @click.stop>
          <i
            v-if="editableTabs.length < 10"
            class="iconfont link-add"
            @click="handleTabsEdit('', 'add')"
          />
          <div class="create-work-order-tabs-container" @click.stop>
            <div class="create-work-order-tabs-left">
              <div class="create-work-order-tabs-first-col" @click.stop>
                <i class="iconfont link-quote-third-party" />
                <div class="create-work-order-tabs-text">
                  {{ t("trade_order_DataReference") }}
                </div>
              </div>
              <el-tooltip :content="t('trade_common_orderMessage')">
                <div
                  class="create-work-order-tabs-col"
                  :class="{ 'create-work-order-active': mainFormVisible }"
                  @click.stop="handleOpenMainFormDialog"
                >
                  <i class="iconfont link-basic-info" />
                </div>
              </el-tooltip>
            </div>
            <div
              class="create-work-order-tabs-right"
              @click.stop="handleCloseDialog"
            >
              <i class="iconfont link-close" />
            </div>
          </div>
        </div>
      </template>
      <el-tab-pane
        v-for="(item, editIndex) in editableTabs"
        :key="item.name"
        :name="item.name"
      >
        <template #label>
          <div class="create-work-header">
            <!-- 检查当前选项卡是否有校验错误，若有则显示错误icon -->
            <i
              v-if="tabsValiFailList.has(item.name)"
              class="iconfont link-tips-warm create-work-icon error-icon"
              style="color: #f56c6c"
            />
            <span class="create-work-order-text">
              {{ t("trade_work_order") }}{{ editIndex + 1 }}
            </span>
            <i
              :class="['iconfont link-copy create-work-icon']"
              @click.stop="handleTabsEdit(item.name, 'copy')"
            />
            <i
              v-if="editIndex !== 0"
              class="iconfont link-ashbin create-work-icon"
              @click.stop="handleTabsEdit(item.name, 'remove')"
            />
            <!-- <i class="iconfont link-tips-warm create-work-icon error-icon" /> -->
          </div>
        </template>
        <div class="create-work-body">
          <div class="create-work-body-left">
            <el-scrollbar class="no-scrollbar">
              <div class="create-work-order-form-container">
                <div class="create-work-order-title">
                  {{ t("trade_work_order") }}{{ editIndex + 1 }}
                </div>
                <div class="dialog-content-form-item">
                  <el-input
                    v-model="item.dataList.name"
                    clearable
                    maxlength="50"
                    show-word-limit
                  />
                </div>
              </div>
              <div class="create-work-order-form-personnel-body">
                <div class="create-work-order-form-header">
                  <div
                    class="create-work-order-form-left"
                    @click="handleAddWorkOrderPeronnel"
                  >
                    <i class="iconfont link-sharing" />
                    {{ t("trade_order_Batchsettingofprocessors") }}
                  </div>
                  <div
                    v-if="getWorkOrderFormAssigned(item) > 1"
                    class="create-work-order-form-right"
                  >
                    <span class="work-order-tooltip-text">
                      {{
                        t("trade_common_cooperateWith", {
                          params0: getWorkOrderFormAssigned(item)
                        })
                      }}
                    </span>
                    <el-tooltip
                      effect="dark"
                      :content="t('trade_order_workOrderReplyAssignedTip')"
                      placement="top"
                      :show-after="500"
                    >
                      <i class="iconfont link-explain" />
                    </el-tooltip>
                  </div>
                </div>
                <div
                  v-for="workOrderItem in item.dataList
                    .workOrderProcessorVOList"
                  :key="workOrderItem"
                  class="create-work-order-form-personnel"
                >
                  <div class="create-work-order-form-top">
                    <div class="create-work-order-form-top-left">
                      {{ workOrderItem.name }}
                    </div>
                    <div class="create-work-order-form-top-right">
                      <el-tooltip
                        :content="t('trade_createWorkOrder_show')"
                        placement="top"
                        :show-after="500"
                      >
                        <div
                          v-show="workOrderItem.formProperty === 'DISPLAY'"
                          class="create-work-order-form-item"
                          @click="handleChangeSwitch(workOrderItem, 'HIDDEN')"
                        >
                          <i class="iconfont link-display" />
                        </div>
                      </el-tooltip>
                      <el-tooltip
                        :content="t('trade_createWorkOrder_hide')"
                        placement="top"
                        :show-after="500"
                      >
                        <div
                          v-show="workOrderItem.formProperty === 'HIDDEN'"
                          class="create-work-order-form-item"
                          @click="handleChangeSwitch(workOrderItem, 'DISPLAY')"
                        >
                          <i class="iconfont link-hide" />
                        </div>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="create-work-order-form-bottom">
                    <div
                      v-if="workOrderItem.formProperty === 'DISPLAY'"
                      class="work-order-personnel-control-operation"
                    >
                      <OrderPersonnelTag
                        :type="personnelType"
                        :workOrderUserInfo="workOrderItem"
                        :personnelList="workOrderItem.collectUserVOList"
                        @handleChangePersonnel="handleChangePersonnel"
                      />
                    </div>
                    <span v-else class="work-order-peronnel-container-text">
                      {{ t("trade_order_BatchsettingofprocessorsTip") }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                v-if="needReplyForm.workOrderReply"
                class="create-work-order-form-personnel-body"
              >
                <div class="create-work-order-form-personnel">
                  <div class="create-work-order-form-top">
                    <div
                      class="create-work-order-form-top-left create-work-order-form-title-require"
                    >
                      {{ t("trade_order_taskReply") }}
                    </div>
                    <div class="create-work-order-form-top-right">
                      <div
                        v-if="workOrderReplyAssigned(item).length > 1"
                        class="work-order-tooltip-body"
                      >
                        <span class="work-order-tooltip-text">
                          {{
                            t("trade_common_cooperateWith", {
                              params0: workOrderReplyAssigned(item).length
                            })
                          }}
                        </span>
                        <el-tooltip
                          effect="dark"
                          :content="t('trade_order_workOrderReplyAssignedTip')"
                          placement="top"
                          :show-after="500"
                        >
                          <i class="iconfont link-explain" />
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                  <div class="create-work-order-form-bottom">
                    <OrderPersonnelTag
                      :type="personnelType"
                      :workOrderUserInfo="item.dataList"
                      :personnelList="workOrderReplyAssigned(item)"
                      @handleChangePersonnel="handleReplyFormPersonnel"
                    />
                  </div>
                </div>
              </div>
              <div
                v-if="needReplyForm.msReply && needReplyForm.needMsReply"
                class="create-work-order-form-personnel-body"
              >
                <div class="create-work-order-form-personnel">
                  <div class="create-work-order-form-top">
                    <div
                      class="create-work-order-form-top-left create-work-order-form-title-require"
                    >
                      {{ t("trade_template_replyBtnName") }}
                      <span class="create-work-order-form-tip">
                        {{ t("trade_order_createWorkTitle") }}
                      </span>
                    </div>
                    <div class="create-work-order-form-top-right">
                      <div
                        v-if="msReplyAssigned(item).length > 1"
                        class="work-order-tooltip-body"
                      >
                        <span class="work-order-tooltip-text">
                          {{
                            t("trade_common_cooperateWith", {
                              params0: msReplyAssigned(item).length
                            })
                          }}
                        </span>
                        <el-tooltip
                          effect="dark"
                          :content="t('trade_order_msReplyAssignedTip')"
                          placement="top"
                          :show-after="500"
                        >
                          <i class="iconfont link-explain" />
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                  <div class="create-work-order-form-bottom">
                    <OrderPersonnelTag
                      :type="personnelType"
                      :workOrderUserInfo="item.dataList"
                      :personnelList="msReplyAssigned(item)"
                      @handleChangePersonnel="handleMsReplyFormPersonnel"
                    />
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
          <div class="create-work-body-right">
            <el-scrollbar class="horizontal-scrollbar-only">
              <div class="dialog-content-flex-right-header">
                {{ t("trade_order_createWorkOrderTip") }}
              </div>
              <LkTrendsAggregationForm
                ref="TrendsAggregationFormRef"
                :key="`trends-form-${item.name}`"
                :formList="widgetFormDataList"
                :formWidgetData="item.dataList.widgetItemDataList"
                :operationalFactorData="operationalFactorData"
                :trendsFormFlag="true"
                :createState="true"
                :defaultValueActuatorFlag="
                  item.dataList.widgetItemDataList.length === 0
                "
                :sccsId="route.query.sccsId"
                :hiddenFormIdList="item.dataList.hiddenFormIdList"
                :trendsWidgetRequireFormIdList="
                  item.dataList.trendsWidgetRequireFormIdList
                "
                :workOrderAllowOperation="workOrderAllowOperation"
                @handleWidgetChange="handleWidgetChange"
              >
                <template #collapseFormHeader="{ collapseData }">
                  <div
                    style="display: inline-flex; margin-left: 20px"
                    @click.stop
                  >
                    <LkAvatarGroupNext
                      :size="22"
                      mode="reply"
                      :avatarListGroup="
                        getWorkOrderFormOperateMember(item, collapseData.id)
                      "
                      :maxAvatar="4"
                    />
                  </div>
                </template>
                <template #otherCollapse>
                  <el-collapse-item
                    v-if="
                      readonlyReplyForm.workOrderReplyForm &&
                      readonlyReplyForm.workOrderReplyForm.length > 0
                    "
                    name="msCollapsePlan"
                  >
                    <template #title>
                      <div class="collapse-form-item-title">
                        {{ t("trade_order_taskReplyComplete") }}
                      </div>
                    </template>
                    <OrderDetailDescWidget
                      :widgetForm="readonlyReplyForm.workOrderReplyForm"
                      :widgetData="[]"
                    />
                  </el-collapse-item>
                  <el-collapse-item
                    v-if="
                      needReplyForm.msReply &&
                      needReplyForm.needMsReply &&
                      readonlyReplyForm.milestoneReplyForm &&
                      readonlyReplyForm.milestoneReplyForm.length > 0
                    "
                    name="msCollapsePlan"
                  >
                    <template #title>
                      <div class="collapse-form-item-title">
                        {{ t("trade_template_replyBtnName") }}
                      </div>
                    </template>
                    <OrderDetailDescWidget
                      :widgetForm="readonlyReplyForm.milestoneReplyForm"
                      :widgetData="[]"
                    />
                  </el-collapse-item>
                </template>
              </LkTrendsAggregationForm>
            </el-scrollbar>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <OrderMainFormHeader ref="OrderMainFormRef" :mainFormData="mainForm" />
    <template #footer>
      <div
        class="create-work-order-footer-text"
        v-html="createSuccessWorkOrderFooterTip"
      />
      <el-button plain @click="handleCloseDialog">
        {{ t("trade_common_cancel") }}
      </el-button>
      <el-button
        type="primary"
        :disabled="btnOperationState"
        @click="handleConfirm"
      >
        {{ t("trade_common_sure") }}
      </el-button>
    </template>
  </el-drawer>
  <LkDialog
    ref="CreateSuccessDialogRef"
    class="create-success-dialog"
    :no-footer="true"
    height="300"
    width="480"
    @close="handleCreateWorkOrder"
  >
    <template #default>
      <div class="create-work-order-container">
        <svg class="icon svg-icon" aria-hidden="true">
          <use xlink:href="#link-work-order-success" />
        </svg>
        <div
          class="create-work-order-text"
          v-html="createSuccessWorkOrderTip"
        />
        <div v-if="containMineWorkOrderFlag" class="create-work-order-tip">
          {{ t("trade_order_workOrderSuccessTip") }}
        </div>
        <div
          v-if="containMineWorkOrderFlag"
          class="create-work-order-btn-group"
        >
          <el-button plain @click="handleCollectionWorkOrder">
            <i class="iconfont link-collect font14 mr-1" />
            {{ t("trade_order_ImmediateProcessing") }}
          </el-button>
          <el-button
            type="primary"
            color="#0070D2"
            @click="handleCreateWorkOrder"
          >
            <i class="iconfont link-to-do font14 mr-1" />
            {{ t("trade_order_saveTodoList") }}
          </el-button>
        </div>
      </div>
    </template>
  </LkDialog>
  <OrderChooseProcessorDialog
    ref="ChooseProcessorRef"
    @handleChooseConfirm="handleAllChooseConfirm"
  />
  <WorkOrderCollectionDialog
    ref="WorkOrderCollectionDialogRef"
    @handleUpdateCollection="handleCollectionSuccess"
  />
</template>
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { ElMessage, TabPaneName } from "element-plus";
import LkTrendsAggregationForm from "@/components/lkTrendsAggregationForm/index";
import LkAvatarGroupNext from "@/components/lkAvatarGroupNext";
import LkDialog from "@/components/lkDialog/index";
import OrderPersonnelTag from "@/views/orderDetail/components/OrderChooseProcessor/OrderPersonnelTag.vue";
import OrderChooseProcessorDialog from "@/views/orderDetail/components/OrderChooseProcessor/OrderChooseProcessorDialog.vue";
import OrderDetailDescWidget from "@/views/orderDetail/components/OrderDescribeWidget/OrderDetailDescWidget.vue";
import OrderMainFormHeader from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderMainFormHeader.vue";
import WorkOrderCollectionDialog from "@/views/orderDetail/components/WorkOrderCollectionDialog.vue";
import { cloneDeep, isEqual, storageLocal } from "@pureadmin/utils";
import { debounce } from "lodash-es";
import {
  getEntireFormData,
  TransformSubmitDataStructure
} from "@/utils/formDesignerUtils";
import {
  createWorkOrder,
  getMileStoneWorkOrderCount,
  getOrderDetail,
  getWorkOrderProcessorList,
  getWorkOrderStructure
} from "@/api/order";
import { useMounted } from "@vueuse/core";
import { emitter } from "@/utils/mitt";
import {
  calculateVisibleFormList,
  ExecuteFormVisibleExpression
} from "@/utils/formVisibleExpression";

interface ReplyMemberProp {
  msReplyTeamMemberId?: string;
  workOrderReplyTeamMemberId?: string;
}

interface NeedReplyProp {
  msReply?: boolean;
  workOrderReply?: boolean;
  needMsReply?: boolean;
}

interface ReadonlyReplyProp {
  milestoneReplyForm?: any;
  workOrderReplyForm?: any;
}

const { t } = useI18n();
const route = useRoute();
let tabIndex = 1;
const ChooseProcessorRef = ref<HTMLElement | any>(null);
const TrendsAggregationFormRef = ref<HTMLElement | any>(null);
const WorkOrderCollectionDialogRef = ref<HTMLElement | any>(null);
const CreateSuccessDialogRef = ref<HTMLElement | any>(null);
const OrderMainFormRef = ref<HTMLElement | any>(null);
const drawerVisible = ref<boolean>(false);
const btnOperationState = ref<boolean>(false);
const mainFormVisible = ref<boolean>(false);
const drawerTabsValue = ref<string>("1");
const personnelType = ref<string>("UPDATE_PROCESSOR");
const msId = ref<string>("");
const mainForm = ref<any>({});
const operationalFactorData = ref<any>();
const replyMemberObject = ref<ReplyMemberProp>({});
const needReplyForm = ref<NeedReplyProp>({});
const readonlyReplyForm = ref<ReadonlyReplyProp>({});
const WorkOrderFormTemplateList = ref<any>([]);
const widgetFormDataList = ref<any>([]);
const thatWorkOrderId = ref<string>("");
const containMineWorkOrderFlag = ref<boolean>(false);
const tabsValiFailList = ref(new Set());
const editableTabs = ref<any[]>([
  {
    name: "1",
    dataList: {
      name: "1#",
      widgetItemDataList: [],
      msReplyAssignedUserVOList: [],
      workOrderReplyAssignedUserVOList: [],
      workOrderProcessorVOList: [],
      hiddenFormIdList: [],
      trendsWidgetRequireFormIdList: []
    }
  }
]);
const isMounted = useMounted();

const emit = defineEmits(["handleCreateWorkOrderSuccess"]);

const workOrderAllowOperation = computed(() => {
  return widgetFormDataList.value.map(form => form.id);
});

const getWorkOrderFormOperateMember = computed(() => {
  return (item: any, formId: string) => {
    const workOrderFormItem = item.dataList.workOrderProcessorVOList.find(
      workOrderForm => workOrderForm.formId === formId
    );
    const workOrderProcessorListData: any = storageLocal().getItem(
      "workOrderProcessorList"
    );

    let userList: any[] = [];
    if (
      workOrderProcessorListData &&
      workOrderFormItem?.collectUserVOList?.length > 0
    ) {
      const { sccsMemberList, sccsCoopTeamList } = workOrderProcessorListData;
      let personnelList = [];

      let sccsMember = [];
      for (let coopTeam of sccsCoopTeamList) {
        sccsMember = sccsMember.concat(coopTeam.teamMemberList);
      }

      sccsMember = sccsMember.concat(sccsMemberList);

      for (let personnel of workOrderFormItem.collectUserVOList) {
        if (Object.keys(personnel)[0] === "assignedTeamMemberId") {
          const userInfo = sccsMember.find(
            member => member.teamMemberId === personnel["assignedTeamMemberId"]
          );
          personnelList.push(
            Object.assign(userInfo, {
              user: true,
              coop: userInfo.showHand
            })
          );
        } else if (Object.keys(personnel)[0] === "assignedTeamId") {
          const teamInfo = sccsCoopTeamList.find(
            team => team.id === personnel["assignedTeamId"]
          );
          personnelList.push(Object.assign(teamInfo, { user: false }));
        }
      }
      userList = personnelList.map(member => {
        return {
          activate: member.activate,
          user: member.user,
          userAvatar: member.avatar,
          coopTeamUser: member.coop,
          email: member.account,
          teamName: member.teamName,
          shortName: member.teamShortName,
          userName: member.username,
          teamAvatar: member.teamAvatar
        };
      });
    } else {
      userList = [];
    }
    return userList;
  };
});

const createSuccessWorkOrderTip = computed(() => {
  return t("trade_workOrder_successTip", {
    params0: `<span class="create-work-order-span-text">${editableTabs.value.length}</span>`
  });
});

const createSuccessWorkOrderFooterTip = computed(() => {
  return t("trade_order_createWorkOrderFooterTip", {
    params0: `<span class='create-work-order-tip'>${editableTabs.value.length}</span>`
  });
});

const getWorkOrderFormAssigned = computed(() => {
  return replyAssign => {
    const collectVoList = replyAssign.dataList.workOrderProcessorVOList
      .filter(workOrder => workOrder.formProperty !== "HIDDEN")
      .flatMap(workOrderProcessor => workOrderProcessor.collectUserVOList);
    return Array.from(
      new Set(
        collectVoList.map(
          collect => collect.assignedTeamMemberId || collect.assignedTeamId
        )
      )
    ).length;
  };
});

const workOrderReplyAssigned = computed(() => {
  return replyAssign => {
    return replyAssign.dataList.workOrderReplyAssignedUserVOList;
  };
});

const msReplyAssigned = computed(() => {
  return replyAssign => {
    return replyAssign.dataList.msReplyAssignedUserVOList;
  };
});

const handleWidgetChange = (trendsWidgetList: any) => {
  // const editItem = editableTabs.value.find(
  //   editable => editable.name === drawerTabsValue.value
  // );
  // editItem.dataList.widgetItemDataList.push(...trendsWidgetList);
  if (!isMounted) {
    handleValidateWorkOrderDebounce(drawerTabsValue.value, true, false);
  }
};

const handleOpenMainFormDialog = () => {
  OrderMainFormRef.value.handleOpenMainFormVisible();
  mainFormVisible.value = !mainFormVisible.value;
};

const handleCreateWorkOrder = debounce(
  (): void => {
    handleCloseDialog();
    drawerVisible.value = false;
    CreateSuccessDialogRef.value.close();
    emit("handleCreateWorkOrderSuccess");
  },
  1000,
  { leading: true, trailing: false }
);

const handleCollectionSuccess = () => {
  handleCreateWorkOrder();
};

const handleAddWorkOrderPeronnel = (): void => {
  ChooseProcessorRef.value.open([], personnelType.value);
};

// 处理工单收集的防抖函数
const handleCollectionWorkOrder = debounce(
  async () => {
    // 从路由参数中获取sccsId和orderId
    const { sccsId, orderId } = route.query as any;
    // 根据sccsId和orderId获取订单详情
    const { data } = await getOrderDetail({
      sccsId: sccsId,
      orderId: orderId
    });
    // 从订单详情中解构出所需的数据
    const { widgetList, subWidgetMap, milestoneGroupList, linkedReferenceMap } =
      data;
    // 在里程碑组列表中查找匹配的里程碑数据
    const milestoneDataItem = milestoneGroupList.find(
      milestoneData => milestoneData.msId === msId.value
    );
    // 在里程碑的工单组列表中查找匹配的工单数据
    const workOrderData = milestoneDataItem.workOrderGroupList.find(
      workOrder => workOrder.workOrderId === thatWorkOrderId.value
    );
    // 转换提交数据结构
    const widgetData = TransformSubmitDataStructure(
      widgetList,
      subWidgetMap,
      linkedReferenceMap
    );
    // 打开工单收集对话框
    WorkOrderCollectionDialogRef.value.open(workOrderData);
  },
  1000,
  { leading: true, trailing: false }
);

const handleAllChooseConfirm = (data): void => {
  const editItem = editableTabs.value.find(
    editable => editable.name === drawerTabsValue.value
  );
  nextTick(() => {
    editItem.dataList.workOrderProcessorVOList.map(
      dataItem => (dataItem.collectUserVOList = data)
    );
    handleGetFormRequireWidget();
  });
};

const open = (milestoneData: any, mainFormData: any, sccsId: string) => {
  const { orderId } = route.query;
  msId.value = milestoneData.msId;
  mainForm.value = mainFormData;
  Promise.all([
    getWorkOrderStructure({
      sccsId: sccsId,
      milestoneId: msId.value,
      orderId: orderId as string
    }),
    getMileStoneWorkOrderCount({
      orderId: orderId,
      msId: msId.value
    }),
    getWorkOrderProcessorList({
      sccsId: sccsId,
      type: "UPDATE_PROCESSOR"
    })
  ]).then(resp => {
    const {
      milestoneReplyForm,
      workOrderFormList,
      workOrderReplyForm,
      needMilestoneReply,
      msReplyTeamMemberId,
      workOrderReplyTeamMemberId
    } = resp[0].data;

    WorkOrderFormTemplateList.value = workOrderFormList;
    replyMemberObject.value = {
      msReplyTeamMemberId: msReplyTeamMemberId,
      workOrderReplyTeamMemberId: workOrderReplyTeamMemberId
    };

    needReplyForm.value = {
      msReply: !!milestoneReplyForm,
      workOrderReply: !!workOrderReplyForm,
      needMsReply: needMilestoneReply
    };

    widgetFormDataList.value = [];
    widgetFormDataList.value.push(...workOrderFormList);
    operationalFactorData.value = getEntireFormData(msId.value, "");

    editableTabs.value = [
      {
        name: "1",
        dataList: {
          name: "1#",
          widgetItemDataList: [],
          msReplyAssignedUserVOList: [],
          workOrderReplyAssignedUserVOList: [],
          workOrderProcessorVOList: []
        }
      }
    ];
    drawerTabsValue.value = "1";

    initRenderWorkOrderStructureList();
    const countIndex = resp[1].data + 1;
    editableTabs.value[0].dataList.name = `${countIndex}#`;

    // 根据运营端配置初始化工单批复人和里程碑批复人信息
    if (needReplyForm.value.workOrderReply && workOrderReplyTeamMemberId) {
      editableTabs.value[0].dataList.workOrderReplyAssignedUserVOList = [
        {
          assignedTeamMemberId: workOrderReplyTeamMemberId
        }
      ];
    }
    if (
      needReplyForm.value.msReply &&
      needReplyForm.value.needMsReply &&
      msReplyTeamMemberId
    ) {
      editableTabs.value[0].dataList.msReplyAssignedUserVOList = [
        {
          assignedTeamMemberId: msReplyTeamMemberId
        }
      ];
    }

    storageLocal().setItem("workOrderProcessorList", {
      sccsMemberList: resp[2].data.teamMemberList,
      sccsCoopTeamList: resp[2].data.coopTeamList
    });

    drawerVisible.value = true;
    handleCalculateReplyFormList(milestoneReplyForm, workOrderReplyForm);
  });
};

/**
 * 默认计算里程碑批复表单和工单批复显隐表单
 * @param milestoneReplyForm 里程碑批复表单
 * @param workOrderReplyForm 工单批复表单
 */
const handleCalculateReplyFormList = (
  milestoneReplyForm: any,
  workOrderReplyForm: any
) => {
  let replyFormIds = [];
  if (milestoneReplyForm) {
    replyFormIds.push(milestoneReplyForm.id);
  }
  if (workOrderReplyForm) {
    replyFormIds.push(workOrderReplyForm.id);
  }
  const replyVisibleFormList =
    operationalFactorData.value.formVisibleTemplateFormConfigure.filter(
      relate => replyFormIds.includes(relate.formId)
    );

  let formVisibleTrendsObject = {};
  for (let visibleTemplateItem of replyVisibleFormList) {
    const formVisibleBoolean = ExecuteFormVisibleExpression(
      visibleTemplateItem.conditions,
      operationalFactorData.value
    );
    for (let widgetId of visibleTemplateItem.widgetIdList) {
      formVisibleTrendsObject[widgetId] = formVisibleBoolean;
    }
  }

  let msReplyWidgetList = [];
  let workOrderWidgetList = [];
  if (milestoneReplyForm) {
    msReplyWidgetList = calculateVisibleFormList(
      milestoneReplyForm.widgetJsonList,
      formVisibleTrendsObject
    );
  }

  if (workOrderReplyForm) {
    workOrderWidgetList = calculateVisibleFormList(
      workOrderReplyForm.widgetJsonList,
      formVisibleTrendsObject
    );
  }

  readonlyReplyForm.value = {
    milestoneReplyForm: milestoneReplyForm ? msReplyWidgetList : [],
    workOrderReplyForm: workOrderReplyForm ? workOrderWidgetList : []
  };
};

const handleChangeSwitch = (workOrderEdit: any, state: string) => {
  workOrderEdit.formProperty = state;
  const editTabData = editableTabs.value.find(
    editItem => editItem.name === drawerTabsValue.value
  );
  const formPropertyList = editTabData.dataList.workOrderProcessorVOList.map(
    edit => edit.formProperty
  );
  if (formPropertyList.every(element => element === "HIDDEN")) {
    ElMessage.error(t("trade_common_addWorkOrderTip"));
    workOrderEdit.formProperty = "DISPLAY";
    return;
  }

  editTabData.dataList.hiddenFormIdList =
    editTabData.dataList.workOrderProcessorVOList
      .filter(dataList => dataList.formProperty === "HIDDEN")
      .map(dataList => dataList.formId);
};

/**
 * 选择工单批复人
 * @param workOrderItemData
 * @param personnelList
 */
const handleReplyFormPersonnel = (
  workOrderItemData: any,
  personnelList: any
) => {
  workOrderItemData.workOrderReplyAssignedUserVOList = personnelList;
  handleValidateWorkOrderDebounce(drawerTabsValue.value, true, false);
};

const handleMsReplyFormPersonnel = (
  workOrderItemData: any,
  personnelList: any
) => {
  editableTabs.value = editableTabs.value.map(item => {
    return {
      ...item,
      dataList: {
        ...item.dataList,
        msReplyAssignedUserVOList: personnelList
      }
    };
  });
};

/**
 * 初始化当前里程碑的表单节点
 */
const initRenderWorkOrderStructureList = () => {
  let structureList = [];
  for (let workOrderFormTemplate of WorkOrderFormTemplateList.value) {
    structureList.push({
      formId: workOrderFormTemplate.id,
      formProperty: "DISPLAY",
      name: workOrderFormTemplate.name,
      collectUserVOList: []
    });
  }
  const index = editableTabs.value.length - 1;
  editableTabs.value[index].dataList.workOrderProcessorVOList = structureList;
};

const handleTabsEdit = async (
  targetName: TabPaneName | undefined | any,
  action: "remove" | "add" | "copy"
) => {
  if (action === "add") {
    const lastTabItem = editableTabs.value.slice(-1)[0];
    const lastTabDataList = lastTabItem.dataList;

    // 不能大于十个
    if (editableTabs.value.length > 9) {
      return false;
    }

    const dataListName = parseInt(lastTabDataList.name);
    const newTabName = `${++tabIndex}`;

    let msReplyAssignedUserVOList: any = [];
    let workOrderReplyAssignedUserVOList: any = [];
    const workOrderReplyTeamMemberId =
      replyMemberObject.value.workOrderReplyTeamMemberId;
    const msReplyTeamMemberId = replyMemberObject.value.msReplyTeamMemberId;

    if (needReplyForm.value.workOrderReply && workOrderReplyTeamMemberId) {
      workOrderReplyAssignedUserVOList = [
        {
          assignedTeamMemberId: workOrderReplyTeamMemberId
        }
      ];
    }
    if (
      needReplyForm.value.msReply &&
      needReplyForm.value.needMsReply &&
      msReplyTeamMemberId
    ) {
      msReplyAssignedUserVOList = [
        {
          assignedTeamMemberId: msReplyTeamMemberId
        }
      ];
    }

    editableTabs.value.push({
      name: newTabName,
      dataList: {
        name: `${dataListName + 1}#`,
        widgetItemDataList: [],
        msReplyAssignedUserVOList: msReplyAssignedUserVOList,
        workOrderReplyAssignedUserVOList: workOrderReplyAssignedUserVOList,
        workOrderProcessorVOList: [],
        hiddenFormIdList: [],
        trendsWidgetRequireFormIdList: []
      }
    });
    drawerTabsValue.value = newTabName;
    initRenderWorkOrderStructureList();
  } else if (action === "remove") {
    const tabs = editableTabs.value;
    let activeName = drawerTabsValue.value;

    if (activeName >= targetName) {
      tabs.forEach((tab, index) => {
        if (tab.name === targetName) {
          const nextTab = tabs[index + 1] || tabs[index - 1];
          if (nextTab) {
            activeName = nextTab.name;
          }
        }
      });
    }

    drawerTabsValue.value = activeName;
    editableTabs.value = tabs.filter(tab => tab.name !== targetName);
  } else if (action === "copy") {
    const currentTabIndex = editableTabs.value.findIndex(
      editable => editable.name === drawerTabsValue.value
    );
    const currentTabItem = editableTabs.value[currentTabIndex];
    const currentTabDataList = currentTabItem.dataList;
    const dataListName = parseInt(currentTabItem.dataList.name);
    const newTabName = `${++tabIndex}`;

    if (editableTabs.value.length > 9) {
      return false;
    }

    const widgetItemDataList =
      await TrendsAggregationFormRef.value[
        currentTabIndex
      ].handleGainWidgetFormData(false);
    const linkedReferenceSourceList =
      TrendsAggregationFormRef.value[currentTabIndex]
        .linkedRefrenceSourceDataLists;

    // if (!widgetItemDataList) return;

    const deduplicateWidgetItemDataList = deduplicateByWidgetId([
      ...currentTabDataList?.widgetItemDataList,
      ...widgetItemDataList
    ]);

    editableTabs.value.push({
      name: newTabName,
      dataList: {
        name: `${dataListName + 1}#`,
        widgetItemDataList: deduplicateWidgetItemDataList,
        linkedReferenceSaveList: linkedReferenceSourceList,
        msReplyAssignedUserVOList:
          currentTabDataList?.msReplyAssignedUserVOList,
        workOrderReplyAssignedUserVOList:
          currentTabDataList?.workOrderReplyAssignedUserVOList,
        workOrderProcessorVOList: cloneDeep(
          currentTabDataList?.workOrderProcessorVOList
        ),
        hiddenFormIdList: [...(currentTabDataList?.hiddenFormIdList || [])],
        trendsWidgetRequireFormIdList: [
          ...(currentTabDataList?.trendsWidgetRequireFormIdList || [])
        ]
      }
    });

    nextTick(async () => {
      drawerTabsValue.value = newTabName;
      nextTick(() => {
        handleGetFormRequireWidget();
      });
    });
  }
};

/**
 * 验证工单
 * @param tabName 标签页名称
 * @param msReplyFormBool 是否需要验证里程碑回复表单
 * @param isActiveVaildFailTab 是否激活失败标签页
 */
const handleValidateWorkOrder = async (
  tabName: any,
  msReplyFormBool: boolean,
  isActiveVaildFailTab: boolean = true
) => {
  // 查找当前工单在可编辑标签页中的索引
  const index = editableTabs.value.findIndex(editData =>
    isEqual(editData.name, tabName)
  );
  const dataList = editableTabs.value[index].dataList;

  // 获取当前激活标签页的名称
  const editActiveName = editableTabs.value[index].name;

  const handleHasError = (message: string) => {
    tabsValiFailList.value.add(tabName);
    if (isActiveVaildFailTab) {
      drawerTabsValue.value = editActiveName;
      ElMessage.error(message);
    }
  };

  // 验证工单名称是否为空
  if (!dataList.name) {
    handleHasError(t("trade_workorder_nameIsNull"));
    return false;
  }

  // 验证是否选择了处理人
  const workOrderProcessorVOList = dataList.workOrderProcessorVOList;
  const hasWorkOrderProcessVoList = workOrderProcessorVOList.filter(
    workOrderProcessVo => {
      return workOrderProcessVo.collectUserVOList.length !== 0;
    }
  );
  if (hasWorkOrderProcessVoList.length === 0) {
    handleHasError(t("trade_order_workOrderChooseProcessorTip"));
    return false;
  }

  // 验证工单回复审批人是否选择
  if (
    needReplyForm.value.workOrderReply &&
    dataList.workOrderReplyAssignedUserVOList.length === 0
  ) {
    handleHasError(t("trade_order_workOrderApproverTip"));
    return false;
  }

  // 验证里程碑回复审批人是否选择
  if (
    needReplyForm.value.msReply &&
    needReplyForm.value.needMsReply &&
    dataList.msReplyAssignedUserVOList.length === 0 &&
    msReplyFormBool
  ) {
    handleHasError(t("trade_order_MilestoneApproverTip"));
    return false;
  }

  // 验证工单表单是否校验通过
  const validateResult =
    await TrendsAggregationFormRef.value[index].handleValidate();
  if (validateResult !== true) {
    handleHasError(t("common_biz_form_valid_fail"));
    return false;
  }

  tabsValiFailList.value.delete(tabName);
  return true;
};

const handleValidateWorkOrderDebounce = debounce(
  handleValidateWorkOrder,
  1000,
  { leading: false, trailing: true }
);

watch(drawerTabsValue, async (newVal, oldVal) => {
  const validateResult = await handleValidateWorkOrder(oldVal, true, false);
  if (!validateResult) {
    tabsValiFailList.value.add(oldVal);
  } else {
    tabsValiFailList.value.delete(oldVal);
  }
});

const handleCloseDialog = () => {
  drawerVisible.value = false;
  mainFormVisible.value = false;
};

/**
 * 选择工单表单采集人
 * @param workOrderItemData
 * @param personnelList
 */
const handleChangePersonnel = (workOrderItemData: any, personnelList: any) => {
  workOrderItemData.collectUserVOList = personnelList;
  handleGetFormRequireWidget();
  handleValidateWorkOrderDebounce(drawerTabsValue.value, true, false);
};

const handleGetFormRequireWidget = () => {
  const editItem = editableTabs.value.find(
    editable => editable.name === drawerTabsValue.value
  );
  editItem.dataList.trendsWidgetRequireFormIdList =
    editItem.dataList.workOrderProcessorVOList
      .filter(form => form.collectUserVOList.length > 0)
      .map(form => form.formId);
  editItem.dataList.hiddenFormIdList =
    editItem.dataList.workOrderProcessorVOList
      .filter(dataList => dataList.formProperty === "HIDDEN")
      .map(dataList => dataList.formId);
};

const deduplicateByWidgetId = arr => {
  const seen = new Map();
  return arr.filter(item => {
    if (!seen.has(item.widgetId)) {
      seen.set(item.widgetId, true);
      return true;
    }
    return false;
  });
};

const handleConfirm = async () => {
  let dataWidgetList = [];
  const editableTabDataList = cloneDeep(editableTabs.value);

  for (let i = 0, len = editableTabDataList.length; i < len; i++) {
    const formEl = cloneDeep(editableTabs.value[i]);
    // 反馈说这里需要检验所有表单的批复人
    if (!(await handleValidateWorkOrder(formEl.name, true, true))) {
      return false;
    }

    const widgetItemDataList =
      await TrendsAggregationFormRef.value[i].handleGainWidgetFormData(true);

    formEl.dataList.linkedReferenceSaveList =
      TrendsAggregationFormRef.value[i].linkedRefrenceSourceDataLists;

    if (!widgetItemDataList) return false;
    formEl.dataList.widgetItemDataList = deduplicateByWidgetId([
      ...widgetItemDataList,
      ...formEl.dataList.widgetItemDataList
    ]);

    let formElObject = {
      name: formEl.dataList.name,
      workOrderProcessorVOList: formEl.dataList.workOrderProcessorVOList,
      workOrderReplyAssignedUserVOList:
        formEl.dataList.workOrderReplyAssignedUserVOList,
      widgetItemDataList: formEl.dataList.widgetItemDataList,
      linkedReferenceSaveList: formEl.dataList.linkedReferenceSaveList
    };
    if (needReplyForm.value.needMsReply) {
      formElObject["msReplyAssignedUserVOList"] =
        formEl.dataList.msReplyAssignedUserVOList;
    }
    dataWidgetList.push(formElObject);
  }
  const { sccsId, orderId } = route.query;
  const { code, data } = await createWorkOrder({
    sccsId: sccsId,
    orderId: orderId,
    msId: msId.value,
    dataList: dataWidgetList
  });
  if (code === 0) {
    const userInfo = storageLocal().getItem("user-info") as any;
    containMineWorkOrderFlag.value = dataWidgetList.some((dataRow, dataIndex) =>
      dataRow.workOrderProcessorVOList?.some(workOrderVo =>
        workOrderVo.collectUserVOList.some(collectVo => {
          const flag =
            collectVo.assignedTeamMemberId === userInfo.latestLoginTeamMemberId;
          if (flag) {
            thatWorkOrderId.value = data[dataIndex]; // 第一个自己有权限编辑表单的工单
          }
          return flag;
        })
      )
    );
    CreateSuccessDialogRef.value.open();
  }
};

onMounted(() => {
  emitter.on("widgetUploadLoading", (isUploading: boolean) => {
    btnOperationState.value = isUploading;
  });
});

defineExpose({
  open
});
</script>
<style lang="scss" scoped>
@use "./style/index.scss";

.el-collapse-item {
  margin-bottom: 19px;
  border: 1px solid #e6eaf0;
  border-radius: 4px;

  ::v-deep(.el-collapse-item__header) {
    padding: 0 15px;
    background: #fdf5ed;

    .collapse-form-item-title {
      font-size: 14px;
      font-weight: bolder;
      color: #262626;
    }
  }

  ::v-deep(.el-collapse-item__content) {
    padding: 20px;

    .el-form-item {
      padding-top: 4px !important;
      margin-bottom: 8px !important;

      .widget-form-item-label {
        display: inline-flex;
        align-items: center;

        .widget-form-item-title {
          margin-left: 5px;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          color: #262626;

          .widget-required {
            color: red;
          }
        }

        .widget-form-item-label-icon {
          font-size: 12px;
          font-weight: 400;
          color: #262626;
          cursor: pointer;
        }
      }

      .el-form-item__content {
        .el-form-item__error {
          position: relative;
          margin-top: 2px;
          color: red;
        }

        .el-input-number {
          width: 100% !important;
        }
      }
    }

    .el-form-item__label {
      margin-bottom: 0 !important;
    }
  }
}
</style>
<style lang="scss">
.create-work-order-drawer-container {
  padding: 0 !important;

  .create-work-order-tabs {
    height: 100%;

    .el-tabs__header {
      display: flex;
      align-items: center;
      justify-content: inherit;
      height: 52px;
      margin: 0 !important;
      background: #fff;

      .el-tabs__nav-wrap {
        flex: none !important;
        max-width: calc(100% - 300px);
        padding: 0 28px !important;

        .el-tabs__nav-scroll {
          margin-top: -1px;
        }

        .el-tabs__nav-prev,
        .el-tabs__nav-next {
          z-index: 2;
          width: 29px;
          height: 45px;
          margin-top: 4px;

          .el-icon {
            width: 1.5em;
            height: 1.5em;

            svg {
              width: 100%;
              height: 100%;
            }
          }
        }

        .el-tabs__nav-prev {
          border-right: 1px solid #e4e7ed;
        }

        .el-tabs__nav-next {
          border-left: 1px solid #e4e7ed;
        }

        .el-tabs__item {
          height: 45px !important;
          padding: 0 8px 0 12px;
          border-radius: 0;

          .create-work-header {
            .create-work-order-text {
              font-size: 14px;
              font-weight: bolder;
              color: #262626;
              text-align: left;
            }

            .create-work-icon {
              display: inline-block;
              width: 22px;
              height: 22px;
              margin-left: 4px;
              font-size: 14px;
              line-height: 22px;
              color: #808080;
              text-align: center;
              border-radius: 4px;

              &:hover {
                background: #e5e5e5;
              }

              &.disabled {
                cursor: default;
                opacity: 0.4;
              }
            }

            .error-icon {
              color: #e61b1b;

              &:hover {
                background: transparent;
              }
            }
          }

          &.is-active {
            background: #f6f6f6;
            border-bottom: 1px solid #f6f6f6;

            .create-work-header {
              .create-work-order-text {
                color: #0070d2;
              }

              .create-work-icon {
                color: #808080;
              }

              .error-icon {
                color: #e61b1b;
              }
            }
          }
        }
      }

      .el-tabs__new-tab {
        position: relative;
        display: flex;
        flex: 1;
        margin: 0;
        border: 0 none;

        .add-work-order-tabs-header {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: space-between;
          width: 100% !important;
          padding: 0 20px;

          .iconfont {
            font-size: 14px;
          }

          .create-work-order-tabs-container {
            display: flex;
            flex: 1;
            align-items: center;

            .create-work-order-tabs-left {
              display: flex;
              flex: 1;
              justify-content: right;
              margin-right: 20px;

              .create-work-order-tabs-first-col {
                display: inline-flex;
                align-items: center;
                justify-content: end;
                // min-width: 100px;
                height: 32px;
                padding: 0 10px;
                margin-right: 20px;
                border-radius: 4px;

                .create-work-order-tabs-text {
                  margin-left: 6px;
                  font-size: 14px;
                  line-height: 20px;
                  color: #595959;
                  text-align: left;
                }

                &:hover {
                  background: #e5e5e5;
                }

                .iconfont {
                  font-size: 13px;
                  color: #595959;
                }
              }

              .create-work-order-tabs-col {
                width: 32px;
                height: 32px;
                line-height: 32px;
                border-radius: 4px;

                .iconfont {
                  font-size: 16px;
                  color: #808080;
                }

                &:hover {
                  background: #e5e5e5;
                }

                &.create-work-order-active {
                  background: #e1edff;

                  .iconfont {
                    color: #0070d2;
                  }
                }
              }
            }

            .create-work-order-tabs-right {
              position: relative;
              width: 20px;
              height: 20px;
              border-radius: 4px;

              &::before {
                position: absolute;
                top: 0;
                left: -10px;
                display: inline-block;
                width: 1px;
                height: 20px;
                content: "";
                background: #e5e5e5;
              }

              .iconfont {
                font-size: 12px;
              }

              &:hover {
                background: #e5e5e5;

                .iconfont {
                  color: #808080;
                }
              }
            }
          }

          &:hover {
            color: #808080;
          }
        }
      }
    }

    .el-tabs__content {
      padding: 10px 11px;
      background: #f6f6f6;

      .el-tab-pane {
        height: 100%;

        .create-work-body {
          display: flex;
          height: 100%;
          overflow: auto;

          .create-work-body-left {
            width: 358px;
            min-width: 358px;
            padding: 20px 14px;
            background: #fff;
            border-radius: 4px;

            .create-work-order-form-container {
              margin-bottom: 16px;

              .create-work-order-title {
                margin-bottom: 10px;
                font-size: 14px;
                font-weight: bolder;
                line-height: 16px;
                text-align: left;

                &::before {
                  margin-right: 6px;
                  color: #e61b1b;
                  content: "*";
                }
              }
            }

            .create-work-order-form-personnel-body {
              padding: 8px;
              margin-bottom: 16px;
              background: #f4f5fa;
              border-radius: 4px;

              .create-work-order-form-personnel {
                padding: 5px 7px;
                margin-bottom: 8px;
                background: linear-gradient(#f7faff, #fff);
                border: 1px solid #e9eaed;
                border-radius: 4px;

                .create-work-order-form-top {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;

                  .create-work-order-form-top-left {
                    overflow: hidden;
                    font-size: 13px;
                    font-weight: bolder;
                    line-height: 14px;
                    color: #262626;
                    text-align: left;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    &.create-work-order-form-title-require {
                      &::before {
                        margin-right: 5px;
                        color: #e61b1b;
                        content: "*";
                      }
                    }

                    .create-work-order-form-tip {
                      font-size: 12px;
                      color: #797979;
                    }
                  }

                  .create-work-order-form-top-right {
                    .create-work-order-form-item {
                      width: 30px;
                      height: 24px;
                      text-align: center;
                      cursor: pointer;
                      border-radius: 4px;

                      .iconfont {
                        font-size: 16px;
                        font-weight: normal;
                        color: #8c8c8c;
                      }

                      &:hover {
                        background: #e5e5e5;
                      }
                    }

                    .work-order-tooltip-body {
                      display: flex;
                      align-items: center;

                      .work-order-tooltip-text {
                        margin-right: 5px;
                        font-size: 12px;
                        color: #f6974f;
                        text-align: center;
                      }

                      .iconfont {
                        font-size: 13px;
                        color: #808080;
                        cursor: pointer;
                      }
                    }
                  }
                }

                .create-work-order-form-bottom {
                  .work-order-peronnel-container-text {
                    display: inline-block;
                    margin-top: 10px;
                    font-size: 14px;
                    line-height: 24px;
                    color: #bfbfbf;
                    text-align: left;
                  }
                }

                &:last-child {
                  margin-bottom: 0;
                }
              }

              .create-work-order-form-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 10px;

                .create-work-order-form-left {
                  display: flex;
                  font-size: 12px;
                  font-weight: bolder;
                  color: #0070d2;
                  text-align: left;
                  cursor: pointer;

                  .iconfont {
                    margin-right: 2px;
                    font-size: 13px;
                  }
                }

                .create-work-order-form-right {
                  .work-order-tooltip-text {
                    margin-right: 5px;
                    font-size: 12px;
                    color: #f6974f;
                    text-align: center;
                  }

                  .iconfont {
                    font-size: 13px;
                    color: #808080;
                    cursor: pointer;
                  }
                }
              }
            }
          }

          .create-work-body-right {
            flex: 1;
            margin-left: 17px;

            .dialog-content-flex-right-header {
              margin: 15px 0 11px;
              font-size: 12px;
              font-weight: bolder;
              line-height: 17px;
              color: #262626;
              text-align: left;
            }
          }
        }
      }
    }
  }
}

.create-work-order-drawer-footer {
  display: flex;

  .create-work-order-footer-text {
    margin-right: 50px;
    font-size: 14px;
    line-height: 14px;
    color: #8c8c8c;
    text-align: left;

    .create-work-order-tip {
      padding: 0 5px;
      font-weight: bolder;
      color: #262626;
    }
  }
}
</style>
