<template>
  <div :class="['login-layout', langValue === 'en' ? 'login-layout-en' : '']">
    <!-- 全屏轮播背景 -->
    <div class="carousel-background">
      <el-carousel :interval="5000" autoplay arrow="never" height="100vh">
        <el-carousel-item
          v-for="(slide, index) in carouselSlides"
          :key="index"
          class="carousel-item"
        >
          <!-- 背景图片 -->
          <div
            class="slide-background"
            :style="{ backgroundImage: `url(${slide.image})` }"
          >
            <!-- 内容容器 -->
            <div class="slide-content-container">
              <div class="slide-content">
                <h1 class="slide-title">{{ t(slide.title) }}</h1>
                <p class="slide-subtitle">{{ t(slide.subtitle) }}</p>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 右上角多语言切换 -->
    <div class="language-switch">
      <LKSwitchLanuage />
    </div>

    <!-- 表单容器插槽 -->
    <div class="form-container">
      <slot name="form" />
    </div>

    <!-- 底部备案号 -->
    <div class="footer-filings">
      <div class="filings-content">
        <div v-show="langValue !== 'en'" class="filing-number">
          闽ICP备17004372号
        </div>
        <div class="copyright">
          &#169; Xiamen Daren Supply Chain Management Co., Ltd.
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import LKSwitchLanuage from "@/components/lKSwitchLanguage";
import { emitter } from "@/utils/mitt";

// 导入新的轮播图片
import slideImage1 from "@/assets/login/login-slide-1.png";
import slideImage2 from "@/assets/login/login-slide-2.png";
import slideImage3 from "@/assets/login/login-slide-3.png";
import { storageLocal } from "@pureadmin/utils";

const { t } = useI18n();
const translationLang = storageLocal().getItem("translationLang");
const langValue = ref<string>(translationLang?.toString());

// 轮播数据
const carouselSlides = [
  {
    image: slideImage1,
    title: "trade_login_sloganOne",
    subtitle: "trade_login_sloganDescribeOne"
  },
  {
    image: slideImage2,
    title: "trade_login_sloganTwo",
    subtitle: "trade_login_sloganDescribeTwo"
  },
  {
    image: slideImage3,
    title: "trade_login_sloganThree",
    subtitle: "trade_login_sloganDescribeThree"
  }
];

onMounted(() => {
  emitter.on("changeLanguage", (lang: string) => {
    langValue.value = lang;
  });
});
</script>

<style lang="scss" scoped>
// 响应式适配
@media screen and (width <= 1366px) {
  .slide-content-container {
    width: 100%;
    max-width: 1000px;
    padding-left: 60px;
  }

  .slide-content {
    .slide-title {
      font-size: 36px;
    }

    .slide-subtitle {
      font-size: 16px;
    }
  }

  .form-container {
    width: 450px;
  }
}

// @media screen and (max-width: 768px) {
@media screen and (width <= 1023px) {
  .carousel-background {
    ::v-deep(.el-carousel) {
      display: none;
    }
  }

  .slide-content-container {
    padding-right: 40px;
    padding-left: 40px;
  }

  .slide-content {
    .slide-title {
      font-size: 28px;
    }

    .slide-subtitle {
      font-size: 14px;
    }
  }

  .form-container {
    width: 90vw;
    transform: translate(-50%, -50%);
  }

  .language-switch {
    top: 20px;
    right: 20px;
  }

  .footer-filings {
    bottom: 16px;

    .filings-content {
      flex-direction: column;
      gap: 8px;

      .filing-number,
      .copyright {
        font-size: 12px;
      }
    }
  }
}

.login-layout {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

// 轮播背景
.carousel-background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: #e9eefd;

  ::v-deep(.el-carousel) {
    width: 100%;
    height: 100%;

    .el-carousel__container {
      width: 100%;
      height: 100%;
    }

    .carousel-item {
      width: 100%;
      height: 100%;
    }

    // 轮播指示器样式
    .el-carousel__indicators--horizontal {
      position: fixed;
      top: 50%;
      left: 50%;
      z-index: 1000;
      transform: translate(-370px, 320px);

      .el-carousel__indicator--horizontal {
        display: inline-block;
        width: 8px;
        height: 8px;
        padding: 0;
        margin-right: 16px;
        background: #cadafa;
        border-radius: 50%;

        &.is-active {
          width: 15px;
          height: 8px;
          background: #8cb0fa;
          border-radius: 4px;
        }

        .el-carousel__button {
          width: 0;
        }
      }
    }
  }
}

// 轮播项样式
.slide-background {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: auto;
}

// 内容容器 1200x640
.slide-content-container {
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  width: 1280px;
  height: 640px;
  padding-left: 100px;
}

.slide-content {
  max-width: 600px;

  .slide-title {
    margin-top: 44px;
    font-family: "PingFang SC", "PingFang SC-Semibold";
    font-size: 36px;
    font-weight: semibold;
    line-height: 50px;
    color: #333;
    color: transparent;
    text-align: left;
    // 渐变文字样式
    background: linear-gradient(146deg, #4851e5 33%, #935ad8 87%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .slide-subtitle {
    margin: 0;
    font-family: "PingFang SC", "PingFang SC-Regular";
    font-size: 20px;
    line-height: 36px;
    color: #262626;
    text-align: left;
  }
}

// 右上角多语言切换
.language-switch {
  position: fixed;
  top: 32px;
  right: 32px;
  z-index: 1000;
}

// 表单容器（居中向右偏移）
.form-container {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 100;
  width: 500px;
  max-width: 90vw;
  transform: translate(10%, -50%); // 居中向右偏移
}

// 底部备案号
.footer-filings {
  position: fixed;
  right: 0;
  bottom: 24px;
  left: 0;
  z-index: 1000;

  .filings-content {
    display: flex;
    gap: 40px;
    align-items: center;
    justify-content: center;

    .filing-number,
    .copyright {
      font-size: 14px;
      line-height: 20px;
      color: #808080;
    }
  }
}

.login-layout-en {
  .slide-content {
    max-width: 560px;

    .slide-title {
      margin-top: 20px;
      font-size: 32px;
    }

    .slide-subtitle {
      font-size: 16px;
    }
  }
}
</style>
