<template>
  <el-card
    class="fixed-card"
    :class="[cardShadow === 'never' ? 'noBorder' : '']"
    style="width: 100%"
    :shadow="cardShadow"
  >
    <template v-if="!noHeader" #header>
      <div class="fixed-card-header">
        <span class="fixed-card-title">{{ dialogTitle }}</span>
        <span class="fixed-card-close" @click="closeCard"
          ><i class="iconfont link-close"
        /></span>
      </div>
    </template>
    <div v-if="type === 'MILESTONE'" class="fixed-card-body-header">
      <div class="fixed-card-col" style="margin-right: 24px">
        <span class="fixed-card-col-title">{{
          t("trade_template_creator")
        }}</span>
        <LkAvatar
          :teamInfo="{
            avatar: milestoneDetail?.createUserAvatar,
            username: milestoneDetail?.createUserName
          }"
          :size="22"
        />
      </div>
      <div class="fixed-card-col">
        <span class="fixed-card-col-title">
          {{ t("trade_order_milestoneApprover") }}
        </span>
        <LkAvatarGroup
          :size="22"
          :avatarList="milestoneDetail.msReplyUser"
          :maxAvatar="4"
        />
      </div>
    </div>
    <div v-else class="fixed-card-body-header">
      <OrderDetailPersonnel
        :workOrder="currentWorkOrderInfo"
        :hidden-time="true"
      />
    </div>
    <div class="fixed-card-body-dynamics">
      <div class="milestone-dynamics-body">
        <el-radio-group v-model="radioValue" style="padding-left: 18px">
          <el-radio-button
            v-for="(radioItem, index) in radioOptions"
            :key="index"
            :label="radioItem.label"
            :value="radioItem.value"
          />
        </el-radio-group>
        <el-scrollbar>
          <div class="milestone-dynamics-content">
            <el-empty
              v-if="dynamicListFliter.length === 0"
              :description="t('trade_common_emptyTip')"
              :image-size="60"
            />
            <el-row
              v-for="(item, index) in dynamicListFliter"
              :key="index"
              class="dynamics-item"
            >
              <div class="dynamics-item-left">
                <!-- 创建工单 -->
                <div
                  v-if="item.operateType === 'CREATE_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-add" />
                </div>
                <!-- 导入工单 -->
                <div
                  v-if="item.operateType === 'IMPORT_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-import" />
                </div>
                <!-- 编辑工单 -->
                <div
                  v-if="item.operateType === 'EDIT_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-edit" />
                </div>
                <!-- 批复工单 -->
                <div
                  v-if="item.operateType === 'REPLY_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-reply" />
                </div>
                <!-- 工单操作 -->
                <!-- 修改采集人 -->
                <div
                  v-if="item.operateType === 'UPDATE_COLLECT_USER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-modify-processor" />
                </div>
                <!-- 修改批复人 -->
                <div
                  v-if="item.operateType === 'UPDATE_REPLY_USER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-modify-processor" />
                </div>
                <!-- 协作方指派采集 -->
                <div
                  v-if="item.operateType === 'ASSIGN_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-distribute" />
                </div>
                <!-- 协作方指派批复 -->
                <div
                  v-if="item.operateType === 'ASSIGN_WORK_ORDER_REPLY'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-reply" />
                </div>
                <!-- 协作方修改采集人 -->
                <div
                  v-if="item.operateType === 'COOP_UPDATE_COLLECT_USER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-modify-processor" />
                </div>
                <!-- 协作方修改批复人 -->
                <div
                  v-if="item.operateType === 'COOP_UPDATE_REPLY_USER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-modify-processor" />
                </div>
                <!-- 终止采集 -->
                <div
                  v-if="item.operateType === 'STOP_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-zhongzhi" />
                </div>
                <!-- 工单互用于 -->
                <div
                  v-if="item.operateType === 'CLONE_TO_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-shared-in" />
                </div>
                <!-- 取消互用 -->
                <div
                  v-if="item.operateType === 'CANCEL_CLONE_TO_WORK_ORDER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-cancelled" />
                </div>
                <!-- 创建里程碑批复 -->
                <div
                  v-if="item.operateType === 'CREATE_MILESTONE_REPLY'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-add" />
                </div>
                <!-- 编辑里程碑批复 -->
                <div
                  v-if="item.operateType === 'EDIT_MILESTONE_REPLY'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-edit" />
                </div>
                <!-- 修改里程碑批复人 -->
                <div
                  v-if="item.operateType === 'UPDATE_MILESTONE_REPLY_USER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-modify-processor" />
                </div>
                <!-- 协作方指派里程碑批复 -->
                <div
                  v-if="item.operateType === 'ASSIGN_MILESTONE_REPLY'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-distribute" />
                </div>
                <!-- 协作方修改里程碑批复人 -->
                <div
                  v-if="item.operateType === 'COOP_UPDATE_MILESTONE_REPLY_USER'"
                  class="dynamics-icon"
                >
                  <i class="iconfont link-edit" />
                </div>
                <div
                  v-if="item.operateType === 'COMMENT'"
                  class="dynamics-avtar"
                >
                  <LkAvatar
                    :size="32"
                    :teamInfo="{
                      avatar: item.creatorAvatar,
                      username: item.creatorName
                    }"
                  />
                </div>
                <div
                  v-if="item.operateType !== 'COMMENT'"
                  class="dynamics-content"
                  :class="[item.historyMark ? 'history' : '']"
                >
                  {{ item.dynamicContent }}
                </div>
                <div
                  v-if="item.operateType === 'COMMENT'"
                  class="dynamics-content"
                >
                  <div class="dynamics-name">{{ item.creatorName }}</div>
                  <div v-if="item.remindDataList.length > 0" class="">
                    <span
                      v-for="(remind, index) in item.remindDataList"
                      :key="index"
                      class="dynamics-remind"
                      >{{ `@${remind.username || remind.teamName}` }}</span
                    >
                    {{ item.content }}
                  </div>
                  <div v-else class="">{{ item.content }}</div>
                </div>
              </div>
              <div class="dynamics-item-right">
                <span
                  v-if="item.historyMark"
                  class="dynamics-item-btn"
                  @click="openHistoryDialog(item)"
                  >{{ t("trade_view_history") }}</span
                >
                <span>{{ getTimeLineStampFormat(item.createTime) }}</span>
              </div>
            </el-row>
          </div>
        </el-scrollbar>
        <div class="milestone-dynamics-remind-box">
          <el-popover
            ref="popoverRef"
            width="276"
            :virtual-ref="btnRef"
            virtual-triggering
            placement="top-start"
            :offset="12"
            teleported
            trigger="click"
          >
            <template #default>
              <div class="popover-body">
                <div class="popover-search">
                  <el-input
                    v-model="searchValue"
                    :prefix-icon="Search"
                    :placeholder="t('trade_common_search')"
                  />
                </div>
                <div class="member-content">
                  <el-scrollbar>
                    <el-checkbox
                      v-if="searchValue === ''"
                      v-model="checkAll"
                      :indeterminate="isIndeterminate"
                      @change="handleCheckAllChange"
                    >
                      <LkAvatar
                        :size="32"
                        :teamInfo="{
                          avatar: '',
                          username: t('trade_all')
                        }"
                      />
                      {{ t("trade_all") }}
                    </el-checkbox>
                    <el-checkbox-group
                      v-model="checkList"
                      @change="handleCheckedChange"
                    >
                      <el-checkbox
                        v-for="(checkItem, index) in currentAllMembersFilter"
                        :key="index"
                        :label="checkItem.value"
                        :value="checkItem.label"
                      >
                        <LkAvatar
                          class="avatar-col"
                          :shape="
                            checkItem.remindType === 'person'
                              ? 'circle'
                              : 'square'
                          "
                          :class="[
                            checkItem.remindType === 'person'
                              ? ''
                              : 'colorOrange'
                          ]"
                          :size="32"
                          :teamInfo="{
                            avatar: checkItem.avatar,
                            username: checkItem.value,
                            coop: checkItem.coopTeamUser || false,
                            email: checkItem.email
                          }"
                        />
                        <ReText type="info" :tippyProps="{ delay: 50000 }">
                          {{
                            `${checkItem.value}${checkItem.email ? `(${checkItem.email})` : ""}`
                          }}
                        </ReText>
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-scrollbar>
                </div>
              </div>
            </template>
          </el-popover>
          <span
            ref="btnRef"
            v-popover="popoverRef"
            v-click-outside="onClickOutside"
            class="remind-btn"
            style="cursor: pointer"
            :class="popoverVisible ? 'active' : ''"
            @click="popoverVisible = !popoverVisible"
            >{{ `@${t("trade_team_members")}` }}</span
          >
          <Transition
            v-for="(checkItem, index) in checkList"
            :key="index"
            leave-active-class="animate__animated animate__fadeOut animate__faster"
            enter-active-class="animate__animated animate__fadeIn animate__faster"
          >
            <!-- eslint-disable-next-line vue/require-toggle-inside-transition -->
            <span class="remind-btn">
              {{
                currentAllMembers.find(e => e.label === checkItem).coopTeamUser
                  ? currentAllMembers.find(e => e.label === checkItem).value +
                    "(" +
                    currentAllMembers.find(e => e.label === checkItem)
                      .shortName +
                    ")"
                  : currentAllMembers.find(e => e.label === checkItem).value
              }}
              <span class="fixed-btn-close" @click="deleteRemindBtn(checkItem)">
                <i class="iconfont link-clean" />
              </span>
            </span>
          </Transition>
        </div>
        <div class="milestone-dynamics-send">
          <el-input
            ref="mentionRef"
            v-model="sendMessage"
            resize="none"
            :disabled="isDisabled"
            type="textarea"
            :placeholder="`${t('trade_comment_dynamics')}@${t('trade_common_teamOrMember')}`"
          />
          <span
            class="milestone-dynamics-send-btn"
            :class="[sendMessage === '' ? 'disabled' : '']"
            @click="handleSendMessage"
          >
            {{ t("trade_send_out") }}
          </span>
        </div>
      </div>
    </div>
  </el-card>
  <OrderHistoryRecordDialog
    ref="OrderHistoryRecordDialogRef"
    :type="historyDialogType"
    :historyRecords="historyRecords"
    :currentVersion="currentVersion"
    :currentMainWorkOrderId="
      historyDialogType === 'MILE_STONE'
        ? msReplyId
        : currentWorkOrderInfo.workOrderId
    "
    :widgetForm="milestoneCardForm"
    :workOrderForm="workOrderForm"
  />
</template>
<script lang="ts" setup>
import { ref, unref, computed, watch } from "vue";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import { ElMessage, ClickOutside as vClickOutside } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { ReText } from "@/components/ReText";
import LkAvatar from "@/components/lkAvatar/index";
import LkAvatarGroup from "@/components/lkAvatarGroup/index";
import OrderHistoryRecordDialog from "@/views/history/OrderHistoryRecordDialog.vue";
import OrderDetailPersonnel from "@/views/orderDetail/components/OrderDetailBaseComponents/OrderDetailPersonnel.vue";
import {
  getWorkOrderDynamicList,
  addDynamic,
  getWorkOrderHistoryList
} from "@/api/history";

const radioValue = ref("2");
const { t } = useI18n();
const emit = defineEmits(["handleClose"]);
const props = defineProps({
  msReplyId: {
    type: String as PropType<string>,
    default: () => ""
  },
  currentAllMembers: {
    type: Array as PropType<any>,
    default: () => []
  },
  currentWorkOrderInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneCardForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  milestoneDetail: {
    type: Object as PropType<any>,
    default: () => {}
  },
  type: {
    type: String as PropType<any>,
    default: () => ""
  },
  noHeader: {
    type: Boolean as PropType<boolean>,
    default: () => false
  },
  cardShadow: {
    type: String as PropType<string>,
    default: () => "always"
  },
  workOrderForm: {
    type: Array as PropType<any>,
    default: () => []
  }
});

const OrderHistoryRecordDialogRef = ref<HTMLElement | any>(null);
const btnRef = ref();
const currentVersion = ref(0);
const historyDialogType = ref("");
const historyRecords = ref([]);
const currentAllMembersFilter = computed(() => {
  if (searchValue.value === "") {
    return props.currentAllMembers;
  }
  return props.currentAllMembers.filter((item: any) => {
    const searchString = item.value + item.email;
    return (
      searchString.toLowerCase().indexOf(searchValue.value.toLowerCase()) !== -1
    );
  });
});

const radioOptions = [
  {
    label: t("trade_order_dynamics"),
    value: "1"
  },
  {
    label: t("trade_comment_only"),
    value: "2"
  },
  {
    label: t("trade_only_modify_history"),
    value: "3"
  }
];

const dynamicList = ref([]);
const dynamicListFliter = computed(() => {
  let res = [];
  if (radioValue.value === "1") {
    res = dynamicList.value;
  } else if (radioValue.value === "2") {
    res = dynamicList.value.filter((item: any) => {
      return item.operateType === "COMMENT";
    });
  } else {
    res = dynamicList.value.filter((item: any) => {
      return item.historyMark === true;
    });
  }
  return res;
});
const sendMessage = ref("");
// 当前发送评论@人员id集合
const mentionIds = ref([]);
const mentionRef = ref();
const searchValue = ref("");
const popoverRef = ref();
const popoverVisible = ref(false);
const checkAll = ref(false);
const isIndeterminate = ref(true);
const checkList = ref([]);

const isDisabled = computed(() => {
  if (
    props.currentWorkOrderInfo?.cloneSourceId !== "" &&
    props.currentWorkOrderInfo?.cloneSourceId !== null
  ) {
    return true;
  } else {
    return false;
  }
});

const onClickOutside = () => {
  popoverVisible.value = false;
  unref(popoverRef).popperRef?.delayHide?.();
};

const handleCheckAllChange = val => {
  checkList.value = val ? currentAllMembersFilter.value.map(e => e.label) : [];
  isIndeterminate.value = false;
};
const handleCheckedChange = checkedValue => {
  checkAll.value = checkedValue.length === currentAllMembersFilter.value.length;
  isIndeterminate.value =
    checkedValue.length > 0 &&
    checkedValue.length < currentAllMembersFilter.value.length;
};

const deleteRemindBtn = (item: any) => {
  checkList.value.splice(checkList.value.indexOf(item), 1);
};

const dialogTitle = computed(() => {
  if (props.type === "WORK_ORDER") {
    return `${t("trade_order_dynamic")}_${props.currentWorkOrderInfo.workOrderName}`;
  } else {
    return `${t("trade_order_dynamic")}_${t("trade_template_replyBtnName")}`;
  }
});

const currentWorkOrderId = computed(() => {
  return props.type === "MILESTONE"
    ? props.msReplyId
    : props.currentWorkOrderInfo.workOrderId;
});

const handleSendMessage = async () => {
  if (sendMessage.value === "") {
    return;
  }
  let workOrderDynamicRemindDataList = [];
  checkList.value.length > 0 &&
    checkList.value.map((item: any) => {
      if (props.currentAllMembers.find(e => e.label === item)) {
        const isTeam =
          props.currentAllMembers.find(e => e.label === item).remindType ===
          "team";
        return workOrderDynamicRemindDataList.push({
          remindToTeam: isTeam ? true : false,
          teamMemberId: isTeam ? "" : item,
          teamId: isTeam ? item : ""
        });
      }
    });
  const res = await addDynamic({
    workOrderId: currentWorkOrderId.value,
    dynamicModule: props.type,
    workOrderDynamicRemindDataList: workOrderDynamicRemindDataList,
    content: sendMessage.value
  });
  if (res.code === 0) {
    popoverVisible.value = false;
    sendMessage.value = "";
    mentionIds.value = [];
    ElMessage.success(t("trade_successfully_sent"));
    getWorkOrderDynamicListApi();
  }
};

const closeCard = () => {
  emit("handleClose", false);
};

// 打开历史记录弹窗
const openHistoryDialog = (item: any) => {
  popoverVisible.value = false;
  props.type === "MILESTONE"
    ? (historyDialogType.value = "MILE_STONE")
    : (historyDialogType.value = "WORK_ORDER");
  currentVersion.value = item.version;
  getWorkOrderHistoryList({ workOrderId: currentWorkOrderId.value }).then(
    res => {
      if (res.code === 0) {
        historyRecords.value = res.data;
        OrderHistoryRecordDialogRef.value.open(
          historyRecords.value,
          currentVersion.value
        );
      }
    }
  );
};

const getTimeLineStampFormat = (dateTime: any) => {
  if (dateTime) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "";
  }
};
const getWorkOrderDynamicListApi = async () => {
  const res = await getWorkOrderDynamicList({
    workOrderId: currentWorkOrderId.value,
    moduleEnum: props.type
  });
  if (res.code === 0 && res.data) {
    dynamicList.value = res.data;
    // 说明已经查看了未读信息
  }
};

watch(
  () => currentWorkOrderId.value,
  () => {
    getWorkOrderDynamicListApi();
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss">
.fixed-card {
  width: 100%;
  height: 100%;

  &.noBorder {
    border: none;
    box-shadow: none;

    .el-card__body {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: hidden;
    }
  }

  .el-card__header {
    display: flex;
    align-items: center;
    height: 54px;
  }

  .el-card__body {
    height: calc(100% - 54px);
    padding: 0;
  }

  .fixed-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .fixed-card-title {
    font-size: 18px;
    line-height: 16px;
    color: #262626;
    text-align: left;
  }

  .fixed-card-close {
    padding: 2px 4px;
    font-size: 13px;
    color: #000;
    cursor: pointer;
  }

  .fixed-card-close:hover {
    background: #e5e5e5;
    border-radius: 4px;
  }

  .fixed-btn-close {
    .iconfont {
      margin-left: 4px;
      font-size: 12px;
      color: #babec6;
      cursor: pointer;
    }
  }

  .fixed-card-body-header {
    display: flex;
    align-items: center;
    padding: 8px 0;
    margin: 0 20px;
    border-bottom: 1px solid #f2f2f2;

    .fixed-card-col-title {
      font-size: 14px;
      color: #797979;

      &::after {
        display: inline-block;
        font-size: 14px;
        line-height: 20px;
        color: #797979;
        content: "：";
      }
    }
  }

  .fixed-card-body-dynamics {
    height: calc(100% - 65px);
  }
}

.milestone-dynamics-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 12px;

  .el-scrollbar {
    flex: 1;
    max-height: calc(100% - 192px);
    padding-top: 18px;
    padding-right: 18px;
    padding-left: 18px;
  }

  .milestone-dynamics-remind-box {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 8px;
    padding-bottom: 3px;
    background: #f2f2f2;
    border-top: 1px solid #e8e8e8;

    .remind-btn {
      display: inline-flex;
      align-items: center;
      padding: 2px 4px;
      margin-right: 4px;
      margin-bottom: 5px;
      font-size: 14px;
      color: #595959;
      white-space: nowrap;
      background: #fff;
      border: 1px solid #dcdcdc;
      border-radius: 33px;

      &.active {
        color: #2082ed;
        background: #fff;
        border: 1px solid #2082ed;
      }
    }
  }

  .milestone-dynamics-send {
    position: relative;
    height: 120px;

    .el-textarea {
      height: 100%;
    }

    .el-textarea__inner {
      height: 100%;
      border-top: 1px solid #e8e8e8;
      border-radius: 0;
      box-shadow: none;
    }

    .milestone-dynamics-send-btn {
      position: absolute;
      right: 18px;
      bottom: 18px;
      width: 50px;
      height: 32px;
      font-size: 14px;
      line-height: 32px;
      color: #0070d2;
      text-align: center;
      cursor: pointer;

      &:hover {
        background: #eef8ff;
        border-radius: 4px;
      }

      &.disabled {
        color: #8c8c8c;
        cursor: not-allowed;
      }
    }
  }

  .milestone-dynamics-content {
    height: 100%;

    .dynamics-item {
      display: flex;
      flex-wrap: nowrap;
      align-items: baseline;
      justify-content: space-between;
      margin-bottom: 12px;

      .dynamics-item-left {
        display: flex;
        align-items: baseline;
        font-size: 14px;
        color: #333;

        .dynamics-content {
          &.history {
            color: #8c8c8c;
          }
        }

        .dynamics-icon {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          margin-right: 10px;

          .iconfont {
            font-size: 14px;
            color: #808080;
          }
        }

        .dynamics-avtar {
          display: flex;
          align-items: center;
          width: 32px;
          height: 32px;
          margin-right: 10px;
        }
      }

      .dynamics-item-right {
        flex-shrink: 0;
        font-size: 14px;
        line-height: 20px;
        color: #8c8c8c;

        .dynamics-item-btn {
          margin-right: 14px;
          font-size: 14px;
          color: #2288fe;
          cursor: pointer;
        }

        .dynamics-content {
          line-height: 20px;
        }
      }
    }

    .dynamics-name {
      font-size: 14px;
      line-height: 20px;
      color: #595959;
    }

    .dynamics-remind {
      font-size: 14px;
      line-height: 20px;
      color: #0070d2;

      &:last-child {
        margin-right: 4px;
      }
    }
  }
}

.popover-body {
  height: 354px;

  .popover-search {
    margin-bottom: 20px;
  }

  .member-content {
    height: calc(100% - 52px);

    .el-checkbox-group {
      display: flex;
      flex-direction: column;

      .el-checkbox__label {
        display: flex;
        align-items: center;
        max-width: 228px;
        color: #262626;

        .el-text {
          color: #262626;
        }

        .lk-avater-container {
          margin-right: 8px;
        }
      }
    }

    .el-checkbox {
      margin-bottom: 16px;
    }
  }
}
</style>
