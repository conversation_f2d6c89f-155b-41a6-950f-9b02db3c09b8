<template>
  <el-input
    v-model="widgetFormulasValue"
    disabled
    type="textarea"
    l
    readonly
    :autosize="{ minRows: 1 }"
  />
  <div v-if="widgetFormulasErrorFlag" class="widget-error-field">
    {{ t("trade_common_computedError") }}
  </div>
</template>
<script setup lang="ts">
import { inject, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import { BigNumber } from "bignumber.js";
import { formatThousandNumber } from "@/utils/common";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  bindTrendFormId: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  formConfig: {
    type: Object as PropType<any>,
    default: () => {}
  },
  currentFormWidgetData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetFormEncapsulatedData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  // 其他表单数据
  otherFormData: {
    type: Object as PropType<any>,
    default: () => {}
  },
  linkedRefrenceSourceFactoryData: {
    type: Object as PropType<any>,
    default: () => {}
  }
});

const { t } = useI18n();
const widgetFormulasValue = ref<any>();
const widgetFormulasErrorFlag = ref<boolean>(false);
const widgetDateMap = ref<any>({
  year: "YYYY",
  month: "YYYY-MM",
  date: "YYYY-MM-DD",
  datetime: "YYYY-MM-DD HH:mm",
  datetimesecond: "YYYY-MM-DD HH:mm:ss"
});

const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const invocationFormulaCalculateExecutor: any = inject(
  "invocationFormulaCalculateExecutor",
  null
);

const handleFormulasValue = (formulasValue: any) => {
  handleWidgetFormsValue(
    props.widgetConfigure._fc_id,
    {
      label: formulasValue,
      obj: formulasValue,
      widgetType: "DrFormulas",
      widgetId: props.widgetConfigure._fc_id,
      $rowIndex: props.widgetRowIndex
    },
    formulasValue
  );
};

const handleTransformFormulasValue = (data: any) => {
  const { type, formatDateType } = props.widgetConfigure.props.formatConfig;

  if (props.widgetConfigure._fc_id === "id_Fzxfmcd336w0b3c") {
    console.log(data, type);
  }

  if (type === "number") {
    const formatNumberType =
      props.widgetConfigure.props.formatConfig.formatNumberType;
    const formulasWidgetValue = Number(data);
    if (!isNaN(formulasWidgetValue)) {
      let widgetFormulasValueData = `${formulasWidgetValue.toFixed(
        props.widgetConfigure.props.formatConfig.precision
      )}`;
      if (formatNumberType === "useThousandSeparator") {
        widgetFormulasValue.value = formatThousandNumber(
          widgetFormulasValueData
        );
      } else {
        widgetFormulasValue.value = widgetFormulasValueData;
      }
      handleFormulasValue(widgetFormulasValue.value);
    }

    widgetFormulasErrorFlag.value =
      formatNumberType === "useThousandSeparator"
        ? false
        : isNaN(formulasWidgetValue);
  } else if (type === "date") {
    if (typeof data === "string" && data.indexOf("Invalid Date") > -1) {
      widgetFormulasValue.value = "";
      return;
    }

    const formulasWidgetValue = new Date(data);

    if (!isNaN(formulasWidgetValue.getTime())) {
      widgetFormulasValue.value = dayjs(formulasWidgetValue).format(
        widgetDateMap.value[formatDateType]
      );
      handleFormulasValue(widgetFormulasValue.value);
      return;
    }
    widgetFormulasErrorFlag.value = isNaN(formulasWidgetValue.getTime());
    widgetFormulasValue.value = "";
  } else if (type === "text") {
    const formulasWidgetValue =
      data instanceof Array ? data.join("") : "" + data;

    if (formulasWidgetValue !== "Error: #VALUE!") {
      widgetFormulasValue.value = formulasWidgetValue;
      handleFormulasValue(widgetFormulasValue.value);
      return;
    }
    widgetFormulasErrorFlag.value = formulasWidgetValue === "Error: #VALUE!";
    widgetFormulasValue.value = "";
  } else if (type === "percentage") {
    const formulasWidgetValue = new BigNumber(data)
      .multipliedBy(100)
      .toNumber();

    if (!isNaN(formulasWidgetValue)) {
      widgetFormulasValue.value = `${formulasWidgetValue.toFixed(
        props.widgetConfigure.props.formatConfig.precision
      )}%`;
      handleWidgetFormsValue(
        props.widgetConfigure._fc_id,
        {
          label: `${formulasWidgetValue.toFixed(
            props.widgetConfigure.props.formatConfig.precision
          )}%`,
          obj: Number(data),
          widgetType: "DrFormulas",
          widgetId: props.widgetConfigure._fc_id,
          $rowIndex: props.widgetRowIndex
        },
        Number(data)
      );
      return;
    }
    widgetFormulasErrorFlag.value = isNaN(formulasWidgetValue);
    widgetFormulasValue.value = "";
  }
};

watch(
  () => ({ ...props.trendsForm }),
  (newVal, oldVal) => {
    if (props.trendsForm.hasOwnProperty(props.widgetConfigure._fc_id)) {
      if (
        props.trendsForm[props.widgetConfigure._fc_id] !== "" &&
        props.trendsForm[props.widgetConfigure._fc_id] !== " "
      ) {
        handleTransformFormulasValue(
          props.trendsForm[props.widgetConfigure._fc_id]
        );
      } else {
        widgetFormulasValue.value =
          props.trendsForm[props.widgetConfigure._fc_id];
        handleFormulasValue(widgetFormulasValue.value);
      }
    }

    if (!props.widgetConfigure.props.formulaObject) {
      return;
    }

    if (!props.widgetConfigure.props.formulaObject.formulasParams) return;
    const factorFormulasParams =
      props.widgetConfigure.props.formulaObject.formulasParams.map(
        param => param.split("@@")[1]
      );

    // 新旧的值都存在则触发公式运算
    for (let formulasParam of factorFormulasParams) {
      // if (props.widgetConfigure._fc_id === "id_F8gdmdfenc8jg9c") {
      //   console.log(formulasParam, newVal, oldVal);
      //   debugger;
      // }

      if (
        newVal &&
        oldVal &&
        newVal.hasOwnProperty(formulasParam) &&
        oldVal.hasOwnProperty(formulasParam) &&
        newVal[formulasParam] !== oldVal[formulasParam]
      ) {
        const formulasResult = invocationFormulaCalculateExecutor(
          props.widgetConfigure.props.formulaObject
        );

        if (formulasResult !== "" && formulasResult !== " ") {
          handleTransformFormulasValue(formulasResult);
        } else {
          widgetFormulasValue.value = formulasResult;
          handleFormulasValue(widgetFormulasValue.value);
        }
        return;
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.linkedRefrenceSourceFactoryData,
  () => {
    if (!props.widgetConfigure.props.formulaObject) {
      return;
    }

    if (!props.widgetConfigure.props.formulaObject.formulasParams) return;
    const factorFormulasParams =
      props.widgetConfigure.props.formulaObject.formulasParams.map(
        param => param.split("@@")[1]
      );

    // 新旧的值都存在则触发公式运算
    if (
      factorFormulasParams.includes(
        props.linkedRefrenceSourceFactoryData.factoryWidgetId
      )
    ) {
      const formulasResult = invocationFormulaCalculateExecutor(
        props.widgetConfigure.props.formulaObject
      );

      if (formulasResult !== "" && formulasResult !== " ") {
        handleTransformFormulasValue(formulasResult);
      } else {
        widgetFormulasValue.value = formulasResult;
        handleFormulasValue(widgetFormulasValue.value);
      }
      return;
    }
  },
  {
    deep: true
  }
);
</script>
<style lang="scss" scoped>
.widget-error-field {
  margin: 4px 2px;
  font-size: 14px;
  line-height: 16px;
  color: #ff2d2d;
  text-align: left;
}
</style>
