<template>
  <div style="pointer-events: auto; cursor: pointer">
    <el-button v-if="!signImage" plain @click="handleOpenSign('')">
      <i class="iconfont link-sign" />
      {{ t("trade_common_addSignature") }}
    </el-button>
    <div
      v-else
      class="sign-image-col"
      :style="{
        height: `${height}px`,
        display: 'inline-flex',
        'align-items': 'center'
      }"
    >
      <el-image :src="signImage" @click="handleOpenSign(signImage)" />
    </div>
  </div>

  <LkDialog
    ref="SignDialogRef"
    append-to-body
    width="800px"
    lock-scroll
    :no-footer="true"
    top="3vh"
  >
    <template #default>
      <div class="sign-canvas-body">
        <canvas
          :id="`${widgetConfigure['_fc_id']}_canvas`"
          width="770px"
          height="300px"
        />
      </div>
    </template>
    <template #footer>
      <div class="sign-canvas-dialog-footer">
        <div class="sign-canvas-dialog-left">
          <el-radio
            v-if="!userHistorySignImage"
            v-model="signSaveFlag"
            value="signSave"
            :label="t('trade_formDesigner_signTip')"
            :border="true"
            @click.prevent="handleCancelRadio('signSave')"
          />
          <div v-if="userHistorySignImage" class="sign-image-body">
            <el-image
              class="sign-image"
              :src="userHistorySignImage"
              @click="handleCopySignImage"
            />
            <span class="sign-footer-icon">
              <i class="iconfont link-ashbin" @click="handleDeleteUserSign" />
            </span>
          </div>
        </div>
        <div class="sign-canvas-dialog-right">
          <el-button plain @click="handleClear">
            {{ t("trade_common_clear") }}
          </el-button>
          <el-button type="primary" color="#0070D2" @click="handleToDataURL">
            {{ t("trade_common_save") }}
          </el-button>
        </div>
      </div>
    </template>
  </LkDialog>
</template>
<script lang="ts" setup>
import { ref, inject, nextTick, watch, unref } from "vue";
import { useI18n } from "vue-i18n";
import LkDialog from "@/components/lkDialog/index";
import SignaturePad from "signature_pad";
import { cloneDeep, isEqual, storageLocal } from "@pureadmin/utils";
import { updateUserSign } from "@/api/common";
import { getUserInfo } from "@/api/login";

const props = defineProps({
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  },
  width: {
    type: Number as PropType<number>,
    default: 0
  },
  height: {
    type: Number as PropType<number>,
    default: 0
  }
});

const { t } = useI18n();
const SignDialogRef = ref<any | HTMLElement>(null);
const signaturePadInstance = ref<SignaturePad | null>(null);
const canvasContext = ref<HTMLCanvasElement | null>(null);
let userHistorySignImage = unref<string>("");
const signSaveFlag = ref<string>("");
const signImage = ref<string>("");

const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const emit = defineEmits(["handleUpdateWidgetData"]);

const handleOpenSign = (base64str: string) => {
  SignDialogRef.value.open();
  nextTick(() => {
    canvasContext.value = document.querySelector(
      `#${props.widgetConfigure["_fc_id"]}_canvas`
    ) as HTMLCanvasElement;
    signaturePadInstance.value = new SignaturePad(canvasContext.value, {
      backgroundColor: "#FFFFFF" // 设置背景为不透明白色
    });
    if (base64str) {
      fromDataURL(base64str);
    }
    const userInfo: any = storageLocal().getItem("user-info");
    if (userInfo && userInfo.userSign) {
      userHistorySignImage = userInfo.userSign;
    }
  });
};

const handleCancelRadio = async (value: string): Promise<void> => {
  signSaveFlag.value = value === signSaveFlag.value ? "" : value;
  if (signSaveFlag.value === "signSave") {
    handleUpdateSignature(signaturePadInstance.value?.toDataURL());
  }
};

const handleUpdateSignature = async (signatureString: string) => {
  const { code } = await updateUserSign({
    userSign: signatureString
  });
  if (code === 0) {
    const { data } = await getUserInfo();
    userHistorySignImage = data.userSign;
    const userInfo: any = storageLocal().getItem("user-info");
    userInfo.userSign = data.userSign;
    storageLocal().setItem("user-info", userInfo);
  }
};

const handleDeleteUserSign = async () => {
  handleUpdateSignature("");
  signSaveFlag.value = "";
};

const handleCopySignImage = () => {
  fromDataURL(userHistorySignImage);
};

const handleToDataURL = () => {
  signImage.value = signaturePadInstance.value?.isEmpty()
    ? ""
    : signaturePadInstance.value?.toDataURL();
  SignDialogRef.value.close();
  if (props.widgetRowIndex !== -1) {
    emit(
      "handleUpdateWidgetData",
      {
        obj: signImage.value,
        label: signImage.value,
        widgetType: props.widgetConfigure.type,
        widgetId: props.widgetConfigure._fc_id,
        $rowIndex: props.widgetRowIndex
      },
      props.widgetRowIndex
    );
  }
};

const fromDataURL = dataUrl => {
  signaturePadInstance.value?.fromDataURL(dataUrl);
};

const handleClear = () => {
  signaturePadInstance.value?.clear();
};

watch(
  () => props.trendsForm?.[props.widgetConfigure._fc_id],
  (newVal, oldVal) => {
    if (isEqual(newVal, oldVal)) return;
    signImage.value = cloneDeep(newVal);
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => signImage.value,
  (newVal, oldVal) => {
    if (
      newVal !== props.trendsForm[props.widgetConfigure._fc_id] &&
      !(!newVal && !oldVal)
    ) {
      const widgetId = props.widgetConfigure._fc_id;
      const signWidgetValue = signImage.value;
      if (props.widgetRowIndex === -1) {
        handleWidgetFormsValue(
          widgetId,
          {
            obj: signWidgetValue,
            label: signWidgetValue,
            widgetType: props.widgetConfigure.type,
            widgetId: widgetId,
            $rowIndex: props.widgetRowIndex
          },
          signWidgetValue
        );
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style lang="scss" scoped>
.sign-canvas-body {
  width: 760px;
  height: 360px;
  margin-top: 28px;
  border: 1px dashed #979797;
  border-radius: 4px;

  canvas {
    width: 100%;
    height: 100%;
  }
}

.sign-image-col {
  // width: 98px;
  .el-image {
    width: 100px;

    ::v-deep(.el-image__inner) {
      border: 1px solid #e0dfe4;
      border-radius: 4px;
    }
  }
}

.sign-canvas-dialog-footer {
  display: flex;

  .sign-canvas-dialog-left {
    display: flex;
    flex: 2;
    text-align: left;

    .sign-image-body {
      display: flex;
      align-items: center;

      .sign-image {
        width: 88px;
        height: 32px;
        vertical-align: bottom;
        background: #f5f7fa;
        border: 1px solid #e0dfe4;
        border-radius: 4px;
      }

      .sign-footer-icon {
        margin-left: 10px;
        font-size: 12px;
        color: #808080;
        vertical-align: middle;
        cursor: pointer;
      }
    }
  }

  .sign-canvas-dialog-right {
    flex: 1;
  }
}
</style>
