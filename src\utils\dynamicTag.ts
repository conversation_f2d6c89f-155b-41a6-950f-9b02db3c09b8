// 文件路径: c:\Users\<USER>\Desktop\Daren-code\linkincrease-trade-pro\src\utils\dynamicTag.ts

import { useTags } from "@/layout/hooks/useTag";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { handleAliveRoute } from "@/router/utils";

const { multiTags } = useTags();

function deleteDynamicTag(
  obj: any,
  currentPath: string,
  route: any,
  router: any
) {
  const valueIndex: number = multiTags.value.findIndex((item: any) => {
    if (item.query) {
      return (
        item.path === obj.path &&
        JSON.stringify(item.query) === JSON.stringify(obj.query)
      );
    } else if (item.params) {
      return (
        item.path === obj.path &&
        JSON.stringify(item.params) === JSON.stringify(obj.params)
      );
    } else {
      return item.path === obj.path;
    }
  });

  if (valueIndex === -1) return;

  useMultiTagsStoreHook().handleTags("splice", "", {
    startIndex: valueIndex,
    length: 1
  });

  // 明确返回值类型为数组
  const newRoute: any[] = useMultiTagsStoreHook().handleTags("slice") as any[];

  if (currentPath === route.path) {
    navigateToFirstTag(newRoute, router);
  } else if (!multiTags.value.some(item => item.path === route.path)) {
    navigateToFirstTag(newRoute, router);
  }
}

function navigateToFirstTag(newRoute: any[], router: any) {
  if (newRoute.length === 0) return;
  const firstTag = newRoute[0];
  if (firstTag.query) {
    router.push({ name: firstTag.name, query: firstTag.query });
  } else if (firstTag.params) {
    router.push({ name: firstTag.name, params: firstTag.params });
  } else {
    router.push({ path: firstTag.path });
  }
}

export const deleteThatMenu = (
  selectTagRoute: any,
  route: any,
  router: any
) => {
  deleteDynamicTag(selectTagRoute, selectTagRoute.path, route, router);
  handleAliveRoute(route as ToRouteType);
};
