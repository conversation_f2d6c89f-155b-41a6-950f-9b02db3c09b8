<template>
  <div class="initiate-coopEmail-list-wrapper">
    <ul
      v-if="initiateCoopEmailList.length > 0"
      v-infinite-scroll="loadInitiateCoopEmailList"
      class="list"
      :infinite-scroll-disabled="disabled"
    >
      <li
        v-for="(item, index) in initiateCoopEmailList"
        :key="index"
        class="list-item"
      >
        <div class="email-list-top-header">
          <div class="email-list-top-header-left">
            <div class="email-list-top-header-left-top">
              <span class="mask" @click="gotoDetail(item)">{{
                item.orderMark
              }}</span>
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                <span class="milestone-name">{{ item.milestoneName }}</span>
              </ReText>
              <span
                v-if="item.msLabelList && item.msLabelList.length > 0"
                class="label-group"
              >
                <span
                  v-for="(labelItem, index) in item.msLabelList"
                  :key="index"
                  class="label-item"
                  :style="{
                    borderColor: labelItem.style,
                    color: labelItem.style
                  }"
                  >{{ labelItem.labelValue }}</span
                >
              </span>
            </div>
            <div class="email-list-top-header-left-bottom">
              <span
                class="work-order-name"
                @click="handleTrigger('workOrderDynamic', item)"
                >{{ item.workOrderName }}</span
              >
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                <span class="sccs-name" @click="gotoSccsManage(item)">{{
                  item.sccsName
                }}</span>
              </ReText>
              <svg
                v-if="item.coopTeamMark"
                class="svg-icon svg-icon-coop"
                aria-hidden="true"
              >
                <use xlink:href="#link-coop" />
              </svg>
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                <span class="coop-team-name">
                  {{ item.workOrderCreateTeamName }}
                </span>
              </ReText>
            </div>
          </div>
          <div class="email-list-top-header-right">
            <span class="plan-endtime">
              {{ `${t("trade_common_planEnd")}：` }}
              {{
                item.plannedEndDate
                  ? getTimeLineStampFormat(item.plannedEndDate)
                  : "- -"
              }}
            </span>
            <span v-if="item.completeTime">{{
              `${getTimeLineStampFormat(item.completeTime)}${item.completeReason}`
            }}</span>
          </div>
        </div>
        <div
          v-for="emailItem in item.coopEmailDataList"
          :key="emailItem.id"
          class="email-card"
        >
          <div class="email-card-left">
            <div class="email-title">
              <ReText type="info" :tippyProps="{ delay: 50000 }">
                <span class="email-theme-name">{{ emailItem.subject }}</span>
              </ReText>
              <span class="email-create-time"
                ><span class="label">{{ t("trade_home_instigation") }}</span
                >{{ getTimeLineStampFormat(emailItem.createTime) }}</span
              >
              <span class="email-receivor"
                ><span class="label">{{ t("trade_collaborator") }}</span
                >{{ emailItem.email }}</span
              >
              <span class="email-update-time">
                <span class="label">{{ t("trade_email_updatesor") }}</span
                >{{ getTimeLineStampFormat(emailItem.updateTime) }}</span
              >
            </div>
            <ReText
              v-if="emailItem.remark"
              type="info"
              :tippyProps="{ delay: 50000 }"
            >
              <span
                class="email-content"
                v-html="replaceRichText(emailItem.remark)"
              />
            </ReText>
          </div>
          <div class="email-card-right">
            <span v-if="emailItem.submitNew" class="email-has-update">{{
              t("trade_common_hasUpdate")
            }}</span>
            <span
              class="email-view-btn"
              @click="handleView(emailItem, item.templateId)"
              ><i class="iconfont link-display" />{{
                t("trade_common_view")
              }}</span
            >
          </div>
        </div>
      </li>
      <div
        v-if="initiateCoopEmailList.length > 0 && loading"
        class="list-tip loading"
      >
        {{ `${t("trade_common_loadingText")}...` }}
      </div>
      <div v-if="initiateCoopEmailList.length > 0 && noMore" class="list-tip">
        {{ t("trade_no_more_tip") }}
      </div>
    </ul>
    <el-empty
      v-else-if="!initiateCoopEmailList.length && !loading"
      :description="t('trade_no_task')"
      :image-size="342"
      :image="taskNoImage"
    />
  </div>
  <LkDialog
    ref="WidgetDialogRef"
    :show-close="false"
    width="80%"
    style="padding: 0"
    class="lk-initiate-coopEmail-dialog lk-maximum-dialog"
    destroy-on-close
    :no-footer="true"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <template #header="{ close }">
      <div class="work-order-flex-header">
        <div class="work-order-tabs-flex-left">
          <div class="work-order-tabs-flex-left-title">
            {{ worderName }}
          </div>
        </div>
        <div class="work-order-tabs-flex-right">
          <span
            v-if="currentEmailIsCollectable"
            class="work-order-header-text"
            @click="handleTrigger('workOrderCollect')"
          >
            {{ t("trade_go_to_process_the_work_order") }}
          </span>
          <div class="work-order-header-col">
            <div
              class="work-order-header-small-button work-order-header-default-button"
            >
              <i class="iconfont link-close" @click="close" />
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <el-scrollbar style="height: 100%; padding: 10px">
        <EmailDetail ref="EmailDetailRef" />
      </el-scrollbar>
    </template>
    <!-- <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" color="#0070D2" @click="handleConfirm">
          {{ t("trade_common_confirm") }}
        </el-button>
      </div>
    </template> -->
  </LkDialog>
  <WorkOrderCollectionDialog ref="WorkOrderCollectionDialogRef" />
  <WorkOrderReplyDialog ref="WorkOrderReplyDialogRef" />
</template>
<script lang="ts" setup>
import { ref, nextTick } from "vue";

import taskNoImage from "@/assets/images/taskCenter/task-center-no-task.png";
import LkDialog from "@/components/lkDialog";
import WorkOrderCollectionDialog from "@/views/orderDetail/components/WorkOrderCollectionDialog.vue";
import WorkOrderReplyDialog from "@/views/orderDetail/components/WorkOrderReplyDialog.vue";
import { getCoopEmailCollectable } from "@/api/taskCenter";

import EmailDetail from "@/views/emailCoop/emailDetail.vue";
import { ReText } from "@/components/ReText";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { storageLocal, storageSession } from "@pureadmin/utils";
import {
  getTradeCoopSccsRolePermission,
  getTradeSccsRolePermission
} from "@/api/order";
import { getSccsRolePermSet } from "@/api/common";

const { t } = useI18n();
const router = useRouter();

const props = defineProps({
  initiateCoopEmailList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pageInfo: {
    type: Object,
    default: () => ({})
  },
  disabled: {
    type: Boolean,
    default: false
  },
  noMore: {
    type: Boolean,
    default: false
  }
});
const WidgetDialogRef = ref<HTMLElement | any>(null);
const EmailDetailRef = ref<HTMLElement | any>(null);
const WorkOrderCollectionDialogRef = ref<HTMLElement | any>(null);
const WorkOrderReplyDialogRef = ref<HTMLElement | any>(null);

const worderName = ref("");
const currentViewEmail = ref<any>({});
const currentTemplateId = ref("");
const emit = defineEmits(["loadInitiateCoopEmailList"]);

const replaceRichText = (content: string) => {
  return (
    content
      // 替换图片
      .replace(/<img\b[^>]*>/gi, "[图片]")
      // 替换表格（包含内容）
      .replace(/<table\b[^>]*>[\s\S]*?<\/table>/gi, "[表格]")
      // 替换链接（保留文字）
      .replace(/<a\b[^>]*href=["']?[^"']*["']?[^>]*>([\s\S]*?)<\/a>/gi, "$1")
      // 替换代码块（保留内容）
      .replace(/<pre\b[^>]*>([\s\S]*?)<\/pre>/gi, "$1")
      .replace(/<code\b[^>]*>([\s\S]*?)<\/code>/gi, "$1")
      // 替换水平线
      .replace(/<hr\b[^>]*>/gi, "---")
      // 替换换行
      .replace(/<br\b[^>]*>/gi, "\n")
      // 替换段落（保留内容）
      .replace(/<p\b[^>]*>([\s\S]*?)<\/p>/gi, "$1\n")
      // 替换引用块（保留内容）
      .replace(/<blockquote\b[^>]*>([\s\S]*?)<\/blockquote>/gi, "$1")
      // 替换其他容器（保留内容）
      .replace(/<div\b[^>]*>([\s\S]*?)<\/div>/gi, "$1")
      // 处理HTML实体
      .replace(/&nbsp;/g, " ")
      // 移除所有剩余HTML标签
      .replace(/<[^>]+>/g, "")
      // 清理多余空行
      .replace(/\n{3,}/g, "\n\n")
      .trim()
  );
};
const getTimeLineStampFormat = (dateTime: any) => {
  if (dateTime) {
    return dayjs(new Date(dateTime)).format("YYYY-MM-DD HH:mm:ss");
  } else {
    return "";
  }
};
const loadInitiateCoopEmailList = () => {
  if (props.loading || props.noMore) return;
  emit("loadInitiateCoopEmailList");
};
const currentEmailIsCollectable = ref(false);

const gotoSccsManage = async item => {
  const { sccsId, templateId, sccsName, coopTeamMark } = item;
  const { data } = await getSccsRolePermSet({ sccsId: sccsId });
  storageSession().setItem("userSccsPerm", data);
  const parameter = {
    sccsId: sccsId,
    templateId: templateId,
    coopTeamMark: !coopTeamMark ? 2 : "",
    sccsName: sccsName
  };
  router.push({ name: "orderManage", query: parameter });
};
const gotoDetail = item => {
  handleJumpDetail(item);
};
const handleJumpDetail = async (row): Promise<void> => {
  const { orderId, templateId, sccsName, sccsId } = row;
  const params = {
    sccsName: sccsName,
    sccsId: sccsId,
    templateId: templateId,
    orderId: orderId,
    orderMark: row.orderMark ? row.orderMark : row.serialNumber,
    coopTeamMark: !row.coopTeamMark ? "2" : ""
  };
  let resp: any = {};
  if (params.coopTeamMark === "2") {
    resp = await getTradeSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  } else {
    resp = await getTradeCoopSccsRolePermission({
      sccsId: sccsId,
      orderIdList: [row.orderId]
    });
  }
  //@ts-ignore
  storageLocal().setItem(`${row.orderId}_userTeamRole`, resp.data[row.orderId]);
  router.push({ name: "orderDetail", query: params });
};

const handleView = async (row, templateId) => {
  currentViewEmail.value = row;
  currentTemplateId.value = templateId;
  worderName.value = row.subject;
  WidgetDialogRef.value.open();
  nextTick(() => {
    EmailDetailRef.value.initRenderTemplate(row.id);
  });
  if (row.completeTime) {
    return;
  }
  const res = await getCoopEmailCollectable({
    workOrderId: row.workOrderId
  });
  if (res.code === 0) {
    currentEmailIsCollectable.value = res.data;
  }
};
const handleConfirm = () => {
  WidgetDialogRef.value.close();
};
const handleClose = (): void => {
  WidgetDialogRef.value.close();
};
const handleTrigger = (type, item?) => {
  if (type === "workOrderDynamic") {
    // 查看工单（动态）
    WorkOrderReplyDialogRef.value.open(
      {
        msId: item.milestoneId,
        workOrderId: item.workOrderId
      },
      {},
      type,
      item.orderId,
      {
        sccsId: item.sccsId,
        templateId: item.templateId
      }
    );
  } else if (type === "workOrderCollect") {
    // 工单采集
    WorkOrderCollectionDialogRef.value.open(
      {
        msId: currentViewEmail.value.milestoneId,
        workOrderId: currentViewEmail.value.workOrderId
      },
      {},
      currentViewEmail.value.orderId,
      {
        sccsId: currentViewEmail.value.sccsId,
        templateId: currentTemplateId.value
      }
    );
  }
};
</script>
<style lang="scss">
.initiate-coopEmail-list-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 1100px;
  padding: 20px 14px 20px 20px;
  overflow: auto;
  background: #f5f6fa;

  .el-text.is-truncated {
    display: inline-flex;
  }

  .list {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    list-style: none;

    .list-item {
      margin-bottom: 20px;

      .email-list-top-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .email-list-top-header-left {
          flex: 1;

          .email-list-top-header-left-top {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .mask {
              padding: 2px 6px;
              font-size: 14px;
              font-weight: bold;
              color: #595959;
              cursor: pointer;
              background: #d9e1f8;
              border-radius: 4px;

              &:hover {
                color: #262626;
                background: #dae3fe;
              }
            }

            .milestone-name {
              max-width: 546px;
              margin-left: 16px;
              font-size: 14px;
              color: #262626;
            }
          }

          .email-list-top-header-left-bottom {
            display: flex;
            align-items: center;

            .work-order-name {
              padding: 2px 6px;
              margin-right: 16px;
              font-size: 14px;
              color: #0070d2;
              cursor: pointer;
              background: #e6f1fb;
              border-radius: 4px;

              &:hover {
                color: #0070d2;
                background: #ccecff;
              }
            }

            .sccs-name {
              max-width: 356px;
              margin-right: 20px;
              font-size: 13px;
              color: #8c8c8c;
              cursor: pointer;

              &:hover {
                color: #0070d2;
              }
            }

            .svg-icon-coop {
              display: inline-block;
              width: 12px;
              height: 12px;
              vertical-align: middle;
            }

            .coop-team-name {
              max-width: 351px;
              margin-left: 3px;
              font-size: 13px;
              color: #8c8c8c;
            }
          }
        }

        .email-list-top-header-right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          font-size: 12px;
          color: #595959;

          .plan-endtime {
            margin-bottom: 18px;
            font-size: 12px;
            color: #595959;
          }
        }
      }

      .email-card {
        display: flex;
        align-items: center;
        padding: 16px;
        margin-bottom: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 13px 0 rgb(0 0 0 / 11%);

        .email-card-left {
          flex: 1;

          .email-title {
            display: flex;
            align-items: center;

            .email-theme-name {
              max-width: 364px;
              font-size: 14px;
              font-weight: bold;
              color: #303133;
            }

            .email-create-time,
            .email-receivor,
            .email-update-time {
              margin-left: 52px;
              font-size: 12px;
              color: #595959;

              .label {
                margin-right: 6px;
                font-size: 12px;
                color: #8c8c8c;
              }
            }
          }

          .email-content {
            display: -webkit-box;
            max-width: 972px;
            margin-top: 12px;
            overflow: hidden;
            font-size: 12px;
            line-height: 17px;
            color: #595959;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            white-space: normal;
            -webkit-box-orient: vertical;
          }
        }

        .email-card-right {
          .email-has-update {
            padding: 3px 5px;
            margin-right: 10px;
            font-size: 12px;
            color: #fdad4d;
            background: rgb(253 173 77 / 15%);
            border-radius: 10px;
          }

          .email-view-btn {
            padding: 6px 12px;
            font-size: 14px;
            color: #595959;
            cursor: pointer;
            background: #efefef;
            border-radius: 4px;

            &:hover {
              color: #0070d2;
              background: #ddefff;

              .link-display {
                color: #0070d2;
              }
            }

            .link-display {
              margin-right: 4px;
              font-size: 14px;
              color: #808080;
            }
          }
        }
      }
    }
  }

  .list-tip {
    width: 100%;
    margin-top: 20px;
    font-size: 14px;
    color: #bfbfbf;
    text-align: center;
  }
}

.initiate-coopEmail-list-wrapper::-webkit-scrollbar-track {
  background-color: #f5f6fa;
}

.lk-initiate-coopEmail-dialog {
  .el-dialog__header {
    padding: 0 !important;
    background: #fff !important;
    border-bottom: 1px solid #e8e8e8;

    .dialog-header {
      display: flex;
      align-items: center;
      height: 60px;
      padding-top: 6px;

      .dialog-header-left {
        flex: 4;
        height: 100%;
        padding-left: 4px;

        .dialog-header-tabs {
          display: flex;
          align-items: center;
          height: 100%;
          border-radius: 4px 0 0;

          .dialog-header-tabs-item {
            align-items: center;
            height: 100%;
            padding: 0 16px;
            line-height: 52px;
            border: 1px solid #e5e7ee;
            border-right: 0 none;

            &.active {
              border-bottom: 0 none;

              .dialog-header-tabs-item-text {
                color: #0070d2;
              }
            }

            &:first-child {
              border-radius: 4px 0 0;
            }

            &:last-child {
              border-right: 1px solid #e5e7ee;
              border-radius: 0 4px 0 0;
            }

            .dialog-header-tabs-item-text {
              margin-right: 7px;
              font-size: 14px;
              color: #262626;
            }

            .dialog-header-tabs-item-icon {
              display: inline-block;
              width: 22px;
              height: 22px;
              margin-right: 4px;
              line-height: 22px;
              text-align: center;
              cursor: pointer;
              border-radius: 2px;

              &:hover {
                background: #e5e5e5;
              }

              .iconfont {
                font-size: 12px;
                color: #808080;
              }
            }
          }
        }
      }

      .dialog-header-right {
        flex: 1;
        text-align: right;

        .dialog-header-box {
          height: 30px;
          padding: 7px 10px;
          font-size: 14px !important;
          color: #595959 !important;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: #e5e5e5;
          }

          .iconfont {
            margin-right: 3px;
            font-size: 12px;
          }
        }

        .dialog-header-info-box {
          margin: 0 20px;

          .dialog-header-icon-box {
            display: inline-block;
            width: 32px;
            height: 30px;
            padding: 4px 0;
            text-align: center;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
              background: #e5e5e5;
            }

            .iconTip {
              font-size: 16px;
              color: #808080 !important;
            }
          }

          &.active {
            background: #e1edff;
          }
        }

        .dialog-header-close-icon {
          position: relative;
          padding: 0 20px;

          .dialog-header-icon-tip {
            // padding: 0 20px;
            box-sizing: border-box;
            display: inline-block;
            height: 30px !important;
            border-radius: 4px;

            .iconfont {
              font-size: 13px;
              color: #808080;
              cursor: pointer;
            }
            // &:hover {
            //   background: #e5e5e5;
            // }
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              display: inline-block;
              width: 1px;
              height: 16px;
              content: "";
              background: #e5e5e5;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
    }
  }

  .el-dialog__body {
    height: calc(100% - 55px);
    background: #e9eaec;

    .dialog-content-flex {
      display: flex;
      height: 100%;
      padding: 10px;
      background: #f6f6f6;

      .dialog-content-flex-left {
        width: 358px;
        padding: 30px 14px;
        margin-right: 13px;
        background: #fff;
        border-radius: 4px 4px 0 0;

        .dialog-content-form {
          .dialog-content-form-row {
            margin-bottom: 25px;

            .dialog-content-title {
              display: flex;
              align-items: center;

              .dialog-content-text {
                margin-bottom: 9px;
                font-size: 14px;
                font-weight: bolder;
                line-height: 16px;
                color: #262626;
                text-align: left;

                &::before {
                  display: inline-block;
                  margin-top: 6px;
                  margin-right: 6px;
                  font-size: 22px;
                  font-weight: bold;
                  line-height: 16px;
                  color: #e61b1b;
                  vertical-align: middle;
                  content: "*";
                }
              }

              .dialog-content-coordination {
                flex: 1;
                margin-right: 3px;
                margin-bottom: 9px;
                font-size: 12px;
                color: #f6974f;
                text-align: right;

                .iconfont {
                  font-size: 13px;
                  color: #808080;
                  cursor: pointer;
                }
              }
            }

            .work-order-personnel-control-operation {
              display: block;
              align-items: center;
              width: 100%;

              .work-order-personnel-control-btn {
                cursor: pointer;

                .iconfont {
                  color: #0070d2;
                }

                .work-order-personnel-control-text {
                  margin-left: 5px;
                  font-size: 13px;
                  font-weight: bold;
                  color: #0070d2;
                }
              }

              .work-order-personnel-control-group {
                .work-order-personnel-control-group-col {
                  display: inline-flex;
                  align-items: center;
                  max-width: 270px;
                  padding: 3px 4px;
                  margin-right: 3px;
                  margin-bottom: 7px;
                  background: #ebf5fb;
                  border-radius: 3px;

                  .work-order-personnel-control-group-name {
                    max-width: 230px;
                    margin: 0 4px;
                    overflow: hidden;
                    font-size: 12px;
                    font-weight: bold;
                    color: #000;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }

                  .iconfont {
                    font-size: 12px;
                    color: #babec6;
                  }
                }

                .iconfont {
                  margin-left: 7px;
                  font-size: 14px;
                  color: #0070d2;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }

      .dialog-content-flex-right {
        flex: 1;

        .dialog-content-flex-right-header {
          margin: 7px 0 12px;
          font-size: 12px;
          font-weight: bolder;
          line-height: 17px;
          color: #262626;
          text-align: left;
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 0 !important;
  }

  .work-order-flex-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 54px;
    padding-left: 25px;

    .work-order-tabs-flex-left {
      display: flex;
      flex: 1;
      align-items: center;
      max-width: 70%;

      .work-order-tabs-flex-left-title {
        max-width: calc(100% - 35px);
        overflow: hidden;
        font-size: 18px;
        font-weight: bolder;
        color: #202020;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .work-order-tabs-flex-tag {
        padding: 2px 4px;
        margin-left: 5px;
        font-size: 12px;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        background: #fa8d0a;
        border: 1px solid #fff;
        border-radius: 3px;
      }
    }

    .work-order-tabs-flex-right {
      display: flex;
      align-items: center;

      .work-order-header-col {
        position: relative;
        display: flex;

        &::after {
          position: absolute;
          top: 50%;
          right: 0;
          display: inline-block;
          width: 1px;
          height: 16px;
          content: "";
          background: #e5e5e5;
          transform: translateY(-50%);
        }

        &:last-child {
          &::after {
            display: none;
            content: "";
          }
        }

        &.work-order-header-col-next {
          margin: 0 9px;
        }

        .work-order-header-button {
          padding: 3px 10px;
          margin-right: 12px;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: #e5e5e5;
          }

          .iconfont {
            font-size: 13px;
            color: #595959 !important;

            &:first-child {
              margin-right: 6px;
            }

            &:last-child {
              margin-left: 6px;
            }
          }
        }

        .work-order-header-small-button {
          display: inline-block;
          width: 28px;
          height: 30px;
          margin-right: 8px;
          line-height: 30px;
          text-align: center;
          border-radius: 4px;

          &.work-order-header-default-button {
            width: 42px;

            &:hover {
              background: transparent;
            }

            .iconfont {
              font-size: 14px;
            }
          }

          &:hover {
            background: #e5e5e5;
          }

          &.active {
            background: #e1edff;

            .iconfont {
              color: #0070d2;
            }
          }

          .iconfont {
            font-size: 16px;
            color: #808080;
            cursor: pointer;

            &.font18 {
              font-size: 18px;
            }
          }
        }
      }

      .work-order-header-text {
        font-size: 14px;
        line-height: 20px;
        color: #2082ed !important;
        cursor: pointer;
      }
    }
  }

  .work-order-container {
    position: relative;
    display: flex;
    height: 100%;
    overflow: hidden;

    .work-order-container-left {
      position: relative;
      width: 59%;
      background: #f5f6fa;
      transition: width linear 0.2s;

      .anchor-icon {
        top: 48px;
      }

      .anchor-list-container {
        top: 48px;
      }

      .work-order-container-top {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: calc(100% - 60px);
        padding: 0 10px;

        .work-order-container-top-header {
          display: flex;
          align-items: center;
          width: 100%;
          padding: 12px 27px;
          margin-top: 9px;

          .work-order-header-col {
            display: inline-flex;
            align-items: center;
            margin-right: 35px;

            .work-order-header-col-span {
              padding-left: 14px;
              font-size: 14px;
              line-height: 20px;
              color: #797979;
              text-align: left;

              &::after {
                content: "：";
              }
            }

            .work-order-header-col-main {
              display: flex;
              align-items: center;

              .work-order-header-create-time {
                margin-left: 8px;
                font-size: 14px;
                color: #262626;
              }

              .work-order-header-create-status-text {
                margin-left: 8px;
                font-size: 12px;
                font-weight: bold;
                color: #e62412;
              }
            }
          }
        }

        .work-order-container-top-main {
          flex: 1;
          height: calc(100% - 145px);
        }
      }

      .work-order-container-bottom {
        height: 60px;
        padding: 14px 18px;
        text-align: right;
        background: #fff;
      }
    }

    .work-order-container-right {
      position: absolute;
      width: 41%;
      height: 100%;
      background: #fff;
      border-left: 1px solid #e8e8e8;
      transition: right linear 0.2s;

      .work-order-container-dynamics-card {
        height: 100%;
      }
    }
  }

  .work-order-tabs-container {
    position: absolute;
    top: 54px;
    left: 0;
    z-index: 3333;
    box-sizing: border-box;
    width: 100%;
    height: 480px;
    padding: 33px 43px;
    background: #fff;
    border-top: 1px solid #e5e5e5;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 8px 13px 0 rgb(0 0 0 / 10%);

    &.nopadding {
      padding: 16px 26px !important;
    }
  }
}
</style>
