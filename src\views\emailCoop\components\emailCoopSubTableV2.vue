<template>
  <LkDialog
    ref="widgetDialogRef"
    :title="widgetDialogTitle"
    append-to-body
    width="50vw"
    confrimText="trade_email_confirmUseData"
    :destroy-on-close="true"
    @confirm="handleConfirmWidget"
  >
    <template #default>
      <div style="margin-top: 10px" />
      <lkSubTable
        ref="lkSubTableRef"
        :widgetConfig="tableWidgetConfig"
        :widgetData="tableData"
        tableFirstType="rowCheckbox"
        :tableStatisticsFlag="false"
        :presentView="{}"
        :modeRadioValue="0"
        :style="{ minHeight: '300px' }"
      />
    </template>
    <template #dialogFooterLeft>
      <div class="dialog-footer-left-body">
        {{ t("trade_email_selected") }}
        <span class="dialog-footer-left-title">{{ checkedRowCount }}</span>
        {{ t("trade_common_strip") }}
      </div>
    </template>
  </LkDialog>
  <EmailCoopSubTableMessageBox
    ref="EmailCoopSubTableMessageBoxRef"
    @handleConfirm="handleSubTableConfirm"
  />
</template>

<script lang="tsx" setup>
import { nextTick, ref, watch, h } from "vue";
import { useI18n } from "vue-i18n";
import { ElTooltip } from "element-plus";
import LkDialog from "@/components/lkDialog";
import lkSubTable from "@/components/lkWidgetForm/src/lkSubTable.vue";
import EmailCoopSubTableMessageBox from "./emailCoopSubTableMessageBox.vue";
import { TransformSubmitDataStructure } from "@/utils/formDesignerUtils";
import { cloneDeep, storageSession } from "@pureadmin/utils";
import dayjs from "dayjs";
import { VTable } from "@visactor/vue-vtable";

// 自定义表头组件，支持子表查看按钮
const CustomHeaderColumn = props => {
  const { column, width, onViewSubTable, quoteVisible, t } = props;

  return h(
    "div",
    {
      class: "subtable-column-body-custom",
      style: {
        width: `${width}px`,
        pointerEvents: "auto",
        cursor: "pointer",
        display: "flex",
        alignItems: "center"
      }
    },
    [
      h(
        "span",
        {
          class: "subtable-column-title",
          style: { flex: "1", fontSize: "14px" }
        },
        column.title || column.props?.title
      ),

      // 查看子表按钮（仅在TABLE_FORM类型且可引用时显示）
      column.hasSubTableViewButton && quoteVisible
        ? h(
            "span",
            {
              class: "column-icon",
              style: {
                width: "20px",
                textAlign: "right",
                cursor: "pointer",
                marginLeft: "5px"
              },
              onClick: () =>
                onViewSubTable(column.fieldName, column.subTableColumns)
            },
            [
              h(
                ElTooltip,
                {
                  content: t("trade_email_subTableViews"),
                  placement: "top",
                  "show-after": 500
                },
                {
                  default: () =>
                    h("i", {
                      class: "iconfont link-open",
                      style: {
                        fontSize: "12px",
                        fontWeight: "normal",
                        color: "#797979"
                      }
                    })
                }
              )
            ]
          )
        : null
    ]
  );
};

// 组件props定义
const props = defineProps({
  workOrderId: { type: String, default: "" },
  milestoneId: { type: String, default: "" },
  worderName: { type: String, default: "" },
  quoteVisible: { type: Boolean, default: false }
});

// 事件emit定义
const emit = defineEmits([
  "handleQuoteData",
  "handleQuoteSubTableData",
  "handleViewSubTable"
]);
const { t } = useI18n();

// 关键ref定义
const widgetDialogRef = ref(null); // 弹窗ref
const EmailCoopSubTableMessageBoxRef = ref(null); // 二次确认弹窗ref
const lkSubTableRef = ref(null); // 子表格ref
const widgetField = ref(""); // 当前操作字段
const widgetDialogTitle = ref(""); // 弹窗标题
const tableWidgetConfig = ref({ type: "DrTableForm", children: [] }); // 表格控件配置
const tableData = ref([]); // 表格数据

// 响应式的选中行数量状态
const checkedRowCount = ref(0);

// 更新选中行数量的方法
const updateCheckedRowCount = () => {
  if (
    lkSubTableRef.value &&
    typeof lkSubTableRef.value.getCheckboxCheckedRow === "function"
  ) {
    checkedRowCount.value = lkSubTableRef.value.getCheckboxCheckedRow().length;
  } else {
    checkedRowCount.value = 0;
  }
};

// 初始化表格事件监听
const initTableEvents = () => {
  if (!lkSubTableRef.value?.tableInstance) return;
  lkSubTableRef.value.tableInstance.off(
    VTable.ListTable.EVENT_TYPE.CHECKBOX_STATE_CHANGE
  );
  updateCheckedRowCount();
};

/**
 * 获取控件类型映射
 * @param widgetType 字段类型
 * @returns 组件类型字符串
 */
function getWidgetTypeMapping(widgetType) {
  const typeMapping = {
    RATE: "DrRate",
    RICH_TEXT: "DrEditor",
    EXCHANGE_RATE: "DrExchangeRates",
    PERCENT: "DrPercentage",
    FILE_UPLOAD: "DrFilesUpload",
    IMAGE_UPLOAD: "DrImagesUpload",
    SIGNATURE: "DrSignature",
    MEMBER: "DrSCCSMemberSelect",
    MULTIPLE_MEMBER: "DrSCCSMemberSelect",
    COOP_TEAM: "DrSCCSGroupMemberSelect",
    MULTIPLE_COOP_TEAM: "DrSCCSGroupMemberSelect",
    CHECKBOX: "DrCheckbox",
    RADIO: "DrRadio",
    TABLE_FORM: "DrTableForm",
    INPUT: "DrInput",
    INPUT_NUMBER: "DrInputNumber",
    TEXTAREA: "DrTextarea",
    DATE_PICKER: "DrDatePicker"
  };
  return typeMapping[widgetType] || "DrInput";
}

/**
 * 获取列的选项（如单选、多选等）
 * @param column 列配置
 * @param name 字段名
 * @returns 选项数组
 */
function getColumnOptions(column, name) {
  let options = column.childrenList || column.options || [];
  if (options.length === 0) {
    // 从sessionStorage中查找widgetData
    const storageData = storageSession().getItem("widgetData") as any;
    if (storageData?.entrieTemplateForm) {
      // 递归查找对应控件的options
      const findWidgetOptions = widgets => {
        for (const widget of widgets) {
          if (widget._fc_id === name && widget.props?.options) {
            return widget.props.options;
          }
          if (widget.children) {
            const found = findWidgetOptions(widget.children);
            if (found) return found;
          }
        }
        return null;
      };
      for (const milestone of storageData.entrieTemplateForm.milestoneList ||
        []) {
        for (const form of milestone.formList || []) {
          const milestoneOptions = findWidgetOptions(form.widgetJsonList || []);
          if (milestoneOptions) {
            options = milestoneOptions;
            break;
          }
        }
        if (options.length > 0) break;
      }
    }
  }
  return options;
}

/**
 * 构建表格列配置
 * @param columns 动态字段配置
 * @returns 表格列数组
 */
function buildTableColumns(columns) {
  const baseColumns = [
    {
      children: [
        {
          type: "DrInput",
          _fc_id: "subject",
          props: { titleEn: "Subject", title: t("trade_email_subject") },
          title: t("trade_email_subject"),
          display: true,
          $required: false
        }
      ]
    },
    {
      children: [
        {
          type: "DrInput",
          _fc_id: "submitEmail",
          props: { titleEn: "Email", title: t("trade_login_email") },
          title: t("trade_login_email"),
          display: true,
          $required: false
        }
      ]
    }
  ];

  const dynamicColumns = columns.map(column => {
    const { label, name, widgetType } = column;
    const componentType = getWidgetTypeMapping(widgetType);
    const columnConfig: Record<string, any> = {
      type: componentType,
      _fc_id: name,
      props: { title: label, titleEn: label },
      title: label,
      display: true,
      $required: false
    };

    // 针对单选/多选控件，补充options
    if (componentType === "DrRadio" || componentType === "DrCheckbox") {
      columnConfig.props.options = getColumnOptions(column, name);
    } else if (componentType === "DrRate") {
      columnConfig.props.starCount = column.starCount || 5;
    }

    // 为 TABLE_FORM 类型添加表头自定义按钮
    if (widgetType === "TABLE_FORM") {
      columnConfig.hasSubTableViewButton = true;
      columnConfig.subTableColumns = column.childrenList || [];
      columnConfig.fieldName = name;
    }

    return { children: [columnConfig] };
  });

  const timeColumns = [
    {
      children: [
        {
          type: "DrDatePicker",
          _fc_id: "createTime",
          props: {
            title: t("trade_common_creatorTime"),
            titleEn: "Create Time"
          },
          title: t("trade_common_creatorTime"),
          display: true,
          $required: false
        }
      ]
    },
    {
      children: [
        {
          type: "DrDatePicker",
          _fc_id: "updateTime",
          props: {
            title: t("trade_common_updateTime"),
            titleEn: "Update Time"
          },
          title: t("trade_common_updateTime"),
          display: true,
          $required: false
        }
      ]
    }
  ];

  return [...baseColumns, ...dynamicColumns, ...timeColumns];
}

/**
 * 构建表格数据列表
 * @param tableDataInput 原始数据
 * @param field 当前字段
 * @returns 表格数据数组
 */
function buildTableList(tableDataInput, field) {
  const tableList = [];
  tableDataInput.forEach(tableRow => {
    const widgetData = TransformSubmitDataStructure(
      tableRow.workOrderCoopEmailDataVO.widgetDataList,
      tableRow.workOrderCoopEmailDataVO.widgetDataSubMap,
      tableRow.workOrderCoopEmailDataVO.linkedReferenceMap
    );
    const widgetItem = widgetData.find(widget => widget.widgetId === field);
    if (widgetItem?.childrenList) {
      widgetItem.childrenList.forEach(widgetChild => {
        let orderWidget = {};
        widgetChild.forEach(widgetChildItem => {
          orderWidget[widgetChildItem["widgetId"]] = [
            "IMAGE_UPLOAD",
            "FILE_UPLOAD"
          ].includes(widgetChildItem.widgetType)
            ? widgetChildItem["obj"]
            : widgetChildItem["label"];
        });
        tableList.push({
          ...cloneDeep(tableRow),
          ...orderWidget,
          rowId: Date.now() + Math.floor(Math.random() * 1000), // 唯一行id
          originalData: widgetChild, // 原始控件数据
          originalRowData: tableRow, // 完整的原始行数据
          widgetList: widgetChild,
          subject: tableRow.subject,
          submitEmail: tableRow.submitEmail,
          createTime: tableRow.createTime,
          updateTime: tableRow.updateTime
        });
      });
    }
  });
  return tableList;
}

/**
 * 格式化表格数据，适配控件类型
 * @param tableList 表格数据
 * @param tableColumns 表格列
 * @returns 格式化后的二维数组
 */
function buildFormattedTableData(tableList, tableColumns) {
  return tableList.map(row => {
    return tableColumns.map(column => {
      const fieldId = column.children[0]._fc_id;
      const widgetType = column.children[0].type;
      const originalWidgetData = row.originalData?.find(
        widget => widget.widgetId === fieldId
      );
      const cellData: Record<string, any> = {
        widgetId: fieldId,
        widgetType,
        label: "",
        obj: "",
        originalRowData: row.originalRowData // 保存原始行数据用于子表查看
      };
      if (originalWidgetData) {
        switch (widgetType) {
          case "DrSCCSMemberSelect":
          case "DrSCCSGroupMemberSelect":
          case "DrSCCSMemberSingleSelect":
          case "DrSCCSGroupMemberSingleSelect":
          case "DrSCCSMemberMultipleSelect":
          case "DrSCCSGroupMemberMultipleSelect":
            cellData.label = originalWidgetData.label || "";
            cellData.obj = originalWidgetData.obj || [];
            cellData.showObj = originalWidgetData.showObj || [];
            break;
          case "DrExchangeRates":
            cellData.label = originalWidgetData.label || "";
            cellData.obj = originalWidgetData.obj || {};
            break;
          default:
            cellData.label = originalWidgetData.label || "";
            cellData.obj =
              originalWidgetData.obj || originalWidgetData.label || "";
        }
      } else {
        if (fieldId === "subject" || fieldId === "submitEmail") {
          cellData.label = row[fieldId] || "";
          cellData.obj = row[fieldId] || "";
        } else if (fieldId === "createTime" || fieldId === "updateTime") {
          const timeValue = row[fieldId]
            ? dayjs(new Date(row[fieldId])).format("YYYY-MM-DD HH:mm:ss")
            : "";
          cellData.label = timeValue;
          cellData.obj = timeValue;
        }
      }
      return cellData;
    });
  });
}

/**
 * 处理查看子表数据的点击事件
 * @param fieldName 字段名
 * @param childColumns 子列配置
 */
const handleViewSubTable = (fieldName, childColumns) => {
  const currentTableData = [];
  tableData.value.forEach(row => {
    const subjectCell = row.find(cell => cell.widgetId === "subject");
    if (subjectCell && subjectCell.originalRowData) {
      const exists = currentTableData.some(
        item => item.id === subjectCell.originalRowData.id
      );
      if (!exists) {
        currentTableData.push(subjectCell.originalRowData);
      }
    }
  });
  emit("handleViewSubTable", fieldName, childColumns, currentTableData);
};

// 打开子表单弹窗，并初始化相关数据
const open = (columns, tableDataInput, field) => {
  widgetDialogRef.value.open();
  widgetField.value = field;
  const tableList = buildTableList(tableDataInput, field);
  const tableColumns = buildTableColumns(columns);

  // 为列配置添加自定义表头支持
  const enhancedTableColumns = tableColumns.map(column => {
    const childColumn = column.children[0];

    // 如果是 TABLE_FORM 类型且有子表查看按钮，添加自定义表头
    if (childColumn.hasSubTableViewButton && props.quoteVisible) {
      return {
        ...column,
        children: [
          {
            ...childColumn,
            headerCustomLayout: args => {
              const { table, row, col, rect } = args;
              const { height, width } = rect ?? table.getCellRect(col, row);

              const container = new VTable.CustomLayout.Group({
                height,
                width,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                // @ts-ignore
                vue: {
                  element: h(CustomHeaderColumn, {
                    column: childColumn,
                    width: width,
                    quoteVisible: props.quoteVisible,
                    onViewSubTable: handleViewSubTable,
                    t: t
                  }),
                  container: table.headerDomContainer
                }
              });

              return {
                rootContainer: container,
                renderDefault: false
              };
            }
          }
        ]
      };
    }
    return column;
  });

  // 配置表格控件
  tableWidgetConfig.value = {
    type: "DrTableForm",
    children: [{ type: "rowSeriesNumber" }, ...enhancedTableColumns]
  };
  // 格式化表格数据并赋值
  tableData.value = buildFormattedTableData(tableList, tableColumns);
  checkedRowCount.value = 0;
  nextTick(() => {
    initTableEvents();
  });
};

/**
 * 点击弹窗确认按钮，打开二次确认弹窗
 */
const handleConfirmWidget = () => {
  EmailCoopSubTableMessageBoxRef.value.open(checkedRowCount.value);
};

/**
 * 二次确认弹窗确认，关闭弹窗并回传选中数据
 */
const handleSubTableConfirm = () => {
  EmailCoopSubTableMessageBoxRef.value.close();
  widgetDialogRef.value.close();
  emit("handleQuoteSubTableData", {
    records: cloneDeep(lkSubTableRef.value.getCheckboxCheckedRow()),
    widgetField: widgetField.value
  });
};

watch(
  () => checkedRowCount.value,
  newCount => {
    nextTick(() => {
      widgetDialogRef.value?.handleSetConfirmBtnDisabled(newCount === 0);
    });
  },
  { immediate: true }
);

defineExpose({ open });
</script>

<style lang="scss" scoped>
@use "./index.scss";
</style>
<style lang="scss">
.dialog-footer-left-body {
  display: inline-block;
  margin-right: 38px;
  font-size: 14px;
  line-height: 14px;
  color: #606266;
  text-align: left;

  .dialog-footer-left-title {
    padding: 0 3px;
    font-weight: bold;
    color: #262626;
  }
}
</style>
