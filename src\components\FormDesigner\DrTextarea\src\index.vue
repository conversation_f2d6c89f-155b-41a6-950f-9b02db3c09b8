<template>
  <el-input
    ref="DrTextareaRef"
    v-model="widgetFormData[widgetConfigure._fc_id]"
    :rows="widgetConfigure.props.rows"
    type="textarea"
    :placeholder="placeholder"
    @keydown.enter.stop
    @change="handleChange"
  />
</template>
<script lang="ts" setup>
import { computed, inject, onMounted, ref, watch } from "vue";
import { ElInput } from "element-plus";
import { cloneDeep, storageLocal } from "@pureadmin/utils";

const props = defineProps({
  // 表单控件配置
  widgetConfigure: {
    type: Object as PropType<any>,
    default: () => {}
  },
  // 当前表单绑定的值
  trendsForm: {
    type: Object as PropType<any>,
    default: () => {}
  },
  // 行索引
  widgetRowIndex: {
    type: Number as PropType<number>,
    default: -1
  }
});

const placeholder = computed(() => {
  const translationLang = storageLocal().getItem("translationLang");

  return translationLang === "en"
    ? props.widgetConfigure.props?.placeholderEn ||
        props.widgetConfigure.props?.placeholder
    : props.widgetConfigure.props?.placeholder;
});

const DrTextareaRef = ref<HTMLElement | any>(null);
const widgetFormData = ref<any>({});
const handleWidgetFormsValue: any = inject("handleWidgetFormsValue", null);
const actuatorDefaultValue: any = inject("ActuatorDefaultValue", null);
const invocationDefaultValueExecutor: any = inject(
  "invocationDefaultValueExecutor",
  null
);

const emit = defineEmits([
  "handleSubTableWidgetValueChange",
  "handleRenderFinish"
]);

watch(
  () => ({ ...props.trendsForm }),
  (newVal, oldVal) => {
    widgetFormData.value = cloneDeep(props.trendsForm);

    if (actuatorDefaultValue) {
      const defaultValueConfig =
        props.widgetConfigure.props.defaultValueConfig || {};

      if (
        defaultValueConfig.type === "relate" &&
        newVal &&
        oldVal &&
        newVal.hasOwnProperty(defaultValueConfig.content) &&
        oldVal.hasOwnProperty(defaultValueConfig.content) &&
        newVal[defaultValueConfig.content] !==
          oldVal[defaultValueConfig.content]
      ) {
        widgetFormData.value[props.widgetConfigure._fc_id] =
          invocationDefaultValueExecutor(
            props.widgetConfigure.props.defaultValueConfig
          );
      } else if (defaultValueConfig.type === "formula") {
        const formulasParams = defaultValueConfig.content.formulasParams.map(
          param => param.split("@@")[1]
        );

        for (let formulasParam of formulasParams) {
          if (
            newVal &&
            oldVal &&
            newVal.hasOwnProperty(formulasParam) &&
            oldVal.hasOwnProperty(formulasParam) &&
            newVal[formulasParam] !== oldVal[formulasParam]
          ) {
            widgetFormData.value[props.widgetConfigure._fc_id] =
              invocationDefaultValueExecutor(
                props.widgetConfigure.props.defaultValueConfig
              );
            return;
          }
        }
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const handleChange = () => {
  const widgetId = props.widgetConfigure._fc_id;
  if (props.widgetRowIndex !== -1) {
    emit(
      "handleSubTableWidgetValueChange",
      widgetId,
      {
        label: widgetFormData.value[widgetId],
        obj: widgetFormData.value[widgetId],
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  } else {
    handleWidgetFormsValue(
      widgetId,
      {
        label: widgetFormData.value[widgetId],
        obj: widgetFormData.value[widgetId],
        widgetId: widgetId,
        $rowIndex: props.widgetRowIndex
      },
      widgetFormData.value[widgetId]
    );
  }
};

// watch(
//   () => widgetFormData.value[props.widgetConfigure._fc_id],
//   (newVal, oldVal) => {
//     if (
//       newVal !== props.trendsForm[props.widgetConfigure._fc_id] &&
//       !(!newVal && !oldVal)
//     ) {
//       const widgetId = props.widgetConfigure._fc_id;
//       if (props.widgetRowIndex !== -1) {
//         emit(
//           "handleSubTableWidgetValueChange",
//           widgetId,
//           {
//             label: widgetFormData.value[widgetId],
//             obj: widgetFormData.value[widgetId],
//             widgetId: widgetId,
//             $rowIndex: props.widgetRowIndex
//           },
//           widgetFormData.value[widgetId]
//         );
//       } else {
//         handleWidgetFormsValue(
//           widgetId,
//           {
//             label: widgetFormData.value[widgetId],
//             obj: widgetFormData.value[widgetId],
//             widgetId: widgetId,
//             $rowIndex: props.widgetRowIndex
//           },
//           widgetFormData.value[widgetId]
//         );
//       }
//     }
//   },
//   {
//     deep: true,
//     immediate: true
//   }
// );

onMounted(() => {
  emit("handleRenderFinish", DrTextareaRef.value);
});
</script>
<!--  -->
