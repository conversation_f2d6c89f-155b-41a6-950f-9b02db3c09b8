<template>
  <div class="login-form-container">
    <el-form
      ref="resetFormRef"
      :model="resetForm"
      :rules="resetRules"
      label-position="top"
      size="large"
    >
      <!-- 设置密码 -->
      <div class="login-form-item">
        <el-form-item
          :label="t('trade_common_setPassword')"
          prop="password"
          required
        >
          <el-input
            v-model="resetForm.password"
            type="password"
            :placeholder="t('trade_common_setPasswordTip')"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>
      </div>

      <!-- 确认密码 -->
      <div class="login-form-item">
        <el-form-item
          :label="t('trade_common_confirmPassword')"
          prop="confirmPassword"
          required
        >
          <el-input
            v-model="resetForm.confirmPassword"
            type="password"
            :placeholder="t('trade_login_passwordConfirmPlace')"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>
      </div>

      <!-- 错误信息显示 -->
      <UserError :respMessage="respMessage" :formData="resetForm" />

      <!-- 提交按钮 -->
      <el-button
        type="primary"
        class="login-button"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        {{ t("trade_login_resetAndLogin") }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch, type PropType } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { resetPswByEmail } from "@/api/login";
import UserError from "./UserError.vue";

const { t } = useI18n();

// Props
const props = defineProps({
  email: {
    type: String as PropType<string>,
    required: true
  }
});

// 表单引用
const resetFormRef = ref<FormInstance>();

// 状态管理
const submitLoading = ref<boolean>(false);
const respMessage = ref<string>("");

// 表单数据
interface ResetForm {
  password: string;
  confirmPassword: string;
}

const resetForm = reactive<ResetForm>({
  password: "",
  confirmPassword: ""
});

// 密码验证函数
const validatePassword = (rule: any, value: any, callback: any) => {
  const regex =
    /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9,./<>?;\'\:\"\[\]\\\{\}\|\`\-\=\~\!\@#\$%\^&*()_+]{6,16}$/;
  if (regex.test(value)) {
    callback();
  } else {
    callback(new Error(t("trade_common_setPasswordTip")));
  }
};

// 确认密码验证函数
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === resetForm.password) {
    callback();
  } else {
    callback(new Error(t("trade_login_passwordInconsistency")));
  }
};

// 表单验证规则
const resetRules = computed<FormRules<ResetForm>>(() => {
  return {
    password: [
      {
        required: true,
        message: t("trade_login_passwordPlaceholder"),
        trigger: "blur"
      },
      { validator: validatePassword, trigger: ["blur", "change"] }
    ],
    confirmPassword: [
      {
        required: true,
        message: t("trade_common_confirmPasswordTip"),
        trigger: "blur"
      },
      { validator: validatePassword, trigger: ["blur", "change"] },
      { validator: validateConfirmPassword, trigger: ["blur", "change"] }
    ]
  };
});

watch(
  () => resetForm.password,
  () => {
    if (resetForm.confirmPassword) {
      resetFormRef.value?.validateField("confirmPassword");
    }
  }
);

// 事件定义
const emit = defineEmits<{
  handleLoginSuccess: [data: any];
}>();

// 处理提交
const handleSubmit = async () => {
  if (!resetFormRef.value) return;

  try {
    await resetFormRef.value.validate();
  } catch (error) {
    return;
  }

  submitLoading.value = true;
  respMessage.value = "";

  try {
    const response = await resetPswByEmail({
      email: props.email,
      password: resetForm.password
    });

    if (response.code !== 0) {
      respMessage.value = response.node || response.msg;
      ElMessage.error(response.msg);
    } else {
      emit("handleLoginSuccess", response.data);
    }
  } catch (error: any) {
    console.error("重置密码失败:", error);
    const errorMsg = error?.response?.data?.msg || "重置密码失败，请重试";
    respMessage.value = errorMsg;
    ElMessage.error(errorMsg);
  } finally {
    submitLoading.value = false;
  }
};

// 监听表单变化，清除错误信息
watch(
  () => resetForm,
  () => {
    respMessage.value = "";
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
@use "./index.scss";
</style>
